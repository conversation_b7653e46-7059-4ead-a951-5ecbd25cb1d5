package I11lI1I1I1.Il11Ill1ll.l111ll1lll.lI1111111I;

import android.R$attr;

/* loaded from: classes.dex */
public final class lllIlIl1ll {
    public static final int ColorStateListItem_alpha = 3;
    public static final int ColorStateListItem_android_alpha = 1;
    public static final int ColorStateListItem_android_color = 0;
    public static final int ColorStateListItem_android_lStar = 2;
    public static final int ColorStateListItem_lStar = 4;
    public static final int FontFamilyFont_android_font = 0;
    public static final int FontFamilyFont_android_fontStyle = 2;
    public static final int FontFamilyFont_android_fontVariationSettings = 4;
    public static final int FontFamilyFont_android_fontWeight = 1;
    public static final int FontFamilyFont_android_ttcIndex = 3;
    public static final int FontFamilyFont_font = 5;
    public static final int FontFamilyFont_fontStyle = 6;
    public static final int FontFamilyFont_fontVariationSettings = 7;
    public static final int FontFamilyFont_fontWeight = 8;
    public static final int FontFamilyFont_ttcIndex = 9;
    public static final int FontFamily_fontProviderAuthority = 0;
    public static final int FontFamily_fontProviderCerts = 1;
    public static final int FontFamily_fontProviderFetchStrategy = 2;
    public static final int FontFamily_fontProviderFetchTimeout = 3;
    public static final int FontFamily_fontProviderPackage = 4;
    public static final int FontFamily_fontProviderQuery = 5;
    public static final int FontFamily_fontProviderSystemFontFamily = 6;
    public static final int GradientColorItem_android_color = 0;
    public static final int GradientColorItem_android_offset = 1;
    public static final int GradientColor_android_centerColor = 7;
    public static final int GradientColor_android_centerX = 3;
    public static final int GradientColor_android_centerY = 4;
    public static final int GradientColor_android_endColor = 1;
    public static final int GradientColor_android_endX = 10;
    public static final int GradientColor_android_endY = 11;
    public static final int GradientColor_android_gradientRadius = 5;
    public static final int GradientColor_android_startColor = 0;
    public static final int GradientColor_android_startX = 8;
    public static final int GradientColor_android_startY = 9;
    public static final int GradientColor_android_tileMode = 6;
    public static final int GradientColor_android_type = 2;
    public static final int[] ColorStateListItem = {R$attr.color, R$attr.alpha, R$attr.lStar, 2130968631, 2130969180};
    public static final int[] FontFamily = {2130969029, 2130969030, 2130969031, 2130969032, 2130969033, 2130969034, 2130969035};
    public static final int[] FontFamilyFont = {R$attr.font, R$attr.fontWeight, R$attr.fontStyle, R$attr.ttcIndex, R$attr.fontVariationSettings, 2130969027, 2130969036, 2130969037, 2130969038, 2130969896};
    public static final int[] GradientColor = {R$attr.startColor, R$attr.endColor, R$attr.type, R$attr.centerX, R$attr.centerY, R$attr.gradientRadius, R$attr.tileMode, R$attr.centerColor, R$attr.startX, R$attr.startY, R$attr.endX, R$attr.endY};
    public static final int[] GradientColorItem = {R$attr.color, R$attr.offset};

    private lllIlIl1ll() {
    }
}
