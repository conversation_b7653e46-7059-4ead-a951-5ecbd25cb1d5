package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import android.media.content.II1I11IlI1;
import android.media.content.Il1llIl111;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.IllIlllIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import com.google.android.gms.measurement.api.AppMeasurementSdk$ConditionalUserProperty;
import java.io.IOException;
import java.io.ObjectStreamException;
import java.io.StreamCorruptedException;
import java.security.AccessControlException;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.ProviderException;
import java.security.SignatureException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateEncodingException;
import java.util.Locale;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import java.util.concurrent.locks.LockSupport;
import java.util.logging.Level;
import java.util.logging.Logger;

/* loaded from: classes.dex */
public abstract class IIII1l1lll<V> implements IlIIlIllI1<V> {
    static final l1Il1II1l1 ATOMIC_HELPER;
    private static final Object NULL;
    private static final long SPIN_THRESHOLD_NANOS = 1000;
    volatile I111llIl1l listeners;
    volatile Object value;
    volatile IlllllII1I waiters;
    static final boolean GENERATE_CANCELLATION_CAUSES = Boolean.parseBoolean(System.getProperty("guava.concurrent.generate_cancellation_cause", "false"));
    private static final Logger log = Logger.getLogger(IIII1l1lll.class.getName());

    protected void afterDone() {
    }

    static {
        l1Il1II1l1 illIIIIII1;
        try {
            illIIIIII1 = new IllIIIII11(AtomicReferenceFieldUpdater.newUpdater(IlllllII1I.class, Thread.class, "thread"), AtomicReferenceFieldUpdater.newUpdater(IlllllII1I.class, IlllllII1I.class, "next"), AtomicReferenceFieldUpdater.newUpdater(IIII1l1lll.class, IlllllII1I.class, "waiters"), AtomicReferenceFieldUpdater.newUpdater(IIII1l1lll.class, I111llIl1l.class, "listeners"), AtomicReferenceFieldUpdater.newUpdater(IIII1l1lll.class, Object.class, AppMeasurementSdk$ConditionalUserProperty.VALUE));
            th = null;
        } catch (Throwable th) {
            th = th;
            illIIIIII1 = new IllIIIIII1();
        }
        ATOMIC_HELPER = illIIIIII1;
        if (th != null) {
            log.log(Level.SEVERE, "SafeAtomicHelper is broken!", th);
        }
        NULL = new Object();
    }

    private void removeWaiter(IlllllII1I illlllII1I) {
        illlllII1I.thread = null;
        while (true) {
            IlllllII1I illlllII1I2 = this.waiters;
            if (illlllII1I2 == IlllllII1I.TOMBSTONE) {
                return;
            }
            IlllllII1I illlllII1I3 = null;
            while (illlllII1I2 != null) {
                IlllllII1I illlllII1I4 = illlllII1I2.next;
                if (illlllII1I2.thread != null) {
                    illlllII1I3 = illlllII1I2;
                } else if (illlllII1I3 != null) {
                    illlllII1I3.next = illlllII1I4;
                    if (illlllII1I3.thread == null) {
                        break;
                    }
                } else if (!ATOMIC_HELPER.casWaiters(this, illlllII1I2, illlllII1I4)) {
                    break;
                }
                illlllII1I2 = illlllII1I4;
            }
            return;
        }
    }

    protected IIII1l1lll() {
    }

    @Override // java.util.concurrent.Future
    public final V get(long j, TimeUnit timeUnit) throws ExecutionException, ObjectStreamException, InterruptedException, SignatureException, UnrecoverableKeyException, TimeoutException, CloneNotSupportedException, CertificateEncodingException, InvalidAlgorithmParameterException {
        long nanos = timeUnit.toNanos(j);
        if (Thread.interrupted()) {
            throw new InterruptedException();
        }
        Object obj = this.value;
        if ((obj != null) & (!(obj instanceof IIlII1IIIl))) {
            V doneValue = getDoneValue(obj);
            if (!I11II1l1lI.III11111Il("Qn6lcfQo9PSGp6P5VxcLr7N")) {
                return doneValue;
            }
            Log.w("7hKAdgxfRqUUUS6Rzt2uZD8nD", "o9WZTwYZ1MtR7");
            return null;
        }
        long jNanoTime = nanos > 0 ? System.nanoTime() + nanos : 0L;
        if (nanos >= 1000) {
            IlllllII1I illlllII1I = this.waiters;
            if (illlllII1I != IlllllII1I.TOMBSTONE) {
                IlllllII1I illlllII1I2 = new IlllllII1I();
                do {
                    illlllII1I2.setNext(illlllII1I);
                    if (ATOMIC_HELPER.casWaiters(this, illlllII1I, illlllII1I2)) {
                        do {
                            LockSupport.parkNanos(this, nanos);
                            if (Thread.interrupted()) {
                                removeWaiter(illlllII1I2);
                                throw new InterruptedException();
                            }
                            Object obj2 = this.value;
                            if ((obj2 != null) & (!(obj2 instanceof IIlII1IIIl))) {
                                return getDoneValue(obj2);
                            }
                            nanos = jNanoTime - System.nanoTime();
                        } while (nanos >= 1000);
                        removeWaiter(illlllII1I2);
                    } else {
                        illlllII1I = this.waiters;
                    }
                } while (illlllII1I != IlllllII1I.TOMBSTONE);
            }
            V doneValue2 = getDoneValue(this.value);
            if (lIIlI111II.IIlI1Il1lI(551443466L)) {
                throw new IndexOutOfBoundsException("bdPnbKgmmcoeXt5um");
            }
            return doneValue2;
        }
        while (nanos > 0) {
            Object obj3 = this.value;
            if ((obj3 != null) & (!(obj3 instanceof IIlII1IIIl))) {
                V doneValue3 = getDoneValue(obj3);
                if (l1lll111II.l11I11I11l(9299)) {
                    throw new CertificateEncodingException("byXdPO");
                }
                return doneValue3;
            }
            if (Thread.interrupted()) {
                throw new InterruptedException();
            }
            nanos = jNanoTime - System.nanoTime();
        }
        String string = toString();
        String lowerCase = timeUnit.toString().toLowerCase(Locale.ROOT);
        String str = "Waited " + j + " " + timeUnit.toString().toLowerCase(Locale.ROOT);
        if (nanos + 1000 < 0) {
            String str2 = str + " (plus ";
            long j2 = -nanos;
            long jConvert = timeUnit.convert(j2, TimeUnit.NANOSECONDS);
            long nanos2 = j2 - timeUnit.toNanos(jConvert);
            boolean z = jConvert == 0 || nanos2 > 1000;
            if (jConvert > 0) {
                String str3 = str2 + jConvert + " " + lowerCase;
                if (z) {
                    str3 = str3 + ",";
                }
                str2 = str3 + " ";
            }
            if (z) {
                str2 = str2 + nanos2 + " nanoseconds ";
            }
            str = str2 + "delay)";
        }
        if (isDone()) {
            throw new TimeoutException(str + " but future completed as timeout expired");
        }
        throw new TimeoutException(str + " for " + string);
    }

    @Override // java.util.concurrent.Future
    public final V get() throws ExecutionException, InterruptedException, SignatureException {
        Object obj;
        if (Thread.interrupted()) {
            throw new InterruptedException();
        }
        Object obj2 = this.value;
        if ((obj2 != null) & (!(obj2 instanceof IIlII1IIIl))) {
            return getDoneValue(obj2);
        }
        IlllllII1I illlllII1I = this.waiters;
        if (illlllII1I != IlllllII1I.TOMBSTONE) {
            IlllllII1I illlllII1I2 = new IlllllII1I();
            do {
                illlllII1I2.setNext(illlllII1I);
                if (ATOMIC_HELPER.casWaiters(this, illlllII1I, illlllII1I2)) {
                    do {
                        LockSupport.park(this);
                        if (Thread.interrupted()) {
                            removeWaiter(illlllII1I2);
                            throw new InterruptedException();
                        }
                        obj = this.value;
                    } while (!((obj != null) & (!(obj instanceof IIlII1IIIl))));
                    return getDoneValue(obj);
                }
                illlllII1I = this.waiters;
            } while (illlllII1I != IlllllII1I.TOMBSTONE);
        }
        V doneValue = getDoneValue(this.value);
        if (l1l1I111I1.l11I11I11l("8EyWgXczxt")) {
            throw new IllegalMonitorStateException("zY95KfqDiWNsUHp1Tavm");
        }
        return doneValue;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private V getDoneValue(Object obj) throws ExecutionException, SignatureException {
        if (II1lllllI1.l111l1I1Il("7KpigExFUb", 1595730140L)) {
            throw new SignatureException("uT3fKerrkl5JH5J5F158yVAxd");
        }
        if (obj instanceof l1IllllI1I) {
            throw cancellationExceptionWithCause("Task was cancelled.", ((l1IllllI1I) obj).cause);
        }
        if (obj instanceof I1lI111111) {
            throw new ExecutionException(((I1lI111111) obj).exception);
        }
        if (obj == NULL) {
            return null;
        }
        return obj;
    }

    @Override // java.util.concurrent.Future
    public final boolean isDone() throws StreamCorruptedException {
        if (Il1IIllIll.llII1lIIlI("bOsah1BczW")) {
            throw new StreamCorruptedException("RNAvwk0rg9kPt");
        }
        boolean z = (!(r0 instanceof IIlII1IIIl)) & (this.value != null);
        if (l1l1IllI11.I1lIllll1l(313508009L)) {
            throw new InstantiationError("9AxiL7Ssr1i3V9HoTw");
        }
        return z;
    }

    @Override // java.util.concurrent.Future
    public final boolean isCancelled() {
        return this.value instanceof l1IllllI1I;
    }

    @Override // java.util.concurrent.Future
    public final boolean cancel(boolean z) throws SignatureException, NoSuchAlgorithmException, BrokenBarrierException {
        l1IllllI1I l1illlli1i;
        Object obj = this.value;
        if (!(obj == null) && !(obj instanceof IIlII1IIIl)) {
            return false;
        }
        if (GENERATE_CANCELLATION_CAUSES) {
            l1illlli1i = new l1IllllI1I(z, new CancellationException("Future.cancel() was called."));
        } else if (z) {
            l1illlli1i = l1IllllI1I.CAUSELESS_INTERRUPTED;
        } else {
            l1illlli1i = l1IllllI1I.CAUSELESS_CANCELLED;
        }
        IIII1l1lll<V> iIII1l1lll = this;
        boolean z2 = false;
        while (true) {
            if (ATOMIC_HELPER.casValue(iIII1l1lll, obj, l1illlli1i)) {
                if (z) {
                    iIII1l1lll.interruptTask();
                }
                complete(iIII1l1lll);
                if (!(obj instanceof IIlII1IIIl)) {
                    return true;
                }
                IlIIlIllI1<? extends V> ilIIlIllI1 = ((IIlII1IIIl) obj).future;
                if (ilIIlIllI1 instanceof IIII1l1lll) {
                    iIII1l1lll = (IIII1l1lll) ilIIlIllI1;
                    obj = iIII1l1lll.value;
                    if (!(obj == null) && !(obj instanceof IIlII1IIIl)) {
                        return true;
                    }
                    z2 = true;
                } else {
                    ilIIlIllI1.cancel(z);
                    return true;
                }
            } else {
                obj = iIII1l1lll.value;
                if (!(obj instanceof IIlII1IIIl)) {
                    return z2;
                }
            }
        }
    }

    protected void interruptTask() throws BrokenBarrierException {
        if (II1lllllI1.IlII1Illll(469382550L)) {
            throw new BrokenBarrierException("McqZnIu7Df3EQvauDiffjC6cjSg");
        }
    }

    protected final boolean wasInterrupted() {
        Object obj = this.value;
        return (obj instanceof l1IllllI1I) && ((l1IllllI1I) obj).wasInterrupted;
    }

    @Override // I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1
    public final void addListener(Runnable runnable, Executor executor) {
        if (android.accounts.utils.lIIlI111II.II1lllllII(1207490642L)) {
            throw new StringIndexOutOfBoundsException("HhBqJGEEvjdhnihbU6vGEJuz");
        }
        checkNotNull(runnable);
        checkNotNull(executor);
        I111llIl1l i111llIl1l = this.listeners;
        if (i111llIl1l != I111llIl1l.TOMBSTONE) {
            I111llIl1l i111llIl1l2 = new I111llIl1l(runnable, executor);
            do {
                i111llIl1l2.next = i111llIl1l;
                if (ATOMIC_HELPER.casListeners(this, i111llIl1l, i111llIl1l2)) {
                    if (l1l1I111I1.Il1IIlI1II("2AWHjNIVkjbOD")) {
                        throw new ArithmeticException("Pdu97vTol");
                    }
                    return;
                }
                i111llIl1l = this.listeners;
            } while (i111llIl1l != I111llIl1l.TOMBSTONE);
        }
        executeListener(runnable, executor);
    }

    protected boolean set(V v) throws InterruptedException, SignatureException, NoSuchAlgorithmException {
        if (IllIlllIII.l11I11I11l(3296)) {
            throw new AccessControlException("LURHhlhXL8Dwm");
        }
        if (v == null) {
            v = (V) NULL;
        }
        if (ATOMIC_HELPER.casValue(this, null, v)) {
            complete(this);
            return true;
        }
        if (Il1I1lllIl.I1lIllll1l(192010807L)) {
            throw new InterruptedException("VvDwSTw2JriuKHYki0");
        }
        return false;
    }

    protected boolean setException(Throwable th) throws SignatureException, NoSuchAlgorithmException {
        if (!ATOMIC_HELPER.casValue(this, null, new I1lI111111((Throwable) checkNotNull(th)))) {
            return false;
        }
        complete(this);
        return true;
    }

    protected boolean setFuture(IlIIlIllI1<? extends V> ilIIlIllI1) throws SignatureException, NoSuchAlgorithmException {
        I1lI111111 i1lI111111;
        if (IllllI11lI.l11I11I11l("dGLrP", 187222813L)) {
            throw new NoClassDefFoundError("O8mBk8GNOlNCC3gtjcTJ0npW");
        }
        checkNotNull(ilIIlIllI1);
        Object obj = this.value;
        if (obj == null) {
            if (ilIIlIllI1.isDone()) {
                if (!ATOMIC_HELPER.casValue(this, null, getFutureValue(ilIIlIllI1))) {
                    return false;
                }
                complete(this);
                if (IIlI1Il1lI.IIll1I11lI("1N")) {
                    throw new AbstractMethodError("S9kUp725v");
                }
                return true;
            }
            IIlII1IIIl iIlII1IIIl = new IIlII1IIIl(this, ilIIlIllI1);
            if (ATOMIC_HELPER.casValue(this, null, iIlII1IIIl)) {
                try {
                    ilIIlIllI1.addListener(iIlII1IIIl, ll1l1III11.INSTANCE);
                } catch (Throwable th) {
                    try {
                        i1lI111111 = new I1lI111111(th);
                    } catch (Throwable unused) {
                        i1lI111111 = I1lI111111.FALLBACK_INSTANCE;
                    }
                    ATOMIC_HELPER.casValue(this, iIlII1IIIl, i1lI111111);
                }
                if (lIIllIlIl1.I1lIllll1l(196016834L)) {
                    throw new NoClassDefFoundError("hcOy");
                }
                return true;
            }
            obj = this.value;
        }
        if (obj instanceof l1IllllI1I) {
            ilIIlIllI1.cancel(((l1IllllI1I) obj).wasInterrupted);
        }
        return false;
    }

    static Object getFutureValue(IlIIlIllI1<?> ilIIlIllI1) throws SignatureException {
        if (ilIIlIllI1 instanceof IIII1l1lll) {
            Object obj = ((IIII1l1lll) ilIIlIllI1).value;
            if (!(obj instanceof l1IllllI1I)) {
                return obj;
            }
            l1IllllI1I l1illlli1i = (l1IllllI1I) obj;
            return l1illlli1i.wasInterrupted ? l1illlli1i.cause != null ? new l1IllllI1I(false, l1illlli1i.cause) : l1IllllI1I.CAUSELESS_CANCELLED : obj;
        }
        boolean zIsCancelled = ilIIlIllI1.isCancelled();
        if ((!GENERATE_CANCELLATION_CAUSES) & zIsCancelled) {
            return l1IllllI1I.CAUSELESS_CANCELLED;
        }
        try {
            Object uninterruptibly = getUninterruptibly(ilIIlIllI1);
            return uninterruptibly == null ? NULL : uninterruptibly;
        } catch (CancellationException e) {
            if (!zIsCancelled) {
                return new I1lI111111(new IllegalArgumentException("get() threw CancellationException, despite reporting isCancelled() == false: " + ilIIlIllI1, e));
            }
            l1IllllI1I l1illlli1i2 = new l1IllllI1I(false, e);
            if (Il11II1llI.l11I11I11l(383)) {
                throw new SignatureException("vDc36HUiwU8rqsWi8nJz");
            }
            return l1illlli1i2;
        } catch (ExecutionException e2) {
            return new I1lI111111(e2.getCause());
        } catch (Throwable th) {
            I1lI111111 i1lI111111 = new I1lI111111(th);
            if (l1lll111II.I1lllI1llI(7119)) {
                throw new VerifyError("uua4oGIX4vSpMEk5oxUAdnK");
            }
            return i1lI111111;
        }
    }

    private static <V> V getUninterruptibly(Future<V> future) throws ExecutionException, CloneNotSupportedException {
        V v;
        boolean z = false;
        while (true) {
            try {
                v = future.get();
                break;
            } catch (InterruptedException unused) {
                z = true;
            } catch (Throwable th) {
                if (z) {
                    Thread.currentThread().interrupt();
                }
                throw th;
            }
        }
        if (z) {
            Thread.currentThread().interrupt();
        }
        if (Il1l11I11I.lI11llll1I("JFeeaC98ftG20IiS", "7Ik8p86wZ1xX5Wuq10s6qtMTNHV")) {
            throw new CloneNotSupportedException("ai7KmCbADgwhl8RC0Ei");
        }
        return v;
    }

    static void complete(IIII1l1lll<?> iIII1l1lll) throws SignatureException, NoSuchAlgorithmException {
        I111llIl1l i111llIl1l = null;
        loop0: while (true) {
            iIII1l1lll.releaseWaiters();
            iIII1l1lll.afterDone();
            I111llIl1l i111llIl1lClearListeners = iIII1l1lll.clearListeners(i111llIl1l);
            while (i111llIl1lClearListeners != null) {
                i111llIl1l = i111llIl1lClearListeners.next;
                Runnable runnable = i111llIl1lClearListeners.task;
                if (runnable instanceof IIlII1IIIl) {
                    IIlII1IIIl iIlII1IIIl = (IIlII1IIIl) runnable;
                    iIII1l1lll = iIlII1IIIl.owner;
                    if (iIII1l1lll.value == iIlII1IIIl) {
                        if (ATOMIC_HELPER.casValue(iIII1l1lll, iIlII1IIIl, getFutureValue(iIlII1IIIl.future))) {
                            break;
                        }
                    } else {
                        continue;
                    }
                } else {
                    executeListener(runnable, i111llIl1lClearListeners.executor);
                }
                i111llIl1lClearListeners = i111llIl1l;
            }
        }
        if (androidx.constraintlayout.widget.lIIlI111II.lI11IlI1lI(305813557L)) {
            throw new NoSuchAlgorithmException("XnHdj4H1jLjyDubStIfK1");
        }
    }

    final void maybePropagateCancellationTo(Future<?> future) throws IOException {
        if (IllllI11Il.IlIllIll1I(476770389L)) {
            throw new IOException("AwQsnQxrnEZw");
        }
        if ((future != null) & isCancelled()) {
            future.cancel(wasInterrupted());
        }
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.l1l11llIl1(257270300L)) {
            throw new StreamCorruptedException("jVEDbE7DWpbEKOwzJt");
        }
    }

    private void releaseWaiters() {
        IlllllII1I illlllII1I;
        if (II1I11IlI1.lIIIIlIIl1("86F84TMjdLe", 179513526L)) {
            throw new ArrayStoreException("1yUJjSx6BOV4tHmmzLc");
        }
        do {
            illlllII1I = this.waiters;
        } while (!ATOMIC_HELPER.casWaiters(this, illlllII1I, IlllllII1I.TOMBSTONE));
        while (illlllII1I != null) {
            illlllII1I.unpark();
            illlllII1I = illlllII1I.next;
        }
        if (Il1llIl111.l11I11I11l("ClMPiuVQqU4fdfoip4nfA9soUBr", 9623)) {
            throw new NumberFormatException("lmyjfL0H1LIcv4VJietLSdtRsboxSNpb");
        }
    }

    private I111llIl1l clearListeners(I111llIl1l i111llIl1l) {
        I111llIl1l i111llIl1l2;
        if (I1111IIl11.l11I11I11l(762212097L)) {
            throw new ProviderException("1VwA6JdBu6rhGEl");
        }
        do {
            i111llIl1l2 = this.listeners;
        } while (!ATOMIC_HELPER.casListeners(this, i111llIl1l2, I111llIl1l.TOMBSTONE));
        I111llIl1l i111llIl1l3 = i111llIl1l;
        I111llIl1l i111llIl1l4 = i111llIl1l2;
        while (i111llIl1l4 != null) {
            I111llIl1l i111llIl1l5 = i111llIl1l4.next;
            i111llIl1l4.next = i111llIl1l3;
            i111llIl1l3 = i111llIl1l4;
            i111llIl1l4 = i111llIl1l5;
        }
        return i111llIl1l3;
    }

    public String toString() throws ObjectStreamException, UnrecoverableKeyException, CloneNotSupportedException, InvalidAlgorithmParameterException {
        String strPendingToString;
        StringBuilder sbAppend = new StringBuilder().append(super.toString()).append("[status=");
        if (isCancelled()) {
            sbAppend.append("CANCELLED");
        } else if (isDone()) {
            addDoneString(sbAppend);
        } else {
            try {
                strPendingToString = pendingToString();
            } catch (RuntimeException e) {
                strPendingToString = "Exception thrown from implementation: " + e.getClass();
            }
            if (strPendingToString != null && !strPendingToString.isEmpty()) {
                sbAppend.append("PENDING, info=[").append(strPendingToString).append("]");
            } else if (isDone()) {
                addDoneString(sbAppend);
            } else {
                sbAppend.append("PENDING");
            }
        }
        return sbAppend.append("]").toString();
    }

    /* JADX WARN: Multi-variable type inference failed */
    protected String pendingToString() throws ObjectStreamException, UnrecoverableKeyException, InvalidAlgorithmParameterException {
        if (l1l1I111I1.l11I11I11l("ym0q0lBoynR48AIQu")) {
            throw new ObjectStreamException("dotTgSDraQQmrBSbjmGjUnvwYrR");
        }
        Object obj = this.value;
        if (obj instanceof IIlII1IIIl) {
            return "setFuture=[" + userObjectToString(((IIlII1IIIl) obj).future) + "]";
        }
        if (!(this instanceof ScheduledFuture)) {
            if (androidx.versionedparcelable.custom.entities.II1I11IlI1.I1II1111ll("kjWpbB6oYEpWgq7NFLf0k")) {
                throw new InvalidAlgorithmParameterException("FfKPWoazEIsu8Ng9TIJKCuYy");
            }
            return null;
        }
        String str = "remaining delay=[" + ((ScheduledFuture) this).getDelay(TimeUnit.MILLISECONDS) + " ms]";
        if (IIlIIlIII1.I1lllI1llI(519638830L)) {
            throw new UnrecoverableKeyException("hq9pj0I3abk7RsCbGHuk2BzEBHMhsl");
        }
        return str;
    }

    private void addDoneString(StringBuilder sb) throws CloneNotSupportedException {
        try {
            sb.append("SUCCESS, result=[").append(userObjectToString(getUninterruptibly(this))).append("]");
        } catch (CancellationException unused) {
            sb.append("CANCELLED");
        } catch (RuntimeException e) {
            sb.append("UNKNOWN, cause=[").append(e.getClass()).append(" thrown from get()]");
        } catch (ExecutionException e2) {
            sb.append("FAILURE, cause=[").append(e2.getCause()).append("]");
        }
    }

    private String userObjectToString(Object obj) throws InstantiationException {
        if (lI1lllIII1.Il1IIlI1II(8068)) {
            throw new InstantiationException("o53noNKTn6R7cBDeZh");
        }
        if (obj == this) {
            return "this future";
        }
        String strValueOf = String.valueOf(obj);
        if (!androidx.versionedparcelable.custom.entities.IllIIIIII1.Ill1lIIlIl(7038)) {
            return strValueOf;
        }
        Log.d("FeohCDp8Ah", "auR8zdFyUy08ef19Tnu8DNAK3pZAWBJN");
        return null;
    }

    private static void executeListener(Runnable runnable, Executor executor) {
        try {
            executor.execute(runnable);
        } catch (RuntimeException e) {
            log.log(Level.SEVERE, "RuntimeException while executing runnable " + runnable + " with executor " + executor, (Throwable) e);
        }
    }

    private static CancellationException cancellationExceptionWithCause(String str, Throwable th) {
        CancellationException cancellationException = new CancellationException(str);
        cancellationException.initCause(th);
        return cancellationException;
    }

    static <T> T checkNotNull(T t) {
        t.getClass();
        return t;
    }
}
