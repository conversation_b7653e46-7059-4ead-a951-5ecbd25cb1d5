package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public abstract class l1Il1II1l1 {
    abstract boolean casListeners(IIII1l1lll<?> iIII1l1lll, I111llIl1l i111llIl1l, I111llIl1l i111llIl1l2);

    abstract boolean casValue(IIII1l1lll<?> iIII1l1lll, Object obj, Object obj2);

    abstract boolean casWaiters(IIII1l1lll<?> iIII1l1lll, IlllllII1I illlllII1I, IlllllII1I illlllII1I2);

    abstract void putNext(IlllllII1I illlllII1I, IlllllII1I illlllII1I2);

    abstract void putThread(IlllllII1I illlllII1I, Thread thread);

    private l1Il1II1l1() {
    }
}
