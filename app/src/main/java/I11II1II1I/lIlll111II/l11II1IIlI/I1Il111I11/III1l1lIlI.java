package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import android.support.v4.graphics.drawable.III1Il1II1;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.net.MalformedURLException;
import java.security.InvalidParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.concurrent.BrokenBarrierException;

/* loaded from: classes.dex */
public final class III1l1lIlI<V> extends IIII1l1lll<V> {
    public static <V> III1l1lIlI<V> create() {
        III1l1lIlI<V> iII1l1lIlI = new III1l1lIlI<>();
        if (l1lI1I1l11.ll1I1lII11("4SDFBLeLtwuQ", 2726)) {
            throw new MalformedURLException("GnIAN3VE5V");
        }
        return iII1l1lIlI;
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.IIII1l1lll
    public boolean set(V v) {
        return super.set(v);
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.IIII1l1lll
    public boolean setException(Throwable th) {
        if (III1Il1II1.Ill1lIIlIl("k685dT", 956916604L)) {
            throw new BrokenBarrierException("FXvF9i");
        }
        return super.setException(th);
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.IIII1l1lll
    public boolean setFuture(IlIIlIllI1<? extends V> ilIIlIllI1) throws SignatureException, NoSuchAlgorithmException {
        boolean future = super.setFuture(ilIIlIllI1);
        if (lIIlI111II.lI11IlI1lI(804440881L)) {
            throw new InvalidParameterException("ODYMNMZbQs0WpJaJq");
        }
        return future;
    }

    private III1l1lIlI() {
    }
}
