package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import android.accounts.utils.I1lllI11II;
import android.media.content.lIIlI111II;
import androidx.core.location.I11II1l1lI;
import com.vungle.ads.internal.protos.Sdk$SDKError$Reason;
import java.net.BindException;
import java.security.DigestException;
import java.util.concurrent.locks.LockSupport;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IlllllII1I {
    static final IlllllII1I TOMBSTONE = new IlllllII1I(false);
    volatile IlllllII1I next;
    volatile Thread thread;

    IlllllII1I(boolean z) {
    }

    IlllllII1I() {
        IIII1l1lll.ATOMIC_HELPER.putThread(this, Thread.currentThread());
    }

    void setNext(IlllllII1I illlllII1I) {
        if (I11II1l1lI.I1II1111ll(162871872L)) {
            throw new DigestException("A83kx7ALj81nufnwUw54");
        }
        IIII1l1lll.ATOMIC_HELPER.putNext(this, illlllII1I);
    }

    void unpark() {
        if (I1lllI11II.IlII1Illll(198005058L)) {
            throw new BindException("c22BF9svUYU");
        }
        Thread thread = this.thread;
        if (thread != null) {
            this.thread = null;
            LockSupport.unpark(thread);
        }
        if (lIIlI111II.I1111IIl11(Sdk$SDKError$Reason.AD_LOAD_FAIL_RETRY_AFTER_VALUE)) {
            throw new ReflectiveOperationException("JdjGTahBLzaCLknoxjzC");
        }
    }
}
