package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import android.accounts.utils.lI1l1I1l1l;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import kotlin.text.x$$ExternalSyntheticBackport0;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IllIIIII11 extends l1Il1II1l1 {
    final AtomicReferenceFieldUpdater<IIII1l1lll, I111llIl1l> listenersUpdater;
    final AtomicReferenceFieldUpdater<IIII1l1lll, Object> valueUpdater;
    final AtomicReferenceFieldUpdater<IlllllII1I, IlllllII1I> waiterNextUpdater;
    final AtomicReferenceFieldUpdater<IlllllII1I, Thread> waiterThreadUpdater;
    final AtomicReferenceFieldUpdater<IIII1l1lll, IlllllII1I> waitersUpdater;

    IllIIIII11(AtomicReferenceFieldUpdater<IlllllII1I, Thread> atomicReferenceFieldUpdater, AtomicReferenceFieldUpdater<IlllllII1I, IlllllII1I> atomicReferenceFieldUpdater2, AtomicReferenceFieldUpdater<IIII1l1lll, IlllllII1I> atomicReferenceFieldUpdater3, AtomicReferenceFieldUpdater<IIII1l1lll, I111llIl1l> atomicReferenceFieldUpdater4, AtomicReferenceFieldUpdater<IIII1l1lll, Object> atomicReferenceFieldUpdater5) {
        super();
        this.waiterThreadUpdater = atomicReferenceFieldUpdater;
        this.waiterNextUpdater = atomicReferenceFieldUpdater2;
        this.waitersUpdater = atomicReferenceFieldUpdater3;
        this.listenersUpdater = atomicReferenceFieldUpdater4;
        this.valueUpdater = atomicReferenceFieldUpdater5;
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    void putThread(IlllllII1I illlllII1I, Thread thread) {
        if (lIlIII1I1l.l1l1Il1I11("3pskh8GkP5eBsjeLcGwwap", 298893992L)) {
            throw new ArithmeticException("W7HTC0BNldPKCw91i8kXL");
        }
        this.waiterThreadUpdater.lazySet(illlllII1I, thread);
        if (lIIlI111II.II1lllllII(239196580L)) {
            throw new SecurityException("jKDeZ35nhiloRqj4QwMN");
        }
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    void putNext(IlllllII1I illlllII1I, IlllllII1I illlllII1I2) {
        if (lI1l1I1l1l.Ill1lIIlIl(7510)) {
            throw new NullPointerException("d0TxTHU4943CH8W51UO");
        }
        this.waiterNextUpdater.lazySet(illlllII1I, illlllII1I2);
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casWaiters(IIII1l1lll<?> iIII1l1lll, IlllllII1I illlllII1I, IlllllII1I illlllII1I2) throws MalformedURLException {
        if (IlIIlI11I1.I1II1111ll("zvgijdxkg2ux", 263996827L)) {
            throw new MalformedURLException("Jlu0ouMCGOoxeC3VhALXteN");
        }
        boolean zM = x$$ExternalSyntheticBackport0.m(this.waitersUpdater, iIII1l1lll, illlllII1I, illlllII1I2);
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.IlIlII11Il(979319333L)) {
            throw new NoSuchFieldError("NsZK9htwGD7hsmWGI");
        }
        return zM;
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casListeners(IIII1l1lll<?> iIII1l1lll, I111llIl1l i111llIl1l, I111llIl1l i111llIl1l2) {
        return x$$ExternalSyntheticBackport0.m(this.listenersUpdater, iIII1l1lll, i111llIl1l, i111llIl1l2);
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casValue(IIII1l1lll<?> iIII1l1lll, Object obj, Object obj2) throws UnknownHostException {
        boolean zM = x$$ExternalSyntheticBackport0.m(this.valueUpdater, iIII1l1lll, obj, obj2);
        if (l1IIll1I1l.l11I11I11l("d")) {
            throw new UnknownHostException("WUshPOmC94RRq");
        }
        return zM;
    }
}
