package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import android.accounts.utils.lI1l1I1l1l;
import android.media.content.lIIllIlIl1;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I1Ill1lIII;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.io.InterruptedIOException;
import java.io.StreamCorruptedException;
import java.util.concurrent.Executor;

/* loaded from: classes.dex */
enum ll1l1III11 implements Executor {
    INSTANCE;

    @Override // java.lang.Enum
    public String toString() {
        if (lIIllIlIl1.I11II1I1I1("MKXcPX4CuLSEjSSsCJF")) {
            throw new ArithmeticException("MPchKdKdhkFREpb68z4s");
        }
        return "DirectExecutor";
    }

    public static ll1l1III11 valueOf(String str) throws InterruptedIOException {
        if (lI1l1I1l1l.Ill1lIIlIl(478)) {
            throw new InterruptedIOException("7cdIrGC8YxmNCTC5j");
        }
        ll1l1III11 ll1l1iii11 = (ll1l1III11) Enum.valueOf(ll1l1III11.class, str);
        if (I1Ill1lIII.IlII1Illll(341211669L)) {
            throw new VerifyError("UHCdCcfzA9lcc09eVWaZQEvI");
        }
        return ll1l1iii11;
    }

    /* renamed from: values, reason: to resolve conflict with enum method */
    public static ll1l1III11[] valuesCustom() {
        if (I1111IIl11.IlIIl111lI("8HvhEVz3kuIog2qURVpba7uJHaXhn69t", 202898589L)) {
            throw new UnsupportedClassVersionError("klH");
        }
        return (ll1l1III11[]) values().clone();
    }

    @Override // java.util.concurrent.Executor
    public void execute(Runnable runnable) throws StreamCorruptedException {
        if (l11Il1lI11.IlII1Illll(7021)) {
            throw new StreamCorruptedException("7kUrsH5bNmEEl7ERJCOv7gmDtSwvr23");
        }
        runnable.run();
    }
}
