package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l1IllllI1I {
    static final l1IllllI1I CAUSELESS_CANCELLED;
    static final l1IllllI1I CAUSELESS_INTERRUPTED;
    final Throwable cause;
    final boolean wasInterrupted;

    static {
        if (IIII1l1lll.GENERATE_CANCELLATION_CAUSES) {
            CAUSELESS_CANCELLED = null;
            CAUSELESS_INTERRUPTED = null;
        } else {
            CAUSELESS_CANCELLED = new l1IllllI1I(false, null);
            CAUSELESS_INTERRUPTED = new l1IllllI1I(true, null);
        }
    }

    l1IllllI1I(boolean z, Throwable th) {
        this.wasInterrupted = z;
        this.cause = th;
    }
}
