package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import java.util.concurrent.Executor;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class I111llIl1l {
    static final I111llIl1l TOMBSTONE = new I111llIl1l(null, null);
    final Executor executor;
    I111llIl1l next;
    final Runnable task;

    I111llIl1l(Runnable runnable, Executor executor) {
        this.task = runnable;
        this.executor = executor;
    }
}
