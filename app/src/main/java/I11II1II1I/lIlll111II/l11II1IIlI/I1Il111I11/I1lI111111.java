package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class I1lI111111 {
    static final I1lI111111 FALLBACK_INSTANCE = new I1lI111111(new IIl11IIIlI("Failure occurred while trying to finish a future."));
    final Throwable exception;

    I1lI111111(Throwable th) {
        this.exception = (Throwable) IIII1l1lll.checkNotNull(th);
    }
}
