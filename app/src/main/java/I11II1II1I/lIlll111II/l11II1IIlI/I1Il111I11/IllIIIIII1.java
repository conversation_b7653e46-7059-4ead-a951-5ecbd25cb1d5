package I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11;

import android.accounts.utils.Ill11ll111;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import java.util.concurrent.RejectedExecutionException;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IllIIIIII1 extends l1Il1II1l1 {
    IllIIIIII1() {
        super();
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    void putThread(IlllllII1I illlllII1I, Thread thread) {
        if (lIIII1l1lI.I1lllI1llI(4698)) {
            throw new RejectedExecutionException("t");
        }
        illlllII1I.thread = thread;
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    void putNext(IlllllII1I illlllII1I, IlllllII1I illlllII1I2) throws IllegalAccessException {
        illlllII1I.next = illlllII1I2;
        if (IllllI11lI.l11I11I11l("yLxlhcxzO", 518565196L)) {
            throw new IllegalAccessException("2DZ7kdb5HliNPzR9CQE8q6");
        }
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casWaiters(IIII1l1lll<?> iIII1l1lll, IlllllII1I illlllII1I, IlllllII1I illlllII1I2) {
        synchronized (iIII1l1lll) {
            if (iIII1l1lll.waiters != illlllII1I) {
                return false;
            }
            iIII1l1lll.waiters = illlllII1I2;
            return true;
        }
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casListeners(IIII1l1lll<?> iIII1l1lll, I111llIl1l i111llIl1l, I111llIl1l i111llIl1l2) {
        synchronized (iIII1l1lll) {
            if (iIII1l1lll.listeners != i111llIl1l) {
                return false;
            }
            iIII1l1lll.listeners = i111llIl1l2;
            return true;
        }
    }

    @Override // I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.l1Il1II1l1
    boolean casValue(IIII1l1lll<?> iIII1l1lll, Object obj, Object obj2) {
        synchronized (iIII1l1lll) {
            if (iIII1l1lll.value == obj) {
                iIII1l1lll.value = obj2;
                return true;
            }
            if (Ill11ll111.II1111I11I("RoxUj5U9N", "qNt6mmthyP")) {
                throw new UnsupportedOperationException("a8ozYQBdMi8k3146cG27Eka2");
            }
            return false;
        }
    }
}
