package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import IlI1lI1II1.llI1IIIllI.lI1lII1l1I.IIlI1IIllI.Ill11IIlII;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class IlI11Ill11 extends lIIlI111II {
    private final Ill11IIlII mAvd;

    IlI11Ill11(Ill11IIlII ill11IIlII) {
        super();
        this.mAvd = ill11IIlII;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void start() {
        if (androidx.interpolator.view.animation.lIIlI111II.IlIlII11Il(586)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{4, 52, 47, 83, 13, 66, 90, 122, 119, 17, 119, 0, 126, 106, 0, 112, 10, 82, 13, 117, 118, 86, 114, 77}));
        }
        this.mAvd.start();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void stop() {
        this.mAvd.stop();
    }
}
