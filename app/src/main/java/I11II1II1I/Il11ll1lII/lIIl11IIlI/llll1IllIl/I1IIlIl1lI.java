package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$Callback;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1IIlIl1lI implements Drawable$Callback {
    private Drawable$Callback mCallback;

    @Override // android.graphics.drawable.Drawable$Callback
    public void invalidateDrawable(Drawable drawable) {
    }

    I1IIlIl1lI() {
    }

    public I1IIlIl1lI wrap(Drawable$Callback drawable$Callback) {
        if (I1I1IIIIl1.IlII1Illll(293943263L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{126, 35, 12, 38, 4, 99, 92, 99, 105, 61, 65, 100, 86, 77, 81, 99, 80, 37}));
        }
        this.mCallback = drawable$Callback;
        return this;
    }

    public Drawable$Callback unwrap() {
        if (IIlII1IIIl.l1l1Il1I11(I1I1lI1II1.a(new byte[]{79, 45, 0, 11, 44, 94, 111, 2, 88, 44, 3, 8, 112, 76, 95, 79, 7, 43, 86, 96, 83, 54, 114, 97, 123, 117}), 202711305L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{3, 13, 50, 31, 15, 66, 103, 105, 92, 29, 82, 88, 100, 8, 4, 119, 17, 50, 87, 107, 9, 17, 99, 68, 119, 120, 6, 21, 53, 96, 123, 15}));
        }
        Drawable$Callback drawable$Callback = this.mCallback;
        this.mCallback = null;
        return drawable$Callback;
    }

    @Override // android.graphics.drawable.Drawable$Callback
    public void scheduleDrawable(Drawable drawable, Runnable runnable, long j) throws UnsupportedEncodingException {
        if (IIIlIll111.IlII1Illll(302340197L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{80, 42, 53, 55}));
        }
        Drawable$Callback drawable$Callback = this.mCallback;
        if (drawable$Callback != null) {
            drawable$Callback.scheduleDrawable(drawable, runnable, j);
        }
    }

    @Override // android.graphics.drawable.Drawable$Callback
    public void unscheduleDrawable(Drawable drawable, Runnable runnable) throws MalformedURLException {
        if (Il1I1lllIl.ll1I1lII11(I1I1lI1II1.a(new byte[]{70, 62, 87, 33, 13, 112, 91, 64, 8, 44, 73, Byte.MAX_VALUE, 5, 73, 101, 82, 12, 25, 32, 87, 84, 85, 101, 97, 91}))) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{114, 92, 90, 29, 33, 94, 114, 0, 99, 13, 68, 93, 109, 72, 76, 113, 45, 18, 11, 2, 114, 39, 68, 79, 66, 108, 97, 21, 7}));
        }
        Drawable$Callback drawable$Callback = this.mCallback;
        if (drawable$Callback != null) {
            drawable$Callback.unscheduleDrawable(drawable, runnable);
        }
        if (android.accounts.utils.lIIlI111II.I1111l111I(785030780L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{117, 54}));
        }
    }
}
