package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class ll1ll1lIl1 implements Runnable {
    final /* synthetic */ IIllIIll1I this$0;

    ll1ll1lIl1(IIllIIll1I iIllIIll1I) {
        this.this$0 = iIllIIll1I;
    }

    @Override // java.lang.Runnable
    public void run() throws KeyManagementException {
        if (lIIllIlIl1.Il1IIlI1II(197581971L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{124, 20, 1, 9}));
        }
        this.this$0.animate(true);
        this.this$0.invalidateSelf();
        if (llIlI11III.l1l1l1IIlI(508214893L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{81, 30, 10, 8, 80, 4, 91, 113, 97, 37, 8, 96, 115, 90, 119, 89, 87, 19, 26, 91, 5, 40, 119, 120, 114}));
        }
    }
}
