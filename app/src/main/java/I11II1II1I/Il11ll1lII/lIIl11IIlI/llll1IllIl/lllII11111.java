package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIllllIlI1;
import android.accounts.utils.IIIlIl1I1l;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff$Mode;
import android.graphics.Rect;
import android.graphics.Region;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$Callback;
import android.media.content.II1I11IlI1;
import android.media.content.lll1IIII11;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.ProviderException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lllII11111 extends Drawable implements Drawable$Callback {
    private Drawable mDrawable;

    public lllII11111(Drawable drawable) {
        setWrappedDrawable(drawable);
    }

    @Override // android.graphics.drawable.Drawable
    public void draw(Canvas canvas) {
        this.mDrawable.draw(canvas);
        if (llIlI11III.l11I11I11l(6653)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{126, 39, 40, 61, 13, 114, 111, 113, Byte.MAX_VALUE, 39, Byte.MAX_VALUE, 1, 90, 107, 109, Byte.MAX_VALUE}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    protected void onBoundsChange(Rect rect) throws CertificateExpiredException {
        this.mDrawable.setBounds(rect);
        if (lI1lllIII1.I1lllI1llI(270541867L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{79, 30, 33, 11, 87, 88, 122, 5, 120, 34, 122}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setChangingConfigurations(int i) throws CharConversionException {
        this.mDrawable.setChangingConfigurations(i);
        if (IIIlIl1I1l.l11I11I11l(389211096L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{95, 45, 10, 4, 83, 67, 112, 71, 9, 93, 91, 113, 7, 72, 89, 86, 83, 11, 27, 81, 124, 40, 92}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public int getChangingConfigurations() {
        return this.mDrawable.getChangingConfigurations();
    }

    @Override // android.graphics.drawable.Drawable
    public void setDither(boolean z) {
        this.mDrawable.setDither(z);
    }

    @Override // android.graphics.drawable.Drawable
    public void setFilterBitmap(boolean z) {
        this.mDrawable.setFilterBitmap(z);
        if (l111Il1lI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{93, 34, 7, 40, 49, 66, 103, 65, 91, 34, 93, 99, 5, 118, 65, 125, 24, 39, 23, 102, 118, 44, 11, 98, 96, 68, 120, 8, 93}), 534824623L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{125, 35, 48, 36}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setAlpha(int i) {
        if (I11II1l1lI.IlIIl111lI(I1I1lI1II1.a(new byte[]{121, 55, 22, 38, 35, 112, 67, 4, 73, 81, 93, 97, 76, 124, 92, 70, 48, 38, 7, 80, 4, 89, 99, 69, 5, 100, 65, 32}), 173144662L)) {
            Log.v(I1I1lI1II1.a(new byte[]{121, 10, 56, 6, 12, 66, 90, 104, 115, 16, 70, 0, 99, 104, 95, 97, 10, 42, 44, 104}), I1I1lI1II1.a(new byte[]{102, 16, 7}));
        } else {
            this.mDrawable.setAlpha(i);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter) {
        this.mDrawable.setColorFilter(colorFilter);
    }

    @Override // android.graphics.drawable.Drawable
    public boolean isStateful() throws IOException {
        if (IllllI11Il.Il11lIlI1I(I1I1lI1II1.a(new byte[]{102, 8, 52, 50, 1, 126, 112, 90, 85, 93, 103, 69, 0, 77, 125, 1, 46}), I1I1lI1II1.a(new byte[]{86, 6, 11, 46, 85, 91, 3, 5, 114, 80, 90, 86, 5, 114, 98, 97, 27, 20, 52, 122, 0, 10, 73, 121}))) {
            throw new IOException(I1I1lI1II1.a(new byte[]{89, 87, 84, 39, 45, 68, 77, 73, 85, 51, 105, 4, 93, 122, 78, 95, 53, 42, 49, 112}));
        }
        return this.mDrawable.isStateful();
    }

    @Override // android.graphics.drawable.Drawable
    public boolean setState(int[] iArr) {
        return this.mDrawable.setState(iArr);
    }

    @Override // android.graphics.drawable.Drawable
    public int[] getState() {
        int[] state = this.mDrawable.getState();
        if (!android.support.v4.graphics.drawable.lIIlI111II.llllI1l1II(IronSourceConstants.NT_INSTANCE_LOAD_ERROR)) {
            return state;
        }
        Log.w(I1I1lI1II1.a(new byte[]{77}), I1I1lI1II1.a(new byte[]{64, 20, 40, 15, 1, 84, 110, 100, 14, 38, 95, 70, 76, 9, 99, 100, 4, 0, 38}));
        return null;
    }

    @Override // android.graphics.drawable.Drawable
    public void jumpToCurrentState() {
        this.mDrawable.jumpToCurrentState();
        if (Il1l11I11I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{7, 28, 20, 34, 59, 90, 71, 8, 87, 60, 0, 97, 125, 123, 85, 4, 82, 40, 14, 90, 117, 13, 116}), 256781480L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{66, 43, 23, 55, 13, 109}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public Drawable getCurrent() {
        if (lll1IIII11.I1II1111ll(220771410L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{123, 80, 49, 18, 52, 90, 121, 100, 99, 33, 66, 116, 68, 14, 112, 83, 10, 40, 20, 88, 125, 55}));
        }
        return this.mDrawable.getCurrent();
    }

    @Override // android.graphics.drawable.Drawable
    public boolean setVisible(boolean z, boolean z2) {
        boolean z3 = super.setVisible(z, z2) || this.mDrawable.setVisible(z, z2);
        if (IllIIIIII1.Il1IIlI1II(1421)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{64, 15, 46, 51, 52, 3, 15, 120, 113, 13, 119, 3, 92, 125, 65, 99, 59, 36, 85, 92, 92, 40, 74, 122, 1, 98}));
        }
        return z3;
    }

    @Override // android.graphics.drawable.Drawable
    public int getOpacity() throws KeyException {
        if (Il1l11I11I.IIll1I11lI(I1I1lI1II1.a(new byte[]{96, 6, 9, 35, 43, 120, 77, 3, 124, 60, 126, 106, 123, 94, 2, 95, 17, 50, 59, 107, 4, 25, 112, 88}), 9401)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{2, 13, 56, 54, 13, 6, 82, 106, 79, 12, 96, 104, 90, 79, 118, 6, 38, 3, 1, 107, 71, 81}));
        }
        int opacity = this.mDrawable.getOpacity();
        if (!I1I1IIIIl1.l1l1l1IIlI(5193)) {
            return opacity;
        }
        Log.v(I1I1lI1II1.a(new byte[]{71}), I1I1lI1II1.a(new byte[]{96, 18, 53, 12, 42}));
        return 0;
    }

    @Override // android.graphics.drawable.Drawable
    public Region getTransparentRegion() {
        return this.mDrawable.getTransparentRegion();
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        return this.mDrawable.getIntrinsicWidth();
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        return this.mDrawable.getIntrinsicHeight();
    }

    @Override // android.graphics.drawable.Drawable
    public int getMinimumWidth() {
        return this.mDrawable.getMinimumWidth();
    }

    @Override // android.graphics.drawable.Drawable
    public int getMinimumHeight() {
        return this.mDrawable.getMinimumHeight();
    }

    @Override // android.graphics.drawable.Drawable
    public boolean getPadding(Rect rect) {
        return this.mDrawable.getPadding(rect);
    }

    @Override // android.graphics.drawable.Drawable$Callback
    public void invalidateDrawable(Drawable drawable) throws IllegalAccessException {
        if (Il1lII1l1l.Il1IIlI1II(4796)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{96, 83, 27, 85, 8, 6, 112, 96, 108, 21, 4, 94, 122, 8, 97, 80, 80, 22, 9, 101, 115, 50, 69, 3, 95, 67, 83, 49, 93, 95, 0}));
        }
        invalidateSelf();
        if (lI11IlI1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{120, 45, 90, 8, 15, 88, 121, 98, 1}), 185895554L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{122, 55, 47, 1, 27, 86, 95, 4, 91, 37, 89, 117, 125, 94, Byte.MAX_VALUE, 88, 23, 7}));
        }
    }

    @Override // android.graphics.drawable.Drawable$Callback
    public void scheduleDrawable(Drawable drawable, Runnable runnable, long j) {
        scheduleSelf(runnable, j);
    }

    @Override // android.graphics.drawable.Drawable$Callback
    public void unscheduleDrawable(Drawable drawable, Runnable runnable) {
        unscheduleSelf(runnable);
    }

    @Override // android.graphics.drawable.Drawable
    protected boolean onLevelChange(int i) throws KeyStoreException {
        boolean level = this.mDrawable.setLevel(i);
        if (IllIIIIII1.I1lIllll1l(9345)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{102, 33, 58, 51, 11, 12, 124, 118, 105, 44, 105, 105, 119, 79, 66, 77, 82, 2, 6, 102}));
        }
        return level;
    }

    @Override // android.graphics.drawable.Drawable
    public void setAutoMirrored(boolean z) {
        IIllllIlI1.setAutoMirrored(this.mDrawable, z);
    }

    @Override // android.graphics.drawable.Drawable
    public boolean isAutoMirrored() {
        return IIllllIlI1.isAutoMirrored(this.mDrawable);
    }

    @Override // android.graphics.drawable.Drawable
    public void setTint(int i) throws MalformedURLException, EOFException {
        if (IIIlIll111.Ill1lIIlIl(1136)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{122, 60, 39}));
        }
        IIllllIlI1.setTint(this.mDrawable, i);
        if (lII1llllI1.lll1111l11(I1I1lI1II1.a(new byte[]{102, 11}), 8065)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{99, 49, 10, 53, 80, 126, 15, 116, 65}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setTintList(ColorStateList colorStateList) throws CertificateNotYetValidException {
        IIllllIlI1.setTintList(this.mDrawable, colorStateList);
    }

    @Override // android.graphics.drawable.Drawable
    public void setTintMode(PorterDuff$Mode porterDuff$Mode) throws KeyException, SocketTimeoutException {
        if (II1I11IlI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{71, 13, 59, 92, 26, 2, 111, 92, 87, 60, 96, 92, 81, 73, 0, 100, 18, 43, 58, 64, 93, 56, 103, 102, 121, 93, 91, 23, 87, 75}), 170605834L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{109, 52, 0, 12, 39, 101, 96, 72, 94, 53, 122, 102, 101, 12, 83, 100, 41, 54, 8, 118, 96, 87, 98}));
        }
        IIllllIlI1.setTintMode(this.mDrawable, porterDuff$Mode);
    }

    @Override // android.graphics.drawable.Drawable
    public void setHotspot(float f, float f2) {
        if (l1IIll1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{65, 40, 21, 48, 85, 69, 80, 72, 73, 61, 99, 2, 5, 77, 1, 5, 11, 10, 55, 107, 94, 12}), 7452)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{85, 80}));
        }
        IIllllIlI1.setHotspot(this.mDrawable, f, f2);
    }

    @Override // android.graphics.drawable.Drawable
    public void setHotspotBounds(int i, int i2, int i3, int i4) {
        if (Il1IIllIll.IIll1I11lI(I1I1lI1II1.a(new byte[]{126, 87, 52, 47, 21, 98, 70, 82, 109, 13, 64, 96, 80, 80, 6, 94, 38, 40, 37, 91}))) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{102, 32, 55, 61, 33, 90, 102, 123, 12, 49, 64, 118, 120, 80, 7, 5, 45, 12, 85, 67, 1, 38, 90, 88, 122, 2, 116, 60}));
        }
        IIllllIlI1.setHotspotBounds(this.mDrawable, i, i2, i3, i4);
    }

    public Drawable getWrappedDrawable() {
        if (lI1lI11Ill.I1lllI1llI(163645574L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{93, 11, 84, 92, 48, 121, 86, 88, 72, 87, 74, 73, 119, 15, 98, 64, 49, 25, 26, 71, 9, 89, 71, 97, 73, 69}));
        }
        Drawable drawable = this.mDrawable;
        if (android.support.v4.graphics.drawable.lIIlI111II.lI11IlI1lI(9599)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{92, 16, 1, 20, 56, 93, 115, 65, 110, 29, 115, 89, 95, 111, 118, 81, 19, 21, 40, 74, 6, 14, 65, 88, 71, 14, 123}));
        }
        return drawable;
    }

    public void setWrappedDrawable(Drawable drawable) {
        Drawable drawable2 = this.mDrawable;
        if (drawable2 != null) {
            drawable2.setCallback(null);
        }
        this.mDrawable = drawable;
        if (drawable != null) {
            drawable.setCallback(this);
        }
    }
}
