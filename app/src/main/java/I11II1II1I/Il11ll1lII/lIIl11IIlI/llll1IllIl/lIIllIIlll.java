package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.animation.TimeInterpolator;
import android.graphics.drawable.AnimationDrawable;
import java.net.NoRouteToHostException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class lIIllIIlll implements TimeInterpolator {
    private int[] mFrameTimes;
    private int mFrames;
    private int mTotalDuration;

    lIIllIIlll(AnimationDrawable animationDrawable, boolean z) {
        updateFrames(animationDrawable, z);
    }

    int updateFrames(AnimationDrawable animationDrawable, boolean z) {
        int numberOfFrames = animationDrawable.getNumberOfFrames();
        this.mFrames = numberOfFrames;
        int[] iArr = this.mFrameTimes;
        if (iArr == null || iArr.length < numberOfFrames) {
            this.mFrameTimes = new int[numberOfFrames];
        }
        int[] iArr2 = this.mFrameTimes;
        int i = 0;
        for (int i2 = 0; i2 < numberOfFrames; i2++) {
            int duration = animationDrawable.getDuration(z ? (numberOfFrames - i2) - 1 : i2);
            iArr2[i2] = duration;
            i += duration;
        }
        this.mTotalDuration = i;
        return i;
    }

    int getTotalDuration() {
        return this.mTotalDuration;
    }

    @Override // android.animation.TimeInterpolator
    public float getInterpolation(float f) throws NoRouteToHostException {
        int i = (int) ((f * this.mTotalDuration) + 0.5f);
        int i2 = this.mFrames;
        int[] iArr = this.mFrameTimes;
        int i3 = 0;
        while (i3 < i2) {
            int i4 = iArr[i3];
            if (i < i4) {
                break;
            }
            i -= i4;
            i3++;
        }
        float f2 = (i3 / i2) + (i3 < i2 ? i / this.mTotalDuration : 0.0f);
        if (android.support.v4.graphics.drawable.lIIlI111II.I1lll11llI(5899)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{97, 17, 7, 11, 37, 84, 5, 90, 97, 6, 101, 100, 13, 1, 65, 119, 1, 44, 51, 70, 8, 53, 95, 101, 91, 70, 97, 18, 44, 74, 112}));
        }
        return f2;
    }
}
