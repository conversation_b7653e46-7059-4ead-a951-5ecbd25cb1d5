package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.util.StateSet;
import androidx.core.location.Il1l11I11I;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.UnsupportedEncodingException;
import java.net.UnknownServiceException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1l1IIIl1I extends lll1lIll1I {
    int[][] mStateSets;

    I1l1IIIl1I(I1l1IIIl1I i1l1IIIl1I, lI1llII1I1 li1llii1i1, Resources resources) {
        super(i1l1IIIl1I, li1llii1i1, resources);
        if (i1l1IIIl1I != null) {
            this.mStateSets = i1l1IIIl1I.mStateSets;
        } else {
            this.mStateSets = new int[getCapacity()][];
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lll1lIll1I
    void mutate() throws UnknownServiceException {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.l1llI1llII(162826588L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{117, 8, 81, 28, 16, 116, 1, 120, 73, 33, 95, 121, 121, 83, 96, 89, 55, 80, 52, 67, 3, 24, 82, 121, 82, 15}));
        }
        int[][] iArr = this.mStateSets;
        int[][] iArr2 = new int[iArr.length][];
        for (int length = iArr.length - 1; length >= 0; length--) {
            int[] iArr3 = this.mStateSets[length];
            iArr2[length] = iArr3 != null ? (int[]) iArr3.clone() : null;
        }
        this.mStateSets = iArr2;
        if (lII1llllI1.Ill1lIIlIl(7129)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{91}));
        }
    }

    int addStateSet(int[] iArr, Drawable drawable) {
        if (I1I1IIIIl1.I111IlIl1I(I1I1lI1II1.a(new byte[]{116, 34, 5, 40, 24, 4, 126, 124, 79, 80, 97, 121, 126, 78, 90, 90, 91, 17, 53, 4}))) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{94, 38, 37, 22, 91, 67, 117, 97, 14, 53, 99, 4, 7}));
        }
        int iAddChild = addChild(drawable);
        this.mStateSets[iAddChild] = iArr;
        return iAddChild;
    }

    int indexOfStateSet(int[] iArr) {
        int[][] iArr2 = this.mStateSets;
        int childCount = getChildCount();
        for (int i = 0; i < childCount; i++) {
            if (StateSet.stateSetMatches(iArr2[i], iArr)) {
                return i;
            }
        }
        return -1;
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public Drawable newDrawable() throws UnsupportedEncodingException {
        if (Il1l11I11I.l11I11I11l(909367954L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{82, 33, 24, 38, 26, 95}));
        }
        return new lI1llII1I1(this, null);
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public Drawable newDrawable(Resources resources) {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 41, 46, 9, 45, 116, 5, 97, 109, 85, 1, 7, 80, 120, 93, 6, 49, 35, 4, 1, 4, 46, 117, 65, 1, 90, 0, 46, 20, 5, 70}), 191654727L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{90, 17, 42, 34, 53, 88, 112, 8, 0, 43, 105, 122, 116, 120, 100}));
        }
        return new lI1llII1I1(this, resources);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lll1lIll1I
    public void growArray(int i, int i2) {
        super.growArray(i, i2);
        int[][] iArr = new int[i2][];
        System.arraycopy(this.mStateSets, 0, iArr, 0, i);
        this.mStateSets = iArr;
    }
}
