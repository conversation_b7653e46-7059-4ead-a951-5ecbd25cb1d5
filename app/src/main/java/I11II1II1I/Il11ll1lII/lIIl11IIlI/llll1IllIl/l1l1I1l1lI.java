package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.animation.ObjectAnimator;
import android.graphics.drawable.AnimationDrawable;
import androidx.core.location.IllIlllIII;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class l1l1I1l1lI extends lIIlI111II {
    private final ObjectAnimator mAnim;
    private final boolean mHasReversibleFlag;

    l1l1I1l1lI(AnimationDrawable animationDrawable, boolean z, boolean z2) {
        super();
        int numberOfFrames = animationDrawable.getNumberOfFrames();
        int i = z ? numberOfFrames - 1 : 0;
        int i2 = z ? 0 : numberOfFrames - 1;
        lIIllIIlll liilliilll = new lIIllIIlll(animationDrawable, z);
        ObjectAnimator objectAnimatorOfInt = ObjectAnimator.ofInt(animationDrawable, I1I1lI1II1.a(new byte[]{84, 17, 16, 23, 7, 91, 67, 121, 87, 0, 85, 72}), i, i2);
        objectAnimatorOfInt.setAutoCancel(true);
        objectAnimatorOfInt.setDuration(liilliilll.getTotalDuration());
        objectAnimatorOfInt.setInterpolator(liilliilll);
        this.mHasReversibleFlag = z2;
        this.mAnim = objectAnimatorOfInt;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public boolean canReverse() {
        return this.mHasReversibleFlag;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void start() throws BrokenBarrierException {
        if (IllIlllIII.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{90, 14, 3, 43, 91, 3, 86, 124, 117, 17, 86, 103, 113, 81, 93, 1, 16, 39}), 7075)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{125, 9, 36, 23, 17, Byte.MAX_VALUE, 88, 85, 81, 53, 67, 3, 64, 118, 114, 112, 53, 54, 5, Byte.MAX_VALUE, 118, 89, 74}));
        }
        this.mAnim.start();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void reverse() {
        this.mAnim.reverse();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void stop() throws NoSuchFieldException {
        if (I1I1IIIIl1.I1lllI1llI(I1I1lI1II1.a(new byte[]{83, 86, 20, 53, 42, 120, 94, 7, 116, 6, Byte.MAX_VALUE, 101, 86, 123, 81, 66, 32, 34, 80, 70, 95, 84, 118, 101, 2, 112, 68, 82, 38}), 160888176L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{91, 30, 44, 23, 26, 111, 65, 91, 91, 60, 100, 66, 83, 104, 70, 109, 48, 87, 53, 69, 121, 40, 114, 114, 100}));
        }
        this.mAnim.cancel();
    }
}
