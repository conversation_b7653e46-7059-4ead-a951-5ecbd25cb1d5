package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.media.content.II1I11IlI1;
import android.media.content.lll1IIII11;
import android.util.Log;
import android.util.StateSet;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.StreamCorruptedException;
import java.io.UnsupportedEncodingException;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIl1I1II.I1Il1l1llI.IIlIllllII.lII1l11111;

/* loaded from: classes.dex */
class IIlIllIl1l extends I1l1IIIl1I {
    private static final long REVERSED_BIT = 4294967296L;
    private static final long REVERSIBLE_FLAG_BIT = 8589934592L;
    lII1l11111<Integer> mStateIds;
    lllIl1I1II.I1Il1l1llI.IIlIllllII.Il1lIIlll1<Long> mTransitions;

    private static long generateTransitionKey(int i, int i2) {
        if (!lIIII1l1lI.IlII1Illll(I1I1lI1II1.a(new byte[]{99}))) {
            return i2 | (i << 32);
        }
        Log.e(I1I1lI1II1.a(new byte[]{95, 1, 18, 9, 3, 125, 71, 5, 113, 49, 98, 118, 114, 110, 109}), I1I1lI1II1.a(new byte[]{101, 60, 27, 86, 46, 98, 92}));
        return 0L;
    }

    IIlIllIl1l(IIlIllIl1l iIlIllIl1l, IIl1IIllIl iIl1IIllIl, Resources resources) {
        super(iIlIllIl1l, iIl1IIllIl, resources);
        if (iIlIllIl1l != null) {
            this.mTransitions = iIlIllIl1l.mTransitions;
            this.mStateIds = iIlIllIl1l.mStateIds;
        } else {
            this.mTransitions = new lllIl1I1II.I1Il1l1llI.IIlIllllII.Il1lIIlll1<>();
            this.mStateIds = new lII1l11111<>();
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.I1l1IIIl1I, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lll1lIll1I
    void mutate() throws UnknownHostException {
        if (lll1IIII11.IlIllIll1I(305600780L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{121, 8, 14, 7, 47, 90, 124, 3, 122, 80, 125, 3, 86, 79, 67, 7, 19, 82, 82, 120, 104, 22, 64, 88, 88}));
        }
        this.mTransitions = this.mTransitions.clone();
        this.mStateIds = this.mStateIds.clone();
    }

    int addTransition(int i, int i2, Drawable drawable, boolean z) throws GeneralSecurityException {
        int iAddChild = super.addChild(drawable);
        long jGenerateTransitionKey = generateTransitionKey(i, i2);
        long j = z ? REVERSIBLE_FLAG_BIT : 0L;
        long j2 = iAddChild;
        this.mTransitions.append(jGenerateTransitionKey, Long.valueOf(j2 | j));
        if (z) {
            this.mTransitions.append(generateTransitionKey(i2, i), Long.valueOf(REVERSED_BIT | j2 | j));
        }
        return iAddChild;
    }

    int addStateSet(int[] iArr, Drawable drawable, int i) {
        int iAddStateSet = super.addStateSet(iArr, drawable);
        this.mStateIds.put(iAddStateSet, Integer.valueOf(i));
        return iAddStateSet;
    }

    int indexOfKeyframe(int[] iArr) throws KeyManagementException {
        if (IllllI11lI.l11I11I11l(I1I1lI1II1.a(new byte[]{88, 81, 53, 48, 49, 6, 80}), 225474703L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{113, 22, 26}));
        }
        int iIndexOfStateSet = super.indexOfStateSet(iArr);
        if (iIndexOfStateSet >= 0) {
            return iIndexOfStateSet;
        }
        int iIndexOfStateSet2 = super.indexOfStateSet(StateSet.WILD_CARD);
        if (IllIlllIII.l11I11I11l(736)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{85, 34, 41, 83, 41, 95, 99, 104, 120, 81, 95, 86, 71}));
        }
        return iIndexOfStateSet2;
    }

    int getKeyframeIdAt(int i) {
        if (i < 0) {
            return 0;
        }
        return this.mStateIds.get(i, 0).intValue();
    }

    int indexOfTransition(int i, int i2) {
        int iLongValue = (int) this.mTransitions.get(generateTransitionKey(i, i2), -1L).longValue();
        if (IIll1llI1l.Il1IIlI1II(3693)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{81, 20, 39, 8, 52, 68, 97, 123, 113, 9, 71, 125, 70, 115, 117, 65, 21, 87, 19, 113, 101, 35, 126, 93, 122, 95, Byte.MAX_VALUE, 42, 2}));
        }
        return iLongValue;
    }

    boolean isTransitionReversed(int i, int i2) {
        if (IIIlIll111.Ill1lIIlIl(8815)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{96, 32, 39, 2, 80, 92, 86, 70, 87, 9, 72, 4, 122, 82, 103, 120, 32, 38, 52, 65, 119, 13, 11, 125, 88, 119, 69, 33, 13, 106, 5}));
        }
        return (this.mTransitions.get(generateTransitionKey(i, i2), -1L).longValue() & REVERSED_BIT) != 0;
    }

    boolean transitionHasReversibleFlag(int i, int i2) {
        return (this.mTransitions.get(generateTransitionKey(i, i2), -1L).longValue() & REVERSIBLE_FLAG_BIT) != 0;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.I1l1IIIl1I, android.graphics.drawable.Drawable$ConstantState
    public Drawable newDrawable() throws StreamCorruptedException {
        if (android.accounts.utils.lIIlI111II.IIlI1II1ll(188508464L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{125, 7, 56, 6, 52, 89, 112, Byte.MAX_VALUE, 94, 16, 124, 102, 0, 109}));
        }
        return new IIl1IIllIl(this, null);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.I1l1IIIl1I, android.graphics.drawable.Drawable$ConstantState
    public Drawable newDrawable(Resources resources) throws UnsupportedEncodingException {
        IIl1IIllIl iIl1IIllIl = new IIl1IIllIl(this, resources);
        if (II1I11IlI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{118, 43, 87, 61, 52}), 290781014L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{118, 45, 86, 32, 11, 118, 4, 6, 83, 9, 5, 94, 103, 107, 94, 99, 13, 56, 22, 64, 92, 47, 3, 100, 5, 64, 67, 17, 60, 66}));
        }
        return iIl1IIllIl;
    }
}
