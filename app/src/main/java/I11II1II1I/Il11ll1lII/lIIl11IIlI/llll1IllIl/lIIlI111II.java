package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import androidx.interpolator.view.animation.lI11IlI1lI;
import java.net.PortUnreachableException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public abstract class lIIlI111II {
    public abstract void start();

    public abstract void stop();

    private lIIlI111II() {
    }

    public void reverse() {
        if (lI11IlI1lI.I11II1I1I1(I1I1lI1II1.a(new byte[]{93, 52, 55, 43, 13, 82, 0, 119, 118, 12, 6, 90, 125, 73, 117, 3, 39, 27, 47, 119, 126, 50, 10, 67, 85, 122, 6, 39, 23, 70, 110}), 6961)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{4, 55, 59, 61, 18, 96, 110, 96, 83, 8, 96, 84, 80, 91, 101, 64}));
        }
    }

    public boolean canReverse() throws PortUnreachableException {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.llIIIl11I1(252718482L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{110, 13, 5, 41, 17, 68, 69, 103}));
        }
        if (android.accounts.utils.lIIlI111II.l1llI1llII(9019)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{115, 61, 83, 1, 11, 79, 101, 114, 13, 5, 93, 100, 98, 67, 103, 111, 50, 50, 54, 121, 66, 37, 4, 79}));
        }
        return false;
    }
}
