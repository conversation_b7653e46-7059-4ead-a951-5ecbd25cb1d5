package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il.ll111Illll;
import III1IllI11.lI1lIll11I.l111I11lII.lIllIl111I.lllIlIl1ll;
import Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIll1l1111;
import IlI1lI1II1.llI1IIIllI.lI1lII1l1I.IIlI1IIllI.Ill11IIlII;
import IlI1lI1II1.llI1IIIllI.lI1lII1l1I.IIlI1IIllI.lIlllIl1I1;
import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.lIIIIII11I;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.PorterDuff$Mode;
import android.graphics.Rect;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.AnimationDrawable;
import android.graphics.drawable.Drawable;
import android.media.content.Il1llIl111;
import android.media.content.lll1IIII11;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Xml;
import androidx.appcompat.widget.I1111IIlIl;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I111I11Ill;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.IllIlllIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.io.InvalidObjectException;
import java.io.ObjectStreamException;
import java.io.UTFDataFormatException;
import java.io.UnsupportedEncodingException;
import java.net.BindException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidParameterException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.ProviderException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateParsingException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class IIl1IIllIl extends lI1llII1I1 implements IIll1l1111 {
    private boolean mMutated;
    private IIlIllIl1l mState;
    private lIIlI111II mTransition;
    private int mTransitionFromIndex;
    private int mTransitionToIndex;
    private static final String ELEMENT_TRANSITION = I1I1lI1II1.a(new byte[]{67, 22, 3, 11, 17, 92, 67, 89, 86, 10});
    private static final String ELEMENT_ITEM = I1I1lI1II1.a(new byte[]{94, 16, 7, 8});
    private static final String TRANSITION_MISSING_DRAWABLE_ERROR = I1I1lI1II1.a(new byte[]{13, 68, 94, 17, 16, 84, 89, 67, 80, 16, 89, 95, 91, 7, 20, 65, 3, 6, 66, 64, 85, 16, 70, 92, 65, 83, 68, 69, 5, 18, 16, 7, 69, 5, 21, 4, 0, 89, 82, 23, 25, 5, 68, 68, 71, 80, 86, 64, 22, 4, 66, 93, 66, 65, 80, 93, 90, 90, 83, 69, 16, 83, 80, 67, 83, 1, 4, 12, 12, 92, 89, 87, 25, 5, 16, 84, 71, 88, 67, 84, 0, 13, 7});
    private static final String TRANSITION_MISSING_FROM_TO_ID = I1I1lI1II1.a(new byte[]{13, 68, 94, 17, 16, 84, 89, 67, 80, 16, 89, 95, 91, 7, 20, 65, 3, 6, 66, 64, 85, 16, 70, 92, 65, 83, 68, 69, 67, 84, 69, 12, 90, 45, 6, 66, 66, 19, 23, 23, 77, 11, 121, 84, 18, 25, 85, 65, 22, 19, 11, 80, 69, 21, 86, 70});
    private static final String ITEM_MISSING_DRAWABLE_ERROR = I1I1lI1II1.a(new byte[]{13, 68, 94, 12, 22, 80, 90, 14, 25, 16, 81, 87, 21, 75, 81, 68, 23, 8, 16, 87, 67, 65, 82, 21, 20, 82, 69, 4, 19, 83, 85, 15, 82, 67, 66, 4, 22, 65, 69, 89, 91, 17, 68, 85, 21, 86, 70, 21, 1, 9, 11, 94, 84, 65, 71, 84, 84, 22, 83, 0, 2, 91, 89, 10, 89, 3, 66, 4, 66, 81, 69, 81, 78, 5, 82, 92, 80});
    private static final String LOGTAG = "IIl1IIllIl";

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public boolean isStateful() {
        return true;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1
    public /* synthetic */ void addState(int[] iArr, Drawable drawable) throws GeneralSecurityException, NoRouteToHostException {
        if (Il1llIl111.I111IlIl1I(1016)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{77, 8, 21, 50, 15, 7, 0, 105, 94, 29, 120, 117, 116, 114}));
        }
        super.addState(iArr, drawable);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void applyTheme(Resources$Theme resources$Theme) throws IllegalAccessException, GeneralSecurityException, NoRouteToHostException {
        super.applyTheme(resources$Theme);
        if (Il1I1lllIl.I1II1111ll(341676174L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{96, 61, 36, 10, 26, 123, 122, 99, 111, 53, 86, 6}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ boolean canApplyTheme() {
        if (!androidx.interpolator.view.animation.lIIlI111II.llII1ll111(5688)) {
            return super.canApplyTheme();
        }
        Log.i(I1I1lI1II1.a(new byte[]{84, 82, 58, 49, 0, 64, 88, 87, 76, 38, 89, 113, 90, 126}), I1I1lI1II1.a(new byte[]{2, 33, 55, 48, 53, 77, 88, Byte.MAX_VALUE, 116, 15, 87, 105, 101, 90, 68, 80, 3, 44, 12, 72, 82, 85, 3, 121, 113, 120, 14, 50, 41}));
        return false;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    public /* synthetic */ I1l1IIIl1I cloneConstantState() throws CertificateParsingException {
        if (android.support.v4.graphics.drawable.lIIlI111II.llIIlI1llI(5432)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{80, 83, 16, 83, 83, 76, 125, 101, 93, 87, 86, 8, 77, 104, 83}));
        }
        return cloneConstantState();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    /* synthetic */ lll1lIll1I cloneConstantState() {
        if (lll1IIII11.l1ll11I11l(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 41, 90, 41, 41, 90, 64, 5, 65, 43, 97, 68, Byte.MAX_VALUE, 72, 12, 94, 53, 87}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{116, 35, 35, 43}));
        }
        return cloneConstantState();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void draw(Canvas canvas) throws CharConversionException {
        super.draw(canvas);
        if (Il1I1lllIl.II1111I11I(I1I1lI1II1.a(new byte[]{0, 32, 20, 18, 33, 4, 81, 94, 120, 32, 90, 73, 12, 108, 4, 13, 13, 47, 40, 113}), 505291598L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{94, 0, 38, 35}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ int getAlpha() {
        return super.getAlpha();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ int getChangingConfigurations() {
        return super.getChangingConfigurations();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ Drawable getCurrent() {
        return super.getCurrent();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void getHotspotBounds(Rect rect) {
        if (IIlII1IIIl.IllIlI1l1I(I1I1lI1II1.a(new byte[]{14, 9, 15, 29, 52, 77, 82, 98, 105, 28, 123, 121, 64, 88, 93, 68, 45, 89, 45, 65, 104, 27, 81, 76, 64, 110, 102, 85}), 167100902L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{91, 11}));
        }
        super.getHotspotBounds(rect);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ int getIntrinsicHeight() throws BindException {
        if (lIIIIII11I.IIll1I11lI(I1I1lI1II1.a(new byte[]{7, 85, 18, 49, 45, 7, 77, 125}))) {
            Log.v(I1I1lI1II1.a(new byte[]{94, 84, 58, 80, 51, 126, 90, 8, 91, 61, 3, 84, 6, 0, 68, 102, 20}), I1I1lI1II1.a(new byte[]{90, 45, 23, 3, 56, 126, 111, 83, 11, 37, 101, 7, 116, 1, Byte.MAX_VALUE, 126, 91, 18, 0, 112, 125, 16, 90, 65, 100, 82, 98, 2}));
            return 0;
        }
        int intrinsicHeight = super.getIntrinsicHeight();
        if (Il1I1lllIl.lI11llll1I(I1I1lI1II1.a(new byte[]{84, 8, 21, 2, 82, 113, 95, 119, 74, 92, 2, 97, 70, 72, 123, 102, 18}), 260628821L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{68, 29, 1, 63, 48, 12, 116, 115, 120, 50, 6, 113, 95, 123, 86, 121, 84}));
        }
        return intrinsicHeight;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ int getIntrinsicWidth() {
        return super.getIntrinsicWidth();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ int getMinimumHeight() {
        if (I1IllIll1l.I1lllI1llI(452575705L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{92, 39, 32, 41, 36, 109, 85, 82, 120, 23, 8, 118, 76, 78, 100, 70, 42, 3, 48}));
        }
        return super.getMinimumHeight();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ int getMinimumWidth() {
        return super.getMinimumWidth();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ int getOpacity() throws CloneNotSupportedException {
        int opacity = super.getOpacity();
        if (llIl1lII1I.III11111Il(I1I1lI1II1.a(new byte[]{113, 48, 51, 80, 42, 120, 93, Byte.MAX_VALUE}))) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{65, 49, 15, 60, 59, 84, 68, 84, 112, 41, 73, 106, 100, 78, 66, 96, 39, 13, 58, 6, 5, 21, 74, 96, 74, 115, 91, 84}));
        }
        return opacity;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void getOutline(Outline outline) throws UnknownHostException {
        super.getOutline(outline);
        if (IIll1llI1l.Ill1lIIlIl(782)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{116, 51, 36, 32, 18, 115, Byte.MAX_VALUE, 118, 12, 47}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ boolean getPadding(Rect rect) throws MalformedURLException, UTFDataFormatException {
        if (l1l1I111I1.IlII1Illll(427677431L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{70, 11, 37, 3, 45, 4, 79, 83, 12, 15, 104, 0, 115, 107, 80, 115, 42, 2, 53, 7, 73, 37, 96, 97, 123, 4, 103, 1, 12}));
        }
        boolean padding = super.getPadding(rect);
        if (IIlI1Il1lI.IllIlI1l1I(I1I1lI1II1.a(new byte[]{70, 55, 41, 31, 46, 103, 118, 84, 123, 7, 106, Byte.MAX_VALUE, 69, 83, 0, 119, 20, 4}), 221049180L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{1, 92, 13, 55, 84, 4, 120, 90, 1, 47, 122, 3, 100, 114, 99, 80, 58}));
        }
        return padding;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable$Callback
    public /* bridge */ /* synthetic */ void invalidateDrawable(Drawable drawable) {
        super.invalidateDrawable(drawable);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ boolean isAutoMirrored() throws KeyException, UnrecoverableEntryException {
        if (androidx.core.location.lIIlI111II.l111lI11I1(783)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{84, 13, 44, 63, 26, 66, 89, 104, 113, 2, 83, 81, 111, 125, 70, 3, 33, 9}));
        }
        boolean zIsAutoMirrored = super.isAutoMirrored();
        if (I111I11Ill.IIll1I11lI(I1I1lI1II1.a(new byte[]{121, 87, 3, 35, 18, 90, 77, 104, 94, 44, 122, Byte.MAX_VALUE, 67, 12, 85, 81, 18, 48, 27, 97, 72, 5}))) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{99, 48, 19, 13, 9, 76, 109, 120, 122, 32, 0, 120, 13}));
        }
        return zIsAutoMirrored;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ boolean onLayoutDirectionChanged(int i) throws ObjectStreamException {
        if (IIIlIl1I1l.l11I11I11l(213164145L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{96}));
        }
        boolean zOnLayoutDirectionChanged = super.onLayoutDirectionChanged(i);
        if (Il1llIl111.l11I11I11l(I1I1lI1II1.a(new byte[]{92, 53, 6, 41, 5, 6, 14, 117, 15, 7, 88, 90, 96, 76, 112, 112, 91, 84, 1, 83, 126, 80, 92, 13, 70, 112, 112, 8, 22}), 4455)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{111, 28, 81, 15, 41, 77, 0, 97, 125, 87, 125, Byte.MAX_VALUE, 88, 11, 119, 67}));
        }
        return zOnLayoutDirectionChanged;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable$Callback
    public /* synthetic */ void scheduleDrawable(Drawable drawable, Runnable runnable, long j) throws InvalidAlgorithmParameterException {
        if (Il1l11I11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{125, 86, 4, 1, 38, 77, 5}), 557745678L)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{68}));
        }
        super.scheduleDrawable(drawable, runnable, j);
        if (lIIIIII11I.llll111lI1(I1I1lI1II1.a(new byte[]{103, 15, 19, 92, 0, 89, 109, 4, 77, 12, 83, 70, 12, 10}), 2491)) {
            Log.e(I1I1lI1II1.a(new byte[]{3, 49, 56, 33, 85, 89, 89, 122, 72, 30, 74, 67, 1, 77, 119, 108}), I1I1lI1II1.a(new byte[]{100, 62, 55, 38, 40, 1, 94, 118, 81, 12, 5, 5, 86, 92, 2, 101, 46, 52, 13, 95, 120, 84, 124, 119, 92, 83, 112, 13, 1, 0, 14, 38}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void setAlpha(int i) throws CloneNotSupportedException {
        if (l11Il1lI11.l1l1l1IIlI(659001833L)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{84, 11, 39, 8, 27, 81, 100, 105, 67, 48, 100, 102, 88, 109, 87, 99, 35}));
        }
        super.setAlpha(i);
        if (IIIlIll111.I111IlIl1I(186507312L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{101, 14, 4, 52, 33, 109, 101, 67, 74, 60, 126, 97, 76, 120, 83, 111, 1, 47, 32, 10}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ void setAutoMirrored(boolean z) {
        super.setAutoMirrored(z);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void setColorFilter(ColorFilter colorFilter) throws InterruptedIOException {
        if (IIlIIlIII1.Ill1lIIlIl(759)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{95, 84, 10, 36, 54, 89, 70, 64, 111, 49, 99, 65, 114, 113, 94, 13, 58, 12, 16, 86, 125, 41}));
        }
        super.setColorFilter(colorFilter);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ void setDither(boolean z) {
        super.setDither(z);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    public /* synthetic */ void setEnterFadeDuration(int i) {
        if (IIIlIl1I1l.IlII1Illll(205293162L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{77, 42, 5, 32, 1, 2, 82, 87, 122, 87, 94, 90, 98, 10, 113}));
        }
        super.setEnterFadeDuration(i);
        if (Il1l11I11I.I111IlIl1I(592945962L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{123, 61, 56, 19, 80, 118, 78, 65, 99, 84}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    public /* synthetic */ void setExitFadeDuration(int i) throws UnsupportedEncodingException {
        if (l11Il111ll.IlII1Illll(289869474L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{80, 19, 24, 60, 91, 100, 113, 74, 12, 3, 99, 83, 82, 93, 69, 83, 20, 83, 5, 93, 104, 56, 10, 66}));
        }
        super.setExitFadeDuration(i);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* bridge */ /* synthetic */ void setHotspot(float f, float f2) {
        super.setHotspot(f, f2);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public /* synthetic */ void setHotspotBounds(int i, int i2, int i3, int i4) {
        super.setHotspotBounds(i, i2, i3, i4);
        if (IIlIIlIII1.Ill1lIIlIl(4691)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{3, 54, 85, 16, 48, 95, 98, 64, 99, 82, 102, 97, 2, 109, 88, 68, 6}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable, Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIll1l1111
    public /* bridge */ /* synthetic */ void setTintList(ColorStateList colorStateList) {
        super.setTintList(colorStateList);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable, Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIll1l1111
    public /* synthetic */ void setTintMode(PorterDuff$Mode porterDuff$Mode) throws CertStoreException {
        if (IIlIIlIII1.I1lllI1llI(280744469L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{109, 0, 9, 28, 82, 77, 79, 90, 104, 42, 66, 0, 1, 104, 88, 6, 5, 56, 3, 84, 115, 51, 7, 68, Byte.MAX_VALUE}));
        }
        super.setTintMode(porterDuff$Mode);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable$Callback
    public /* bridge */ /* synthetic */ void unscheduleDrawable(Drawable drawable, Runnable runnable) {
        super.unscheduleDrawable(drawable, runnable);
    }

    public IIl1IIllIl() {
        this(null, null);
    }

    IIl1IIllIl(IIlIllIl1l iIlIllIl1l, Resources resources) throws MalformedURLException, KeyStoreException, KeyManagementException, FileNotFoundException, UTFDataFormatException, CertStoreException {
        super(null);
        this.mTransitionToIndex = -1;
        this.mTransitionFromIndex = -1;
        setConstantState(new IIlIllIl1l(iIlIllIl1l, this, resources));
        onStateChange(getState());
        jumpToCurrentState();
    }

    public static IIl1IIllIl create(Context context, int i, Resources$Theme resources$Theme) throws XmlPullParserException, Resources$NotFoundException, IOException {
        int next;
        try {
            Resources resources = context.getResources();
            XmlResourceParser xml = resources.getXml(i);
            AttributeSet attributeSetAsAttributeSet = Xml.asAttributeSet(xml);
            do {
                next = xml.next();
                if (next == 2) {
                    break;
                }
            } while (next != 1);
            if (next != 2) {
                throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{121, 11, 66, 22, 22, 84, 69, 68, 25, 16, 81, 87, 21, 95, 91, 64, 12, 5}));
            }
            return createFromXmlInner(context, resources, xml, attributeSetAsAttributeSet, resources$Theme);
        } catch (IOException e) {
            Log.e(LOGTAG, I1I1lI1II1.a(new byte[]{71, 5, 16, 22, 7, 71, 23, 85, 75, 22, 95, 66}), e);
            return null;
        } catch (XmlPullParserException e2) {
            Log.e(LOGTAG, I1I1lI1II1.a(new byte[]{71, 5, 16, 22, 7, 71, 23, 85, 75, 22, 95, 66}), e2);
            return null;
        }
    }

    public static IIl1IIllIl createFromXmlInner(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IllegalAccessException, IOException, CertPathValidatorException, CertificateException, KeyManagementException {
        String name = xmlPullParser.getName();
        if (!name.equals(I1I1lI1II1.a(new byte[]{86, 10, 11, 8, 3, 65, 82, 84, 20, 23, 85, 92, 80, 90, 64, 90, 16}))) {
            throw new XmlPullParserException(xmlPullParser.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 11, 11, 20, 84, 91, 89, 93, 68, 81, 94, 92, 84, 85, 65, 7, 5, 79, 65, 85, 13, 86, 86, 71, 89, 69, 69, 16, 83, 80, 67}) + name);
        }
        IIl1IIllIl iIl1IIllIl = new IIl1IIllIl();
        iIl1IIllIl.inflate(context, resources, xmlPullParser, attributeSet, resources$Theme);
        if (IIll1llI1l.Ill1lIIlIl(4895)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{7, 20, 20, 32, 16, 113, 122, 89, Byte.MAX_VALUE, 17, 103, 102, 96, 14, 87, 112, 37, 43, 37, 0, 4, 82, 93, 77, 67, 3, 91, 22, 41, 118}));
        }
        return iIl1IIllIl;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1
    public void inflate(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IllegalAccessException, IOException, CertPathValidatorException, CertificateException, KeyManagementException {
        TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.AnimatedStateListDrawableCompat);
        setVisible(typedArrayObtainAttributes.getBoolean(lllIlIl1ll.AnimatedStateListDrawableCompat_android_visible, true), true);
        updateStateFromTypedArray(typedArrayObtainAttributes);
        updateDensity(resources);
        typedArrayObtainAttributes.recycle();
        inflateChildElements(context, resources, xmlPullParser, attributeSet, resources$Theme);
        init();
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public boolean setVisible(boolean z, boolean z2) {
        boolean visible = super.setVisible(z, z2);
        lIIlI111II liili111ii = this.mTransition;
        if (liili111ii != null && (visible || z2)) {
            if (z) {
                liili111ii.start();
            } else {
                jumpToCurrentState();
            }
        }
        if (!android.media.content.lIIlI111II.llI1llI1l1(244557194L)) {
            return visible;
        }
        Log.d(I1I1lI1II1.a(new byte[]{94, 23, 0, 19, 59, 86, 85, 9, 83, 28, 115, 84, 88, 114, 90, 1}), I1I1lI1II1.a(new byte[]{86, 20, 10, 51, 52, 70, 69, 104, 105, 28, 88, 102, 126, 87, 125, 81, 8, 22, 82, 75, 125, 87, 93, 3, 117, 92, 70, 48, 38}));
        return false;
    }

    public void addState(int[] iArr, Drawable drawable, int i) throws KeyManagementException {
        if (drawable == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{115, 22, 3, 18, 3, 87, 91, 85, 25, 9, 69, 67, 65, 25, 90, 90, 22, 65, 0, 87, 16, 15, 70, 89, 95}));
        }
        this.mState.addStateSet(iArr, drawable, i);
        onStateChange(getState());
    }

    public <T extends Drawable & Animatable> void addTransition(int i, int i2, T t, boolean z) throws GeneralSecurityException {
        if (Il11II1llI.I1lllI1llI(571349231L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{3, 6, 50, 92, 19, 103, 110, 126}));
        }
        if (t == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 92, 67, 89, 86, 10, 16, 84, 71, 88, 67, 84, 0, 13, 7, 18, 93, 20, 64, 65, 19, 88, 88, 17, 68, 80, 82, 67, 89, 17, 14, 9}));
        }
        this.mState.addTransition(i, i2, t, z);
        if (Il1I1lllIl.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{101, 17, 40, 16, 6, 83, 100, 114, 12, 7, 1, 102, 3, 113, 118, 6, 14, 85, 85, 104, 85, 27, 6, 79}), 315705101L)) {
            Log.d(I1I1lI1II1.a(new byte[]{4, 32, 81, 39, 24, 1, 82, 115, 111, 21, 82, 103}), I1I1lI1II1.a(new byte[]{71, 38, 26, 45, 46, 91, 89, 88, 86}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public void jumpToCurrentState() {
        super.jumpToCurrentState();
        lIIlI111II liili111ii = this.mTransition;
        if (liili111ii != null) {
            liili111ii.stop();
            this.mTransition = null;
            selectDrawable(this.mTransitionToIndex);
            this.mTransitionToIndex = -1;
            this.mTransitionFromIndex = -1;
        }
        if (llIl1lII1I.I111IlIl1I(206815643L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{71, 44, 10, 19, 43, 91, 86, 106, 122, 33, 91, 101, 88, 122, 100, 4, 86, 55, 51, 75, 71, 3, 118, 125, 93, 123, 5, 17, 37, 5, 116}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    protected boolean onStateChange(int[] iArr) throws KeyManagementException {
        int iIndexOfKeyframe = this.mState.indexOfKeyframe(iArr);
        boolean z = iIndexOfKeyframe != getCurrentIndex() && (selectTransition(iIndexOfKeyframe) || selectDrawable(iIndexOfKeyframe));
        Drawable current = getCurrent();
        return current != null ? z | current.setState(iArr) : z;
    }

    private boolean selectTransition(int i) throws CertificateException {
        int currentIndex;
        int iIndexOfTransition;
        lIIlI111II il1ll1IIll;
        lIIlI111II liili111ii = this.mTransition;
        if (liili111ii != null) {
            if (i == this.mTransitionToIndex) {
                return true;
            }
            if (i == this.mTransitionFromIndex && liili111ii.canReverse()) {
                liili111ii.reverse();
                this.mTransitionToIndex = this.mTransitionFromIndex;
                this.mTransitionFromIndex = i;
                return true;
            }
            currentIndex = this.mTransitionToIndex;
            liili111ii.stop();
        } else {
            currentIndex = getCurrentIndex();
        }
        this.mTransition = null;
        this.mTransitionFromIndex = -1;
        this.mTransitionToIndex = -1;
        IIlIllIl1l iIlIllIl1l = this.mState;
        int keyframeIdAt = iIlIllIl1l.getKeyframeIdAt(currentIndex);
        int keyframeIdAt2 = iIlIllIl1l.getKeyframeIdAt(i);
        if (keyframeIdAt2 == 0 || keyframeIdAt == 0 || (iIndexOfTransition = iIlIllIl1l.indexOfTransition(keyframeIdAt, keyframeIdAt2)) < 0) {
            return false;
        }
        boolean zTransitionHasReversibleFlag = iIlIllIl1l.transitionHasReversibleFlag(keyframeIdAt, keyframeIdAt2);
        selectDrawable(iIndexOfTransition);
        Object current = getCurrent();
        if (current instanceof AnimationDrawable) {
            il1ll1IIll = new l1l1I1l1lI((AnimationDrawable) current, iIlIllIl1l.isTransitionReversed(keyframeIdAt, keyframeIdAt2), zTransitionHasReversibleFlag);
        } else if (current instanceof Ill11IIlII) {
            il1ll1IIll = new IlI11Ill11((Ill11IIlII) current);
        } else {
            if (!(current instanceof Animatable)) {
                if (l111Il1lI1.I1lIllll1l(I1I1lI1II1.a(new byte[]{89, 6, 3}))) {
                    throw new CertificateException(I1I1lI1II1.a(new byte[]{115, 42, 84, 7, 22, 67, 101, 115, 9, 12, 120, 68, 119, 114, 76, 66, 35, 53, 1, 107, 106, 19, 92}));
                }
                return false;
            }
            il1ll1IIll = new Il1ll1IIll((Animatable) current);
        }
        il1ll1IIll.start();
        this.mTransition = il1ll1IIll;
        this.mTransitionFromIndex = currentIndex;
        this.mTransitionToIndex = i;
        return true;
    }

    private void updateStateFromTypedArray(TypedArray typedArray) throws InvalidObjectException {
        if (l111Il1lI1.IIll1I11lI(I1I1lI1II1.a(new byte[]{98, 6, 12, 51, 42, 100, 102, 113}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{0, 86, 7, 55, 24, 103, 1, 102, 82, 80, 94, 104, 87, 110, 86, 64, 49, 20, 54, 122, 81, 46, 1}));
        }
        IIlIllIl1l iIlIllIl1l = this.mState;
        iIlIllIl1l.mChangingConfigurations |= typedArray.getChangingConfigurations();
        iIlIllIl1l.setVariablePadding(typedArray.getBoolean(lllIlIl1ll.AnimatedStateListDrawableCompat_android_variablePadding, iIlIllIl1l.mVariablePadding));
        iIlIllIl1l.setConstantSize(typedArray.getBoolean(lllIlIl1ll.AnimatedStateListDrawableCompat_android_constantSize, iIlIllIl1l.mConstantSize));
        iIlIllIl1l.setEnterFadeDuration(typedArray.getInt(lllIlIl1ll.AnimatedStateListDrawableCompat_android_enterFadeDuration, iIlIllIl1l.mEnterFadeDuration));
        iIlIllIl1l.setExitFadeDuration(typedArray.getInt(lllIlIl1ll.AnimatedStateListDrawableCompat_android_exitFadeDuration, iIlIllIl1l.mExitFadeDuration));
        setDither(typedArray.getBoolean(lllIlIl1ll.AnimatedStateListDrawableCompat_android_dither, iIlIllIl1l.mDither));
        if (Il1l11I11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{2, 21, 47, 41, 86, 81, 69, 66, 108, 2, 114, 117, 123, 113, 95, 115, 49, 14, 11, 104, 117, 35, 73, 92, 0, 93, 89, 50}), 184664587L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{79, 44, 11, 11, 44, 84, 15, 125}));
        }
    }

    private void init() throws IOException, KeyManagementException {
        onStateChange(getState());
        if (IllIlllIII.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{1, 92, 84, 84, 3, 89, 95, 102, 108, 11, 117, 119, 76, 83, 101}), 1994)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{102, 49, 6, 11, 22, 94, 113, 4, 113, 47, 93}));
        }
    }

    private void inflateChildElements(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IllegalAccessException, IOException, CertPathValidatorException, CertificateException {
        int depth = xmlPullParser.getDepth() + 1;
        while (true) {
            int next = xmlPullParser.next();
            if (next == 1) {
                return;
            }
            int depth2 = xmlPullParser.getDepth();
            if (depth2 < depth && next == 3) {
                return;
            }
            if (next == 2 && depth2 <= depth) {
                if (xmlPullParser.getName().equals(ELEMENT_ITEM)) {
                    parseItem(context, resources, xmlPullParser, attributeSet, resources$Theme);
                } else if (xmlPullParser.getName().equals(ELEMENT_TRANSITION)) {
                    parseTransition(context, resources, xmlPullParser, attributeSet, resources$Theme);
                }
            }
        }
    }

    private int parseTransition(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IOException {
        int next;
        if (IllllI11lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{71, 0, 38, 92, 44, 94, 71, 86, 125, 6, 106, 123, 66, 107, 82, 88, 54, 12, 40, 91, 117, 85, 106, 71, 86, 117, 70}), 1121186271L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{117, 46, 26, 87, 35, 77, 78, 125}));
        }
        TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.AnimatedStateListDrawableTransition);
        int resourceId = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.AnimatedStateListDrawableTransition_android_fromId, -1);
        int resourceId2 = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.AnimatedStateListDrawableTransition_android_toId, -1);
        int resourceId3 = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.AnimatedStateListDrawableTransition_android_drawable, -1);
        Drawable drawable = resourceId3 > 0 ? I1111IIlIl.get().getDrawable(context, resourceId3) : null;
        boolean z = typedArrayObtainAttributes.getBoolean(lllIlIl1ll.AnimatedStateListDrawableTransition_android_reversible, false);
        typedArrayObtainAttributes.recycle();
        if (drawable == null) {
            do {
                next = xmlPullParser.next();
            } while (next == 4);
            if (next != 2) {
                throw new XmlPullParserException(xmlPullParser.getPositionDescription() + TRANSITION_MISSING_DRAWABLE_ERROR);
            }
            drawable = xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{86, 10, 11, 8, 3, 65, 82, 84, 20, 18, 85, 83, 65, 86, 70})) ? Ill11IIlII.createFromXmlInner(context, resources, xmlPullParser, attributeSet, resources$Theme) : Drawable.createFromXmlInner(resources, xmlPullParser, attributeSet, resources$Theme);
        }
        if (drawable == null) {
            throw new XmlPullParserException(xmlPullParser.getPositionDescription() + TRANSITION_MISSING_DRAWABLE_ERROR);
        }
        if (resourceId == -1 || resourceId2 == -1) {
            throw new XmlPullParserException(xmlPullParser.getPositionDescription() + TRANSITION_MISSING_FROM_TO_ID);
        }
        return this.mState.addTransition(resourceId, resourceId2, drawable, z);
    }

    private int parseItem(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IllegalAccessException, IOException, CertPathValidatorException, CertificateException {
        int next;
        TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.AnimatedStateListDrawableItem);
        int resourceId = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.AnimatedStateListDrawableItem_android_id, 0);
        int resourceId2 = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.AnimatedStateListDrawableItem_android_drawable, -1);
        Drawable drawable = resourceId2 > 0 ? I1111IIlIl.get().getDrawable(context, resourceId2) : null;
        typedArrayObtainAttributes.recycle();
        int[] iArrExtractStateSet = extractStateSet(attributeSet);
        if (drawable == null) {
            do {
                next = xmlPullParser.next();
            } while (next == 4);
            if (next != 2) {
                throw new XmlPullParserException(xmlPullParser.getPositionDescription() + ITEM_MISSING_DRAWABLE_ERROR);
            }
            if (xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{65, 1, 1, 17, 13, 71}))) {
                drawable = lIlllIl1I1.createFromXmlInner(resources, xmlPullParser, attributeSet, resources$Theme);
            } else {
                drawable = Drawable.createFromXmlInner(resources, xmlPullParser, attributeSet, resources$Theme);
            }
        }
        if (drawable == null) {
            throw new XmlPullParserException(xmlPullParser.getPositionDescription() + ITEM_MISSING_DRAWABLE_ERROR);
        }
        return this.mState.addStateSet(iArrExtractStateSet, drawable, resourceId);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public Drawable mutate() throws UnknownHostException {
        if (IllIIIIII1.Ill1lIIlIl(659)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{114, 2, 32, 39, 45, 70}));
        }
        if (!this.mMutated && super.mutate() == this) {
            this.mState.mutate();
            this.mMutated = true;
        }
        if (lI11IlI1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{90, 52, 91, 49, 44, 119, 67, 68, 111, 53, 0, 92, 125, 99, 123, 93, 21, 59, 83, 71, 67, 24, 90, 64, 125}), 194951052L)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{114, 7, 47, 52, 19, 116, 14, 67, 10, 17, 83, 120, 93, 107, 113, 96, 22, 87, 19, 4, 89, 59, 90, 102, 118, 71, Byte.MAX_VALUE}));
        }
        return this;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    public IIlIllIl1l cloneConstantState() {
        if (l1lI1I1l11.I1II1111ll(192283997L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{14, 23, 27, 16, 5, 124, 100, 74, 86, 52}));
        }
        return new IIlIllIl1l(this.mState, this, null);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    void clearMutated() throws CertPathBuilderException, UnknownHostException, EOFException, CloneNotSupportedException {
        super.clearMutated();
        this.mMutated = false;
        if (llIl1lII1I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{97, 2, 82, 29, 16}), 168301454L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{109, 50, 24, 50, 56, 6, 1, 106, 93, 39, 122, 123, 7, 126, 109, 69, 45, 85, 47, 91, 119, 2, 88, 114, 103, 117, 93, 3}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lI1llII1I1, I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    void setConstantState(lll1lIll1I lll1lill1i) throws MalformedURLException, KeyStoreException, FileNotFoundException, UTFDataFormatException, CertStoreException {
        if (Il1I1lllIl.llII1lIIlI(I1I1lI1II1.a(new byte[]{81, 20, 3, 17, 26, 77, 65, 86, Byte.MAX_VALUE, 12, 83, 96, 112, 75, 119}))) {
            Log.w(I1I1lI1II1.a(new byte[]{1}), I1I1lI1II1.a(new byte[]{99, 5, 45, 11, 7, 126, 70, 0, 72, 82, 121, 114, 1, 107, 13, 116, 82, 2, 6, Byte.MAX_VALUE}));
            return;
        }
        super.setConstantState(lll1lill1i);
        if (lll1lill1i instanceof IIlIllIl1l) {
            this.mState = (IIlIllIl1l) lll1lill1i;
        }
        if (l1IIll1I1l.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{4, 46, 53, 52, 55, 93, 115, 95, 115, 19, 117, 82, 95, 82, 6, 12, 84, 44, 45, 102, 70, 5, 101, 120, 0}), 4480)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{78, 20, 13, 19, 27, 91, 103, 4, 90, 17}));
        }
    }
}
