package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il.ll111Illll;
import III1IllI11.lI1lIll11I.l111I11lII.lIllIl111I.lllIlIl1ll;
import android.accounts.utils.IIIlIl1I1l;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.AttributeSet;
import android.util.StateSet;
import androidx.appcompat.widget.I1111IIlIl;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1111IIl11;
import androidx.core.location.l1l1I111I1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UTFDataFormatException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyStoreException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertStoreException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
class lI1llII1I1 extends IIllIIll1I {
    private static final boolean DEBUG = false;
    private static final String TAG = I1I1lI1II1.a(new byte[]{100, 16, 3, 17, 7, 121, 94, 67, 77, 32, 66, 81, 66, 88, 86, 89, 7});
    private boolean mMutated;
    private I1l1IIIl1I mStateListState;

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public boolean isStateful() {
        return true;
    }

    lI1llII1I1() {
        this(null, null);
    }

    public void addState(int[] iArr, Drawable drawable) throws GeneralSecurityException, NoRouteToHostException {
        if (drawable != null) {
            this.mStateListState.addStateSet(iArr, drawable);
            onStateChange(getState());
        }
        if (androidx.core.location.lIIlI111II.l111I1ll1l(3905)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{4, 83, 0, 6, 43, 119, 90, 68, 116, 3}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    protected boolean onStateChange(int[] iArr) throws GeneralSecurityException, NoRouteToHostException {
        if (I1111IIl11.l1l1l1IIlI(175619640L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{65, 1, 23, 41, 37, 83, 69, Byte.MAX_VALUE}));
        }
        boolean zOnStateChange = super.onStateChange(iArr);
        int iIndexOfStateSet = this.mStateListState.indexOfStateSet(iArr);
        if (iIndexOfStateSet < 0) {
            iIndexOfStateSet = this.mStateListState.indexOfStateSet(StateSet.WILD_CARD);
        }
        boolean z = selectDrawable(iIndexOfStateSet) || zOnStateChange;
        if (IIlI1Il1lI.IIll1I11lI(I1I1lI1II1.a(new byte[]{101}))) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{77, 45, 27, 33, 42, 65, 82, 72, Byte.MAX_VALUE, 15, 106, 113, 6, 76}));
        }
        return z;
    }

    public void inflate(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, GeneralSecurityException, IOException {
        if (I1IllIll1l.llll111lI1(I1I1lI1II1.a(new byte[]{109, 33, 85, 83, 83, 91, 124, 103, 104, 11}), 221998848L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{2, 21, 37, 13, 59, 80, 6, 123, 14, 50, 98, 1, 65, 14, 69, 111, 50, 9, 58, 2}));
        }
        TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.StateListDrawable);
        setVisible(typedArrayObtainAttributes.getBoolean(lllIlIl1ll.StateListDrawable_android_visible, true), true);
        updateStateFromTypedArray(typedArrayObtainAttributes);
        updateDensity(resources);
        typedArrayObtainAttributes.recycle();
        inflateChildElements(context, resources, xmlPullParser, attributeSet, resources$Theme);
        onStateChange(getState());
    }

    private void updateStateFromTypedArray(TypedArray typedArray) {
        if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{96, 80, 41, 11, 9, 7, 78, 118, 116, 32, 95, 88, 116, 13, 120, 108, 32, 40, 58, 6, 69, 15, 70, 82}), 168527471L)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{78, 42}));
        }
        I1l1IIIl1I i1l1IIIl1I = this.mStateListState;
        i1l1IIIl1I.mChangingConfigurations |= typedArray.getChangingConfigurations();
        i1l1IIIl1I.mVariablePadding = typedArray.getBoolean(lllIlIl1ll.StateListDrawable_android_variablePadding, i1l1IIIl1I.mVariablePadding);
        i1l1IIIl1I.mConstantSize = typedArray.getBoolean(lllIlIl1ll.StateListDrawable_android_constantSize, i1l1IIIl1I.mConstantSize);
        i1l1IIIl1I.mEnterFadeDuration = typedArray.getInt(lllIlIl1ll.StateListDrawable_android_enterFadeDuration, i1l1IIIl1I.mEnterFadeDuration);
        i1l1IIIl1I.mExitFadeDuration = typedArray.getInt(lllIlIl1ll.StateListDrawable_android_exitFadeDuration, i1l1IIIl1I.mExitFadeDuration);
        i1l1IIIl1I.mDither = typedArray.getBoolean(lllIlIl1ll.StateListDrawable_android_dither, i1l1IIIl1I.mDither);
    }

    /* JADX WARN: Code restructure failed: missing block: B:30:0x0093, code lost:
    
        if (androidx.interpolator.view.animation.lIIlI111II.I1IlI11II1(3258) != false) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0095, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x00a4, code lost:
    
        throw new java.security.GeneralSecurityException(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{126, 85, 80}));
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private void inflateChildElements(Context context, Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, GeneralSecurityException, IOException {
        int depth;
        int next;
        I1l1IIIl1I i1l1IIIl1I = this.mStateListState;
        int depth2 = xmlPullParser.getDepth() + 1;
        while (true) {
            int next2 = xmlPullParser.next();
            if (next2 == 1 || ((depth = xmlPullParser.getDepth()) < depth2 && next2 == 3)) {
                break;
            }
            if (next2 == 2 && depth <= depth2 && xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{94, 16, 7, 8}))) {
                TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.StateListDrawableItem);
                int resourceId = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.StateListDrawableItem_android_drawable, -1);
                Drawable drawable = resourceId > 0 ? I1111IIlIl.get().getDrawable(context, resourceId) : null;
                typedArrayObtainAttributes.recycle();
                int[] iArrExtractStateSet = extractStateSet(attributeSet);
                if (drawable == null) {
                    do {
                        next = xmlPullParser.next();
                    } while (next == 4);
                    if (next != 2) {
                        throw new XmlPullParserException(xmlPullParser.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 94, 12, 22, 80, 90, 14, 25, 16, 81, 87, 21, 75, 81, 68, 23, 8, 16, 87, 67, 65, 82, 21, 20, 82, 69, 4, 19, 83, 85, 15, 82, 67, 66, 4, 22, 65, 69, 89, 91, 17, 68, 85, 21, 86, 70, 21, 1, 9, 11, 94, 84, 65, 71, 84, 84, 22, 83, 0, 2, 91, 89, 10, 89, 3, 66, 4, 66, 81, 69, 81, 78, 5, 82, 92, 80}));
                    }
                    drawable = Drawable.createFromXmlInner(resources, xmlPullParser, attributeSet, resources$Theme);
                }
                i1l1IIIl1I.addStateSet(iArrExtractStateSet, drawable);
            }
        }
    }

    int[] extractStateSet(AttributeSet attributeSet) {
        if (l1lI1I1l11.IlII1Illll(242297460L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{115, 42, 56, 52, 44, 3, 7, 105, 116, 8, 91, 121, 126, 85, 70, 93, 85, 18, 26, 80, 122, 20, 105, 13, 120}));
        }
        int attributeCount = attributeSet.getAttributeCount();
        int[] iArr = new int[attributeCount];
        int i = 0;
        for (int i2 = 0; i2 < attributeCount; i2++) {
            int attributeNameResource = attributeSet.getAttributeNameResource(i2);
            if (attributeNameResource != 0 && attributeNameResource != 16842960 && attributeNameResource != 16843161) {
                int i3 = i + 1;
                if (!attributeSet.getAttributeBooleanValue(i2, false)) {
                    attributeNameResource = -attributeNameResource;
                }
                iArr[i] = attributeNameResource;
                i = i3;
            }
        }
        return StateSet.trimStateSet(iArr, i);
    }

    I1l1IIIl1I getStateListState() {
        I1l1IIIl1I i1l1IIIl1I = this.mStateListState;
        if (l1l1I111I1.I111IlIl1I(181404118L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{14, 0, 35, 4, 41, 88, 123, 100, 108, 83, 71, 117, 76, 81, 90, 6, 36, 42, 11, 6}));
        }
        return i1l1IIIl1I;
    }

    int getStateCount() {
        return this.mStateListState.getChildCount();
    }

    int[] getStateSet(int i) throws InvalidAlgorithmParameterException {
        if (IIIlIl1I1l.I1lllI1llI(I1I1lI1II1.a(new byte[]{126, 43, 84, 38, 17, 6}), 5878)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{125, 93, 48, 87, 80, 99, 117, 91, 99, 49, 5, 69, 76, 122, 98, 121, 91, 45, 56, 81, 118}));
        }
        return this.mStateListState.mStateSets[i];
    }

    Drawable getStateDrawable(int i) {
        return this.mStateListState.getChild(i);
    }

    int getStateDrawableIndex(int[] iArr) {
        int iIndexOfStateSet = this.mStateListState.indexOfStateSet(iArr);
        if (Il1IIllIll.l1Il11I1Il(I1I1lI1II1.a(new byte[]{118, 87, 11, 31, 54, 5, 71, 117, 74, 47, 83, 2}), I1I1lI1II1.a(new byte[]{79, 85, 41, 6, 7, 64, 100, 88, 11, 3, 82, 8, 66, 113, 124, 108, 39, 55, 86, 116, 93, 12, 100, 64, 95, 7}))) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{2, 0, 45, 92, 11, 102, 64, 125, 115, 42, 96, 121, 99, 126, 91, 102, 1, 11, 59, 90, 87, 5, 86, 101, 66}));
        }
        return iIndexOfStateSet;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public Drawable mutate() throws UnknownServiceException {
        if (!this.mMutated && super.mutate() == this) {
            this.mStateListState.mutate();
            this.mMutated = true;
        }
        return this;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    public I1l1IIIl1I cloneConstantState() {
        return new I1l1IIIl1I(this.mStateListState, this, null);
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    void clearMutated() throws CertPathBuilderException, UnknownHostException, EOFException, CloneNotSupportedException {
        if (lI1lllIII1.I1lllI1llI(203297719L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{116, 85, 3, 13, 43, 13, 90, 65, 125, 61, 114, 82, 86, 125}));
        }
        super.clearMutated();
        this.mMutated = false;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I, android.graphics.drawable.Drawable
    public void applyTheme(Resources$Theme resources$Theme) throws IllegalAccessException, GeneralSecurityException, NoRouteToHostException {
        super.applyTheme(resources$Theme);
        onStateChange(getState());
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.IIllIIll1I
    void setConstantState(lll1lIll1I lll1lill1i) throws MalformedURLException, KeyStoreException, FileNotFoundException, UTFDataFormatException, CertStoreException {
        super.setConstantState(lll1lill1i);
        if (lll1lill1i instanceof I1l1IIIl1I) {
            this.mStateListState = (I1l1IIIl1I) lll1lill1i;
        }
    }

    lI1llII1I1(I1l1IIIl1I i1l1IIIl1I, Resources resources) throws GeneralSecurityException, MalformedURLException, FileNotFoundException, UTFDataFormatException, NoRouteToHostException {
        setConstantState(new I1l1IIIl1I(i1l1IIIl1I, this, resources));
        onStateChange(getState());
    }

    lI1llII1I1(I1l1IIIl1I i1l1IIIl1I) throws MalformedURLException, KeyStoreException, FileNotFoundException, UTFDataFormatException, CertStoreException {
        if (i1l1IIIl1I != null) {
            setConstantState(i1l1IIIl1I);
        }
    }
}
