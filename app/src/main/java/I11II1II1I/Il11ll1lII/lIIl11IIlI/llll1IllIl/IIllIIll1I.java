package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIllllIlI1;
import android.accounts.utils.lIIIIII11I;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Outline;
import android.graphics.PorterDuff$Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$Callback;
import android.graphics.drawable.Drawable$ConstantState;
import android.media.content.IIl1l1IllI;
import android.media.content.Il1llIl111;
import android.os.SystemClock;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1111IIl11;
import androidx.core.location.IllIlllIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.InvalidObjectException;
import java.io.UTFDataFormatException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import java.security.AccessControlException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CRLException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CertificateParsingException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIllIIll1I extends Drawable implements Drawable$Callback {
    private static final boolean DEBUG = false;
    private static final boolean DEFAULT_DITHER = true;
    private static final String TAG = I1I1lI1II1.a(new byte[]{115, 22, 3, 18, 3, 87, 91, 85, 122, 11, 94, 68, 84, 80, 90, 80, 16});
    private Runnable mAnimationRunnable;
    private I1IIlIl1lI mBlockInvalidateCallback;
    private Drawable mCurrDrawable;
    private lll1lIll1I mDrawableContainerState;
    private long mEnterAnimationEnd;
    private long mExitAnimationEnd;
    private boolean mHasAlpha;
    private Rect mHotspotBounds;
    private Drawable mLastDrawable;
    private boolean mMutated;
    private int mAlpha = 255;
    private int mCurIndex = -1;

    IIllIIll1I() {
    }

    @Override // android.graphics.drawable.Drawable
    public void draw(Canvas canvas) {
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            drawable.draw(canvas);
        }
        Drawable drawable2 = this.mLastDrawable;
        if (drawable2 != null) {
            drawable2.draw(canvas);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public int getChangingConfigurations() {
        return super.getChangingConfigurations() | this.mDrawableContainerState.getChangingConfigurations();
    }

    private boolean needsMirroring() {
        return isAutoMirrored() && IIllllIlI1.getLayoutDirection(this) == 1;
    }

    @Override // android.graphics.drawable.Drawable
    public boolean getPadding(Rect rect) throws SocketTimeoutException, ClassNotFoundException, BrokenBarrierException {
        boolean padding;
        if (Il1llIl111.Ill1lIIlIl(207597647L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{122, 84, 33, 50, 10, 6, 114, 70, 87, 20, 1, 85, 66}));
        }
        Rect constantPadding = this.mDrawableContainerState.getConstantPadding();
        if (constantPadding != null) {
            rect.set(constantPadding);
            padding = (constantPadding.right | ((constantPadding.left | constantPadding.top) | constantPadding.bottom)) != 0;
        } else {
            Drawable drawable = this.mCurrDrawable;
            padding = drawable != null ? drawable.getPadding(rect) : super.getPadding(rect);
        }
        if (needsMirroring()) {
            int i = rect.left;
            rect.left = rect.right;
            rect.right = i;
        }
        return padding;
    }

    @Override // android.graphics.drawable.Drawable
    public void getOutline(Outline outline) {
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            lIlIIlIII1.getOutline(drawable, outline);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setAlpha(int i) {
        if (this.mHasAlpha && this.mAlpha == i) {
            return;
        }
        this.mHasAlpha = true;
        this.mAlpha = i;
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            if (this.mEnterAnimationEnd == 0) {
                drawable.setAlpha(i);
            } else {
                animate(false);
            }
        }
    }

    @Override // android.graphics.drawable.Drawable
    public int getAlpha() throws InvalidObjectException {
        if (l11Il1lI11.IlIllIll1I(I1I1lI1II1.a(new byte[]{123, 44, 44, 61, 41, 102, 84, 126, 85, 18, 105, 0, 87, 65, 91, 87, 10, 53, 48, 87, 74, 52, 74, 111, 119}), 2051)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{102, 23}));
        }
        int i = this.mAlpha;
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{81, 5, 43, 29, 27, 64, 99}))) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{117, 18, 58, 55, 12, 67, 7, 68, 1, 50, 86, 83, 116, 96, 83, 113, 14, 0, 85, 72, 101, 32, 2, 96, 120, 89, 110, 12, 11}));
        }
        return i;
    }

    @Override // android.graphics.drawable.Drawable
    public void setDither(boolean z) {
        if (this.mDrawableContainerState.mDither != z) {
            this.mDrawableContainerState.mDither = z;
            Drawable drawable = this.mCurrDrawable;
            if (drawable != null) {
                drawable.setDither(this.mDrawableContainerState.mDither);
            }
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter) throws FileNotFoundException {
        this.mDrawableContainerState.mHasColorFilter = true;
        if (this.mDrawableContainerState.mColorFilter != colorFilter) {
            this.mDrawableContainerState.mColorFilter = colorFilter;
            Drawable drawable = this.mCurrDrawable;
            if (drawable != null) {
                drawable.setColorFilter(colorFilter);
            }
        }
        if (I1111IIl11.l11I11I11l(236683131L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{125, 82, 0, 17, 42, 119, 110, 3, 120, 2, 82, 9, 119, 123, 13, 109, 21, 40, 17, 0, 68, 47, 86, 87}));
        }
    }

    @Override // android.graphics.drawable.Drawable, Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIll1l1111
    public void setTintList(ColorStateList colorStateList) throws CertificateNotYetValidException {
        this.mDrawableContainerState.mHasTintList = true;
        if (this.mDrawableContainerState.mTintList != colorStateList) {
            this.mDrawableContainerState.mTintList = colorStateList;
            IIllllIlI1.setTintList(this.mCurrDrawable, colorStateList);
        }
    }

    @Override // android.graphics.drawable.Drawable, Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIll1l1111
    public void setTintMode(PorterDuff$Mode porterDuff$Mode) throws KeyException {
        this.mDrawableContainerState.mHasTintMode = true;
        if (this.mDrawableContainerState.mTintMode != porterDuff$Mode) {
            this.mDrawableContainerState.mTintMode = porterDuff$Mode;
            IIllllIlI1.setTintMode(this.mCurrDrawable, porterDuff$Mode);
        }
        if (lI1lllIII1.IlII1Illll(8688)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{94, 45, 90, 80}));
        }
    }

    public void setEnterFadeDuration(int i) throws CertificateParsingException {
        if (android.media.content.lIIlI111II.ll1I111ll1(9097)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{66, 34, 36, 0, 53, 80, 110, 99, 91}));
        }
        this.mDrawableContainerState.mEnterFadeDuration = i;
        if (android.accounts.utils.lIIlI111II.I11II1111l(203911198L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{103, 53, 20, 86, 84, 68, 117, 94, 73, 34, Byte.MAX_VALUE, 90, 94, 15, 112, 111, 35, 27, 22, 119, 85, 87, 93, 109}));
        }
    }

    public void setExitFadeDuration(int i) {
        this.mDrawableContainerState.mExitFadeDuration = i;
        if (l1lI1I1l11.Il1IIlI1II(561488938L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{80, 5, 26, 8, 11}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    protected void onBoundsChange(Rect rect) {
        Drawable drawable = this.mLastDrawable;
        if (drawable != null) {
            drawable.setBounds(rect);
        }
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 != null) {
            drawable2.setBounds(rect);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public boolean isStateful() throws UnknownServiceException {
        boolean zIsStateful = this.mDrawableContainerState.isStateful();
        if (androidx.constraintlayout.widget.lIIlI111II.II1lllllII(383016980L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{7}));
        }
        return zIsStateful;
    }

    @Override // android.graphics.drawable.Drawable
    public void setAutoMirrored(boolean z) {
        if (android.accounts.utils.lIIlI111II.lIll1IIl11(248324931L)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{98, 30, 20, 49, 47, 88, 91, 73, 86, 14, 88, 120, 115, 122, 3, 81, 87, 7, 8, 68, 83, 27, 70, 7, 6, 84, 6}));
        }
        if (this.mDrawableContainerState.mAutoMirrored != z) {
            this.mDrawableContainerState.mAutoMirrored = z;
            Drawable drawable = this.mCurrDrawable;
            if (drawable != null) {
                IIllllIlI1.setAutoMirrored(drawable, this.mDrawableContainerState.mAutoMirrored);
            }
        }
    }

    @Override // android.graphics.drawable.Drawable
    public boolean isAutoMirrored() throws NoRouteToHostException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{2, 45, 50, 17, 51, 71, 94, 72, 0, 10, 113, 115, 126, 116, 1, 121, 20, 39, 91, 121, 95, 15, 10, 114}), 645918708L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{118, 7, 86}));
        }
        return this.mDrawableContainerState.mAutoMirrored;
    }

    @Override // android.graphics.drawable.Drawable
    public void jumpToCurrentState() {
        boolean z;
        Drawable drawable = this.mLastDrawable;
        boolean z2 = true;
        if (drawable != null) {
            drawable.jumpToCurrentState();
            this.mLastDrawable = null;
            z = true;
        } else {
            z = false;
        }
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 != null) {
            drawable2.jumpToCurrentState();
            if (this.mHasAlpha) {
                this.mCurrDrawable.setAlpha(this.mAlpha);
            }
        }
        if (this.mExitAnimationEnd != 0) {
            this.mExitAnimationEnd = 0L;
            z = true;
        }
        if (this.mEnterAnimationEnd != 0) {
            this.mEnterAnimationEnd = 0L;
        } else {
            z2 = z;
        }
        if (z2) {
            invalidateSelf();
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setHotspot(float f, float f2) throws NoSuchMethodException, UnrecoverableEntryException {
        if (lIIIIII11I.Ill1lIIlIl(5566)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{81, 41, 47, 61, 52, 114, 117, 66, 8, 46, 103, 67, 66, 74, 115, 90, 19, 42}));
        }
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            IIllllIlI1.setHotspot(drawable, f, f2);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setHotspotBounds(int i, int i2, int i3, int i4) throws UTFDataFormatException {
        Rect rect = this.mHotspotBounds;
        if (rect == null) {
            this.mHotspotBounds = new Rect(i, i2, i3, i4);
        } else {
            rect.set(i, i2, i3, i4);
        }
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            IIllllIlI1.setHotspotBounds(drawable, i, i2, i3, i4);
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void getHotspotBounds(Rect rect) {
        Rect rect2 = this.mHotspotBounds;
        if (rect2 != null) {
            rect.set(rect2);
        } else {
            super.getHotspotBounds(rect);
        }
    }

    @Override // android.graphics.drawable.Drawable
    protected boolean onStateChange(int[] iArr) throws GeneralSecurityException {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{15, 12, 37, 43, 82, 93, 123, 90, 95}), 8798)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{118, 62, 6, 54, 90, 69, 97, 65, 90, 37, 7, 97, 102, 123, 85, 98, 43}));
        }
        Drawable drawable = this.mLastDrawable;
        if (drawable != null) {
            boolean state = drawable.setState(iArr);
            if (android.support.v4.graphics.drawable.lIIlI111II.lI1lIIll11(449334753L)) {
                throw new InternalError(I1I1lI1II1.a(new byte[]{68, 15, 8, 22, 58, 13, 1, 113, 117, 41, 95, 125}));
            }
            return state;
        }
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 != null) {
            return drawable2.setState(iArr);
        }
        if (llIlII1IlI.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 38, 53}), 279094342L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{121, 47, 38, 10, 14, 66, 79, 114, 80, 10, 99, 67, 123, 65, 12, 68, 41, 18, 21, 122, 97, 32, 87, 93, 89}));
        }
        return false;
    }

    @Override // android.graphics.drawable.Drawable
    protected boolean onLevelChange(int i) throws InvalidObjectException {
        Drawable drawable = this.mLastDrawable;
        if (drawable != null) {
            boolean level = drawable.setLevel(i);
            if (Il1IIllIll.I1lllI1llI(717394400L)) {
                throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{81, 14, 26, 14, 35, 66, 93, 93, 11, 86, 87, 99, 123, 126, 91, 66, 27, 39, 26, 89, 74, 46, 80}));
            }
            return level;
        }
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 == null) {
            return false;
        }
        boolean level2 = drawable2.setLevel(i);
        if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{7, 52}), 3793)) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{110, 49, 86, 46, 18, 126, 95, 64, 111, 23, 69, 101, 4, 81, 7, 6, 20, 59, 83, 103, 65, 53, 75, 118, 102, 113, 68, 29, 13, 120, 5}));
        }
        return level2;
    }

    @Override // android.graphics.drawable.Drawable
    public boolean onLayoutDirectionChanged(int i) {
        return this.mDrawableContainerState.setLayoutDirection(i, getCurrentIndex());
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        if (this.mDrawableContainerState.isConstantSize()) {
            return this.mDrawableContainerState.getConstantWidth();
        }
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            return drawable.getIntrinsicWidth();
        }
        return -1;
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        if (this.mDrawableContainerState.isConstantSize()) {
            return this.mDrawableContainerState.getConstantHeight();
        }
        Drawable drawable = this.mCurrDrawable;
        int intrinsicHeight = drawable != null ? drawable.getIntrinsicHeight() : -1;
        if (androidx.core.location.lIIlI111II.I1111IIl11(2434)) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{126, 49, 12, 33}));
        }
        return intrinsicHeight;
    }

    @Override // android.graphics.drawable.Drawable
    public int getMinimumWidth() throws BrokenBarrierException {
        if (this.mDrawableContainerState.isConstantSize()) {
            int constantMinimumWidth = this.mDrawableContainerState.getConstantMinimumWidth();
            if (l1l1I111I1.I111IlIl1I(857023847L)) {
                throw new NullPointerException(I1I1lI1II1.a(new byte[]{92, 19, 10, 29, 26}));
            }
            return constantMinimumWidth;
        }
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            return drawable.getMinimumWidth();
        }
        return 0;
    }

    @Override // android.graphics.drawable.Drawable
    public int getMinimumHeight() throws BrokenBarrierException, InvalidKeyException {
        if (this.mDrawableContainerState.isConstantSize()) {
            int constantMinimumHeight = this.mDrawableContainerState.getConstantMinimumHeight();
            if (lI1lllIII1.Ill1lIIlIl(234973771L)) {
                throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{70, 87, 47, 85, 46, 3, 125, 104, 14, 38, 98, 90, 66, 85, 115, 84, 87, 40, 13, 122, 104, 32, 7, 86, 119, 5, 0, 55, 37, 89}));
            }
            return constantMinimumHeight;
        }
        Drawable drawable = this.mCurrDrawable;
        if (drawable != null) {
            return drawable.getMinimumHeight();
        }
        return 0;
    }

    public void invalidateDrawable(Drawable drawable) throws CRLException, CloneNotSupportedException, CertStoreException {
        if (android.media.content.lIIlI111II.l11I1Ill11(2631)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{99, 6, 8, 81, 81, 120, 7, 121, 120, 16, 117, 85, 83, 126, 124, 114, 91}));
        }
        lll1lIll1I lll1lill1i = this.mDrawableContainerState;
        if (lll1lill1i != null) {
            lll1lill1i.invalidateCache();
        }
        if (drawable == this.mCurrDrawable && getCallback() != null) {
            getCallback().invalidateDrawable(this);
        }
        if (lII1llllI1.IlIIlIllI1(I1I1lI1II1.a(new byte[]{90, 43, 40, 21, 32, 2, 4, 96, 78}), 189791388L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{80, 92}));
        }
    }

    public void scheduleDrawable(Drawable drawable, Runnable runnable, long j) {
        if (drawable != this.mCurrDrawable || getCallback() == null) {
            return;
        }
        getCallback().scheduleDrawable(this, runnable, j);
    }

    public void unscheduleDrawable(Drawable drawable, Runnable runnable) {
        if (IIl1l1IllI.IlII1Illll(165651413L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{78, 40, 3, 44, 85, 90, 112, 71, 11, 34, 97, 66, 97, 124, 77, 84, 80, 16, 18, 90, 95, 13, 113, 113, 112, Byte.MAX_VALUE, 67, 40, 6, 68}));
        }
        if (drawable == this.mCurrDrawable && getCallback() != null) {
            getCallback().unscheduleDrawable(this, runnable);
        }
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{113, 53, 45, 46, 8, 87, 68}), 185561775L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{2, 62, 51, 55, 22, 87}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public boolean setVisible(boolean z, boolean z2) throws InvalidKeyException {
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{110, 14, 36, 11, 12, 125, 113, 101, 114, 11, 81, 8, 71}), 5404)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{93, 23, 3, 49}));
        }
        boolean visible = super.setVisible(z, z2);
        Drawable drawable = this.mLastDrawable;
        if (drawable != null) {
            drawable.setVisible(z, z2);
        }
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 != null) {
            drawable2.setVisible(z, z2);
        }
        return visible;
    }

    @Override // android.graphics.drawable.Drawable
    public int getOpacity() {
        Drawable drawable = this.mCurrDrawable;
        if (drawable == null || !drawable.isVisible()) {
            return -2;
        }
        return this.mDrawableContainerState.getOpacity();
    }

    void setCurrentIndex(int i) throws IllegalAccessException, MalformedURLException, SocketTimeoutException, KeyStoreException, FileNotFoundException, CertStoreException {
        if (IlIIlI11I1.I111IlIl1I(1062854741L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{118, 14, 80, 8, 24, 98, 96, 118, 99, 60, 5, 64, 121}));
        }
        selectDrawable(i);
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{110, 13, 8, 10, 33, 108, 70, 98, 117, 9}), 172278769L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{111, 50, 11, 6, 50, 97, 66, 123, 73, 53, 70, 68, 7, 92, 82, 96, 0, 48, 1, 71, 83, 80, 10, 118, 7, 122, 92, 14, 47, 75, 67, 49}));
        }
    }

    int getCurrentIndex() {
        int i = this.mCurIndex;
        if (l1lll111II.l11I11I11l(7584)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{116, 14, 22, 43, 41, Byte.MAX_VALUE, 79, 96, Byte.MAX_VALUE, 43, 74, 103, 99, 122, 110, 64, 27, 17, 83, 74, 0, 3, 82, 77, 67, 101, 112, 85, 22}));
        }
        return i;
    }

    boolean selectDrawable(int i) throws MalformedURLException, SocketTimeoutException, KeyStoreException, FileNotFoundException, CertStoreException {
        if (androidx.constraintlayout.widget.lIIlI111II.I1lll11llI(4600)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{15, 20, 21, 20, 32, 125, 4, 102, 88, 15, 4, 70, 108, 72, 87, 3, 26, 45, 47}));
        }
        if (i == this.mCurIndex) {
            return false;
        }
        long jUptimeMillis = SystemClock.uptimeMillis();
        if (this.mDrawableContainerState.mExitFadeDuration > 0) {
            Drawable drawable = this.mLastDrawable;
            if (drawable != null) {
                drawable.setVisible(false, false);
            }
            Drawable drawable2 = this.mCurrDrawable;
            if (drawable2 != null) {
                this.mLastDrawable = drawable2;
                this.mExitAnimationEnd = this.mDrawableContainerState.mExitFadeDuration + jUptimeMillis;
            } else {
                this.mLastDrawable = null;
                this.mExitAnimationEnd = 0L;
            }
        } else {
            Drawable drawable3 = this.mCurrDrawable;
            if (drawable3 != null) {
                drawable3.setVisible(false, false);
            }
        }
        if (i < 0 || i >= this.mDrawableContainerState.mNumChildren) {
            this.mCurrDrawable = null;
            this.mCurIndex = -1;
        } else {
            Drawable child = this.mDrawableContainerState.getChild(i);
            this.mCurrDrawable = child;
            this.mCurIndex = i;
            if (child != null) {
                if (this.mDrawableContainerState.mEnterFadeDuration > 0) {
                    this.mEnterAnimationEnd = jUptimeMillis + this.mDrawableContainerState.mEnterFadeDuration;
                }
                initializeDrawableForDisplay(child);
            }
        }
        if (this.mEnterAnimationEnd != 0 || this.mExitAnimationEnd != 0) {
            Runnable runnable = this.mAnimationRunnable;
            if (runnable == null) {
                this.mAnimationRunnable = new ll1ll1lIl1(this);
            } else {
                unscheduleSelf(runnable);
            }
            animate(true);
        }
        invalidateSelf();
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{14, 6, 55, 53, 22, 124, 112, 81, 84, 39, 95, 101, 77, 123, 97, 67, 56, 10, 48, 96, 66, 11, 68, Byte.MAX_VALUE, 11, 117, 1, 19, 86, 98, 80}), 5611)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{121, 53, 56, 54, 44, 86, 92}));
        }
        return true;
    }

    private void initializeDrawableForDisplay(Drawable drawable) throws KeyStoreException {
        if (this.mBlockInvalidateCallback == null) {
            this.mBlockInvalidateCallback = new I1IIlIl1lI();
        }
        drawable.setCallback(this.mBlockInvalidateCallback.wrap(drawable.getCallback()));
        try {
            if (this.mDrawableContainerState.mEnterFadeDuration <= 0 && this.mHasAlpha) {
                drawable.setAlpha(this.mAlpha);
            }
            if (this.mDrawableContainerState.mHasColorFilter) {
                drawable.setColorFilter(this.mDrawableContainerState.mColorFilter);
            } else {
                if (this.mDrawableContainerState.mHasTintList) {
                    IIllllIlI1.setTintList(drawable, this.mDrawableContainerState.mTintList);
                }
                if (this.mDrawableContainerState.mHasTintMode) {
                    IIllllIlI1.setTintMode(drawable, this.mDrawableContainerState.mTintMode);
                }
            }
            drawable.setVisible(isVisible(), true);
            drawable.setDither(this.mDrawableContainerState.mDither);
            drawable.setState(getState());
            drawable.setLevel(getLevel());
            drawable.setBounds(getBounds());
            IIllllIlI1.setLayoutDirection(drawable, IIllllIlI1.getLayoutDirection(this));
            IIllllIlI1.setAutoMirrored(drawable, this.mDrawableContainerState.mAutoMirrored);
            Rect rect = this.mHotspotBounds;
            if (rect != null) {
                IIllllIlI1.setHotspotBounds(drawable, rect.left, rect.top, rect.right, rect.bottom);
            }
            drawable.setCallback(this.mBlockInvalidateCallback.unwrap());
            if (IIIlIll111.Ill1lIIlIl(4398)) {
                throw new KeyStoreException(I1I1lI1II1.a(new byte[]{69, 93, 81, 35, 18, 121, 99, 64, 122, 11, 81, 94, 115, 74, 83, 123, 21, 54}));
            }
        } catch (Throwable th) {
            drawable.setCallback(this.mBlockInvalidateCallback.unwrap());
            throw th;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:14:0x003f  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x0065  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x006a A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:27:0x007c A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:28:0x007d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    void animate(boolean z) {
        boolean z2;
        Drawable drawable;
        boolean z3 = true;
        this.mHasAlpha = true;
        long jUptimeMillis = SystemClock.uptimeMillis();
        Drawable drawable2 = this.mCurrDrawable;
        if (drawable2 != null) {
            long j = this.mEnterAnimationEnd;
            if (j != 0) {
                if (j <= jUptimeMillis) {
                    drawable2.setAlpha(this.mAlpha);
                    this.mEnterAnimationEnd = 0L;
                } else {
                    this.mCurrDrawable.setAlpha(((255 - (((int) ((j - jUptimeMillis) * 255)) / this.mDrawableContainerState.mEnterFadeDuration)) * this.mAlpha) / 255);
                    z2 = true;
                }
            }
            drawable = this.mLastDrawable;
            if (drawable == null) {
                long j2 = this.mExitAnimationEnd;
                if (j2 != 0) {
                    if (j2 <= jUptimeMillis) {
                        drawable.setVisible(false, false);
                        this.mLastDrawable = null;
                        this.mExitAnimationEnd = 0L;
                    } else {
                        this.mLastDrawable.setAlpha(((((int) ((j2 - jUptimeMillis) * 255)) / this.mDrawableContainerState.mExitFadeDuration) * this.mAlpha) / 255);
                    }
                }
                if (z && z3) {
                    scheduleSelf(this.mAnimationRunnable, jUptimeMillis + 16);
                }
                if (androidx.recyclerview.widget.content.adapter.lIIlI111II.I1I11l11l1(729)) {
                    throw new CertificateException(I1I1lI1II1.a(new byte[]{114, 7, 53, 22, 14, 88, 121, 125, 1, 50, 3, 126, 82, 115, 80, 84, 50, 7, 80, 94, 89, 21, 3, 83, 99, 102, 78, 22, 61}));
                }
                return;
            }
            this.mExitAnimationEnd = 0L;
            z3 = z2;
            if (z) {
                scheduleSelf(this.mAnimationRunnable, jUptimeMillis + 16);
            }
            if (androidx.recyclerview.widget.content.adapter.lIIlI111II.I1I11l11l1(729)) {
            }
        } else {
            this.mEnterAnimationEnd = 0L;
        }
        z2 = false;
        drawable = this.mLastDrawable;
        if (drawable == null) {
        }
        z3 = z2;
        if (z) {
        }
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.I1I11l11l1(729)) {
        }
    }

    @Override // android.graphics.drawable.Drawable
    public Drawable getCurrent() {
        return this.mCurrDrawable;
    }

    final void updateDensity(Resources resources) throws KeyManagementException {
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{14, 16, 10, 60, 37, 92, 96, 96, 109, 13, 115, 94, 65, 80, 7, 89, 80, 59, 56, 113, 84, 35, Byte.MAX_VALUE}), 246236704L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{110, 2, 35, 92, 52, 89, 103, 99, 73, 60, 116, 85, 123, 80, 6, 123, 8, 59, 18, 98, 106, 42, 100, 84, 89}));
        }
        this.mDrawableContainerState.updateDensity(resources);
        if (lIIIIII11I.l1l1l1IIlI(301096638L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{68, 80, 35, 49, 32, 120, 1, 123, 79, 13, 71, 98, 68, 124, 115, 97, 22, 14, 47, 75, 115, 52, 73, 93}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void applyTheme(Resources$Theme resources$Theme) throws IllegalAccessException {
        this.mDrawableContainerState.applyTheme(resources$Theme);
    }

    @Override // android.graphics.drawable.Drawable
    public boolean canApplyTheme() throws TimeoutException, KeyStoreException, FileNotFoundException {
        boolean zCanApplyTheme = this.mDrawableContainerState.canApplyTheme();
        if (lI1lI11Ill.IlIllIll1I(I1I1lI1II1.a(new byte[]{111, 5, 8, 86, 35, 76, 67, 113, 88, 42, 102, 0, 126, 0}), 442990138L)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{90, 28, 86, 21, 82, 71, 79, 114, 82, 42, 93, 64, 93, 109, 68}));
        }
        return zCanApplyTheme;
    }

    @Override // android.graphics.drawable.Drawable
    public final Drawable$ConstantState getConstantState() {
        if (!this.mDrawableContainerState.canConstantState()) {
            return null;
        }
        this.mDrawableContainerState.mChangingConfigurations = getChangingConfigurations();
        return this.mDrawableContainerState;
    }

    @Override // android.graphics.drawable.Drawable
    public Drawable mutate() throws MalformedURLException, KeyStoreException, CRLException, FileNotFoundException, UTFDataFormatException, CertStoreException, InvalidAlgorithmParameterException {
        if (!this.mMutated && super.mutate() == this) {
            lll1lIll1I lll1lill1iCloneConstantState = cloneConstantState();
            lll1lill1iCloneConstantState.mutate();
            setConstantState(lll1lill1iCloneConstantState);
            this.mMutated = true;
        }
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{124, 37, 13, 31, 17, 94, 117, 116, 112, 17, 101, 103, 66, 116, 78, 6, 41, 8, 1, 70, 102, 11}))) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{15, 83, 58, 16, 24, 112, 69, 9, 8, 35, 2}));
        }
        return this;
    }

    lll1lIll1I cloneConstantState() {
        if (l111Il1lI1.IlIIl111lI(I1I1lI1II1.a(new byte[]{15, 49, 44, 28, 35, 92, 14, 86, 91, 85, 96, 113, 83, 83, 123, 100, 54, 6, 21, 93, Byte.MAX_VALUE, 16, 89, 100, 103, 83, 111}), 250270496L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{100, 19, 55, 53, 9, 97, 125, 4, 122, 33, 8, Byte.MAX_VALUE, 115}));
        }
        return this.mDrawableContainerState;
    }

    void clearMutated() throws CertPathBuilderException, UnknownHostException, EOFException, CloneNotSupportedException {
        if (l111Il1lI1.Il1IIlI1II(294337223L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{84, 29, 40, 80, 38, 113, 96, 94, 119, 12, 5, 102, 81}));
        }
        this.mDrawableContainerState.clearMutated();
        this.mMutated = false;
        if (IIl1l1IllI.Ill1lIIlIl(3687)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{79, 23, 46, 93, 0, 2, 15, 86, 118, 82, 5, 93, 90}));
        }
    }

    void setConstantState(lll1lIll1I lll1lill1i) throws MalformedURLException, KeyStoreException, FileNotFoundException, UTFDataFormatException, CertStoreException {
        if (l11Il1lI11.I111IlIl1I(I1I1lI1II1.a(new byte[]{112, 15, 46, 4, 5, 77, 2, 73, 116, 33, 73}), 6976)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{123, 12, 16, 1, 26, 69, 112, 121, 119, 6, 0, 85}));
        }
        this.mDrawableContainerState = lll1lill1i;
        int i = this.mCurIndex;
        if (i >= 0) {
            Drawable child = lll1lill1i.getChild(i);
            this.mCurrDrawable = child;
            if (child != null) {
                initializeDrawableForDisplay(child);
            }
        }
        this.mLastDrawable = null;
        if (lII1llllI1.ll1I1lII11(I1I1lI1II1.a(new byte[]{126, 29, 82, 21, 36, 77, 93, 98, 88, 62, 73, 82, 123, 76, 121, 64, 21, 52, 45, 94, 7, 49, 0, 111, 82, 76}))) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{78, 53, 6, 52, 14, 115, 81, 85, 14, 20, 74, 93, 115, 86, 119, 79, 15, 23, 33, 101, 3, 83, 7, 115, 125, 82, 125, 6, 33, 103}));
        }
    }

    static int resolveDensity(Resources resources, int i) {
        if (resources != null) {
            i = resources.getDisplayMetrics().densityDpi;
        }
        if (i == 0) {
            return 160;
        }
        return i;
    }
}
