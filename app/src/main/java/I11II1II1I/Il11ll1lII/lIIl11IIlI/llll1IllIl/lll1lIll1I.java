package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIllllIlI1;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.graphics.ColorFilter;
import android.graphics.PorterDuff$Mode;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$ConstantState;
import android.media.content.IIl1l1IllI;
import android.media.content.Il1llIl111;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.util.SparseArray;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I111I11Ill;
import androidx.core.location.IllIlllIII;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.FileNotFoundException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.cert.CRLException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CancellationException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
abstract class lll1lIll1I extends Drawable$ConstantState {
    boolean mAutoMirrored;
    boolean mCanConstantState;
    int mChangingConfigurations;
    boolean mCheckedConstantSize;
    boolean mCheckedConstantState;
    boolean mCheckedOpacity;
    boolean mCheckedPadding;
    boolean mCheckedStateful;
    int mChildrenChangingConfigurations;
    ColorFilter mColorFilter;
    int mConstantHeight;
    int mConstantMinimumHeight;
    int mConstantMinimumWidth;
    Rect mConstantPadding;
    boolean mConstantSize;
    int mConstantWidth;
    int mDensity;
    boolean mDither;
    SparseArray<Drawable$ConstantState> mDrawableFutures;
    Drawable[] mDrawables;
    int mEnterFadeDuration;
    int mExitFadeDuration;
    boolean mHasColorFilter;
    boolean mHasTintList;
    boolean mHasTintMode;
    int mLayoutDirection;
    boolean mMutated;
    int mNumChildren;
    int mOpacity;
    final IIllIIll1I mOwner;
    Resources mSourceRes;
    boolean mStateful;
    ColorStateList mTintList;
    PorterDuff$Mode mTintMode;
    boolean mVariablePadding;

    lll1lIll1I(lll1lIll1I lll1lill1i, IIllIIll1I iIllIIll1I, Resources resources) {
        Resources resources2;
        this.mVariablePadding = false;
        this.mConstantSize = false;
        this.mDither = true;
        this.mEnterFadeDuration = 0;
        this.mExitFadeDuration = 0;
        this.mOwner = iIllIIll1I;
        if (resources != null) {
            resources2 = resources;
        } else {
            resources2 = lll1lill1i != null ? lll1lill1i.mSourceRes : null;
        }
        this.mSourceRes = resources2;
        int iResolveDensity = IIllIIll1I.resolveDensity(resources, lll1lill1i != null ? lll1lill1i.mDensity : 0);
        this.mDensity = iResolveDensity;
        if (lll1lill1i != null) {
            this.mChangingConfigurations = lll1lill1i.mChangingConfigurations;
            this.mChildrenChangingConfigurations = lll1lill1i.mChildrenChangingConfigurations;
            this.mCheckedConstantState = true;
            this.mCanConstantState = true;
            this.mVariablePadding = lll1lill1i.mVariablePadding;
            this.mConstantSize = lll1lill1i.mConstantSize;
            this.mDither = lll1lill1i.mDither;
            this.mMutated = lll1lill1i.mMutated;
            this.mLayoutDirection = lll1lill1i.mLayoutDirection;
            this.mEnterFadeDuration = lll1lill1i.mEnterFadeDuration;
            this.mExitFadeDuration = lll1lill1i.mExitFadeDuration;
            this.mAutoMirrored = lll1lill1i.mAutoMirrored;
            this.mColorFilter = lll1lill1i.mColorFilter;
            this.mHasColorFilter = lll1lill1i.mHasColorFilter;
            this.mTintList = lll1lill1i.mTintList;
            this.mTintMode = lll1lill1i.mTintMode;
            this.mHasTintList = lll1lill1i.mHasTintList;
            this.mHasTintMode = lll1lill1i.mHasTintMode;
            if (lll1lill1i.mDensity == iResolveDensity) {
                if (lll1lill1i.mCheckedPadding) {
                    this.mConstantPadding = lll1lill1i.mConstantPadding != null ? new Rect(lll1lill1i.mConstantPadding) : null;
                    this.mCheckedPadding = true;
                }
                if (lll1lill1i.mCheckedConstantSize) {
                    this.mConstantWidth = lll1lill1i.mConstantWidth;
                    this.mConstantHeight = lll1lill1i.mConstantHeight;
                    this.mConstantMinimumWidth = lll1lill1i.mConstantMinimumWidth;
                    this.mConstantMinimumHeight = lll1lill1i.mConstantMinimumHeight;
                    this.mCheckedConstantSize = true;
                }
            }
            if (lll1lill1i.mCheckedOpacity) {
                this.mOpacity = lll1lill1i.mOpacity;
                this.mCheckedOpacity = true;
            }
            if (lll1lill1i.mCheckedStateful) {
                this.mStateful = lll1lill1i.mStateful;
                this.mCheckedStateful = true;
            }
            Drawable[] drawableArr = lll1lill1i.mDrawables;
            this.mDrawables = new Drawable[drawableArr.length];
            this.mNumChildren = lll1lill1i.mNumChildren;
            SparseArray<Drawable$ConstantState> sparseArray = lll1lill1i.mDrawableFutures;
            if (sparseArray != null) {
                this.mDrawableFutures = sparseArray.clone();
            } else {
                this.mDrawableFutures = new SparseArray<>(this.mNumChildren);
            }
            int i = this.mNumChildren;
            for (int i2 = 0; i2 < i; i2++) {
                Drawable drawable = drawableArr[i2];
                if (drawable != null) {
                    Drawable$ConstantState constantState = drawable.getConstantState();
                    if (constantState != null) {
                        this.mDrawableFutures.put(i2, constantState);
                    } else {
                        this.mDrawables[i2] = drawableArr[i2];
                    }
                }
            }
            return;
        }
        this.mDrawables = new Drawable[10];
        this.mNumChildren = 0;
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public int getChangingConfigurations() {
        return this.mChangingConfigurations | this.mChildrenChangingConfigurations;
    }

    public final int addChild(Drawable drawable) throws CertStoreException {
        if (II1I11IlI1.Il1IIlI1II(I1I1lI1II1.a(new byte[]{90, 85, 42, 33, 40, 84, 86, 126, 9, 92, 115, 118, 93, 109, 101, 88, 3, 3, 8, 115, 74, 88, 91, 69, 98, Byte.MAX_VALUE, 110}), 180453870L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{122, 30, 91, 60, 24, 67, 117, 70, 77, 40, 81, 123, 103, 80, 97, 79, 87, 43, 9, 123, 71, 51, 95, Byte.MAX_VALUE, 125}));
        }
        int i = this.mNumChildren;
        if (i >= this.mDrawables.length) {
            growArray(i, i + 10);
        }
        drawable.mutate();
        drawable.setVisible(false, true);
        drawable.setCallback(this.mOwner);
        this.mDrawables[i] = drawable;
        this.mNumChildren++;
        this.mChildrenChangingConfigurations = drawable.getChangingConfigurations() | this.mChildrenChangingConfigurations;
        invalidateCache();
        this.mConstantPadding = null;
        this.mCheckedPadding = false;
        this.mCheckedConstantSize = false;
        this.mCheckedConstantState = false;
        return i;
    }

    void invalidateCache() throws CertStoreException {
        this.mCheckedOpacity = false;
        this.mCheckedStateful = false;
        if (androidx.interpolator.view.animation.lIIlI111II.I11II1111l(7267)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{103, 19, 35, 48, 26, 112, 4, 82, Byte.MAX_VALUE, 37, 71, 103, 83, 86, 2, 68, 27, 11, 23, 72, 92, 81, 11, 103, 120, 113}));
        }
    }

    final int getCapacity() {
        int length = this.mDrawables.length;
        if (I111I11Ill.IlIIl111lI(I1I1lI1II1.a(new byte[]{100, 51, 80, 55, 32, 65, 2, 84, 81, 87, 85, 88, 100, 77, 92, 81, 21, 21, 38, 85}), 8307)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{66, 7, 7, 6, 46, 102, 94, 83, 116, 9, 104, 81, 118, 122, 108, 113, 53}));
        }
        return length;
    }

    private void createAllFutures() {
        SparseArray<Drawable$ConstantState> sparseArray = this.mDrawableFutures;
        if (sparseArray != null) {
            int size = sparseArray.size();
            for (int i = 0; i < size; i++) {
                this.mDrawables[this.mDrawableFutures.keyAt(i)] = prepareDrawable(this.mDrawableFutures.valueAt(i).newDrawable(this.mSourceRes));
            }
            this.mDrawableFutures = null;
        }
    }

    private Drawable prepareDrawable(Drawable drawable) throws FileNotFoundException {
        IIllllIlI1.setLayoutDirection(drawable, this.mLayoutDirection);
        Drawable drawableMutate = drawable.mutate();
        drawableMutate.setCallback(this.mOwner);
        return drawableMutate;
    }

    public final int getChildCount() throws KeyException {
        int i = this.mNumChildren;
        if (Il1lII1l1l.I1lllI1llI(369173962L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{101, 61, 0, 12, 14, 118, 1}));
        }
        return i;
    }

    public final Drawable getChild(int i) throws MalformedURLException, FileNotFoundException, CertStoreException {
        int iIndexOfKey;
        if (l111Il1lI1.ll1I1lII11(I1I1lI1II1.a(new byte[]{0, 81, 80, 31, 10, 90, 6}), 291359682L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{100, 86, 1, 0, 20, 99, 3, 98, 97, 86}));
        }
        Drawable drawable = this.mDrawables[i];
        if (drawable != null) {
            return drawable;
        }
        SparseArray<Drawable$ConstantState> sparseArray = this.mDrawableFutures;
        if (sparseArray == null || (iIndexOfKey = sparseArray.indexOfKey(i)) < 0) {
            if (llIlI11III.I1lllI1llI(9232)) {
                throw new MalformedURLException(I1I1lI1II1.a(new byte[]{68, 60, 37, 47, 10}));
            }
            return null;
        }
        Drawable drawablePrepareDrawable = prepareDrawable(this.mDrawableFutures.valueAt(iIndexOfKey).newDrawable(this.mSourceRes));
        this.mDrawables[i] = drawablePrepareDrawable;
        this.mDrawableFutures.removeAt(iIndexOfKey);
        if (this.mDrawableFutures.size() == 0) {
            this.mDrawableFutures = null;
        }
        if (lI1lI11Ill.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{117, 86, 80, 47, 27, 86, 120, 106, 114, 87, 3, 7, 114, 1, 99, 89, 17, 39, 8, 98, 73, 12, 98, 82}))) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{0, 52, 82, 9, 12}));
        }
        return drawablePrepareDrawable;
    }

    final boolean setLayoutDirection(int i, int i2) throws FileNotFoundException {
        int i3 = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        boolean z = false;
        for (int i4 = 0; i4 < i3; i4++) {
            if (drawableArr[i4] != null) {
                boolean layoutDirection = IIllllIlI1.setLayoutDirection(drawableArr[i4], i);
                if (i4 == i2) {
                    z = layoutDirection;
                }
            }
        }
        this.mLayoutDirection = i;
        return z;
    }

    final void updateDensity(Resources resources) {
        if (resources != null) {
            this.mSourceRes = resources;
            int iResolveDensity = IIllIIll1I.resolveDensity(resources, this.mDensity);
            int i = this.mDensity;
            this.mDensity = iResolveDensity;
            if (i != iResolveDensity) {
                this.mCheckedConstantSize = false;
                this.mCheckedPadding = false;
            }
        }
    }

    final void applyTheme(Resources$Theme resources$Theme) throws IllegalAccessException {
        if (IIlI1ll1ll.Ill1lIIlIl(8004)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{112, 93, 19, 4, 3, 5, Byte.MAX_VALUE, 3, 111}));
        }
        if (resources$Theme != null) {
            createAllFutures();
            int i = this.mNumChildren;
            Drawable[] drawableArr = this.mDrawables;
            for (int i2 = 0; i2 < i; i2++) {
                Drawable drawable = drawableArr[i2];
                if (drawable != null && IIllllIlI1.canApplyTheme(drawable)) {
                    IIllllIlI1.applyTheme(drawableArr[i2], resources$Theme);
                    this.mChildrenChangingConfigurations |= drawableArr[i2].getChangingConfigurations();
                }
            }
            updateDensity(lIlIIlIII1.getResources(resources$Theme));
        }
    }

    @Override // android.graphics.drawable.Drawable$ConstantState
    public boolean canApplyTheme() throws TimeoutException, FileNotFoundException {
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        for (int i2 = 0; i2 < i; i2++) {
            Drawable drawable = drawableArr[i2];
            if (drawable != null) {
                if (IIllllIlI1.canApplyTheme(drawable)) {
                    if (android.support.v4.graphics.drawable.lIIlI111II.llllI1l1II(1318)) {
                        throw new TimeoutException(I1I1lI1II1.a(new byte[]{70, 14, 48, 61, 5, 76, 70, 97, 76, 49, 64, 91, 124, 14, 110}));
                    }
                    return true;
                }
            } else {
                Drawable$ConstantState drawable$ConstantState = this.mDrawableFutures.get(i2);
                if (drawable$ConstantState != null && lIlIIlIII1.canApplyTheme(drawable$ConstantState)) {
                    return true;
                }
            }
        }
        if (IIl1l1IllI.l11I11I11l(I1I1lI1II1.a(new byte[]{83, 28, 27, 22, 45}), 10757)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{122, 84, 80, 2, 21, 88, 81, 65}));
        }
        return false;
    }

    void mutate() throws CRLException {
        if (l1lI1I1l11.I111IlIl1I(202992361L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{6, 47, 17, 46, 6, 87, 69, 98, 124, 7}));
        }
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        for (int i2 = 0; i2 < i; i2++) {
            Drawable drawable = drawableArr[i2];
            if (drawable != null) {
                drawable.mutate();
            }
        }
        this.mMutated = true;
        if (IIlI1Il1lI.IlII1Illll(298589205L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{69, 33, 40, 36, 80, 120, 101, 85, 126, 52, 71, 123, 92, 74, 82, 79, 33, 13, 87, 89, 104, 89, 118, 3, 73, 87, 69, 61, 3}));
        }
    }

    final void clearMutated() throws UnknownHostException, CloneNotSupportedException {
        if (IIl1l1IllI.I1lllI1llI(8328)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{14, 14, 42, 87, 35, 3, 67, 126, 105, 61, 96, 96, 121, 72, 83, 82, 45, 27, 21, 98, 5, 4, 102, 88, 117, 98, 92, 15, 30}));
        }
        this.mMutated = false;
        if (ll1l11I1II.Il1IIlI1II(I1I1lI1II1.a(new byte[]{5, 22, 19, 92, 47, 99, 3, 3, 83, 5, 5, 125, 89, 106, 67, 101, 41, 9, 10, 85, 120, 0}), 8288)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{109, 38, 0, 92, 41, 97, 88, 93, 79, 82, 91, 84, 122, 93, 86, 89}));
        }
    }

    public final void setVariablePadding(boolean z) {
        this.mVariablePadding = z;
    }

    public final Rect getConstantPadding() throws SocketTimeoutException, ClassNotFoundException {
        if (Il1lII1l1l.IlII1Illll(7308)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{4, 8, 85, 22, 21, 0, 79, 115, 116, 61, 123, 105, 69, 76, 113, 95, 6, 36, 59, 3, 104, 89, 70, 122, 4, 84, 126, 85, 46, 122}));
        }
        Rect rect = null;
        if (this.mVariablePadding) {
            if (l1IIll1I1l.l11I11I11l(I1I1lI1II1.a(new byte[]{91, 46, 86, 93, 37, 83, 102, 115}))) {
                throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{83, 22, 6, 87, 33, 84, 14, 115, Byte.MAX_VALUE, 16}));
            }
            return null;
        }
        Rect rect2 = this.mConstantPadding;
        if (rect2 != null || this.mCheckedPadding) {
            if (llIlII1IlI.l111l1I1Il(I1I1lI1II1.a(new byte[]{93, 19, 47}), 868033047L)) {
                throw new SecurityException(I1I1lI1II1.a(new byte[]{86, 20, 16, 23, 90, 3, 14, 85, 97, 53, 95, 93}));
            }
            return rect2;
        }
        createAllFutures();
        Rect rect3 = new Rect();
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        for (int i2 = 0; i2 < i; i2++) {
            if (drawableArr[i2].getPadding(rect3)) {
                if (rect == null) {
                    rect = new Rect(0, 0, 0, 0);
                }
                if (rect3.left > rect.left) {
                    rect.left = rect3.left;
                }
                if (rect3.top > rect.top) {
                    rect.top = rect3.top;
                }
                if (rect3.right > rect.right) {
                    rect.right = rect3.right;
                }
                if (rect3.bottom > rect.bottom) {
                    rect.bottom = rect3.bottom;
                }
            }
        }
        this.mCheckedPadding = true;
        this.mConstantPadding = rect;
        if (androidx.interpolator.view.animation.lIIlI111II.llIIlI1llI(8772)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{98, 19, 90, 84, 7, 108, 81, 122, 67, 13, 6, 121, 6, 86, 94, 2, 32, 59, 82}));
        }
        return rect;
    }

    public final void setConstantSize(boolean z) throws CertificateException {
        this.mConstantSize = z;
        if (IllllI11lI.l11I11I11l(I1I1lI1II1.a(new byte[]{91, 38, 16, 31, 5, 111, 78, 93, 79, 53, 74, 102, 99, 107, 80, Byte.MAX_VALUE, 36, 8, 91, 11, 91, 83, 98, 121, 73, 97, 6, 49, 44, 95, 64}), 341676174L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{82, 86, 23, 92, 87, 116, 89, 94, 74, 0, 5, 65, 13, 72, 115, 115, 42, 15, 4, 88, 3, 55, 118, 77, 67, 121, 81, 61, 80}));
        }
    }

    public final boolean isConstantSize() {
        return this.mConstantSize;
    }

    public final int getConstantWidth() throws BrokenBarrierException, KeyManagementException {
        if (l111Il1lI1.l1Il11I1Il(I1I1lI1II1.a(new byte[]{6, 44, 5, 13, 47, 6, 118, 100, 86, 10, 2, 3, 119, 9, 4, 101, 3}), 282142104L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{92, 12, 53, 44, 56, 124, 71, 6, 14, 85, 118, 83, 7, 126, 98, 122}));
        }
        if (!this.mCheckedConstantSize) {
            computeConstantSize();
        }
        return this.mConstantWidth;
    }

    public final int getConstantHeight() throws BrokenBarrierException {
        if (!this.mCheckedConstantSize) {
            computeConstantSize();
        }
        int i = this.mConstantHeight;
        if (lI11IlI1lI.Il1IIlI1II(2885)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{121, 83, 5, 33, 3, 96, 78, 1, 117, 61, 105, 3, 115, 79, 68, 13, 18, 52}));
        }
        return i;
    }

    public final int getConstantMinimumWidth() throws BrokenBarrierException {
        if (!this.mCheckedConstantSize) {
            computeConstantSize();
        }
        int i = this.mConstantMinimumWidth;
        if (IllIlllIII.I111IlIl1I(3409)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{80, 22, 21, 60, 81, 123, 66, 94, 79, 84, 3, 65, 71, 111, 89, 113, 17, 32, 53, 68, 124, 17, 118, 91, 66, 97, 103, 63, 14}));
        }
        return i;
    }

    public final int getConstantMinimumHeight() throws BrokenBarrierException {
        if (IIlI1ll1ll.III11111Il(257270301L)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{91, 13, 90, 38, 37, 84, 94, 99, 67, 52, 85, 91, 100, 99, 101, 65, 82, 46, 41, 99, 91}));
        }
        if (!this.mCheckedConstantSize) {
            computeConstantSize();
        }
        return this.mConstantMinimumHeight;
    }

    protected void computeConstantSize() throws BrokenBarrierException {
        if (I111lIl11I.l111l1I1Il(I1I1lI1II1.a(new byte[]{14, 17, 80, 10, 44, 100, 93, 66, 119, 50}), 320018012L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{80, 54, 87, 0, 12, 5, 85, 92, 90, 7, 97, 65, 115, 92, 123}));
        }
        this.mCheckedConstantSize = true;
        createAllFutures();
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        this.mConstantHeight = -1;
        this.mConstantWidth = -1;
        this.mConstantMinimumHeight = 0;
        this.mConstantMinimumWidth = 0;
        for (int i2 = 0; i2 < i; i2++) {
            Drawable drawable = drawableArr[i2];
            int intrinsicWidth = drawable.getIntrinsicWidth();
            if (intrinsicWidth > this.mConstantWidth) {
                this.mConstantWidth = intrinsicWidth;
            }
            int intrinsicHeight = drawable.getIntrinsicHeight();
            if (intrinsicHeight > this.mConstantHeight) {
                this.mConstantHeight = intrinsicHeight;
            }
            int minimumWidth = drawable.getMinimumWidth();
            if (minimumWidth > this.mConstantMinimumWidth) {
                this.mConstantMinimumWidth = minimumWidth;
            }
            int minimumHeight = drawable.getMinimumHeight();
            if (minimumHeight > this.mConstantMinimumHeight) {
                this.mConstantMinimumHeight = minimumHeight;
            }
        }
    }

    public final void setEnterFadeDuration(int i) {
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{114, 80, 16, 19, 87}), 472030867L)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{81, 80, 90}));
        }
        this.mEnterFadeDuration = i;
        if (lIIllIlIl1.I11II1I1I1(I1I1lI1II1.a(new byte[]{95, 20, 44, 33, 6, 100, 78, 73, 84, 87, 82, 100, 80, 96, 90, 86, 5, 83, 82, 92, 86, 25, 96, 3, 99, 6}))) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{91, 5, 33, 82, 21, 81, 79}));
        }
    }

    public final int getEnterFadeDuration() {
        return this.mEnterFadeDuration;
    }

    public final void setExitFadeDuration(int i) {
        if (llIlI11III.I1lllI1llI(7090)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{126, 50, 14, 51, 1, 97, 96, 7, 83, 40, 94, 125, 2, 72, 98, 99, 4, 7, 82, 5, 92, 52, 113, 116, 93, 67, 68, 43}));
        }
        this.mExitFadeDuration = i;
    }

    public final int getExitFadeDuration() {
        if (androidx.constraintlayout.widget.lIIlI111II.lI1lIIll11(252718482L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{83, 93, 84, 11, 36, 121}));
        }
        int i = this.mExitFadeDuration;
        if (Il1llIl111.l11I11I11l(I1I1lI1II1.a(new byte[]{117, 50, 48, 39, 82, 92, 6, 73, 65, 29, 99, 103, 6, 1, 100, 64, 49, 88, 87, 10, 64, 23, 126, 66, 93, 112, 83, 41, 9, 122, 3}), 4484)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{66, 62, 54, 11, 50, 66, 69, 8, 15, 15, 7, 89, 122, 90, 2, 96, 42, 22, 12, 84, 89, 44, Byte.MAX_VALUE}));
        }
        return i;
    }

    public final int getOpacity() {
        if (l1IIll1I1l.Il1IIlI1II(8116)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{90, 51, 80, 47, 8, 65, 114, 7, 118, 86, 1, 8, 79, 122, 65, 111, 18, 84, 4, 71, 121, 25, 68, 92}));
        }
        if (this.mCheckedOpacity) {
            return this.mOpacity;
        }
        createAllFutures();
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        int opacity = i > 0 ? drawableArr[0].getOpacity() : -2;
        for (int i2 = 1; i2 < i; i2++) {
            opacity = Drawable.resolveOpacity(opacity, drawableArr[i2].getOpacity());
        }
        this.mOpacity = opacity;
        this.mCheckedOpacity = true;
        return opacity;
    }

    public final boolean isStateful() {
        if (Il11II1llI.I1II1111ll(I1I1lI1II1.a(new byte[]{94, 3, 83, 46, 54}))) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{113, 6, 45, 39, 82, 70, 0, 64, 112, 0, 89, 92, 112, 88, 68, 122, 8, 50, 87, 88, 89, 53}));
        }
        if (this.mCheckedStateful) {
            return this.mStateful;
        }
        createAllFutures();
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        boolean z = false;
        int i2 = 0;
        while (true) {
            if (i2 >= i) {
                break;
            }
            if (drawableArr[i2].isStateful()) {
                z = true;
                break;
            }
            i2++;
        }
        this.mStateful = z;
        this.mCheckedStateful = true;
        return z;
    }

    public void growArray(int i, int i2) {
        Drawable[] drawableArr = new Drawable[i2];
        Drawable[] drawableArr2 = this.mDrawables;
        if (drawableArr2 != null) {
            System.arraycopy(drawableArr2, 0, drawableArr, 0, i);
        }
        this.mDrawables = drawableArr;
    }

    public boolean canConstantState() {
        if (android.media.content.II1I11IlI1.I1lIllll1l(171321198L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{65, 32, 20, 2, 22, 114, 115, 74, 109, 21, 89, 117, 2, 114}));
        }
        if (this.mCheckedConstantState) {
            return this.mCanConstantState;
        }
        createAllFutures();
        this.mCheckedConstantState = true;
        int i = this.mNumChildren;
        Drawable[] drawableArr = this.mDrawables;
        for (int i2 = 0; i2 < i; i2++) {
            if (drawableArr[i2].getConstantState() == null) {
                this.mCanConstantState = false;
                return false;
            }
        }
        this.mCanConstantState = true;
        return true;
    }
}
