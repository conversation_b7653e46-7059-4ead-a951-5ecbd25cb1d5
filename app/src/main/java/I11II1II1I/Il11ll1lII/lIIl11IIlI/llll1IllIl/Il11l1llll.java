package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import I1lI11lIll.lIlll111II.I111IlIl1l.lIIIllIlII.I1IllIll1l;
import I1lI11lIll.lIlll111II.I111IlIl1l.lIIIllIlII.ll111IIlI1;
import I1lI11lIll.lIlll111II.I111IlIl1l.lIIIllIlII.lllIlIl1ll;
import Il1l1I1IIl.II1lll11ll.Il11Ill1ll.Il1I111Il1.IIllllIlI1;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.Paint$Cap;
import android.graphics.Paint$Join;
import android.graphics.Paint$Style;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.io.InvalidClassException;
import java.io.SyncFailedException;
import java.security.AccessControlException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class Il11l1llll extends Drawable {
    public static final int ARROW_DIRECTION_END = 3;
    public static final int ARROW_DIRECTION_LEFT = 0;
    public static final int ARROW_DIRECTION_RIGHT = 1;
    public static final int ARROW_DIRECTION_START = 2;
    private static final float ARROW_HEAD_ANGLE = (float) Math.toRadians(45.0d);
    private float mArrowHeadLength;
    private float mArrowShaftLength;
    private float mBarGap;
    private float mBarLength;
    private int mDirection;
    private float mMaxCutForBarSize;
    private final Paint mPaint;
    private final Path mPath;
    private float mProgress;
    private final int mSize;
    private boolean mSpin;
    private boolean mVerticalMirror;

    private static float lerp(float f, float f2, float f3) {
        if (Il1l11I11I.III111l111(I1I1lI1II1.a(new byte[]{77, 18, 44, 40, 14, 98, 71, 66, 105, 19, 102, 70, 98, 104, 65, 82, 23, 38, 9, 71, 8}), I1I1lI1II1.a(new byte[]{84, 82, 91, 8, 59, 66, 84, 96, 99, 44, 113, 82, 7, 79, Byte.MAX_VALUE, 118, 14, 87, 35, 66, 88}))) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{0, 9, 6, 33, 84, 95}));
        }
        return f + ((f2 - f) * f3);
    }

    public Il11l1llll(Context context) {
        Paint paint = new Paint();
        this.mPaint = paint;
        this.mPath = new Path();
        this.mVerticalMirror = false;
        this.mDirection = 2;
        paint.setStyle(Paint$Style.STROKE);
        paint.setStrokeJoin(Paint$Join.MITER);
        paint.setStrokeCap(Paint$Cap.BUTT);
        paint.setAntiAlias(true);
        TypedArray typedArrayObtainStyledAttributes = context.getTheme().obtainStyledAttributes(null, lllIlIl1ll.DrawerArrowToggle, ll111IIlI1.drawerArrowStyle, I1IllIll1l.Base_Widget_AppCompat_DrawerArrowToggle);
        setColor(typedArrayObtainStyledAttributes.getColor(lllIlIl1ll.DrawerArrowToggle_color, 0));
        setBarThickness(typedArrayObtainStyledAttributes.getDimension(lllIlIl1ll.DrawerArrowToggle_thickness, 0.0f));
        setSpinEnabled(typedArrayObtainStyledAttributes.getBoolean(lllIlIl1ll.DrawerArrowToggle_spinBars, true));
        setGapSize(Math.round(typedArrayObtainStyledAttributes.getDimension(lllIlIl1ll.DrawerArrowToggle_gapBetweenBars, 0.0f)));
        this.mSize = typedArrayObtainStyledAttributes.getDimensionPixelSize(lllIlIl1ll.DrawerArrowToggle_drawableSize, 0);
        this.mBarLength = Math.round(typedArrayObtainStyledAttributes.getDimension(lllIlIl1ll.DrawerArrowToggle_barLength, 0.0f));
        this.mArrowHeadLength = Math.round(typedArrayObtainStyledAttributes.getDimension(lllIlIl1ll.DrawerArrowToggle_arrowHeadLength, 0.0f));
        this.mArrowShaftLength = typedArrayObtainStyledAttributes.getDimension(lllIlIl1ll.DrawerArrowToggle_arrowShaftLength, 0.0f);
        typedArrayObtainStyledAttributes.recycle();
    }

    public void setArrowHeadLength(float f) throws BrokenBarrierException {
        if (this.mArrowHeadLength != f) {
            this.mArrowHeadLength = f;
            invalidateSelf();
        }
        if (l1l1IllI11.l1l1Il1I11(I1I1lI1II1.a(new byte[]{85, 32, 91, 23, 17, 76, 66, 70, 83, 15, 116, 7, 113, 91, 109}), 172923118L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{1, 51, 53, 54, 8, 84, 122, 4, 74, 16, 96, 82, 94, 116, 101, 88, 81, 17, 23, 68, 70}));
        }
    }

    public float getArrowHeadLength() {
        return this.mArrowHeadLength;
    }

    public void setArrowShaftLength(float f) {
        if (this.mArrowShaftLength != f) {
            this.mArrowShaftLength = f;
            invalidateSelf();
        }
    }

    public float getArrowShaftLength() throws InvalidClassException, CertificateEncodingException {
        if (l1l1I111I1.IlII1Illll(208385587L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{67, 61, 91, 2, 3, 103, 3, 64, 80, 38, 87, 82, 5, 117, 126, 1, 37, 40, 10, 69, 6}));
        }
        float f = this.mArrowShaftLength;
        if (l11Il111ll.IlII1Illll(522730998L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{97, 45, 53, 48, 49, 1, 92, 68, 87, 5, 119, 98, 111, 8, 96, 121, 41, 86, 84, 72}));
        }
        return f;
    }

    public float getBarLength() {
        if (IIll1llI1l.Ill1lIIlIl(74)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{91, 53, 41, 32, 83, 101, 5, 73, 104, 14, 86, 2, 65, 119, 100, 126, 90, 88}));
        }
        return this.mBarLength;
    }

    public void setBarLength(float f) throws SyncFailedException {
        if (this.mBarLength != f) {
            this.mBarLength = f;
            invalidateSelf();
        }
        if (Il1l11I11I.l11I11I11l(232240434L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{125, 82, 16, 52, 5, 2, 70, 90, 86, 23, 64, 125, 65}));
        }
    }

    public void setColor(int i) {
        if (i != this.mPaint.getColor()) {
            this.mPaint.setColor(i);
            invalidateSelf();
        }
    }

    public int getColor() throws CertPathValidatorException {
        int color = this.mPaint.getColor();
        if (I111lIl11I.IlIllIll1I(1526410848L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{7, 54, 50}));
        }
        return color;
    }

    public void setBarThickness(float f) {
        if (this.mPaint.getStrokeWidth() != f) {
            this.mPaint.setStrokeWidth(f);
            this.mMaxCutForBarSize = (float) ((f / 2.0f) * Math.cos(ARROW_HEAD_ANGLE));
            invalidateSelf();
        }
    }

    public float getBarThickness() {
        return this.mPaint.getStrokeWidth();
    }

    public float getGapSize() throws NoSuchFieldException {
        if (IIIlIll111.Ill1lIIlIl(3259)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{93, 50, 80, 8, 33, Byte.MAX_VALUE, 7, 124, 15, 60, 102, 64, 98, 83, 7, 96, 37, 80, 33, 115, 71, 19, 97, 67, 116, 111, 2}));
        }
        return this.mBarGap;
    }

    public void setGapSize(float f) {
        if (f != this.mBarGap) {
            this.mBarGap = f;
            invalidateSelf();
        }
    }

    public void setDirection(int i) {
        if (i != this.mDirection) {
            this.mDirection = i;
            invalidateSelf();
        }
    }

    public boolean isSpinEnabled() {
        return this.mSpin;
    }

    public void setSpinEnabled(boolean z) {
        if (this.mSpin != z) {
            this.mSpin = z;
            invalidateSelf();
        }
    }

    public int getDirection() {
        if (lI1lllIII1.Ill1lIIlIl(172346369L)) {
            Log.w(I1I1lI1II1.a(new byte[]{116, 51, 33, 82, 11, 12, 102, 114, 104}), I1I1lI1II1.a(new byte[]{66, 81, 54, 52, 19, 0, 110, 99, 95, 7, 116, 90, 97, 67, 108, 1, 32, 38, 7, 104, 66, 20, 64, 116, 88, 122, 123}));
            return 0;
        }
        int i = this.mDirection;
        if (I1Ill1lIII.Ill1lIIlIl(425708477L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{67, 92, 90, 1, 47, 103, 90, 0, 1, 14, 125, 117, 3, 107, 92, 7, 82, 36, 84, 100, 70, 11, 86, 66}));
        }
        return i;
    }

    public void setVerticalMirror(boolean z) {
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.l11I1Ill11(9078)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 50, 54, 19, 10, 113, 82, 71, 11, 38}));
        }
        if (this.mVerticalMirror != z) {
            this.mVerticalMirror = z;
            invalidateSelf();
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void draw(Canvas canvas) {
        Rect bounds = getBounds();
        int i = this.mDirection;
        boolean z = false;
        if (i != 0 && (i == 1 || (i == 3 ? IIllllIlI1.getLayoutDirection(this) == 0 : IIllllIlI1.getLayoutDirection(this) == 1))) {
            z = true;
        }
        float f = this.mArrowHeadLength;
        float fLerp = lerp(this.mBarLength, (float) Math.sqrt(f * f * 2.0f), this.mProgress);
        float fLerp2 = lerp(this.mBarLength, this.mArrowShaftLength, this.mProgress);
        float fRound = Math.round(lerp(0.0f, this.mMaxCutForBarSize, this.mProgress));
        float fLerp3 = lerp(0.0f, ARROW_HEAD_ANGLE, this.mProgress);
        float fLerp4 = lerp(z ? 0.0f : -180.0f, z ? 180.0f : 0.0f, this.mProgress);
        double d = fLerp;
        double d2 = fLerp3;
        boolean z2 = z;
        float fRound2 = Math.round(Math.cos(d2) * d);
        float fRound3 = Math.round(d * Math.sin(d2));
        this.mPath.rewind();
        float fLerp5 = lerp(this.mBarGap + this.mPaint.getStrokeWidth(), -this.mMaxCutForBarSize, this.mProgress);
        float f2 = (-fLerp2) / 2.0f;
        this.mPath.moveTo(f2 + fRound, 0.0f);
        this.mPath.rLineTo(fLerp2 - (fRound * 2.0f), 0.0f);
        this.mPath.moveTo(f2, fLerp5);
        this.mPath.rLineTo(fRound2, fRound3);
        this.mPath.moveTo(f2, -fLerp5);
        this.mPath.rLineTo(fRound2, -fRound3);
        this.mPath.close();
        canvas.save();
        float strokeWidth = this.mPaint.getStrokeWidth();
        float fHeight = bounds.height() - (3.0f * strokeWidth);
        canvas.translate(bounds.centerX(), ((((int) (fHeight - (2.0f * r5))) / 4) * 2) + (strokeWidth * 1.5f) + this.mBarGap);
        if (this.mSpin) {
            canvas.rotate(fLerp4 * (this.mVerticalMirror ^ z2 ? -1 : 1));
        } else if (z2) {
            canvas.rotate(180.0f);
        }
        canvas.drawPath(this.mPath, this.mPaint);
        canvas.restore();
    }

    @Override // android.graphics.drawable.Drawable
    public void setAlpha(int i) {
        if (i != this.mPaint.getAlpha()) {
            this.mPaint.setAlpha(i);
            invalidateSelf();
        }
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{125, 52, 59, 40, 80, 4, 78, 73, 91, 54, 126, 98, 80, 85, 114, 98, 42, 89, 80, 84, 94, 47, 101, 96}), 212751136L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{115, 0, 42, 93, 7, 64, 101, 83, 119, 84, 103, 105, 71, 76, 4, 116, 46, 12, 26, 66, 115, 43, 82, 66, 3, 113, Byte.MAX_VALUE, 36, 30}));
        }
    }

    @Override // android.graphics.drawable.Drawable
    public void setColorFilter(ColorFilter colorFilter) {
        this.mPaint.setColorFilter(colorFilter);
        invalidateSelf();
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicHeight() {
        return this.mSize;
    }

    @Override // android.graphics.drawable.Drawable
    public int getIntrinsicWidth() {
        return this.mSize;
    }

    @Override // android.graphics.drawable.Drawable
    public int getOpacity() {
        if (IIl1l1IllI.I1lllI1llI(7744)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{115, 44, 82, 52, 16, 113, 113, 1, 15, 85, 125, 96, 3, 13, 90, 116, 54, 40, 39, 102}));
        }
        return -3;
    }

    public float getProgress() {
        return this.mProgress;
    }

    public void setProgress(float f) throws UnrecoverableKeyException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{96, 9}), 236778825L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{121, 42, 91, 42, 5, 1, 0, 106, 108, 13, 68, 64, 83, 84, 102, 100, 85, 59, 42, 106, 67, 59, 4, 124, 70, 71, 5}));
        }
        if (this.mProgress != f) {
            this.mProgress = f;
            invalidateSelf();
        }
    }

    public final Paint getPaint() {
        return this.mPaint;
    }
}
