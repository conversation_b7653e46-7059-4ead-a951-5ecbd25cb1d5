package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.graphics.Outline;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.Drawable$ConstantState;
import android.util.Log;
import androidx.core.location.Il1l11I11I;
import androidx.interpolator.view.animation.lIIII1l1lI;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CRLException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class lIlIIlIII1 {
    private lIlIIlIII1() {
    }

    public static boolean canApplyTheme(Drawable$ConstantState drawable$ConstantState) {
        if (lIIII1l1lI.l11I11I11l(7719)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{3, 51, 6, 60, 40, 120, 111, 117, 73, 8, 124, 82}));
        }
        return drawable$ConstantState.canApplyTheme();
    }

    public static Resources getResources(Resources$Theme resources$Theme) throws CRLException, InvalidAlgorithmParameterException {
        if (I1lllI11II.Ill1lIIlIl(5962)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{112, 87, 4, 23, 22, 93, 96, 121, 117, 15, 83, 118, 84, 108, 70, 84, 27, 52, 22, 112, 9, 6, 101, 118}));
        }
        Resources resources = resources$Theme.getResources();
        if (Il1l11I11I.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{82, 93, 80, 81, 23, 101, 93, 125, 75, 80, 98, 81, 79, 11, 65, 125}), 262772792L)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{126, 20, 91, 38, 35, 103, 98, 72, 91, 93, 122}));
        }
        return resources;
    }

    public static void getOutline(Drawable drawable, Outline outline) {
        if (Ill11ll111.llII1lIIlI(189443485L)) {
            Log.v(I1I1lI1II1.a(new byte[]{83, 21, 17, 43, 84, 76, 91}), I1I1lI1II1.a(new byte[]{120, 83, 56, 8, 42, 92, 126, 115, 86, 46, 72, 97, 81, 93, 76, 84, 45, 20, 49, 119, 115, 9, 86, 109, 3, 88}));
        } else {
            drawable.getOutline(outline);
        }
    }
}
