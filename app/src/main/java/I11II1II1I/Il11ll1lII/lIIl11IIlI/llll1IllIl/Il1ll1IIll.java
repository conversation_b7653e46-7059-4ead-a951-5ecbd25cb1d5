package I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl;

import android.accounts.utils.Ill11ll111;
import android.graphics.drawable.Animatable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class Il1ll1IIll extends lIIlI111II {
    private final Animatable mA;

    Il1ll1IIll(Animatable animatable) {
        super();
        this.mA = animatable;
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void start() {
        this.mA.start();
        if (Ill11ll111.IlIIl111lI(I1I1lI1II1.a(new byte[]{111, 44, 80, 43, 14, 90, 71, 3, 8, 42, 7, 74, 94, 81, 6, 103, 19, 14, 18, 94, 0, 40, 1, 13, 126, 110, 15, 83, 47, 107, 6}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{5, 87, 3, 34, 17, 82, 91, 117, 78, 10, 94, 119, 121, 75, 114, 111, 86, 15, 5, 115}));
        }
    }

    @Override // I11II1II1I.Il11ll1lII.lIIl11IIlI.llll1IllIl.lIIlI111II
    public void stop() {
        this.mA.stop();
    }
}
