package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import android.support.v4.graphics.drawable.I111lIl11I;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class Il1lI1IllI {
    private final long elementEndPosition;
    private final int elementId;

    static /* synthetic */ long access$000(Il1lI1IllI il1lI1IllI) throws CertificateException {
        long j = il1lI1IllI.elementEndPosition;
        if (I111lIl11I.l111l1I1Il(I1I1lI1II1.a(new byte[]{66, 22, 59, 60, 26, 2, 3, 105, 91, 85, 98, 3, 12, 124, 98, 116, 86, 11, 24, 116, 65, 6}), 219887219L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{120, 10, 49, 28, 32, 66}));
        }
        return j;
    }

    private Il1lI1IllI(int i, long j) {
        this.elementId = i;
        this.elementEndPosition = j;
    }
}
