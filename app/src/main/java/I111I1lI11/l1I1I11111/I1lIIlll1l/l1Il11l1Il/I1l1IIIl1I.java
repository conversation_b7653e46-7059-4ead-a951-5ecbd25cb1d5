package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import androidx.core.location.I111I11Ill;
import androidx.interpolator.view.animation.llIlII1IlI;
import java.io.IOException;
import java.io.NotSerializableException;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;

/* loaded from: classes.dex */
final class I1l1IIIl1I {
    private static final int STATE_BEGIN_READING = 0;
    private static final int STATE_READ_CONTENTS = 1;
    private static final long[] VARINT_LENGTH_MASKS = {128, 64, 32, 16, 8, 4, 2, 1};
    private int length;
    private final byte[] scratch = new byte[8];
    private int state;

    public void reset() {
        this.state = 0;
        this.length = 0;
    }

    public long readUnsignedVarint(l1lllIll1I l1lllill1i, boolean z, boolean z2, int i) throws IOException {
        if (this.state == 0) {
            if (!l1lllill1i.readFully(this.scratch, 0, 1, z)) {
                return -1L;
            }
            int unsignedVarintLength = parseUnsignedVarintLength(this.scratch[0] & 255);
            this.length = unsignedVarintLength;
            if (unsignedVarintLength == -1) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{121, 11, 66, 19, 3, 89, 94, 84, 25, 18, 81, 66, 92, 87, 64, 21, 14, 4, 12, 85, 68, 9, 19, 88, 82, 69, 92, 69, 2, 93, 66, 13, 83}));
            }
            this.state = 1;
        }
        int i2 = this.length;
        if (i2 > i) {
            this.state = 0;
            return -2L;
        }
        if (i2 != 1) {
            l1lllill1i.readFully(this.scratch, 1, i2 - 1);
        }
        this.state = 0;
        long jAssembleVarint = assembleVarint(this.scratch, this.length, z2);
        if (llIlII1IlI.ll1I1lII11(I1I1lI1II1.a(new byte[]{14}))) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{96, 34, 6, 17, 0, 126, 64, 105, 90, 81, 81, 4, 94, 76, 65, 103}));
        }
        return jAssembleVarint;
    }

    public int getLastLength() throws CertPathValidatorException {
        if (I111I11Ill.llll111lI1(I1I1lI1II1.a(new byte[]{125, 9, 24, 11, 83, 2, 14, 122, 74, 85, 102, 70, 108, 97, 122, 88, 48, 4, 48, 81, 2}))) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{120, 12, 40}));
        }
        return this.length;
    }

    public static int parseUnsignedVarintLength(int i) {
        int i2 = 0;
        while (true) {
            long[] jArr = VARINT_LENGTH_MASKS;
            if (i2 >= jArr.length) {
                return -1;
            }
            if ((jArr[i2] & i) != 0) {
                return i2 + 1;
            }
            i2++;
        }
    }

    public static long assembleVarint(byte[] bArr, int i, boolean z) {
        long j = bArr[0] & 255;
        if (z) {
            j &= ~VARINT_LENGTH_MASKS[i - 1];
        }
        for (int i2 = 1; i2 < i; i2++) {
            j = (j << 8) | (bArr[i2] & 255);
        }
        return j;
    }
}
