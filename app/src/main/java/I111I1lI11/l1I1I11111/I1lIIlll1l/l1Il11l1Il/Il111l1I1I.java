package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.media.session.PlaybackStateCompat;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.FileNotFoundException;
import java.io.IOException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.III1llIlIl;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;

/* loaded from: classes.dex */
final class Il111l1I1I {
    private static final int ID_EBML = 440786851;
    private static final int SEARCH_LENGTH = 1024;
    private int peekLength;
    private final III1llIlIl scratch = new III1llIlIl(8);

    public boolean sniff(l1lllIll1I l1lllill1i) throws IOException {
        long length = l1lllill1i.getLength();
        long j = PlaybackStateCompat.ACTION_PLAY_FROM_MEDIA_ID;
        if (length != -1 && length <= PlaybackStateCompat.ACTION_PLAY_FROM_MEDIA_ID) {
            j = length;
        }
        int i = (int) j;
        l1lllill1i.peekFully(this.scratch.getData(), 0, 4);
        long unsignedInt = this.scratch.readUnsignedInt();
        this.peekLength = 4;
        while (unsignedInt != 440786851) {
            int i2 = this.peekLength + 1;
            this.peekLength = i2;
            if (i2 == i) {
                return false;
            }
            l1lllill1i.peekFully(this.scratch.getData(), 0, 1);
            unsignedInt = ((unsignedInt << 8) & (-256)) | (this.scratch.getData()[0] & 255);
        }
        long uint = readUint(l1lllill1i);
        long j2 = this.peekLength;
        if (uint == Long.MIN_VALUE) {
            return false;
        }
        if (length != -1 && j2 + uint >= length) {
            return false;
        }
        while (true) {
            int i3 = this.peekLength;
            long j3 = j2 + uint;
            if (i3 >= j3) {
                return ((long) i3) == j3;
            }
            if (readUint(l1lllill1i) == Long.MIN_VALUE) {
                if (IllllI11Il.IIll1I11lI(170407263L)) {
                    throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{70, 32, 48, 21, 85, 91, 123, 4, 76, 83, 8, 0, 91, 73, 78}));
                }
                return false;
            }
            long uint2 = readUint(l1lllill1i);
            if (uint2 < 0 || uint2 > 2147483647L) {
                break;
            }
            if (uint2 != 0) {
                int i4 = (int) uint2;
                l1lllill1i.advancePeekPosition(i4);
                this.peekLength += i4;
            }
        }
        return false;
    }

    private long readUint(l1lllIll1I l1lllill1i) throws IOException {
        if (lII1llllI1.Il1IIlI1II(184800556L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{94, 21, 87, 13, 55, 114, 78, 70, 126, 92, 4, 106, 80, 77, 100, 99, 33, 87, 39, 104, 119, 83, 1, 122, 75, 79, 103, 17, 55, 6}));
        }
        int i = 0;
        l1lllill1i.peekFully(this.scratch.getData(), 0, 1);
        int i2 = this.scratch.getData()[0] & 255;
        if (i2 == 0) {
            return Long.MIN_VALUE;
        }
        int i3 = 128;
        int i4 = 0;
        while ((i2 & i3) == 0) {
            i3 >>= 1;
            i4++;
        }
        int i5 = i2 & (~i3);
        l1lllill1i.peekFully(this.scratch.getData(), 1, i4);
        while (i < i4) {
            i++;
            i5 = (this.scratch.getData()[i] & 255) + (i5 << 8);
        }
        this.peekLength += i4 + 1;
        long j = i5;
        if (l111Il1lI1.Il1IIlI1II(381602712L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{90, 55, 45, 92, 38, 65}));
        }
        return j;
    }
}
