package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import androidx.media3.common.l1llI1llII;
import java.io.IOException;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;

/* loaded from: classes.dex */
public interface llI1l11lII {
    public static final int ELEMENT_TYPE_BINARY = 4;
    public static final int ELEMENT_TYPE_FLOAT = 5;
    public static final int ELEMENT_TYPE_MASTER = 1;
    public static final int ELEMENT_TYPE_STRING = 3;
    public static final int ELEMENT_TYPE_UNKNOWN = 0;
    public static final int ELEMENT_TYPE_UNSIGNED_INT = 2;

    void binaryElement(int i, int i2, l1lllIll1I l1lllill1i) throws IOException;

    void endMasterElement(int i) throws l1llI1llII;

    void floatElement(int i, double d) throws l1llI1llII;

    int getElementType(int i);

    void integerElement(int i, long j) throws l1llI1llII;

    boolean isLevel1Element(int i);

    void startMasterElement(int i, long j, long j2) throws l1llI1llII;

    void stringElement(int i, String str) throws l1llI1llII;
}
