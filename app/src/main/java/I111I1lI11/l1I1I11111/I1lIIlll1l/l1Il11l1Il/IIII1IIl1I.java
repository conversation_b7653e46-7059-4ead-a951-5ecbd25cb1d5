package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import android.accounts.utils.lIIIIII11I;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.l1llI1llII;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.IOException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayDeque;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;

/* loaded from: classes.dex */
final class IIII1IIl1I implements Il1ll1IIll {
    private static final int ELEMENT_STATE_READ_CONTENT = 2;
    private static final int ELEMENT_STATE_READ_CONTENT_SIZE = 1;
    private static final int ELEMENT_STATE_READ_ID = 0;
    private static final int MAX_ID_BYTES = 4;
    private static final int MAX_INTEGER_ELEMENT_SIZE_BYTES = 8;
    private static final int MAX_LENGTH_BYTES = 8;
    private static final int VALID_FLOAT32_ELEMENT_SIZE_BYTES = 4;
    private static final int VALID_FLOAT64_ELEMENT_SIZE_BYTES = 8;
    private long elementContentSize;
    private int elementId;
    private int elementState;
    private llI1l11lII processor;
    private final byte[] scratch = new byte[8];
    private final ArrayDeque<Il1lI1IllI> masterElementsStack = new ArrayDeque<>();
    private final I1l1IIIl1I varintReader = new I1l1IIIl1I();

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.Il1ll1IIll
    public void init(llI1l11lII lli1l11lii) {
        this.processor = lli1l11lii;
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.Il1ll1IIll
    public void reset() {
        this.elementState = 0;
        this.masterElementsStack.clear();
        this.varintReader.reset();
        if (l11Il1lI11.I1lIllll1l(8250)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{85, 34, 26, 85, 39, 64, 121, 101, 91, 13, 68, 98, 3, 107, 81, 87, 38, 7, 81, 68, 120, 85, 4, 70, 117, 3, 113, 23, 92, 64, 1, 5}));
        }
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.Il1ll1IIll
    public boolean read(l1lllIll1I l1lllill1i) throws CertificateParsingException, IOException, UnrecoverableEntryException {
        ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkStateNotNull(this.processor);
        while (true) {
            Il1lI1IllI il1lI1IllIPeek = this.masterElementsStack.peek();
            if (il1lI1IllIPeek == null || l1lllill1i.getPosition() < Il1lI1IllI.access$000(il1lI1IllIPeek)) {
                if (this.elementState == 0) {
                    long unsignedVarint = this.varintReader.readUnsignedVarint(l1lllill1i, true, false, 4);
                    if (unsignedVarint == -2) {
                        unsignedVarint = maybeResyncToNextLevel1Element(l1lllill1i);
                    }
                    if (unsignedVarint == -1) {
                        if (llIlI11III.I1II1111ll(I1I1lI1II1.a(new byte[]{113, 35, 33, 61}))) {
                            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{68, 17, 40, 85, 19, 122, 111, 64, 10, 87, 82, 116, 112, 78, 124, 84, 16, 48, 16, 5}));
                        }
                        return false;
                    }
                    this.elementId = (int) unsignedVarint;
                    this.elementState = 1;
                }
                if (this.elementState == 1) {
                    this.elementContentSize = this.varintReader.readUnsignedVarint(l1lllill1i, false, true, 8);
                    this.elementState = 2;
                }
                int elementType = this.processor.getElementType(this.elementId);
                if (elementType != 0) {
                    if (elementType == 1) {
                        long position = l1lllill1i.getPosition();
                        this.masterElementsStack.push(new Il1lI1IllI(this.elementId, this.elementContentSize + position));
                        this.processor.startMasterElement(this.elementId, position, this.elementContentSize);
                        this.elementState = 0;
                        return true;
                    }
                    if (elementType == 2) {
                        long j = this.elementContentSize;
                        if (j > 8) {
                            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{126, 10, 20, 4, 14, 92, 83, 16, 80, 10, 68, 85, 82, 92, 70, 21, 17, 8, 24, 87, 10, 65}) + this.elementContentSize, null);
                        }
                        this.processor.integerElement(this.elementId, readInteger(l1lllill1i, (int) j));
                        this.elementState = 0;
                        return true;
                    }
                    if (elementType == 3) {
                        long j2 = this.elementContentSize;
                        if (j2 > 2147483647L) {
                            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 23, 85, 85, 1, 93, 85, 91, 77, 20, 70, 11, 27, 7, 8, 16}) + this.elementContentSize, null);
                        }
                        this.processor.stringElement(this.elementId, readString(l1lllill1i, (int) j2));
                        this.elementState = 0;
                        if (l1l1IllI11.I111IlIl1I(175164395L)) {
                            throw new InstantiationError(I1I1lI1II1.a(new byte[]{118, 62, 42, 49, 87, 80, 6, 65, 104, 8, 69, 123, 92, 109, 92, 12, 83, 81}));
                        }
                        return true;
                    }
                    if (elementType == 4) {
                        this.processor.binaryElement(this.elementId, (int) this.elementContentSize, l1lllill1i);
                        this.elementState = 0;
                        if (l11Il111ll.Ill1lIIlIl(319378092L)) {
                            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{123, 92, 52, 31, 19, 5, 80, 125, 92, 85, 73, 96, 4, 80, 95, 83, 47, 84, 38, 96, 3, 57, 123, 92, 123, 0, 14, 55, 34, 87}));
                        }
                        return true;
                    }
                    if (elementType == 5) {
                        long j3 = this.elementContentSize;
                        if (j3 != 4 && j3 != 8) {
                            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{126, 10, 20, 4, 14, 92, 83, 16, 95, 8, 95, 81, 65, 25, 71, 92, 24, 4, 88, 18}) + this.elementContentSize, null);
                        }
                        this.processor.floatElement(this.elementId, readFloat(l1lllill1i, (int) j3));
                        this.elementState = 0;
                        return true;
                    }
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{126, 10, 20, 4, 14, 92, 83, 16, 92, 8, 85, 93, 80, 87, 64, 21, 22, 24, 18, 87, 16}) + elementType, null);
                }
                l1lllill1i.skipFully((int) this.elementContentSize);
                this.elementState = 0;
            } else {
                this.processor.endMasterElement(this.masterElementsStack.pop().elementId);
                return true;
            }
        }
    }

    private long maybeResyncToNextLevel1Element(l1lllIll1I l1lllill1i) throws IOException {
        if (IllllI11Il.III111l111(I1I1lI1II1.a(new byte[]{98, 16, 24, 47, 21, 0, 100, 70, 95, 15, 106}), 819067080L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{120, 52, 46, 53}));
        }
        l1lllill1i.resetPeekPosition();
        while (true) {
            l1lllill1i.peekFully(this.scratch, 0, 4);
            int unsignedVarintLength = I1l1IIIl1I.parseUnsignedVarintLength(this.scratch[0]);
            if (unsignedVarintLength != -1 && unsignedVarintLength <= 4) {
                int iAssembleVarint = (int) I1l1IIIl1I.assembleVarint(this.scratch, unsignedVarintLength, false);
                if (this.processor.isLevel1Element(iAssembleVarint)) {
                    l1lllill1i.skipFully(unsignedVarintLength);
                    return iAssembleVarint;
                }
            }
            l1lllill1i.skipFully(1);
        }
    }

    private long readInteger(l1lllIll1I l1lllill1i, int i) throws IOException, CertStoreException {
        if (lIIIIII11I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{65, 14, 48, 55, 90, 66, 115, 106, 77, 50, 82}), 618408619L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{89, 53, 52, 52, 15, 83, 125, 94, 73, 50, 98, 9, 89, 90, 80, 4, 47, 89, 91, 72, 124, 85, 123, 6, 113, 110, 117, 17, 51, 96, 5}));
        }
        l1lllill1i.readFully(this.scratch, 0, i);
        long j = 0;
        for (int i2 = 0; i2 < i; i2++) {
            j = (j << 8) | (this.scratch[i2] & 255);
        }
        return j;
    }

    private double readFloat(l1lllIll1I l1lllill1i, int i) throws IOException, CertStoreException {
        double dLongBitsToDouble;
        long integer = readInteger(l1lllill1i, i);
        if (i == 4) {
            dLongBitsToDouble = Float.intBitsToFloat((int) integer);
        } else {
            dLongBitsToDouble = Double.longBitsToDouble(integer);
        }
        if (lI11IlI1lI.Ill1lIIlIl(8620)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{126, 52, 33, 22, 82, 87, 124, 82, 73, 39, 67, 82, 94, 75, 77, 5, 27, 23, 41, 7, 121, 85, 105, 76, 113, 116, 121, 3, 53}));
        }
        return dLongBitsToDouble;
    }

    private static String readString(l1lllIll1I l1lllill1i, int i) throws TimeoutException, IOException {
        if (lIIlI111II.l1llI1llII(2827)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{65, 12, 91, 0, 23, 79, 103, 114, 65, 49, 68, 6, 13, 125, 0, 84, 87, 35, 7, 0, 83, 37, 116, 87, 96, 122, 103}));
        }
        if (i == 0) {
            return "";
        }
        byte[] bArr = new byte[i];
        l1lllill1i.readFully(bArr, 0, i);
        while (i > 0 && bArr[i - 1] == 0) {
            i--;
        }
        return new String(bArr, 0, i);
    }
}
