package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lIIIIII11I;
import android.media.content.Il1llIl111;
import android.media.content.lll1IIII11;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import android.util.SparseArray;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.DrmInitData;
import androidx.media3.common.DrmInitData$SchemeData;
import androidx.media3.common.l1llI1llII;
import androidx.media3.common.lII1111ll1;
import androidx.media3.common.lIlI1IIII1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.NotActiveException;
import java.io.StreamCorruptedException;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.AccessControlException;
import java.security.InvalidParameterException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.SignatureException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CertificateParsingException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.I1I1I1IIlI;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.III1llIlIl;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIl1llIlll;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I11ll1lI11;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.IIlII1l1Il;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.Il1Il1l1Il;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.IlII11I1I1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.lII1lIII1I;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.lIl11lllI1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.ll1III1lIl;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.ll1ll1IlIl;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.llllllllI1;

/* loaded from: classes.dex */
public class I11ll1lIl1 implements llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I {
    private static final int BLOCK_ADDITIONAL_ID_VP9_ITU_T_35 = 4;
    private static final int BLOCK_ADD_ID_TYPE_DVCC = 1685480259;
    private static final int BLOCK_ADD_ID_TYPE_DVVC = 1685485123;
    private static final int BLOCK_STATE_DATA = 2;
    private static final int BLOCK_STATE_HEADER = 1;
    private static final int BLOCK_STATE_START = 0;
    private static final int ENCRYPTION_IV_SIZE = 8;
    public static final int FLAG_DISABLE_SEEK_FOR_CUES = 1;
    private static final int FOURCC_COMPRESSION_DIVX = 1482049860;
    private static final int FOURCC_COMPRESSION_H263 = 859189832;
    private static final int FOURCC_COMPRESSION_VC1 = 826496599;
    private static final int ID_AUDIO = 225;
    private static final int ID_AUDIO_BIT_DEPTH = 25188;
    private static final int ID_BLOCK = 161;
    private static final int ID_BLOCK_ADDITIONAL = 165;
    private static final int ID_BLOCK_ADDITIONS = 30113;
    private static final int ID_BLOCK_ADDITION_MAPPING = 16868;
    private static final int ID_BLOCK_ADD_ID = 238;
    private static final int ID_BLOCK_ADD_ID_EXTRA_DATA = 16877;
    private static final int ID_BLOCK_ADD_ID_TYPE = 16871;
    private static final int ID_BLOCK_DURATION = 155;
    private static final int ID_BLOCK_GROUP = 160;
    private static final int ID_BLOCK_MORE = 166;
    private static final int ID_CHANNELS = 159;
    private static final int ID_CLUSTER = 524531317;
    private static final int ID_CODEC_DELAY = 22186;
    private static final int ID_CODEC_ID = 134;
    private static final int ID_CODEC_PRIVATE = 25506;
    private static final int ID_COLOUR = 21936;
    private static final int ID_COLOUR_PRIMARIES = 21947;
    private static final int ID_COLOUR_RANGE = 21945;
    private static final int ID_COLOUR_TRANSFER = 21946;
    private static final int ID_CONTENT_COMPRESSION = 20532;
    private static final int ID_CONTENT_COMPRESSION_ALGORITHM = 16980;
    private static final int ID_CONTENT_COMPRESSION_SETTINGS = 16981;
    private static final int ID_CONTENT_ENCODING = 25152;
    private static final int ID_CONTENT_ENCODINGS = 28032;
    private static final int ID_CONTENT_ENCODING_ORDER = 20529;
    private static final int ID_CONTENT_ENCODING_SCOPE = 20530;
    private static final int ID_CONTENT_ENCRYPTION = 20533;
    private static final int ID_CONTENT_ENCRYPTION_AES_SETTINGS = 18407;
    private static final int ID_CONTENT_ENCRYPTION_AES_SETTINGS_CIPHER_MODE = 18408;
    private static final int ID_CONTENT_ENCRYPTION_ALGORITHM = 18401;
    private static final int ID_CONTENT_ENCRYPTION_KEY_ID = 18402;
    private static final int ID_CUES = 475249515;
    private static final int ID_CUE_CLUSTER_POSITION = 241;
    private static final int ID_CUE_POINT = 187;
    private static final int ID_CUE_TIME = 179;
    private static final int ID_CUE_TRACK_POSITIONS = 183;
    private static final int ID_DEFAULT_DURATION = 2352003;
    private static final int ID_DISCARD_PADDING = 30114;
    private static final int ID_DISPLAY_HEIGHT = 21690;
    private static final int ID_DISPLAY_UNIT = 21682;
    private static final int ID_DISPLAY_WIDTH = 21680;
    private static final int ID_DOC_TYPE = 17026;
    private static final int ID_DOC_TYPE_READ_VERSION = 17029;
    private static final int ID_DURATION = 17545;
    private static final int ID_EBML = 440786851;
    private static final int ID_EBML_READ_VERSION = 17143;
    private static final int ID_FLAG_DEFAULT = 136;
    private static final int ID_FLAG_FORCED = 21930;
    private static final int ID_INFO = 357149030;
    private static final int ID_LANGUAGE = 2274716;
    private static final int ID_LUMNINANCE_MAX = 21977;
    private static final int ID_LUMNINANCE_MIN = 21978;
    private static final int ID_MASTERING_METADATA = 21968;
    private static final int ID_MAX_BLOCK_ADDITION_ID = 21998;
    private static final int ID_MAX_CLL = 21948;
    private static final int ID_MAX_FALL = 21949;
    private static final int ID_NAME = 21358;
    private static final int ID_PIXEL_HEIGHT = 186;
    private static final int ID_PIXEL_WIDTH = 176;
    private static final int ID_PRIMARY_B_CHROMATICITY_X = 21973;
    private static final int ID_PRIMARY_B_CHROMATICITY_Y = 21974;
    private static final int ID_PRIMARY_G_CHROMATICITY_X = 21971;
    private static final int ID_PRIMARY_G_CHROMATICITY_Y = 21972;
    private static final int ID_PRIMARY_R_CHROMATICITY_X = 21969;
    private static final int ID_PRIMARY_R_CHROMATICITY_Y = 21970;
    private static final int ID_PROJECTION = 30320;
    private static final int ID_PROJECTION_POSE_PITCH = 30324;
    private static final int ID_PROJECTION_POSE_ROLL = 30325;
    private static final int ID_PROJECTION_POSE_YAW = 30323;
    private static final int ID_PROJECTION_PRIVATE = 30322;
    private static final int ID_PROJECTION_TYPE = 30321;
    private static final int ID_REFERENCE_BLOCK = 251;
    private static final int ID_SAMPLING_FREQUENCY = 181;
    private static final int ID_SEEK = 19899;
    private static final int ID_SEEK_HEAD = 290298740;
    private static final int ID_SEEK_ID = 21419;
    private static final int ID_SEEK_POSITION = 21420;
    private static final int ID_SEEK_PRE_ROLL = 22203;
    private static final int ID_SEGMENT = 408125543;
    private static final int ID_SEGMENT_INFO = 357149030;
    private static final int ID_SIMPLE_BLOCK = 163;
    private static final int ID_STEREO_MODE = 21432;
    private static final int ID_TIMECODE_SCALE = 2807729;
    private static final int ID_TIME_CODE = 231;
    private static final int ID_TRACKS = 374648427;
    private static final int ID_TRACK_ENTRY = 174;
    private static final int ID_TRACK_NUMBER = 215;
    private static final int ID_TRACK_TYPE = 131;
    private static final int ID_VIDEO = 224;
    private static final int ID_WHITE_POINT_CHROMATICITY_X = 21975;
    private static final int ID_WHITE_POINT_CHROMATICITY_Y = 21976;
    private static final int LACING_EBML = 3;
    private static final int LACING_FIXED_SIZE = 2;
    private static final int LACING_NONE = 0;
    private static final int LACING_XIPH = 1;
    private static final int OPUS_MAX_INPUT_SIZE = 5760;
    private static final int SSA_PREFIX_END_TIMECODE_OFFSET = 21;
    private static final long SSA_TIMECODE_LAST_VALUE_SCALING_FACTOR = 10000;
    private static final int SUBRIP_PREFIX_END_TIMECODE_OFFSET = 19;
    private static final long SUBRIP_TIMECODE_LAST_VALUE_SCALING_FACTOR = 1000;
    private static final Map<String, Integer> TRACK_NAME_TO_ROTATION_DEGREES;
    private static final int TRACK_TYPE_AUDIO = 2;
    private static final int UNSET_ENTRY_ID = -1;
    private static final int VORBIS_MAX_INPUT_SIZE = 8192;
    private static final int VTT_PREFIX_END_TIMECODE_OFFSET = 25;
    private static final long VTT_TIMECODE_LAST_VALUE_SCALING_FACTOR = 1000;
    private static final int WAVE_FORMAT_EXTENSIBLE = 65534;
    private static final int WAVE_FORMAT_PCM = 1;
    private static final int WAVE_FORMAT_SIZE = 18;
    private int blockAdditionalId;
    private long blockDurationUs;
    private int blockFlags;
    private long blockGroupDiscardPaddingNs;
    private boolean blockHasReferenceBlock;
    private int blockSampleCount;
    private int blockSampleIndex;
    private int[] blockSampleSizes;
    private int blockState;
    private long blockTimeUs;
    private int blockTrackNumber;
    private int blockTrackNumberLength;
    private long clusterTimecodeUs;
    private I1I1I1IIlI cueClusterPositions;
    private I1I1I1IIlI cueTimesUs;
    private long cuesContentPosition;
    private I1l11I1I1I currentTrack;
    private long durationTimecode;
    private long durationUs;
    private final III1llIlIl encryptionInitializationVector;
    private final III1llIlIl encryptionSubsampleData;
    private ByteBuffer encryptionSubsampleDataBuffer;
    private ll1III1lIl extractorOutput;
    private boolean haveOutputSample;
    private final III1llIlIl nalLength;
    private final III1llIlIl nalStartCode;
    private final Il1ll1IIll reader;
    private int sampleBytesRead;
    private int sampleBytesWritten;
    private int sampleCurrentNalBytesRemaining;
    private boolean sampleEncodingHandled;
    private boolean sampleInitializationVectorRead;
    private int samplePartitionCount;
    private boolean samplePartitionCountRead;
    private byte sampleSignalByte;
    private boolean sampleSignalByteRead;
    private final III1llIlIl sampleStrippedBytes;
    private final III1llIlIl scratch;
    private int seekEntryId;
    private final III1llIlIl seekEntryIdBytes;
    private long seekEntryPosition;
    private boolean seekForCues;
    private final boolean seekForCuesEnabled;
    private long seekPositionAfterBuildingCues;
    private boolean seenClusterPositionForCurrentCuePoint;
    private long segmentContentPosition;
    private long segmentContentSize;
    private boolean sentSeekMap;
    private final III1llIlIl subtitleSample;
    private final III1llIlIl supplementalData;
    private long timecodeScale;
    private final SparseArray<I1l11I1I1I> tracks;
    private final I1l1IIIl1I varintReader;
    private final III1llIlIl vorbisNumPageSamples;
    private static final String TAG = I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16});
    private static final String DOC_TYPE_MATROSKA = I1I1lI1II1.a(new byte[]{90, 5, 22, 23, 13, 70, 92, 81});
    private static final String DOC_TYPE_WEBM = I1I1lI1II1.a(new byte[]{64, 1, 0, 8});
    private static final String CODEC_ID_VP8 = I1I1lI1II1.a(new byte[]{97, 59, 52, 53, 90});
    private static final String CODEC_ID_VP9 = I1I1lI1II1.a(new byte[]{97, 59, 52, 53, 91});
    private static final String CODEC_ID_AV1 = I1I1lI1II1.a(new byte[]{97, 59, 35, 51, 83});
    private static final String CODEC_ID_MPEG2 = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 5});
    private static final String CODEC_ID_MPEG4_SP = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 102, 105});
    private static final String CODEC_ID_MPEG4_ASP = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 106, 100});
    private static final String CODEC_ID_MPEG4_AP = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 105});
    private static final String CODEC_ID_H264 = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 111, 119});
    private static final String CODEC_ID_H265 = I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, Byte.MAX_VALUE, 31, 112, 55, Byte.MAX_VALUE, 31, 125, 124, 98, 118});
    private static final String CODEC_ID_FOURCC = I1I1lI1II1.a(new byte[]{97, 59, 47, 54, 77, 99, 113, 103, 22, 34, Byte.MAX_VALUE, 101, 103, 122, 119});
    private static final String CODEC_ID_THEORA = I1I1lI1II1.a(new byte[]{97, 59, 54, 45, 39, 122, 101, 113});
    private static final String CODEC_ID_VORBIS = I1I1lI1II1.a(new byte[]{118, 59, 52, 42, 48, 119, 126, 99});
    private static final String CODEC_ID_OPUS = I1I1lI1II1.a(new byte[]{118, 59, 45, 53, 55, 102});
    private static final String CODEC_ID_AAC = I1I1lI1II1.a(new byte[]{118, 59, 35, 36, 33});
    private static final String CODEC_ID_MP2 = I1I1lI1II1.a(new byte[]{118, 59, 47, 53, 39, 114, 24, 124, 11});
    private static final String CODEC_ID_MP3 = I1I1lI1II1.a(new byte[]{118, 59, 47, 53, 39, 114, 24, 124, 10});
    private static final String CODEC_ID_AC3 = I1I1lI1II1.a(new byte[]{118, 59, 35, 38, 81});
    private static final String CODEC_ID_E_AC3 = I1I1lI1II1.a(new byte[]{118, 59, 39, 36, 33, 6});
    private static final String CODEC_ID_TRUEHD = I1I1lI1II1.a(new byte[]{118, 59, 54, 55, 55, 112, Byte.MAX_VALUE, 116});
    private static final String CODEC_ID_DTS = I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49});
    private static final String CODEC_ID_DTS_EXPRESS = I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49, 26, 114, 104, 105, 54, 117, 99, 102});
    private static final String CODEC_ID_DTS_LOSSLESS = I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49, 26, 123, Byte.MAX_VALUE, 106, 55, 124, 117, 102, 106});
    private static final String CODEC_ID_FLAC = I1I1lI1II1.a(new byte[]{118, 59, 36, 41, 35, 118});
    private static final String CODEC_ID_ACM = I1I1lI1II1.a(new byte[]{118, 59, 47, 54, 77, 116, 116, 125});
    private static final String CODEC_ID_PCM_INT_LIT = I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 126, 126, 109, 75, 124, 121, 97});
    private static final String CODEC_ID_PCM_INT_BIG = I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 126, 126, 109, 75, 114, 121, 114});
    private static final String CODEC_ID_PCM_FLOAT = I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 113, 124, 118, 37, 100, 31, 124, 124, 113, 112});
    private static final String CODEC_ID_SUBRIP = I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 101, 109, 34, 8});
    private static final String CODEC_ID_ASS = I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 113, 106, 55});
    private static final String CODEC_ID_VTT = I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 103, 124, 38, 102, 100, 97});
    private static final String CODEC_ID_VOBSUB = I1I1lI1II1.a(new byte[]{100, 59, 52, 42, 32, 102, 98, 114});
    private static final String CODEC_ID_PGS = I1I1lI1II1.a(new byte[]{100, 59, 42, 33, 47, 99, 24, 96, 126, 55});
    private static final String CODEC_ID_DVBSUB = I1I1lI1II1.a(new byte[]{100, 59, 38, 51, 32, 102, 98, 114});
    private static final String SUBRIP_TIMECODE_FORMAT = I1I1lI1II1.a(new byte[]{18, 84, 80, 1, 88, 16, 7, 2, 93, 94, 21, 0, 7, 93, 24, 16, 82, 82, 6});
    private static final String SSA_TIMECODE_FORMAT = I1I1lI1II1.a(new byte[]{18, 84, 83, 1, 88, 16, 7, 2, 93, 94, 21, 0, 7, 93, 14, 16, 82, 83, 6});
    private static final String VTT_TIMECODE_FORMAT = I1I1lI1II1.a(new byte[]{18, 84, 80, 1, 88, 16, 7, 2, 93, 94, 21, 0, 7, 93, 26, 16, 82, 82, 6});
    public static final ll1ll1IlIl FACTORY = new ll1ll1IlIl() { // from class: I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.I11ll1lIl1$$ExternalSyntheticLambda0
        @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.ll1ll1IlIl
        public final llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I[] createExtractors() {
            return I11ll1lIl1.lambda$static$0();
        }
    };
    private static final byte[] SUBRIP_PREFIX = {49, 10, 48, 48, 58, 48, 48, 58, 48, 48, 44, 48, 48, 48, 32, 45, 45, 62, 32, 48, 48, 58, 48, 48, 58, 48, 48, 44, 48, 48, 48, 10};
    private static final byte[] SSA_DIALOGUE_FORMAT = llIIlI1llI.getUtf8Bytes(I1I1lI1II1.a(new byte[]{113, 11, 16, 8, 3, 65, 13, 16, 106, 16, 81, 66, 65, 21, 20, 112, 12, 5, 78, 18, 98, 4, 82, 81, 124, 68, 83, 0, 22, 30, 23, 47, 86, 29, 7, 23, 78, 21, 100, 68, 64, 8, 85, 28, 21, 119, 85, 88, 7, 77, 66, Byte.MAX_VALUE, 81, 19, 84, 92, 93, 122, 27, 69, 41, 83, 69, 4, 94, 10, 48, 73, 66, 120, 86, 66, 94, 13, 94, 102, 25, 25, 113, 83, 4, 4, 1, 70, 28, 65, 103, 80, 75, 66}));
    private static final byte[] SSA_PREFIX = {68, 105, 97, 108, 111, 103, 117, 101, 58, 32, 48, 58, 48, 48, 58, 48, 48, 58, 48, 48, 44, 48, 58, 48, 48, 58, 48, 48, 58, 48, 48, 44};
    private static final byte[] VTT_PREFIX = {87, 69, 66, 86, 84, 84, 10, 10, 48, 48, 58, 48, 48, 58, 48, 48, 46, 48, 48, 48, 32, 45, 45, 62, 32, 48, 48, 58, 48, 48, 58, 48, 48, 46, 48, 48, 48, 10};
    private static final UUID WAVE_SUBFORMAT_PCM = new UUID(72057594037932032L, -9223371306706625679L);

    @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I
    public final void release() {
    }

    static /* synthetic */ Map access$600() {
        Map<String, Integer> map = TRACK_NAME_TO_ROTATION_DEGREES;
        if (Il1lII1l1l.I1lllI1llI(177661936L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{7, 9, 90, 92, 18, 109, 124, 91, 114, 52, 9, 8, 111, 65}));
        }
        return map;
    }

    static {
        HashMap map = new HashMap();
        map.put(I1I1lI1II1.a(new byte[]{95, 16, 1, 58, 20, 92, 83, 85, 86, 59, 66, 95, 65, 120, 25, 5, 82, 81}), 0);
        map.put(I1I1lI1II1.a(new byte[]{95, 16, 1, 58, 20, 92, 83, 85, 86, 59, 66, 95, 65, 120, 25, 5, 91, 81}), 90);
        map.put(I1I1lI1II1.a(new byte[]{95, 16, 1, 58, 20, 92, 83, 85, 86, 59, 66, 95, 65, 120, 25, 4, 90, 81}), 180);
        map.put(I1I1lI1II1.a(new byte[]{95, 16, 1, 58, 20, 92, 83, 85, 86, 59, 66, 95, 65, 120, 25, 7, 85, 81}), 270);
        TRACK_NAME_TO_ROTATION_DEGREES = Collections.unmodifiableMap(map);
    }

    static /* synthetic */ llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I[] lambda$static$0() {
        return new llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I[]{new I11ll1lIl1()};
    }

    public I11ll1lIl1() {
        this(0);
    }

    public I11ll1lIl1(int i) {
        this(new IIII1IIl1I(), i);
    }

    I11ll1lIl1(Il1ll1IIll il1ll1IIll, int i) {
        this.segmentContentPosition = -1L;
        this.timecodeScale = -9223372036854775807L;
        this.durationTimecode = -9223372036854775807L;
        this.durationUs = -9223372036854775807L;
        this.cuesContentPosition = -1L;
        this.seekPositionAfterBuildingCues = -1L;
        this.clusterTimecodeUs = -9223372036854775807L;
        this.reader = il1ll1IIll;
        il1ll1IIll.init(new l1I1l1Il1I(this));
        this.seekForCuesEnabled = (i & 1) == 0;
        this.varintReader = new I1l1IIIl1I();
        this.tracks = new SparseArray<>();
        this.scratch = new III1llIlIl(4);
        this.vorbisNumPageSamples = new III1llIlIl(ByteBuffer.allocate(4).putInt(-1).array());
        this.seekEntryIdBytes = new III1llIlIl(4);
        this.nalStartCode = new III1llIlIl(IIlII1l1Il.NAL_START_CODE);
        this.nalLength = new III1llIlIl(4);
        this.sampleStrippedBytes = new III1llIlIl();
        this.subtitleSample = new III1llIlIl();
        this.encryptionInitializationVector = new III1llIlIl(8);
        this.encryptionSubsampleData = new III1llIlIl();
        this.supplementalData = new III1llIlIl();
        this.blockSampleSizes = new int[1];
    }

    @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I
    public final boolean sniff(l1lllIll1I l1lllill1i) throws IOException, KeyStoreException {
        boolean zSniff = new Il111l1I1I().sniff(l1lllill1i);
        if (I1111IIl11.llll111lI1(I1I1lI1II1.a(new byte[]{82, 30, 37, 61, 85, 118, 67, 104, 123, 22}), 7212)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{7, 82, 23, 45, 19, 87, 67, 123, 122, 16, 7, 102, 99, 9, 112, 99}));
        }
        return zSniff;
    }

    @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I
    public final void init(ll1III1lIl ll1iii1lil) {
        this.extractorOutput = ll1iii1lil;
    }

    @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I
    public void seek(long j, long j2) throws BrokenBarrierException, IOException {
        if (I111I11Ill.IlIllIll1I(365563646L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{94, 20, 42, 1, 54, 65, 0, 115, 117, 12, 73, 67, 121, 13, 119, 66, 5, 84, 55, 107, 5, 48, 94, 103, 123, 15, 69, 43, 92, Byte.MAX_VALUE, 124}));
        }
        this.clusterTimecodeUs = -9223372036854775807L;
        this.blockState = 0;
        this.reader.reset();
        this.varintReader.reset();
        resetWriteSampleData();
        for (int i = 0; i < this.tracks.size(); i++) {
            this.tracks.valueAt(i).reset();
        }
        if (I11II1l1lI.ll1I1lII11(I1I1lI1II1.a(new byte[]{92, 28, 50, 81, 16, 120, 118, 93, 65, 47, 67, 90, 70, 79, 65, 6, 12, 47, 13, 87, 124, 23, 112, 13, 67, 82, 109, 43, 81}), 394720037L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{81, 49, 33, 43, 80, 111}));
        }
    }

    @Override // llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIIl1I
    public final int read(l1lllIll1I l1lllill1i, lII1lIII1I lii1liii1i) throws NoSuchFieldException, UnrecoverableKeyException, IOException {
        if (IllllI11Il.Ill1lIIlIl(4548)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{92, 87, 48, 80, 90, 94, 101, 117, 76, 51}));
        }
        this.haveOutputSample = false;
        boolean z = true;
        while (z && !this.haveOutputSample) {
            z = this.reader.read(l1lllill1i);
            if (z && maybeSeekForCues(lii1liii1i, l1lllill1i.getPosition())) {
                return 1;
            }
        }
        if (z) {
            return 0;
        }
        for (int i = 0; i < this.tracks.size(); i++) {
            I1l11I1I1I i1l11I1I1IValueAt = this.tracks.valueAt(i);
            i1l11I1I1IValueAt.assertOutputInitialized();
            i1l11I1I1IValueAt.outputPendingSampleMetadata();
        }
        if (l1lI1I1l11.IlII1Illll(177626012L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{1, 50, 59, 43, 52, 69, 121, 9, 126, 28, 95, 85, 68, 112, 90, 91, 21, 27, 86, 91, 103, 8, 10, 7, 64, 97, 92, 7, 45, 3, 103}));
        }
        return -1;
    }

    protected int getElementType(int i) {
        switch (i) {
            case 131:
            case 136:
            case ID_BLOCK_DURATION /* 155 */:
            case ID_CHANNELS /* 159 */:
            case ID_PIXEL_WIDTH /* 176 */:
            case ID_CUE_TIME /* 179 */:
            case ID_PIXEL_HEIGHT /* 186 */:
            case 215:
            case ID_TIME_CODE /* 231 */:
            case ID_BLOCK_ADD_ID /* 238 */:
            case ID_CUE_CLUSTER_POSITION /* 241 */:
            case ID_REFERENCE_BLOCK /* 251 */:
            case ID_BLOCK_ADD_ID_TYPE /* 16871 */:
            case ID_CONTENT_COMPRESSION_ALGORITHM /* 16980 */:
            case ID_DOC_TYPE_READ_VERSION /* 17029 */:
            case ID_EBML_READ_VERSION /* 17143 */:
            case ID_CONTENT_ENCRYPTION_ALGORITHM /* 18401 */:
            case ID_CONTENT_ENCRYPTION_AES_SETTINGS_CIPHER_MODE /* 18408 */:
            case ID_CONTENT_ENCODING_ORDER /* 20529 */:
            case ID_CONTENT_ENCODING_SCOPE /* 20530 */:
            case ID_SEEK_POSITION /* 21420 */:
            case ID_STEREO_MODE /* 21432 */:
            case ID_DISPLAY_WIDTH /* 21680 */:
            case ID_DISPLAY_UNIT /* 21682 */:
            case ID_DISPLAY_HEIGHT /* 21690 */:
            case ID_FLAG_FORCED /* 21930 */:
            case ID_COLOUR_RANGE /* 21945 */:
            case ID_COLOUR_TRANSFER /* 21946 */:
            case ID_COLOUR_PRIMARIES /* 21947 */:
            case ID_MAX_CLL /* 21948 */:
            case ID_MAX_FALL /* 21949 */:
            case ID_MAX_BLOCK_ADDITION_ID /* 21998 */:
            case ID_CODEC_DELAY /* 22186 */:
            case ID_SEEK_PRE_ROLL /* 22203 */:
            case ID_AUDIO_BIT_DEPTH /* 25188 */:
            case ID_DISCARD_PADDING /* 30114 */:
            case ID_PROJECTION_TYPE /* 30321 */:
            case ID_DEFAULT_DURATION /* 2352003 */:
            case ID_TIMECODE_SCALE /* 2807729 */:
                if (IIlI1ll1ll.l1l1Il1I11(I1I1lI1II1.a(new byte[]{112, 80, 49, 50, 51, 122, 81, 69, 15, 11, 5, 104, 116, 9, 109, 122, 90}), 236587515L)) {
                    throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{69, 14, 26, 1, 40}));
                }
                return 2;
            case 134:
            case 17026:
            case ID_NAME /* 21358 */:
            case ID_LANGUAGE /* 2274716 */:
                if (!lIIlI111II.I1111l111I(8727)) {
                    return 3;
                }
                Log.d(I1I1lI1II1.a(new byte[]{126, 33, 0, 46, 19, Byte.MAX_VALUE, 6, 120, 119, 19}), I1I1lI1II1.a(new byte[]{102, 0, 58, 36, 55, 112, 14, 121, 13, 83, 66, 66, 2, 82, 83, 115, 10, 83, 56}));
                return 0;
            case ID_BLOCK_GROUP /* 160 */:
            case ID_BLOCK_MORE /* 166 */:
            case ID_TRACK_ENTRY /* 174 */:
            case ID_CUE_TRACK_POSITIONS /* 183 */:
            case ID_CUE_POINT /* 187 */:
            case 224:
            case ID_AUDIO /* 225 */:
            case ID_BLOCK_ADDITION_MAPPING /* 16868 */:
            case ID_CONTENT_ENCRYPTION_AES_SETTINGS /* 18407 */:
            case ID_SEEK /* 19899 */:
            case ID_CONTENT_COMPRESSION /* 20532 */:
            case ID_CONTENT_ENCRYPTION /* 20533 */:
            case ID_COLOUR /* 21936 */:
            case ID_MASTERING_METADATA /* 21968 */:
            case ID_CONTENT_ENCODING /* 25152 */:
            case ID_CONTENT_ENCODINGS /* 28032 */:
            case ID_BLOCK_ADDITIONS /* 30113 */:
            case ID_PROJECTION /* 30320 */:
            case ID_SEEK_HEAD /* 290298740 */:
            case 357149030:
            case ID_TRACKS /* 374648427 */:
            case ID_SEGMENT /* 408125543 */:
            case ID_EBML /* 440786851 */:
            case ID_CUES /* 475249515 */:
            case ID_CLUSTER /* 524531317 */:
                return 1;
            case ID_BLOCK /* 161 */:
            case ID_SIMPLE_BLOCK /* 163 */:
            case ID_BLOCK_ADDITIONAL /* 165 */:
            case ID_BLOCK_ADD_ID_EXTRA_DATA /* 16877 */:
            case ID_CONTENT_COMPRESSION_SETTINGS /* 16981 */:
            case ID_CONTENT_ENCRYPTION_KEY_ID /* 18402 */:
            case ID_SEEK_ID /* 21419 */:
            case ID_CODEC_PRIVATE /* 25506 */:
            case ID_PROJECTION_PRIVATE /* 30322 */:
                return 4;
            case ID_SAMPLING_FREQUENCY /* 181 */:
            case ID_DURATION /* 17545 */:
            case ID_PRIMARY_R_CHROMATICITY_X /* 21969 */:
            case ID_PRIMARY_R_CHROMATICITY_Y /* 21970 */:
            case ID_PRIMARY_G_CHROMATICITY_X /* 21971 */:
            case ID_PRIMARY_G_CHROMATICITY_Y /* 21972 */:
            case ID_PRIMARY_B_CHROMATICITY_X /* 21973 */:
            case ID_PRIMARY_B_CHROMATICITY_Y /* 21974 */:
            case ID_WHITE_POINT_CHROMATICITY_X /* 21975 */:
            case ID_WHITE_POINT_CHROMATICITY_Y /* 21976 */:
            case ID_LUMNINANCE_MAX /* 21977 */:
            case ID_LUMNINANCE_MIN /* 21978 */:
            case ID_PROJECTION_POSE_YAW /* 30323 */:
            case ID_PROJECTION_POSE_PITCH /* 30324 */:
            case ID_PROJECTION_POSE_ROLL /* 30325 */:
                if (I11II1l1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{85, 92, 53, 19, 23, 0, 77}), I1I1lI1II1.a(new byte[]{120, 12, 85, 93, 84, 120, 101, 73, 94, 48, 9, 88, 108, 112, 86, 77, 54, 25, 55, 124, 88, 14, 87, 91, 102}))) {
                    throw new InterruptedException(I1I1lI1II1.a(new byte[]{96, 83, 80, 80, 42, 125, 71, Byte.MAX_VALUE, 86, 34, 102, 82, 108, Byte.MAX_VALUE, 122, 93, 23}));
                }
                return 5;
            default:
                return 0;
        }
    }

    protected boolean isLevel1Element(int i) {
        boolean z = i == 357149030 || i == ID_CLUSTER || i == ID_CUES || i == ID_TRACKS;
        if (IIlI1ll1ll.IIll1I11lI(I1I1lI1II1.a(new byte[]{100}))) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{113, 62, 41, 28, 26, 7}));
        }
        return z;
    }

    protected void startMasterElement(int i, long j, long j2) throws l1llI1llII {
        if (I1lllI11II.IlIllIll1I(189504787L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{123, 55, 14, 19, 56, 79, 120, 65, 12, 15, 122, 74, 88, 113, 124, 76, 24, 82, 37, 66, Byte.MAX_VALUE, 82, 123, 65, 96}));
        }
        assertInitialized();
        if (i == ID_BLOCK_GROUP) {
            this.blockHasReferenceBlock = false;
            this.blockGroupDiscardPaddingNs = 0L;
        } else if (i == ID_TRACK_ENTRY) {
            this.currentTrack = new I1l11I1I1I();
        } else if (i == ID_CUE_POINT) {
            this.seenClusterPositionForCurrentCuePoint = false;
        } else if (i == ID_SEEK) {
            this.seekEntryId = -1;
            this.seekEntryPosition = -1L;
        } else if (i == ID_CONTENT_ENCRYPTION) {
            getCurrentTrack(i).hasContentEncryption = true;
        } else if (i == ID_MASTERING_METADATA) {
            getCurrentTrack(i).hasColorInfo = true;
        } else if (i == ID_SEGMENT) {
            long j3 = this.segmentContentPosition;
            if (j3 != -1 && j3 != j) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{122, 17, 14, 17, 11, 69, 91, 85, 25, 55, 85, 87, 88, 92, 90, 65, 66, 4, 14, 87, 93, 4, 93, 65, 64, 22, 89, 10, 16, 18, 68, 22, 71, 20, 13, 23, 22, 80, 83}), null);
            }
            this.segmentContentPosition = j;
            this.segmentContentSize = j2;
        } else if (i == ID_CUES) {
            this.cueTimesUs = new I1I1I1IIlI();
            this.cueClusterPositions = new I1I1I1IIlI();
        } else if (i == ID_CLUSTER && !this.sentSeekMap) {
            if (!this.seekForCuesEnabled || this.cuesContentPosition == -1) {
                this.extractorOutput.seekMap(new I11ll1lI11(this.durationUs));
                this.sentSeekMap = true;
            } else {
                this.seekForCues = true;
            }
        }
        if (lll1IIII11.IlII1Illll(455391106L)) {
            Log.d(I1I1lI1II1.a(new byte[]{66, 9, 44, 44, 49, 111, 101, 118, 76, 18, 116, 114, 71}), I1I1lI1II1.a(new byte[]{112, 14, 10, 63, 53, 80, 101, 101, 10, 37}));
        }
    }

    protected void endMasterElement(int i) throws l1llI1llII {
        if (IllllI11Il.l1l1Il1I11(I1I1lI1II1.a(new byte[]{109, 32, 9, 41, 4, 88, 69, 92, 1, 6, 88, 71, 113, 97, 12, 66, 86, 25, 54, 11}), 296422960L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{112, 42, 49, 20, 50, 81, 7, 124, 82, 40, 65, 106}));
        }
        assertInitialized();
        if (i == ID_BLOCK_GROUP) {
            if (this.blockState != 2) {
                if (l1lll111II.l11I11I11l(4321)) {
                    throw new ArithmeticException(I1I1lI1II1.a(new byte[]{5, 3, 9, 92, 9, 0, 5, 66, 79, 83, 89, 119, 98, 126, 80, 66, 81, 5, 39, 81, 6, 25, 70, 121, 66, 102, 15}));
                }
                return;
            }
            I1l11I1I1I i1l11I1I1I = this.tracks.get(this.blockTrackNumber);
            i1l11I1I1I.assertOutputInitialized();
            if (this.blockGroupDiscardPaddingNs > 0 && CODEC_ID_OPUS.equals(i1l11I1I1I.codecId)) {
                this.supplementalData.reset(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(this.blockGroupDiscardPaddingNs).array());
            }
            int i2 = 0;
            for (int i3 = 0; i3 < this.blockSampleCount; i3++) {
                i2 += this.blockSampleSizes[i3];
            }
            int i4 = 0;
            while (i4 < this.blockSampleCount) {
                long j = this.blockTimeUs + ((i1l11I1I1I.defaultSampleDurationNs * i4) / 1000);
                int i5 = this.blockFlags;
                if (i4 == 0 && !this.blockHasReferenceBlock) {
                    i5 |= 1;
                }
                int i6 = this.blockSampleSizes[i4];
                int i7 = i2 - i6;
                commitSampleToOutput(i1l11I1I1I, j, i5, i6, i7);
                i4++;
                i2 = i7;
            }
            this.blockState = 0;
        } else if (i == ID_TRACK_ENTRY) {
            I1l11I1I1I i1l11I1I1I2 = (I1l11I1I1I) ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkStateNotNull(this.currentTrack);
            if (i1l11I1I1I2.codecId == null) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 6, 0, 1, 124, 83, 16, 80, 23, 16, 93, 92, 74, 71, 92, 12, 6, 66, 91, 94, 65, 103, 71, 82, 85, 92, 32, 10, 70, 69, 26, 23, 1, 14, 0, 15, 80, 89, 68}), null);
            }
            if (isCodecSupported(i1l11I1I1I2.codecId)) {
                i1l11I1I1I2.initializeOutput(this.extractorOutput, i1l11I1I1I2.number);
                this.tracks.put(i1l11I1I1I2.number, i1l11I1I1I2);
            }
            this.currentTrack = null;
        } else {
            if (i == ID_SEEK) {
                int i8 = this.seekEntryId;
                if (i8 != -1) {
                    long j2 = this.seekEntryPosition;
                    if (j2 != -1) {
                        if (i8 == ID_CUES) {
                            this.cuesContentPosition = j2;
                        }
                    }
                }
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{122, 5, 12, 1, 3, 65, 88, 66, 64, 68, 85, 92, 80, 84, 81, 91, 22, 65, 49, 87, 85, 10, 122, 113, 19, 89, 69, 69, 55, 87, 82, 8, 103, 11, 17, 12, 22, 92, 88, 94, 25, 10, 95, 68, 21, 95, 91, 64, 12, 5}), null);
            }
            if (i == ID_CONTENT_ENCODING) {
                assertInTrackEntry(i);
                if (this.currentTrack.hasContentEncryption) {
                    if (this.currentTrack.cryptoData == null) {
                        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 10, 1, 23, 27, 69, 67, 85, 93, 68, 100, 66, 84, 90, 95, 21, 4, 14, 23, 92, 84, 65, 81, 64, 71, 22, 116, 10, 10, 70, 82, 13, 67, 33, 12, 6, 41, 80, 78, 121, 125, 68, 71, 81, 70, 25, 90, 90, 22, 65, 4, 93, 69, 15, 87}), null);
                    }
                    this.currentTrack.drmInitData = new DrmInitData(new DrmInitData$SchemeData(lIlI1IIII1.UUID_NIL, I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 64, 85, 91, 9}), this.currentTrack.cryptoData.encryptionKey));
                }
            } else if (i == ID_CONTENT_ENCODINGS) {
                assertInTrackEntry(i);
                if (this.currentTrack.hasContentEncryption && this.currentTrack.sampleStrippedBytes != null) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 15, 7, 11, 91, 94, 94, 94, 68, 85, 94, 86, 75, 77, 69, 22, 8, 13, 92, 16, 0, 93, 81, 19, 85, 88, 8, 20, 64, 82, 16, 68, 13, 13, 11, 66, 92, 68, 16, 87, 11, 68, 16, 70, 76, 68, 69, 13, 19, 22, 87, 84}), null);
                }
            } else if (i == 357149030) {
                if (this.timecodeScale == -9223372036854775807L) {
                    this.timecodeScale = 1000000L;
                }
                long j3 = this.durationTimecode;
                if (j3 != -9223372036854775807L) {
                    this.durationUs = scaleTimecodeToUs(j3);
                }
            } else if (i == ID_TRACKS) {
                if (this.tracks.size() == 0) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{121, 11, 66, 19, 3, 89, 94, 84, 25, 16, 66, 81, 86, 82, 71, 21, 21, 4, 16, 87, 16, 7, 92, 64, 93, 82}), null);
                }
                this.extractorOutput.endTracks();
            } else if (i == ID_CUES) {
                if (!this.sentSeekMap) {
                    this.extractorOutput.seekMap(buildSeekMap(this.cueTimesUs, this.cueClusterPositions));
                    this.sentSeekMap = true;
                }
                this.cueTimesUs = null;
                this.cueClusterPositions = null;
            }
        }
        if (lll1IIII11.I1lllI1llI(926)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{123, 23, 80, 32, 81, 120, 116, 67, 83, 34, 106, 8, 123, 126, 94, 68}));
        }
    }

    protected void integerElement(int i, long j) throws l1llI1llII {
        if (i == ID_CONTENT_ENCODING_ORDER) {
            if (j != 0) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 117, 87, 7, 95, 84, 92, 87, 83, 122, 16, 5, 7, 64, 16}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
            }
            return;
        }
        if (i == ID_CONTENT_ENCODING_SCOPE) {
            if (j != 1) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 117, 87, 7, 95, 84, 92, 87, 83, 102, 1, 14, 18, 87, 16}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
            }
            return;
        }
        switch (i) {
            case 131:
                getCurrentTrack(i).type = (int) j;
                return;
            case 136:
                getCurrentTrack(i).flagDefault = j == 1;
                return;
            case ID_BLOCK_DURATION /* 155 */:
                this.blockDurationUs = scaleTimecodeToUs(j);
                return;
            case ID_CHANNELS /* 159 */:
                getCurrentTrack(i).channelCount = (int) j;
                return;
            case ID_PIXEL_WIDTH /* 176 */:
                getCurrentTrack(i).width = (int) j;
                return;
            case ID_CUE_TIME /* 179 */:
                assertInCues(i);
                this.cueTimesUs.add(scaleTimecodeToUs(j));
                return;
            case ID_PIXEL_HEIGHT /* 186 */:
                getCurrentTrack(i).height = (int) j;
                return;
            case 215:
                getCurrentTrack(i).number = (int) j;
                return;
            case ID_TIME_CODE /* 231 */:
                this.clusterTimecodeUs = scaleTimecodeToUs(j);
                return;
            case ID_BLOCK_ADD_ID /* 238 */:
                this.blockAdditionalId = (int) j;
                return;
            case ID_CUE_CLUSTER_POSITION /* 241 */:
                if (this.seenClusterPositionForCurrentCuePoint) {
                    return;
                }
                assertInCues(i);
                this.cueClusterPositions.add(j);
                this.seenClusterPositionForCurrentCuePoint = true;
                return;
            case ID_REFERENCE_BLOCK /* 251 */:
                this.blockHasReferenceBlock = true;
                return;
            case ID_BLOCK_ADD_ID_TYPE /* 16871 */:
                I1l11I1I1I.access$202(getCurrentTrack(i), (int) j);
                return;
            case ID_CONTENT_COMPRESSION_ALGORITHM /* 16980 */:
                if (j != 3) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 115, 86, 9, 64, 113, 89, 94, 91, 21}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
                }
                return;
            case ID_DOC_TYPE_READ_VERSION /* 17029 */:
                if (j < 1 || j > 2) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{115, 11, 1, 49, 27, 69, 82, 98, 92, 5, 84, 102, 80, 75, 71, 92, 13, 15, 66}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
                }
                return;
            case ID_EBML_READ_VERSION /* 17143 */:
                if (j != 1) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 38, 47, 41, 48, 80, 86, 84, 111, 1, 66, 67, 92, 86, 90, 21}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
                }
                return;
            case ID_CONTENT_ENCRYPTION_ALGORITHM /* 18401 */:
                if (j != 5) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 117, 87, 7, 113, 92, 82, 86, 20}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
                }
                return;
            case ID_CONTENT_ENCRYPTION_AES_SETTINGS_CIPHER_MODE /* 18408 */:
                if (j != 1) {
                    throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{118, 33, 49, 54, 7, 65, 67, 89, 87, 3, 67, 115, 92, 73, 92, 80, 16, 44, 13, 86, 85, 65}) + j + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
                }
                return;
            case ID_SEEK_POSITION /* 21420 */:
                this.seekEntryPosition = j + this.segmentContentPosition;
                return;
            case ID_STEREO_MODE /* 21432 */:
                int i2 = (int) j;
                assertInTrackEntry(i);
                if (i2 == 0) {
                    this.currentTrack.stereoMode = 0;
                    return;
                }
                if (i2 == 1) {
                    this.currentTrack.stereoMode = 2;
                    return;
                } else if (i2 == 3) {
                    this.currentTrack.stereoMode = 1;
                    return;
                } else {
                    if (i2 != 15) {
                        return;
                    }
                    this.currentTrack.stereoMode = 3;
                    return;
                }
            case ID_DISPLAY_WIDTH /* 21680 */:
                getCurrentTrack(i).displayWidth = (int) j;
                return;
            case ID_DISPLAY_UNIT /* 21682 */:
                getCurrentTrack(i).displayUnit = (int) j;
                return;
            case ID_DISPLAY_HEIGHT /* 21690 */:
                getCurrentTrack(i).displayHeight = (int) j;
                return;
            case ID_FLAG_FORCED /* 21930 */:
                getCurrentTrack(i).flagForced = j == 1;
                return;
            case ID_MAX_BLOCK_ADDITION_ID /* 21998 */:
                getCurrentTrack(i).maxBlockAdditionId = (int) j;
                return;
            case ID_CODEC_DELAY /* 22186 */:
                getCurrentTrack(i).codecDelayNs = j;
                return;
            case ID_SEEK_PRE_ROLL /* 22203 */:
                getCurrentTrack(i).seekPreRollNs = j;
                return;
            case ID_AUDIO_BIT_DEPTH /* 25188 */:
                getCurrentTrack(i).audioBitDepth = (int) j;
                return;
            case ID_DISCARD_PADDING /* 30114 */:
                this.blockGroupDiscardPaddingNs = j;
                return;
            case ID_PROJECTION_TYPE /* 30321 */:
                assertInTrackEntry(i);
                int i3 = (int) j;
                if (i3 == 0) {
                    this.currentTrack.projectionType = 0;
                    return;
                }
                if (i3 == 1) {
                    this.currentTrack.projectionType = 1;
                    return;
                } else if (i3 == 2) {
                    this.currentTrack.projectionType = 2;
                    return;
                } else {
                    if (i3 != 3) {
                        return;
                    }
                    this.currentTrack.projectionType = 3;
                    return;
                }
            case ID_DEFAULT_DURATION /* 2352003 */:
                getCurrentTrack(i).defaultSampleDurationNs = (int) j;
                return;
            case ID_TIMECODE_SCALE /* 2807729 */:
                this.timecodeScale = j;
                return;
            default:
                switch (i) {
                    case ID_COLOUR_RANGE /* 21945 */:
                        assertInTrackEntry(i);
                        int i4 = (int) j;
                        if (i4 == 1) {
                            this.currentTrack.colorRange = 2;
                            return;
                        } else {
                            if (i4 != 2) {
                                return;
                            }
                            this.currentTrack.colorRange = 1;
                            return;
                        }
                    case ID_COLOUR_TRANSFER /* 21946 */:
                        assertInTrackEntry(i);
                        int iIsoTransferCharacteristicsToColorTransfer = lII1111ll1.isoTransferCharacteristicsToColorTransfer((int) j);
                        if (iIsoTransferCharacteristicsToColorTransfer != -1) {
                            this.currentTrack.colorTransfer = iIsoTransferCharacteristicsToColorTransfer;
                            return;
                        }
                        return;
                    case ID_COLOUR_PRIMARIES /* 21947 */:
                        assertInTrackEntry(i);
                        this.currentTrack.hasColorInfo = true;
                        int iIsoColorPrimariesToColorSpace = lII1111ll1.isoColorPrimariesToColorSpace((int) j);
                        if (iIsoColorPrimariesToColorSpace != -1) {
                            this.currentTrack.colorSpace = iIsoColorPrimariesToColorSpace;
                            return;
                        }
                        return;
                    case ID_MAX_CLL /* 21948 */:
                        getCurrentTrack(i).maxContentLuminance = (int) j;
                        return;
                    case ID_MAX_FALL /* 21949 */:
                        getCurrentTrack(i).maxFrameAverageLuminance = (int) j;
                        return;
                    default:
                        return;
                }
        }
    }

    protected void floatElement(int i, double d) throws l1llI1llII {
        if (I111lIl11I.I1lI11IIll(I1I1lI1II1.a(new byte[]{88, 93, 91, 87, 41, 13}), 478980067L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{120, 19, 3, 8, 5, 123, 95, 3, 106, 19, 0, 3, 97, 105, 91, 67, 6}));
        }
        if (i == ID_SAMPLING_FREQUENCY) {
            getCurrentTrack(i).sampleRate = (int) d;
            return;
        }
        if (i == ID_DURATION) {
            this.durationTimecode = (long) d;
            return;
        }
        switch (i) {
            case ID_PRIMARY_R_CHROMATICITY_X /* 21969 */:
                getCurrentTrack(i).primaryRChromaticityX = (float) d;
                return;
            case ID_PRIMARY_R_CHROMATICITY_Y /* 21970 */:
                getCurrentTrack(i).primaryRChromaticityY = (float) d;
                return;
            case ID_PRIMARY_G_CHROMATICITY_X /* 21971 */:
                getCurrentTrack(i).primaryGChromaticityX = (float) d;
                return;
            case ID_PRIMARY_G_CHROMATICITY_Y /* 21972 */:
                getCurrentTrack(i).primaryGChromaticityY = (float) d;
                return;
            case ID_PRIMARY_B_CHROMATICITY_X /* 21973 */:
                getCurrentTrack(i).primaryBChromaticityX = (float) d;
                return;
            case ID_PRIMARY_B_CHROMATICITY_Y /* 21974 */:
                getCurrentTrack(i).primaryBChromaticityY = (float) d;
                return;
            case ID_WHITE_POINT_CHROMATICITY_X /* 21975 */:
                getCurrentTrack(i).whitePointChromaticityX = (float) d;
                return;
            case ID_WHITE_POINT_CHROMATICITY_Y /* 21976 */:
                getCurrentTrack(i).whitePointChromaticityY = (float) d;
                return;
            case ID_LUMNINANCE_MAX /* 21977 */:
                getCurrentTrack(i).maxMasteringLuminance = (float) d;
                return;
            case ID_LUMNINANCE_MIN /* 21978 */:
                getCurrentTrack(i).minMasteringLuminance = (float) d;
                return;
            default:
                switch (i) {
                    case ID_PROJECTION_POSE_YAW /* 30323 */:
                        getCurrentTrack(i).projectionPoseYaw = (float) d;
                        return;
                    case ID_PROJECTION_POSE_PITCH /* 30324 */:
                        getCurrentTrack(i).projectionPosePitch = (float) d;
                        return;
                    case ID_PROJECTION_POSE_ROLL /* 30325 */:
                        getCurrentTrack(i).projectionPoseRoll = (float) d;
                        return;
                    default:
                        return;
                }
        }
    }

    protected void stringElement(int i, String str) throws l1llI1llII {
        if (Il1llIl111.I111IlIl1I(2849)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{14}));
        }
        if (i == 134) {
            getCurrentTrack(i).codecId = str;
            return;
        }
        if (i == 17026) {
            if (!DOC_TYPE_WEBM.equals(str) && !DOC_TYPE_MATROSKA.equals(str)) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{115, 11, 1, 49, 27, 69, 82, 16}) + str + I1I1lI1II1.a(new byte[]{23, 10, 13, 17, 66, 70, 66, 64, 73, 11, 66, 68, 80, 93}), null);
            }
        } else if (i == ID_NAME) {
            getCurrentTrack(i).name = str;
        } else {
            if (i != ID_LANGUAGE) {
                return;
            }
            getCurrentTrack(i).language = str;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:85:0x0257, code lost:
    
        throw androidx.media3.common.l1llI1llII.createForMalformedContainer(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{114, 38, 47, 41, 66, 89, 86, 83, 80, 10, 87, 16, 70, 88, 89, 69, 14, 4, 66, 65, 89, 27, 86, 21, 92, 67, 67, 69, 11, 84, 23, 17, 86, 10, 5, 0, 76}), null);
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    protected void binaryElement(int i, int i2, l1lllIll1I l1lllill1i) throws IOException {
        I1l11I1I1I i1l11I1I1I;
        int i3;
        I1l11I1I1I i1l11I1I1I2;
        I1l11I1I1I i1l11I1I1I3;
        long j;
        int i4;
        int i5;
        int i6;
        int i7 = 0;
        int i8 = 1;
        if (i != ID_BLOCK && i != ID_SIMPLE_BLOCK) {
            if (i == ID_BLOCK_ADDITIONAL) {
                if (this.blockState != 2) {
                    return;
                }
                handleBlockAdditionalData(this.tracks.get(this.blockTrackNumber), this.blockAdditionalId, l1lllill1i, i2);
                return;
            }
            if (i == ID_BLOCK_ADD_ID_EXTRA_DATA) {
                handleBlockAddIDExtraData(getCurrentTrack(i), l1lllill1i, i2);
                return;
            }
            if (i == ID_CONTENT_COMPRESSION_SETTINGS) {
                assertInTrackEntry(i);
                this.currentTrack.sampleStrippedBytes = new byte[i2];
                l1lllill1i.readFully(this.currentTrack.sampleStrippedBytes, 0, i2);
                return;
            }
            if (i == ID_CONTENT_ENCRYPTION_KEY_ID) {
                byte[] bArr = new byte[i2];
                l1lllill1i.readFully(bArr, 0, i2);
                getCurrentTrack(i).cryptoData = new lIl11lllI1(1, bArr, 0, 0);
                return;
            }
            if (i == ID_SEEK_ID) {
                Arrays.fill(this.seekEntryIdBytes.getData(), (byte) 0);
                l1lllill1i.readFully(this.seekEntryIdBytes.getData(), 4 - i2, i2);
                this.seekEntryIdBytes.setPosition(0);
                this.seekEntryId = (int) this.seekEntryIdBytes.readUnsignedInt();
                return;
            }
            if (i == ID_CODEC_PRIVATE) {
                assertInTrackEntry(i);
                this.currentTrack.codecPrivate = new byte[i2];
                l1lllill1i.readFully(this.currentTrack.codecPrivate, 0, i2);
                return;
            } else {
                if (i == ID_PROJECTION_PRIVATE) {
                    assertInTrackEntry(i);
                    this.currentTrack.projectionData = new byte[i2];
                    l1lllill1i.readFully(this.currentTrack.projectionData, 0, i2);
                    return;
                }
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 89, 81, 3, 20}) + i, null);
            }
        }
        if (this.blockState == 0) {
            this.blockTrackNumber = (int) this.varintReader.readUnsignedVarint(l1lllill1i, false, true, 8);
            this.blockTrackNumberLength = this.varintReader.getLastLength();
            this.blockDurationUs = -9223372036854775807L;
            this.blockState = 1;
            this.scratch.reset(0);
        }
        I1l11I1I1I i1l11I1I1I4 = this.tracks.get(this.blockTrackNumber);
        if (i1l11I1I1I4 == null) {
            l1lllill1i.skipFully(i2 - this.blockTrackNumberLength);
            this.blockState = 0;
            return;
        }
        i1l11I1I1I4.assertOutputInitialized();
        if (this.blockState == 1) {
            readScratch(l1lllill1i, 3);
            int i9 = (this.scratch.getData()[2] & 6) >> 1;
            byte b = 255;
            if (i9 == 0) {
                this.blockSampleCount = 1;
                int[] iArrEnsureArrayCapacity = ensureArrayCapacity(this.blockSampleSizes, 1);
                this.blockSampleSizes = iArrEnsureArrayCapacity;
                iArrEnsureArrayCapacity[0] = (i2 - this.blockTrackNumberLength) - 3;
            } else {
                int i10 = 4;
                readScratch(l1lllill1i, 4);
                int i11 = (this.scratch.getData()[3] & 255) + 1;
                this.blockSampleCount = i11;
                int[] iArrEnsureArrayCapacity2 = ensureArrayCapacity(this.blockSampleSizes, i11);
                this.blockSampleSizes = iArrEnsureArrayCapacity2;
                if (i9 == 2) {
                    int i12 = (i2 - this.blockTrackNumberLength) - 4;
                    int i13 = this.blockSampleCount;
                    Arrays.fill(iArrEnsureArrayCapacity2, 0, i13, i12 / i13);
                } else if (i9 == 1) {
                    int i14 = 0;
                    int i15 = 0;
                    while (true) {
                        i4 = this.blockSampleCount;
                        if (i14 >= i4 - 1) {
                            break;
                        }
                        this.blockSampleSizes[i14] = 0;
                        do {
                            i10++;
                            readScratch(l1lllill1i, i10);
                            i5 = this.scratch.getData()[i10 - 1] & 255;
                            int[] iArr = this.blockSampleSizes;
                            i6 = iArr[i14] + i5;
                            iArr[i14] = i6;
                        } while (i5 == 255);
                        i15 += i6;
                        i14++;
                    }
                    this.blockSampleSizes[i4 - 1] = ((i2 - this.blockTrackNumberLength) - i10) - i15;
                } else {
                    if (i9 != 3) {
                        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 92, 84, 90, 93, 91, 5, 65, 20, 83, 92, 20, 86, 15, 19}) + i9, null);
                    }
                    int i16 = 0;
                    int i17 = 0;
                    while (true) {
                        int i18 = this.blockSampleCount;
                        if (i16 < i18 - 1) {
                            this.blockSampleSizes[i16] = i7;
                            i10++;
                            readScratch(l1lllill1i, i10);
                            int i19 = i10 - 1;
                            if (this.scratch.getData()[i19] == 0) {
                                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{121, 11, 66, 19, 3, 89, 94, 84, 25, 18, 81, 66, 92, 87, 64, 21, 14, 4, 12, 85, 68, 9, 19, 88, 82, 69, 92, 69, 2, 93, 66, 13, 83}), null);
                            }
                            int i20 = i7;
                            while (true) {
                                if (i20 >= 8) {
                                    i1l11I1I1I3 = i1l11I1I1I4;
                                    j = 0;
                                    break;
                                }
                                int i21 = i8 << (7 - i20);
                                if ((this.scratch.getData()[i19] & i21) != 0) {
                                    int i22 = i10 + i20;
                                    readScratch(l1lllill1i, i22);
                                    i1l11I1I1I3 = i1l11I1I1I4;
                                    j = (~i21) & this.scratch.getData()[i19] & b;
                                    int i23 = i19 + 1;
                                    while (i23 < i22) {
                                        j = (j << 8) | (this.scratch.getData()[i23] & b);
                                        i23++;
                                        i22 = i22;
                                        b = 255;
                                    }
                                    int i24 = i22;
                                    if (i16 > 0) {
                                        j -= (1 << ((i20 * 7) + 6)) - 1;
                                    }
                                    i10 = i24;
                                } else {
                                    i20++;
                                    i8 = 1;
                                    b = 255;
                                }
                            }
                            if (j < -2147483648L || j > 2147483647L) {
                                break;
                            }
                            int i25 = (int) j;
                            int[] iArr2 = this.blockSampleSizes;
                            if (i16 != 0) {
                                i25 += iArr2[i16 - 1];
                            }
                            iArr2[i16] = i25;
                            i17 += i25;
                            i16++;
                            i1l11I1I1I4 = i1l11I1I1I3;
                            i7 = 0;
                            i8 = 1;
                            b = 255;
                        } else {
                            i1l11I1I1I2 = i1l11I1I1I4;
                            this.blockSampleSizes[i18 - 1] = ((i2 - this.blockTrackNumberLength) - i10) - i17;
                            break;
                        }
                    }
                }
            }
            i1l11I1I1I2 = i1l11I1I1I4;
            this.blockTimeUs = this.clusterTimecodeUs + scaleTimecodeToUs((this.scratch.getData()[0] << 8) | (this.scratch.getData()[1] & 255));
            i1l11I1I1I = i1l11I1I1I2;
            this.blockFlags = (i1l11I1I1I.type == 2 || (i == ID_SIMPLE_BLOCK && (this.scratch.getData()[2] & 128) == 128)) ? 1 : 0;
            this.blockState = 2;
            this.blockSampleIndex = 0;
            i3 = ID_SIMPLE_BLOCK;
        } else {
            i1l11I1I1I = i1l11I1I1I4;
            i3 = ID_SIMPLE_BLOCK;
        }
        if (i == i3) {
            while (true) {
                int i26 = this.blockSampleIndex;
                if (i26 < this.blockSampleCount) {
                    commitSampleToOutput(i1l11I1I1I, ((this.blockSampleIndex * i1l11I1I1I.defaultSampleDurationNs) / 1000) + this.blockTimeUs, this.blockFlags, writeSampleData(l1lllill1i, i1l11I1I1I, this.blockSampleSizes[i26], false), 0);
                    this.blockSampleIndex++;
                } else {
                    this.blockState = 0;
                    return;
                }
            }
        } else {
            while (true) {
                int i27 = this.blockSampleIndex;
                if (i27 >= this.blockSampleCount) {
                    return;
                }
                int[] iArr3 = this.blockSampleSizes;
                iArr3[i27] = writeSampleData(l1lllill1i, i1l11I1I1I, iArr3[i27], true);
                this.blockSampleIndex++;
            }
        }
    }

    protected void handleBlockAddIDExtraData(I1l11I1I1I i1l11I1I1I, l1lllIll1I l1lllill1i, int i) throws IOException {
        if (i1l11I1I1I.blockAddIdType == 1685485123 || i1l11I1I1I.blockAddIdType == 1685480259) {
            i1l11I1I1I.dolbyVisionConfigBytes = new byte[i];
            l1lllill1i.readFully(i1l11I1I1I.dolbyVisionConfigBytes, 0, i);
        } else {
            l1lllill1i.skipFully(i);
        }
    }

    protected void handleBlockAdditionalData(I1l11I1I1I i1l11I1I1I, int i, l1lllIll1I l1lllill1i, int i2) throws UnrecoverableKeyException, IOException {
        if (lIIIIII11I.Il1IIlI1II(7109)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{79, 48, 19}));
        }
        if (i != 4 || !CODEC_ID_VP9.equals(i1l11I1I1I.codecId)) {
            l1lllill1i.skipFully(i2);
        } else {
            this.supplementalData.reset(i2);
            l1lllill1i.readFully(this.supplementalData.getData(), 0, i2);
        }
    }

    private void assertInTrackEntry(int i) throws l1llI1llII, NotActiveException {
        if (this.currentTrack == null) {
            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 8, 7, 8, 7, 91, 67, 16}) + i + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 85, 85, 25, 13, 94, 16, 84, 25, 96, 71, 3, 2, 9, 119, 94, 21, 65, 76}), null);
        }
        if (l11Il111ll.I1lllI1llI(3207)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{92, 23, 20, 3, 53, 2, 3, 70, 75, 7, 104, 93, 111, 114, 71, 95, 33, 10, 50, 91, 118, 24, 82, 84, 88}));
        }
    }

    private void assertInCues(int i) throws l1llI1llII {
        if (this.cueTimesUs == null || this.cueClusterPositions == null) {
            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 8, 7, 8, 7, 91, 67, 16}) + i + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 85, 85, 25, 13, 94, 16, 84, 25, 119, 64, 7, 18}), null);
        }
        if (l1l1I111I1.I1lllI1llI(I1I1lI1II1.a(new byte[]{122, 54, 56, 84, 10}))) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{123, 82, 58, 16, 12}));
        }
    }

    protected I1l11I1I1I getCurrentTrack(int i) throws l1llI1llII, NotActiveException {
        assertInTrackEntry(i);
        I1l11I1I1I i1l11I1I1I = this.currentTrack;
        if (!Il1I1lllIl.IIlIl1Illl(I1I1lI1II1.a(new byte[]{71, 38, 49, 22, 20, 71, 84, 8, 10}), I1I1lI1II1.a(new byte[]{116, 15, 86, 46, 8, 114, 14, 102, 92, 37, 83, 86}))) {
            return i1l11I1I1I;
        }
        Log.d(I1I1lI1II1.a(new byte[]{100, 51, 7}), I1I1lI1II1.a(new byte[]{123, 49, 56, 21, 46, 12, 95, 93, 99, 13, 4, 97, 100, 118, 67, 102, 84, 40, 14, 119, 7, 49, 112, 121, 88, 114}));
        return null;
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x00b1  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private void commitSampleToOutput(I1l11I1I1I i1l11I1I1I, long j, int i, int i2, int i3) throws SignatureException, CertificateParsingException, IOException {
        int iLimit;
        if (i1l11I1I1I.trueHdSampleRechunker != null) {
            i1l11I1I1I.trueHdSampleRechunker.sampleMetadata(i1l11I1I1I.output, j, i, i2, i3, i1l11I1I1I.cryptoData);
        } else if (CODEC_ID_SUBRIP.equals(i1l11I1I1I.codecId) || CODEC_ID_ASS.equals(i1l11I1I1I.codecId) || CODEC_ID_VTT.equals(i1l11I1I1I.codecId)) {
            if (this.blockSampleCount > 1) {
                lIl1llIlll.w(TAG, I1I1lI1II1.a(new byte[]{100, 15, 11, 21, 18, 92, 89, 87, 25, 23, 69, 82, 65, 80, 64, 89, 7, 65, 17, 83, 93, 17, 95, 80, 19, 95, 89, 69, 8, 83, 84, 6, 83, 68, 0, 9, 13, 86, 92, 30}));
            } else if (this.blockDurationUs == -9223372036854775807L) {
                lIl1llIlll.w(TAG, I1I1lI1II1.a(new byte[]{100, 15, 11, 21, 18, 92, 89, 87, 25, 23, 69, 82, 65, 80, 64, 89, 7, 65, 17, 83, 93, 17, 95, 80, 19, 65, 94, 17, 12, 18, 89, 12, 23, 0, 23, 23, 3, 65, 94, 95, 87, 74}));
            } else {
                setSubtitleEndTime(i1l11I1I1I.codecId, this.blockDurationUs, this.subtitleSample.getData());
                int position = this.subtitleSample.getPosition();
                while (true) {
                    if (position >= this.subtitleSample.limit()) {
                        break;
                    }
                    if (this.subtitleSample.getData()[position] == 0) {
                        this.subtitleSample.setLimit(position);
                        break;
                    }
                    position++;
                }
                llllllllI1 lllllllli1 = i1l11I1I1I.output;
                III1llIlIl iII1llIlIl = this.subtitleSample;
                lllllllli1.sampleData(iII1llIlIl, iII1llIlIl.limit());
                iLimit = i2 + this.subtitleSample.limit();
                if ((i & 268435456) != 0) {
                    if (this.blockSampleCount > 1) {
                        this.supplementalData.reset(0);
                    } else {
                        int iLimit2 = this.supplementalData.limit();
                        i1l11I1I1I.output.sampleData(this.supplementalData, iLimit2, 2);
                        iLimit += iLimit2;
                    }
                }
                i1l11I1I1I.output.sampleMetadata(j, i, iLimit, i3, i1l11I1I1I.cryptoData);
            }
            iLimit = i2;
            if ((i & 268435456) != 0) {
            }
            i1l11I1I1I.output.sampleMetadata(j, i, iLimit, i3, i1l11I1I1I.cryptoData);
        } else {
            iLimit = i2;
            if ((i & 268435456) != 0) {
            }
            i1l11I1I1I.output.sampleMetadata(j, i, iLimit, i3, i1l11I1I1I.cryptoData);
        }
        this.haveOutputSample = true;
        if (androidx.interpolator.view.animation.lIIlI111II.llI1llI1l1(282142104L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{97, 11, 58, 12, 36, 99, 116, 96, 119, 85, 95, 119, 4, 83, 6, 68, 47, 80, 50, 120, 81, 41, 105, 84, 2}));
        }
    }

    private void readScratch(l1lllIll1I l1lllill1i, int i) throws SignatureException, IOException {
        if (this.scratch.limit() >= i) {
            if (lI11IlI1lI.IlIllIll1I(433053706L)) {
                throw new SyncFailedException(I1I1lI1II1.a(new byte[]{6, 53, 33, 44, 5, 90, 93, 101, 109, 47, 126, 103, 112, 95, 66, 118, 39, 22, 47, 124, 99, 4, 99, 123, 3, 15}));
            }
            return;
        }
        if (this.scratch.capacity() < i) {
            III1llIlIl iII1llIlIl = this.scratch;
            iII1llIlIl.ensureCapacity(Math.max(iII1llIlIl.capacity() * 2, i));
        }
        l1lllill1i.readFully(this.scratch.getData(), this.scratch.limit(), i - this.scratch.limit());
        this.scratch.setLimit(i);
    }

    private int writeSampleData(l1lllIll1I l1lllill1i, I1l11I1I1I i1l11I1I1I, int i, boolean z) throws IllegalAccessException, SignatureException, TimeoutException, CertificateNotYetValidException, IOException, BrokenBarrierException, KeyManagementException, CertPathBuilderException, CertPathValidatorException {
        int i2;
        if (CODEC_ID_SUBRIP.equals(i1l11I1I1I.codecId)) {
            writeSubtitleSampleData(l1lllill1i, SUBRIP_PREFIX, i);
            int iFinishWriteSampleData = finishWriteSampleData();
            if (I1111IIl11.llll111lI1(I1I1lI1II1.a(new byte[]{93, 8}), 8589)) {
                throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{3, 62, 51, 34, 39, 92, 93, 3, 122, 62, 70, 68, 115, 122, 80, Byte.MAX_VALUE, 85, 82, 46, 112, 105, 0, 103}));
            }
            return iFinishWriteSampleData;
        }
        if (CODEC_ID_ASS.equals(i1l11I1I1I.codecId)) {
            writeSubtitleSampleData(l1lllill1i, SSA_PREFIX, i);
            return finishWriteSampleData();
        }
        if (CODEC_ID_VTT.equals(i1l11I1I1I.codecId)) {
            writeSubtitleSampleData(l1lllill1i, VTT_PREFIX, i);
            return finishWriteSampleData();
        }
        llllllllI1 lllllllli1 = i1l11I1I1I.output;
        if (!this.sampleEncodingHandled) {
            if (i1l11I1I1I.hasContentEncryption) {
                this.blockFlags &= -1073741825;
                if (!this.sampleSignalByteRead) {
                    l1lllill1i.readFully(this.scratch.getData(), 0, 1);
                    this.sampleBytesRead++;
                    if ((this.scratch.getData()[0] & 128) == 128) {
                        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 28, 22, 0, 12, 70, 94, 95, 87, 68, 82, 89, 65, 25, 93, 70, 66, 18, 7, 70, 16, 8, 93, 21, 64, 95, 80, 11, 5, 94, 23, 1, 78, 16, 7}), null);
                    }
                    this.sampleSignalByte = this.scratch.getData()[0];
                    this.sampleSignalByteRead = true;
                }
                byte b = this.sampleSignalByte;
                if ((b & 1) == 1) {
                    boolean z2 = (b & 2) == 2;
                    this.blockFlags |= 1073741824;
                    if (!this.sampleInitializationVectorRead) {
                        l1lllill1i.readFully(this.encryptionInitializationVector.getData(), 0, 8);
                        this.sampleBytesRead += 8;
                        this.sampleInitializationVectorRead = true;
                        this.scratch.getData()[0] = (byte) ((z2 ? 128 : 0) | 8);
                        this.scratch.setPosition(0);
                        lllllllli1.sampleData(this.scratch, 1, 1);
                        this.sampleBytesWritten++;
                        this.encryptionInitializationVector.setPosition(0);
                        lllllllli1.sampleData(this.encryptionInitializationVector, 8, 1);
                        this.sampleBytesWritten += 8;
                    }
                    if (z2) {
                        if (!this.samplePartitionCountRead) {
                            l1lllill1i.readFully(this.scratch.getData(), 0, 1);
                            this.sampleBytesRead++;
                            this.scratch.setPosition(0);
                            this.samplePartitionCount = this.scratch.readUnsignedByte();
                            this.samplePartitionCountRead = true;
                        }
                        int i3 = this.samplePartitionCount * 4;
                        this.scratch.reset(i3);
                        l1lllill1i.readFully(this.scratch.getData(), 0, i3);
                        this.sampleBytesRead += i3;
                        short s = (short) ((this.samplePartitionCount / 2) + 1);
                        int i4 = (s * 6) + 2;
                        ByteBuffer byteBuffer = this.encryptionSubsampleDataBuffer;
                        if (byteBuffer == null || byteBuffer.capacity() < i4) {
                            this.encryptionSubsampleDataBuffer = ByteBuffer.allocate(i4);
                        }
                        this.encryptionSubsampleDataBuffer.position(0);
                        this.encryptionSubsampleDataBuffer.putShort(s);
                        int i5 = 0;
                        int i6 = 0;
                        while (true) {
                            i2 = this.samplePartitionCount;
                            if (i5 >= i2) {
                                break;
                            }
                            int unsignedIntToInt = this.scratch.readUnsignedIntToInt();
                            if (i5 % 2 == 0) {
                                this.encryptionSubsampleDataBuffer.putShort((short) (unsignedIntToInt - i6));
                            } else {
                                this.encryptionSubsampleDataBuffer.putInt(unsignedIntToInt - i6);
                            }
                            i5++;
                            i6 = unsignedIntToInt;
                        }
                        int i7 = (i - this.sampleBytesRead) - i6;
                        if (i2 % 2 == 1) {
                            this.encryptionSubsampleDataBuffer.putInt(i7);
                        } else {
                            this.encryptionSubsampleDataBuffer.putShort((short) i7);
                            this.encryptionSubsampleDataBuffer.putInt(0);
                        }
                        this.encryptionSubsampleData.reset(this.encryptionSubsampleDataBuffer.array(), i4);
                        lllllllli1.sampleData(this.encryptionSubsampleData, i4, 1);
                        this.sampleBytesWritten += i4;
                    }
                }
            } else if (i1l11I1I1I.sampleStrippedBytes != null) {
                this.sampleStrippedBytes.reset(i1l11I1I1I.sampleStrippedBytes, i1l11I1I1I.sampleStrippedBytes.length);
            }
            if (i1l11I1I1I.samplesHaveSupplementalData(z)) {
                this.blockFlags |= 268435456;
                this.supplementalData.reset(0);
                int iLimit = (this.sampleStrippedBytes.limit() + i) - this.sampleBytesRead;
                this.scratch.reset(4);
                this.scratch.getData()[0] = (byte) ((iLimit >> 24) & 255);
                this.scratch.getData()[1] = (byte) ((iLimit >> 16) & 255);
                this.scratch.getData()[2] = (byte) ((iLimit >> 8) & 255);
                this.scratch.getData()[3] = (byte) (iLimit & 255);
                lllllllli1.sampleData(this.scratch, 4, 2);
                this.sampleBytesWritten += 4;
            }
            this.sampleEncodingHandled = true;
        }
        int iLimit2 = i + this.sampleStrippedBytes.limit();
        if (CODEC_ID_H264.equals(i1l11I1I1I.codecId) || CODEC_ID_H265.equals(i1l11I1I1I.codecId)) {
            byte[] data = this.nalLength.getData();
            data[0] = 0;
            data[1] = 0;
            data[2] = 0;
            int i8 = i1l11I1I1I.nalUnitLengthFieldLength;
            int i9 = 4 - i1l11I1I1I.nalUnitLengthFieldLength;
            while (this.sampleBytesRead < iLimit2) {
                int i10 = this.sampleCurrentNalBytesRemaining;
                if (i10 == 0) {
                    writeToTarget(l1lllill1i, data, i9, i8);
                    this.sampleBytesRead += i8;
                    this.nalLength.setPosition(0);
                    this.sampleCurrentNalBytesRemaining = this.nalLength.readUnsignedIntToInt();
                    this.nalStartCode.setPosition(0);
                    lllllllli1.sampleData(this.nalStartCode, 4);
                    this.sampleBytesWritten += 4;
                } else {
                    int iWriteToOutput = writeToOutput(l1lllill1i, lllllllli1, i10);
                    this.sampleBytesRead += iWriteToOutput;
                    this.sampleBytesWritten += iWriteToOutput;
                    this.sampleCurrentNalBytesRemaining -= iWriteToOutput;
                }
            }
        } else {
            if (i1l11I1I1I.trueHdSampleRechunker != null) {
                ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkState(this.sampleStrippedBytes.limit() == 0);
                i1l11I1I1I.trueHdSampleRechunker.startSample(l1lllill1i);
            }
            while (true) {
                int i11 = this.sampleBytesRead;
                if (i11 >= iLimit2) {
                    break;
                }
                int iWriteToOutput2 = writeToOutput(l1lllill1i, lllllllli1, iLimit2 - i11);
                this.sampleBytesRead += iWriteToOutput2;
                this.sampleBytesWritten += iWriteToOutput2;
            }
        }
        if (CODEC_ID_VORBIS.equals(i1l11I1I1I.codecId)) {
            this.vorbisNumPageSamples.setPosition(0);
            lllllllli1.sampleData(this.vorbisNumPageSamples, 4);
            this.sampleBytesWritten += 4;
        }
        int iFinishWriteSampleData2 = finishWriteSampleData();
        if (l1lll111II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{114, 62, 42, 51, 27, 84, 83, 90, 107, 41, Byte.MAX_VALUE, 5, 89, 13, 3, 82, 90}))) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{80, 44, 0, 55, 54, 64, 113, 97, 79, 23, Byte.MAX_VALUE, 0, 1, 111}));
        }
        return iFinishWriteSampleData2;
    }

    private int finishWriteSampleData() throws BrokenBarrierException {
        int i = this.sampleBytesWritten;
        resetWriteSampleData();
        return i;
    }

    private void resetWriteSampleData() throws BrokenBarrierException {
        this.sampleBytesRead = 0;
        this.sampleBytesWritten = 0;
        this.sampleCurrentNalBytesRemaining = 0;
        this.sampleEncodingHandled = false;
        this.sampleSignalByteRead = false;
        this.samplePartitionCountRead = false;
        this.samplePartitionCount = 0;
        this.sampleSignalByte = (byte) 0;
        this.sampleInitializationVectorRead = false;
        this.sampleStrippedBytes.reset(0);
        if (Il1llIl111.Ill1lIIlIl(177482461L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{93, 9, 83, 35, 8, 121, 116, 113, 109, 7, 91, 8, 81, 126, 87, 69}));
        }
    }

    private void writeSubtitleSampleData(l1lllIll1I l1lllill1i, byte[] bArr, int i) throws SignatureException, IOException, BrokenBarrierException, CertPathValidatorException {
        if (l1l1IllI11.l1l1Il1I11(I1I1lI1II1.a(new byte[]{110, 43, 44, 16, 27, 95, 112, 113, 65, 35, Byte.MAX_VALUE, 71, 97, 73, 66, 118, 14}), 160067318L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 43, 48, 34, 37, 96}));
        }
        int length = bArr.length + i;
        if (this.subtitleSample.capacity() < length) {
            this.subtitleSample.reset(Arrays.copyOf(bArr, length + i));
        } else {
            System.arraycopy(bArr, 0, this.subtitleSample.getData(), 0, bArr.length);
        }
        l1lllill1i.readFully(this.subtitleSample.getData(), bArr.length, i);
        this.subtitleSample.setPosition(0);
        this.subtitleSample.setLimit(length);
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x0035  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static void setSubtitleEndTime(String str, long j, byte[] bArr) throws CertificateParsingException, IOException {
        char c;
        byte[] subtitleTimecode;
        int i;
        int iHashCode = str.hashCode();
        if (iHashCode != 738597099) {
            if (iHashCode != 1045209816) {
                c = (iHashCode == 1422270023 && str.equals(CODEC_ID_SUBRIP)) ? (char) 0 : (char) 65535;
            } else if (str.equals(CODEC_ID_VTT)) {
                c = 2;
            }
        } else if (str.equals(CODEC_ID_ASS)) {
            c = 1;
        }
        if (c == 0) {
            subtitleTimecode = formatSubtitleTimecode(j, SUBRIP_TIMECODE_FORMAT, 1000L);
            i = 19;
        } else if (c == 1) {
            subtitleTimecode = formatSubtitleTimecode(j, SSA_TIMECODE_FORMAT, 10000L);
            i = 21;
        } else if (c == 2) {
            subtitleTimecode = formatSubtitleTimecode(j, VTT_TIMECODE_FORMAT, 1000L);
            i = 25;
        } else {
            throw new IllegalArgumentException();
        }
        System.arraycopy(subtitleTimecode, 0, bArr, i, subtitleTimecode.length);
    }

    private static byte[] formatSubtitleTimecode(long j, String str, long j2) throws CertificateParsingException, IOException {
        ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkArgument(j != -9223372036854775807L);
        int i = (int) (j / 3600000000L);
        long j3 = j - ((i * 3600) * 1000000);
        int i2 = (int) (j3 / 60000000);
        long j4 = j3 - ((i2 * 60) * 1000000);
        int i3 = (int) (j4 / 1000000);
        byte[] utf8Bytes = llIIlI1llI.getUtf8Bytes(String.format(Locale.US, str, Integer.valueOf(i), Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf((int) ((j4 - (i3 * 1000000)) / j2))));
        if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{110, 17, 44, 14, 51, 91, 0, 83, 93, 32, 99}), 3726)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{93, 87, 22, 87}));
        }
        return utf8Bytes;
    }

    private void writeToTarget(l1lllIll1I l1lllill1i, byte[] bArr, int i, int i2) throws CertPathBuilderException, CertificateNotYetValidException, IOException {
        if (lIIlI111II.Il1lII1l1l(9728)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{100, 15, 48, 85}));
        }
        int iMin = Math.min(i2, this.sampleStrippedBytes.bytesLeft());
        l1lllill1i.readFully(bArr, i + iMin, i2 - iMin);
        if (iMin > 0) {
            this.sampleStrippedBytes.readBytes(bArr, i, iMin);
        }
    }

    private int writeToOutput(l1lllIll1I l1lllill1i, llllllllI1 lllllllli1, int i) throws TimeoutException, IOException {
        int iSampleData;
        int iBytesLeft = this.sampleStrippedBytes.bytesLeft();
        if (iBytesLeft > 0) {
            iSampleData = Math.min(i, iBytesLeft);
            lllllllli1.sampleData(this.sampleStrippedBytes, iSampleData);
        } else {
            iSampleData = lllllllli1.sampleData((androidx.media3.common.Il1lII1l1l) l1lllill1i, i, false);
        }
        if (I1lllI11II.l1Il11I1Il(I1I1lI1II1.a(new byte[]{126, 87, 49, 18, 35, 1, 99, 86, 9, 32, 86, 86}), 169912851L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{3, 15, 54, 38, 44, 98, Byte.MAX_VALUE, 70, 120, 49, 0, 106, Byte.MAX_VALUE, 114, 123, 124, 27, 7, 59, 96, 96, 0, 90, 79, 1, 117, 99, 2, 87, 71, 85}));
        }
        return iSampleData;
    }

    private Il1Il1l1Il buildSeekMap(I1I1I1IIlI i1I1I1IIlI, I1I1I1IIlI i1I1I1IIlI2) {
        int i;
        if (this.segmentContentPosition == -1 || this.durationUs == -9223372036854775807L || i1I1I1IIlI == null || i1I1I1IIlI.size() == 0 || i1I1I1IIlI2 == null || i1I1I1IIlI2.size() != i1I1I1IIlI.size()) {
            return new I11ll1lI11(this.durationUs);
        }
        int size = i1I1I1IIlI.size();
        int[] iArrCopyOf = new int[size];
        long[] jArrCopyOf = new long[size];
        long[] jArrCopyOf2 = new long[size];
        long[] jArrCopyOf3 = new long[size];
        int i2 = 0;
        for (int i3 = 0; i3 < size; i3++) {
            jArrCopyOf3[i3] = i1I1I1IIlI.get(i3);
            jArrCopyOf[i3] = this.segmentContentPosition + i1I1I1IIlI2.get(i3);
        }
        while (true) {
            i = size - 1;
            if (i2 >= i) {
                break;
            }
            int i4 = i2 + 1;
            iArrCopyOf[i2] = (int) (jArrCopyOf[i4] - jArrCopyOf[i2]);
            jArrCopyOf2[i2] = jArrCopyOf3[i4] - jArrCopyOf3[i2];
            i2 = i4;
        }
        iArrCopyOf[i] = (int) ((this.segmentContentPosition + this.segmentContentSize) - jArrCopyOf[i]);
        long j = this.durationUs - jArrCopyOf3[i];
        jArrCopyOf2[i] = j;
        if (j <= 0) {
            lIl1llIlll.w(TAG, I1I1lI1II1.a(new byte[]{115, 13, 17, 6, 3, 71, 83, 89, 87, 3, 16, 92, 84, 74, 64, 21, 1, 20, 7, 18, 64, 14, 90, 91, 71, 22, 64, 12, 16, 90, 23, 22, 89, 1, 26, 21, 7, 86, 67, 85, 93, 68, 84, 69, 71, 88, 64, 92, 13, 15, 88, 18}) + j);
            iArrCopyOf = Arrays.copyOf(iArrCopyOf, i);
            jArrCopyOf = Arrays.copyOf(jArrCopyOf, i);
            jArrCopyOf2 = Arrays.copyOf(jArrCopyOf2, i);
            jArrCopyOf3 = Arrays.copyOf(jArrCopyOf3, i);
        }
        return new IlII11I1I1(iArrCopyOf, jArrCopyOf, jArrCopyOf2, jArrCopyOf3);
    }

    private boolean maybeSeekForCues(lII1lIII1I lii1liii1i, long j) {
        if (this.seekForCues) {
            this.seekPositionAfterBuildingCues = j;
            lii1liii1i.position = this.cuesContentPosition;
            this.seekForCues = false;
            return true;
        }
        if (this.sentSeekMap) {
            long j2 = this.seekPositionAfterBuildingCues;
            if (j2 != -1) {
                lii1liii1i.position = j2;
                this.seekPositionAfterBuildingCues = -1L;
                return true;
            }
        }
        return false;
    }

    private long scaleTimecodeToUs(long j) throws l1llI1llII, CertificateException {
        if (android.support.v4.graphics.drawable.lIIlI111II.llllI1l1II(39)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{110, 15, 85, 63, 5, 102, 114, 93, 14, 61, 72, 121, 89, 9, 12, 114, 6, 41, 35, 90, 100, 25, 67, 64, 64}));
        }
        long j2 = this.timecodeScale;
        if (j2 != -9223372036854775807L) {
            return llIIlI1llI.scaleLargeTimestamp(j, j2, 1000L);
        }
        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{116, 5, 12, 66, 22, 21, 68, 83, 88, 8, 85, 16, 65, 80, 89, 80, 1, 14, 6, 87, 16, 17, 65, 92, 92, 68, 23, 17, 11, 18, 67, 10, 90, 1, 1, 10, 6, 80, 100, 83, 88, 8, 85, 16, 87, 92, 93, 91, 5, 65, 17, 87, 68, 79}), null);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:104:0x0187  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static boolean isCodecSupported(String str) throws FileNotFoundException {
        char c;
        switch (str.hashCode()) {
            case -2095576542:
                if (!str.equals(CODEC_ID_MPEG4_AP)) {
                    c = 65535;
                    break;
                } else {
                    c = 6;
                    break;
                }
            case -2095575984:
                if (str.equals(CODEC_ID_MPEG4_SP)) {
                    c = 4;
                    break;
                }
                break;
            case -1985379776:
                if (str.equals(CODEC_ID_ACM)) {
                    c = 23;
                    break;
                }
                break;
            case -1784763192:
                if (str.equals(CODEC_ID_TRUEHD)) {
                    c = 18;
                    break;
                }
                break;
            case -1730367663:
                if (str.equals(CODEC_ID_VORBIS)) {
                    c = '\f';
                    break;
                }
                break;
            case -1482641358:
                if (str.equals(CODEC_ID_MP2)) {
                    c = 14;
                    break;
                }
                break;
            case -1482641357:
                if (str.equals(CODEC_ID_MP3)) {
                    c = 15;
                    break;
                }
                break;
            case -1373388978:
                if (str.equals(CODEC_ID_FOURCC)) {
                    c = '\t';
                    break;
                }
                break;
            case -933872740:
                if (str.equals(CODEC_ID_DVBSUB)) {
                    c = ' ';
                    break;
                }
                break;
            case -538363189:
                if (str.equals(CODEC_ID_MPEG4_ASP)) {
                    c = 5;
                    break;
                }
                break;
            case -538363109:
                if (str.equals(CODEC_ID_H264)) {
                    c = 7;
                    break;
                }
                break;
            case -425012669:
                if (str.equals(CODEC_ID_VOBSUB)) {
                    c = 30;
                    break;
                }
                break;
            case -356037306:
                if (str.equals(CODEC_ID_DTS_LOSSLESS)) {
                    c = 21;
                    break;
                }
                break;
            case 62923557:
                if (str.equals(CODEC_ID_AAC)) {
                    c = '\r';
                    break;
                }
                break;
            case 62923603:
                if (str.equals(CODEC_ID_AC3)) {
                    c = 16;
                    break;
                }
                break;
            case 62927045:
                if (str.equals(CODEC_ID_DTS)) {
                    c = 19;
                    break;
                }
                break;
            case 82318131:
                if (str.equals(CODEC_ID_AV1)) {
                    c = 2;
                    break;
                }
                break;
            case 82338133:
                if (str.equals(CODEC_ID_VP8)) {
                    c = 0;
                    break;
                }
                break;
            case 82338134:
                if (str.equals(CODEC_ID_VP9)) {
                    c = 1;
                    break;
                }
                break;
            case 99146302:
                if (str.equals(CODEC_ID_PGS)) {
                    c = 31;
                    break;
                }
                break;
            case 444813526:
                if (str.equals(CODEC_ID_THEORA)) {
                    c = '\n';
                    break;
                }
                break;
            case 542569478:
                if (str.equals(CODEC_ID_DTS_EXPRESS)) {
                    c = 20;
                    break;
                }
                break;
            case 635596514:
                if (str.equals(CODEC_ID_PCM_FLOAT)) {
                    c = 26;
                    break;
                }
                break;
            case 725948237:
                if (str.equals(CODEC_ID_PCM_INT_BIG)) {
                    c = 25;
                    break;
                }
                break;
            case 725957860:
                if (str.equals(CODEC_ID_PCM_INT_LIT)) {
                    c = 24;
                    break;
                }
                break;
            case 738597099:
                if (str.equals(CODEC_ID_ASS)) {
                    c = 28;
                    break;
                }
                break;
            case 855502857:
                if (str.equals(CODEC_ID_H265)) {
                    c = '\b';
                    break;
                }
                break;
            case 1045209816:
                if (str.equals(CODEC_ID_VTT)) {
                    c = 29;
                    break;
                }
                break;
            case 1422270023:
                if (str.equals(CODEC_ID_SUBRIP)) {
                    c = 27;
                    break;
                }
                break;
            case 1809237540:
                if (str.equals(CODEC_ID_MPEG2)) {
                    c = 3;
                    break;
                }
                break;
            case 1950749482:
                if (str.equals(CODEC_ID_E_AC3)) {
                    c = 17;
                    break;
                }
                break;
            case 1950789798:
                if (str.equals(CODEC_ID_FLAC)) {
                    c = 22;
                    break;
                }
                break;
            case 1951062397:
                if (str.equals(CODEC_ID_OPUS)) {
                    c = 11;
                    break;
                }
                break;
        }
        switch (c) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case '\b':
            case '\t':
            case '\n':
            case 11:
            case '\f':
            case '\r':
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case ' ':
                return true;
            default:
                if (lll1IIII11.IIll1I11lI(275894925L)) {
                    throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{98, 83, 3, 84, 86, 5, 79, 105, 67, 54, 114, 6, 95, 65, 78, 111, 5, 21, 56, 116, 69, 89, 90, 71}));
                }
                return false;
        }
    }

    private static int[] ensureArrayCapacity(int[] iArr, int i) throws SocketTimeoutException {
        if (iArr == null) {
            return new int[i];
        }
        if (iArr.length < i) {
            return new int[Math.max(iArr.length * 2, i)];
        }
        if (lIIIIII11I.I1lIllll1l(I1I1lI1II1.a(new byte[]{70, 38, 19, 15, 55, 12, 98, 102, 90, 37, 72, 7, Byte.MAX_VALUE, 84, 97}))) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{81, 11, 55, 40}));
        }
        return iArr;
    }

    private void assertInitialized() {
        if (II1I11IlI1.l11I11I11l(I1I1lI1II1.a(new byte[]{69, 19, 85, 12, 58, 119, 0, 98, 116, 34, 95, 105, 7, 12, 71}))) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{81, 55, 44, 18, 6, 88, 77, 82, 90, 43, 65, 117, 0, 119, 119, 123, 7, 45, 48, 120, 81, 43, 6, 83, 80, 119, 124, 10, 50, 86, 123}));
        }
        ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkStateNotNull(this.extractorOutput);
    }
}
