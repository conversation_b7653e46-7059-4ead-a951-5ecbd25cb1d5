package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.core.location.I111I11Ill;
import androidx.core.location.l1l1I111I1;
import androidx.media3.common.l1llI1llII;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertPathBuilderException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.l1lllIll1I;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l1I1l1Il1I implements llI1l11lII {
    final /* synthetic */ I11ll1lIl1 this$0;

    private l1I1l1Il1I(I11ll1lIl1 i11ll1lIl1) {
        this.this$0 = i11ll1lIl1;
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public int getElementType(int i) throws GeneralSecurityException {
        int elementType = this.this$0.getElementType(i);
        if (I111I11Ill.l111IIlII1(I1I1lI1II1.a(new byte[]{14, 48, 84, 4, 40, Byte.MAX_VALUE, 69, 101, 107, 55, 73, 1, 91, 87, 117, 4, 84, 2, 45, 64, 70, 32, 102, 13}), 465280425L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{65, 33, 3, 63, 5}));
        }
        return elementType;
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public boolean isLevel1Element(int i) {
        boolean zIsLevel1Element = this.this$0.isLevel1Element(i);
        if (lIIllIlIl1.Il1IIlI1II(6757)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{91, 45, 0, 44, 35, 122, 85, 99, 97, 32, 87, 66, 13, 81, 93, 122, 80}));
        }
        return zIsLevel1Element;
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void startMasterElement(int i, long j, long j2) throws l1llI1llII {
        this.this$0.startMasterElement(i, j, j2);
        if (l1l1I111I1.Il1IIlI1II(I1I1lI1II1.a(new byte[]{103, 0, 84, 42, 47, 112, 89, 83, 9, 48, 103, 83, 84, 117, 121, 97, 26, 3}))) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{90, 43, 4, 81, 15, 71, 6, 7, 126, 19, 64, 118}));
        }
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void endMasterElement(int i) throws l1llI1llII, CertPathBuilderException {
        this.this$0.endMasterElement(i);
        if (l11Il111ll.I1lllI1llI(4721)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{66, 20, 5, 3, 90, 0, Byte.MAX_VALUE}));
        }
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void integerElement(int i, long j) throws l1llI1llII, UnrecoverableEntryException {
        if (II1I11IlI1.Ill1lIIlIl(5800)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 22, 90, 34, 48, 125, 7, 126, 110, 85, 2, 6, 126, 96}));
        }
        this.this$0.integerElement(i, j);
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void floatElement(int i, double d) throws l1llI1llII {
        this.this$0.floatElement(i, d);
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void stringElement(int i, String str) throws l1llI1llII {
        this.this$0.stringElement(i, str);
    }

    @Override // I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il.llI1l11lII
    public void binaryElement(int i, int i2, l1lllIll1I l1lllill1i) throws IOException, UnrecoverableEntryException {
        if (Il1I1lllIl.II1111I11I(I1I1lI1II1.a(new byte[]{0, 93, 1, 42, 0, 88, 100, 99, 65, 17, 67, 5, 7, 99, 81, 95, 10, 89, 0, 66, 84, 48, 120, 83, 64, 14}), 165308514L)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{81, 40, 87, 31, 10, 121, 124}));
        }
        this.this$0.binaryElement(i, i2, l1lllill1i);
    }
}
