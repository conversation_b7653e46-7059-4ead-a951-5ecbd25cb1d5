package I111I1lI11.l1I1I11111.I1lIIlll1l.l1Il11l1Il;

import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.IIllI1III1;
import android.accounts.utils.lIIIIII11I;
import android.accounts.utils.lIIlI111II;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import android.util.Pair;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.lI1lI11Ill;
import androidx.media3.common.DrmInitData;
import androidx.media3.common.IIl1Il11Il;
import androidx.media3.common.IlIIl111lI;
import androidx.media3.common.l1llI1llII;
import androidx.media3.common.lII1111ll1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.InvalidObjectException;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.KeyStoreException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.III1llIlIl;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIl1llIlll;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.I1l1IIlI1I;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.II1ll1l1ll;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.IIl11lIIll;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.Il111I11Il;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.lIl11lllI1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.ll1III1lIl;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.llI1lIlIlI;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.llI1llI1l1;
import llII1l1lIl.lIllllI1lI.llI111llII.IlIlI1l1I1.llllllllI1;

/* JADX INFO: Access modifiers changed from: protected */
/* loaded from: classes.dex */
public final class I1l11I1I1I {
    private static final int DEFAULT_MAX_CLL = 1000;
    private static final int DEFAULT_MAX_FALL = 200;
    private static final int DISPLAY_UNIT_PIXELS = 0;
    private static final int MAX_CHROMATICITY = 50000;
    private int blockAddIdType;
    public String codecId;
    public byte[] codecPrivate;
    public lIl11lllI1 cryptoData;
    public int defaultSampleDurationNs;
    public byte[] dolbyVisionConfigBytes;
    public DrmInitData drmInitData;
    public boolean flagForced;
    public boolean hasContentEncryption;
    public int maxBlockAdditionId;
    public int nalUnitLengthFieldLength;
    public String name;
    public int number;
    public llllllllI1 output;
    public byte[] sampleStrippedBytes;
    public I1l1IIlI1I trueHdSampleRechunker;
    public int type;
    public int width = -1;
    public int height = -1;
    public int displayWidth = -1;
    public int displayHeight = -1;
    public int displayUnit = 0;
    public int projectionType = -1;
    public float projectionPoseYaw = 0.0f;
    public float projectionPosePitch = 0.0f;
    public float projectionPoseRoll = 0.0f;
    public byte[] projectionData = null;
    public int stereoMode = -1;
    public boolean hasColorInfo = false;
    public int colorSpace = -1;
    public int colorTransfer = -1;
    public int colorRange = -1;
    public int maxContentLuminance = 1000;
    public int maxFrameAverageLuminance = 200;
    public float primaryRChromaticityX = -1.0f;
    public float primaryRChromaticityY = -1.0f;
    public float primaryGChromaticityX = -1.0f;
    public float primaryGChromaticityY = -1.0f;
    public float primaryBChromaticityX = -1.0f;
    public float primaryBChromaticityY = -1.0f;
    public float whitePointChromaticityX = -1.0f;
    public float whitePointChromaticityY = -1.0f;
    public float maxMasteringLuminance = -1.0f;
    public float minMasteringLuminance = -1.0f;
    public int channelCount = 1;
    public int audioBitDepth = -1;
    public int sampleRate = 8000;
    public long codecDelayNs = 0;
    public long seekPreRollNs = 0;
    public boolean flagDefault = true;
    private String language = I1I1lI1II1.a(new byte[]{82, 10, 5});

    protected I1l11I1I1I() {
    }

    static /* synthetic */ int access$202(I1l11I1I1I i1l11I1I1I, int i) throws UnsupportedEncodingException {
        i1l11I1I1I.blockAddIdType = i;
        if (Il1l11I11I.llII1lIIlI(I1I1lI1II1.a(new byte[]{121, 37, 1, 55, 36, 12, 95, 81, 74, 46, 64, 6, 122, 116, 96, 66, 14, 57, 42, 121, 90, 51, 92, 93, 1, 89, 98}))) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{122, 82, 19, 81, 47, Byte.MAX_VALUE, 1, 125, 120, 54, 2, 97, Byte.MAX_VALUE, 112, 109, 125, 42}));
        }
        return i;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:104:0x027f  */
    /* JADX WARN: Removed duplicated region for block: B:171:0x0678  */
    /* JADX WARN: Removed duplicated region for block: B:176:0x069a  */
    /* JADX WARN: Removed duplicated region for block: B:177:0x069c  */
    /* JADX WARN: Removed duplicated region for block: B:180:0x06a9  */
    /* JADX WARN: Removed duplicated region for block: B:181:0x06bb  */
    /* JADX WARN: Removed duplicated region for block: B:246:0x0806  */
    /* JADX WARN: Removed duplicated region for block: B:251:0x0862 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:252:0x0863  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void initializeOutput(ll1III1lIl ll1iii1lil, int i) throws l1llI1llII, IllegalAccessException, MalformedURLException, SocketTimeoutException, InvalidObjectException, KeyStoreException, UTFDataFormatException {
        String strA;
        String str;
        List<byte[]> listSingletonList;
        List<byte[]> list;
        String str2;
        int pcmEncoding;
        int i2;
        List<byte[]> vorbisCodecPrivate;
        List<byte[]> listSingletonList2;
        IlIIl111lI ilIIl111lI;
        int i3;
        int i4;
        IIl11lIIll iIl11lIIll;
        String str3 = this.codecId;
        char c = 6;
        int iIntValue = -1;
        switch (str3.hashCode()) {
            case -2095576542:
                if (!str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 105}))) {
                    c = 65535;
                    break;
                }
                break;
            case -2095575984:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 102, 105}))) {
                    c = 4;
                    break;
                }
                break;
            case -1985379776:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 47, 54, 77, 116, 116, 125}))) {
                    c = 23;
                    break;
                }
                break;
            case -1784763192:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 54, 55, 55, 112, Byte.MAX_VALUE, 116}))) {
                    c = 18;
                    break;
                }
                break;
            case -1730367663:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 52, 42, 48, 119, 126, 99}))) {
                    c = 11;
                    break;
                }
                break;
            case -1482641358:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 47, 53, 39, 114, 24, 124, 11}))) {
                    c = 14;
                    break;
                }
                break;
            case -1482641357:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 47, 53, 39, 114, 24, 124, 10}))) {
                    c = 15;
                    break;
                }
                break;
            case -1373388978:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 54, 77, 99, 113, 103, 22, 34, Byte.MAX_VALUE, 101, 103, 122, 119}))) {
                    c = '\t';
                    break;
                }
                break;
            case -933872740:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 38, 51, 32, 102, 98, 114}))) {
                    c = ' ';
                    break;
                }
                break;
            case -538363189:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 106, 100}))) {
                    c = 5;
                    break;
                }
                break;
            case -538363109:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 3, 31, 112, 55, Byte.MAX_VALUE, 31, 116, 111, 119}))) {
                    c = 7;
                    break;
                }
                break;
            case -425012669:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 52, 42, 32, 102, 98, 114}))) {
                    c = 30;
                    break;
                }
                break;
            case -356037306:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49, 26, 123, Byte.MAX_VALUE, 106, 55, 124, 117, 102, 106}))) {
                    c = 21;
                    break;
                }
                break;
            case 62923557:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 35, 36, 33}))) {
                    c = '\r';
                    break;
                }
                break;
            case 62923603:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 35, 38, 81}))) {
                    c = 16;
                    break;
                }
                break;
            case 62927045:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49}))) {
                    c = 19;
                    break;
                }
                break;
            case 82318131:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 35, 51, 83}))) {
                    c = 2;
                    break;
                }
                break;
            case 82338133:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 52, 53, 90}))) {
                    c = 0;
                    break;
                }
                break;
            case 82338134:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 52, 53, 91}))) {
                    c = 1;
                    break;
                }
                break;
            case 99146302:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 42, 33, 47, 99, 24, 96, 126, 55}))) {
                    c = 31;
                    break;
                }
                break;
            case 444813526:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 54, 45, 39, 122, 101, 113}))) {
                    c = '\n';
                    break;
                }
                break;
            case 542569478:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 38, 49, 49, 26, 114, 104, 105, 54, 117, 99, 102}))) {
                    c = 20;
                    break;
                }
                break;
            case 635596514:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 113, 124, 118, 37, 100, 31, 124, 124, 113, 112}))) {
                    c = 26;
                    break;
                }
                break;
            case 725948237:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 126, 126, 109, 75, 114, 121, 114}))) {
                    c = 25;
                    break;
                }
                break;
            case 725957860:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 50, 38, 47, 26, 126, 126, 109, 75, 124, 121, 97}))) {
                    c = 24;
                    break;
                }
                break;
            case 738597099:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 113, 106, 55}))) {
                    c = 28;
                    break;
                }
                break;
            case 855502857:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, Byte.MAX_VALUE, 31, 112, 55, Byte.MAX_VALUE, 31, 125, 124, 98, 118}))) {
                    c = '\b';
                    break;
                }
                break;
            case 1045209816:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 103, 124, 38, 102, 100, 97}))) {
                    c = 29;
                    break;
                }
                break;
            case 1422270023:
                if (str3.equals(I1I1lI1II1.a(new byte[]{100, 59, 54, 32, 58, 97, 24, 101, 109, 34, 8}))) {
                    c = 27;
                    break;
                }
                break;
            case 1809237540:
                if (str3.equals(I1I1lI1II1.a(new byte[]{97, 59, 47, 53, 39, 114, 5}))) {
                    c = 3;
                    break;
                }
                break;
            case 1950749482:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 39, 36, 33, 6}))) {
                    c = 17;
                    break;
                }
                break;
            case 1950789798:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 36, 41, 35, 118}))) {
                    c = 22;
                    break;
                }
                break;
            case 1951062397:
                if (str3.equals(I1I1lI1II1.a(new byte[]{118, 59, 45, 53, 55, 102}))) {
                    c = '\f';
                    break;
                }
                break;
        }
        switch (c) {
            case 0:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 79, 29, 79, 10, 84, 30, 90, 87, 6, 27, 20, 17, 90});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null && (iIl11lIIll = IIl11lIIll.parse(new III1llIlIl(this.dolbyVisionConfigBytes))) != null) {
                    str = iIl11lIIll.codecs;
                    strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 83, 95, 85, 6, 73, 29, 67, 80, 71, 92, 13, 15});
                }
                int i5 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    ilIIl111lI.setChannelCount(this.channelCount).setSampleRate(this.sampleRate).setPcmEncoding(pcmEncoding);
                    i3 = 1;
                } else if (androidx.media3.common.I1l1IIlI1I.isVideo(strA)) {
                    if (this.displayUnit == 0) {
                        int i6 = this.displayWidth;
                        if (i6 == -1) {
                            i6 = this.width;
                        }
                        this.displayWidth = i6;
                        int i7 = this.displayHeight;
                        if (i7 == -1) {
                            i7 = this.height;
                        }
                        this.displayHeight = i7;
                    }
                    float f = (this.displayWidth == -1 || (i4 = this.displayHeight) == -1) ? -1.0f : (this.height * r3) / (this.width * i4);
                    lII1111ll1 lii1111ll1 = this.hasColorInfo ? new lII1111ll1(this.colorSpace, this.colorRange, this.colorTransfer, getHdrStaticInfo()) : null;
                    if (this.name != null && I11ll1lIl1.access$600().containsKey(this.name)) {
                        iIntValue = ((Integer) I11ll1lIl1.access$600().get(this.name)).intValue();
                    }
                    if (this.projectionType == 0 && Float.compare(this.projectionPoseYaw, 0.0f) == 0 && Float.compare(this.projectionPosePitch, 0.0f) == 0) {
                        if (Float.compare(this.projectionPoseRoll, 0.0f) == 0) {
                            iIntValue = 0;
                        } else if (Float.compare(this.projectionPosePitch, 90.0f) == 0) {
                            iIntValue = 90;
                        } else if (Float.compare(this.projectionPosePitch, -180.0f) == 0 || Float.compare(this.projectionPosePitch, 180.0f) == 0) {
                            iIntValue = 180;
                        } else if (Float.compare(this.projectionPosePitch, -90.0f) == 0) {
                            iIntValue = 270;
                        }
                    }
                    ilIIl111lI.setWidth(this.width).setHeight(this.height).setPixelWidthHeightRatio(f).setRotationDegrees(iIntValue).setProjectionData(this.projectionData).setStereoMode(this.stereoMode).setColorInfo(lii1111ll1);
                    i3 = 2;
                } else {
                    if (!I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 77, 20, 71, 64, 0, 19, 11, 66}).equals(strA) && !I1I1lI1II1.a(new byte[]{67, 1, 26, 17, 77, 77, 26, 67, 74, 5}).equals(strA) && !I1I1lI1II1.a(new byte[]{67, 1, 26, 17, 77, 67, 67, 68}).equals(strA) && !I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 67, 86, 86, 70, 23, 3}).equals(strA) && !I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 69, 94, 71}).equals(strA) && !I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 81, 79, 86, 70, 23, 3, 17}).equals(strA)) {
                        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 125, 124, 116, 113, 21, 22, 24, 18, 87, 30}), null);
                    }
                    i3 = 3;
                }
                if (this.name != null && !I11ll1lIl1.access$600().containsKey(this.name)) {
                    ilIIl111lI.setLabel(this.name);
                }
                IIl1Il11Il iIl1Il11IlBuild = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track;
                lllllllli1Track.format(iIl1Il11IlBuild);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{68, 9, 12, 6}));
                }
                return;
            case 1:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 79, 29, 79, 10, 84, 30, 90, 87, 6, 27, 20, 17, 91});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                    str = iIl11lIIll.codecs;
                    strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 83, 95, 85, 6, 73, 29, 67, 80, 71, 92, 13, 15});
                    break;
                }
                int i52 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                    ilIIl111lI.setLabel(this.name);
                    break;
                }
                IIl1Il11Il iIl1Il11IlBuild2 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2;
                lllllllli1Track2.format(iIl1Il11IlBuild2);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 2:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 86, 70, 9, 85});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22;
                lllllllli1Track22.format(iIl1Il11IlBuild22);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 3:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 90, 64, 92, 3, 2});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222;
                lllllllli1Track222.format(iIl1Il11IlBuild222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 4:
            case 5:
            case 6:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 90, 64, 13, 18, 29, 85, 70});
                byte[] bArr = this.codecPrivate;
                str = null;
                listSingletonList = bArr == null ? null : Collections.singletonList(bArr);
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222;
                lllllllli1Track2222.format(iIl1Il11IlBuild2222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 7:
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 86, 70, 90});
                llI1llI1l1 lli1lli1l1 = llI1llI1l1.parse(new III1llIlIl(getCodecPrivate(this.codecId)));
                list = lli1lli1l1.initializationData;
                this.nalUnitLengthFieldLength = lli1lli1l1.nalUnitLengthFieldLength;
                str2 = lli1lli1l1.codecs;
                str = str2;
                listSingletonList = list;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222;
                lllllllli1Track22222.format(iIl1Il11IlBuild22222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case '\b':
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 95, 85, 79, 7});
                llI1lIlIlI lli1lilili = llI1lIlIlI.parse(new III1llIlIl(getCodecPrivate(this.codecId)));
                list = lli1lilili.initializationData;
                this.nalUnitLengthFieldLength = lli1lilili.nalUnitLengthFieldLength;
                str2 = lli1lilili.codecs;
                str = str2;
                listSingletonList = list;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222;
                lllllllli1Track222222.format(iIl1Il11IlBuild222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case '\t':
                Pair<String, List<byte[]>> fourCcPrivate = parseFourCcPrivate(new III1llIlIl(getCodecPrivate(this.codecId)));
                String str4 = (String) fourCcPrivate.first;
                str = null;
                listSingletonList = (List) fourCcPrivate.second;
                strA = str4;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222;
                lllllllli1Track2222222.format(iIl1Il11IlBuild2222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case '\n':
                strA = I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222;
                lllllllli1Track22222222.format(iIl1Il11IlBuild22222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 11:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 65, 95, 75, 6, 89, 67});
                vorbisCodecPrivate = parseVorbisCodecPrivate(getCodecPrivate(this.codecId));
                i2 = 8192;
                str = null;
                listSingletonList = vorbisCodecPrivate;
                pcmEncoding = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222;
                lllllllli1Track222222222.format(iIl1Il11IlBuild222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case '\f':
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 88, 64, 76, 23});
                vorbisCodecPrivate = new ArrayList<>(3);
                vorbisCodecPrivate.add(getCodecPrivate(this.codecId));
                vorbisCodecPrivate.add(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(this.codecDelayNs).array());
                vorbisCodecPrivate.add(ByteBuffer.allocate(8).order(ByteOrder.LITTLE_ENDIAN).putLong(this.seekPreRollNs).array());
                i2 = 5760;
                str = null;
                listSingletonList = vorbisCodecPrivate;
                pcmEncoding = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222;
                lllllllli1Track2222222222.format(iIl1Il11IlBuild2222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case '\r':
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 90, 64, 13, 5, 29, 92, 84, 77, 89});
                List<byte[]> listSingletonList3 = Collections.singletonList(getCodecPrivate(this.codecId));
                II1ll1l1ll audioSpecificConfig = Il111I11Il.parseAudioSpecificConfig(this.codecPrivate);
                this.sampleRate = audioSpecificConfig.sampleRateHz;
                this.channelCount = audioSpecificConfig.channelCount;
                listSingletonList = listSingletonList3;
                str = audioSpecificConfig.codecs;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222;
                lllllllli1Track22222222222.format(iIl1Il11IlBuild22222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 14:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 90, 64, 92, 3, 29, 124, 7});
                listSingletonList = null;
                str = null;
                i2 = 4096;
                pcmEncoding = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222;
                lllllllli1Track222222222222.format(iIl1Il11IlBuild222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 15:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 90, 64, 92, 3});
                listSingletonList = null;
                str = null;
                i2 = 4096;
                pcmEncoding = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222222;
                lllllllli1Track2222222222222.format(iIl1Il11IlBuild2222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 16:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 86, 83, 10});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222222;
                lllllllli1Track22222222222222.format(iIl1Il11IlBuild22222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 17:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 82, 81, 90, 87});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222222;
                lllllllli1Track222222222222222.format(iIl1Il11IlBuild222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 18:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 67, 66, 76, 1, 29, 88, 81});
                this.trueHdSampleRechunker = new I1l1IIlI1I();
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222222222;
                lllllllli1Track2222222222222222.format(iIl1Il11IlBuild2222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 19:
            case 20:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 65, 94, 93, 74, 84, 68, 70});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222222222;
                lllllllli1Track22222222222222222.format(iIl1Il11IlBuild22222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 21:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 65, 94, 93, 74, 84, 68, 70, 23, 92, 81});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222222222;
                lllllllli1Track222222222222222222.format(iIl1Il11IlBuild222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 22:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 81, 92, 88, 7});
                listSingletonList2 = Collections.singletonList(getCodecPrivate(this.codecId));
                str = null;
                listSingletonList = listSingletonList2;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222222222222;
                lllllllli1Track2222222222222222222.format(iIl1Il11IlBuild2222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 23:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 69, 81, 78});
                if (parseMsAcmCodecPrivate(new III1llIlIl(getCodecPrivate(this.codecId)))) {
                    pcmEncoding = llIIlI1llI.getPcmEncoding(this.audioBitDepth);
                    if (pcmEncoding == 0) {
                        strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                        lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{98, 10, 17, 16, 18, 69, 88, 66, 77, 1, 84, 16, 101, 122, 121, 21, 0, 8, 22, 18, 84, 4, 67, 65, 91, 12, 23}) + this.audioBitDepth + I1I1lI1II1.a(new byte[]{25, 68, 49, 0, 22, 65, 94, 94, 94, 68, 93, 89, 88, 92, 96, 76, 18, 4, 66, 70, 95, 65}) + strA);
                    }
                    listSingletonList = null;
                    str = null;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i522222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild22222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track22222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track22222222222222222222;
                    lllllllli1Track22222222222222222222.format(iIl1Il11IlBuild22222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                } else {
                    strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                    lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{121, 11, 12, 72, 50, 118, 122, 16, 116, 55, 31, 113, 118, 116, 20, 92, 17, 65, 23, 92, 67, 20, 67, 69, 92, 68, 67, 0, 0, 28, 23, 48, 82, 16, 22, 12, 12, 82, 23, 93, 80, 9, 85, 100, 76, 73, 81, 21, 22, 14, 66}) + strA);
                }
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222222222222;
                lllllllli1Track222222222222222222222.format(iIl1Il11IlBuild222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 24:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 69, 81, 78});
                pcmEncoding = llIIlI1llI.getPcmEncoding(this.audioBitDepth);
                if (pcmEncoding == 0) {
                    strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                    lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{98, 10, 17, 16, 18, 69, 88, 66, 77, 1, 84, 16, 89, 80, 64, 65, 14, 4, 66, 87, 94, 5, 90, 84, 93, 22, 103, 38, 41, 18, 85, 10, 67, 68, 6, 0, 18, 65, 95, 10, 25}) + this.audioBitDepth + I1I1lI1II1.a(new byte[]{25, 68, 49, 0, 22, 65, 94, 94, 94, 68, 93, 89, 88, 92, 96, 76, 18, 4, 66, 70, 95, 65}) + strA);
                    listSingletonList = null;
                    str = null;
                    pcmEncoding = -1;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i52222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild2222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track2222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track2222222222222222222222;
                    lllllllli1Track2222222222222222222222.format(iIl1Il11IlBuild2222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                }
                listSingletonList = null;
                str = null;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222222222222222;
                lllllllli1Track22222222222222222222222.format(iIl1Il11IlBuild22222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 25:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 69, 81, 78});
                int i8 = this.audioBitDepth;
                if (i8 == 8) {
                    listSingletonList = null;
                    str = null;
                    i2 = -1;
                    pcmEncoding = 3;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i5222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track222222222222222222222222;
                    lllllllli1Track222222222222222222222222.format(iIl1Il11IlBuild222222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                } else if (i8 == 16) {
                    pcmEncoding = 268435456;
                    listSingletonList = null;
                    str = null;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i52222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild2222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track2222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track2222222222222222222222222;
                    lllllllli1Track2222222222222222222222222.format(iIl1Il11IlBuild2222222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                } else {
                    strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                    lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{98, 10, 17, 16, 18, 69, 88, 66, 77, 1, 84, 16, 87, 80, 83, 21, 7, 15, 6, 91, 81, 15, 19, 101, 112, 123, 23, 7, 13, 70, 23, 7, 82, 20, 22, 13, 88, 21}) + this.audioBitDepth + I1I1lI1II1.a(new byte[]{25, 68, 49, 0, 22, 65, 94, 94, 94, 68, 93, 89, 88, 92, 96, 76, 18, 4, 66, 70, 95, 65}) + strA);
                    listSingletonList = null;
                    str = null;
                    pcmEncoding = -1;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i522222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild22222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track22222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track22222222222222222222222222;
                    lllllllli1Track22222222222222222222222222.format(iIl1Il11IlBuild22222222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                }
                break;
            case 26:
                strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 69, 81, 78});
                if (this.audioBitDepth == 32) {
                    listSingletonList = null;
                    str = null;
                    pcmEncoding = 4;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i5222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track222222222222222222222222222;
                    lllllllli1Track222222222222222222222222222.format(iIl1Il11IlBuild222222222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                } else {
                    strA = I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90});
                    lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{98, 10, 17, 16, 18, 69, 88, 66, 77, 1, 84, 16, 83, 85, 91, 84, 22, 8, 12, 85, 16, 17, 92, 92, 93, 66, 23, 53, 39, Byte.MAX_VALUE, 23, 1, 94, 16, 66, 1, 7, 69, 67, 88, 3, 68}) + this.audioBitDepth + I1I1lI1II1.a(new byte[]{25, 68, 49, 0, 22, 65, 94, 94, 94, 68, 93, 89, 88, 92, 96, 76, 18, 4, 66, 70, 95, 65}) + strA);
                    listSingletonList = null;
                    str = null;
                    pcmEncoding = -1;
                    i2 = -1;
                    if (this.dolbyVisionConfigBytes != null) {
                    }
                    int i52222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                    ilIIl111lI = new IlIIl111lI();
                    if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                    }
                    if (this.name != null) {
                    }
                    IIl1Il11Il iIl1Il11IlBuild2222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                    llllllllI1 lllllllli1Track2222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                    this.output = lllllllli1Track2222222222222222222222222222;
                    lllllllli1Track2222222222222222222222222222.format(iIl1Il11IlBuild2222222222222222222222222222);
                    if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                    }
                }
                break;
            case 27:
                strA = I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 77, 20, 71, 64, 0, 19, 11, 66});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222222222222222222222;
                lllllllli1Track22222222222222222222222222222.format(iIl1Il11IlBuild22222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 28:
                strA = I1I1lI1II1.a(new byte[]{67, 1, 26, 17, 77, 77, 26, 67, 74, 5});
                listSingletonList2 = IIllI1III1.of(I11ll1lIl1.SSA_DIALOGUE_FORMAT, getCodecPrivate(this.codecId));
                str = null;
                listSingletonList = listSingletonList2;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222222222222222222222;
                lllllllli1Track222222222222222222222222222222.format(iIl1Il11IlBuild222222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 29:
                strA = I1I1lI1II1.a(new byte[]{67, 1, 26, 17, 77, 67, 67, 68});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222222222222222222222222;
                lllllllli1Track2222222222222222222222222222222.format(iIl1Il11IlBuild2222222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 30:
                strA = I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 67, 86, 86, 70, 23, 3});
                listSingletonList2 = IIllI1III1.of(getCodecPrivate(this.codecId));
                str = null;
                listSingletonList = listSingletonList2;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i522222222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild22222222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i522222222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track22222222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track22222222222222222222222222222222;
                lllllllli1Track22222222222222222222222222222222.format(iIl1Il11IlBuild22222222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case 31:
                strA = I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 69, 94, 71});
                listSingletonList = null;
                str = null;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i5222222222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild222222222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i5222222222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track222222222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track222222222222222222222222222222222;
                lllllllli1Track222222222222222222222222222222222.format(iIl1Il11IlBuild222222222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            case ' ':
                strA = I1I1lI1II1.a(new byte[]{86, 20, 18, 9, 11, 86, 86, 68, 80, 11, 94, 31, 81, 79, 86, 70, 23, 3, 17});
                byte[] bArr2 = new byte[4];
                System.arraycopy(getCodecPrivate(this.codecId), 0, bArr2, 0, 4);
                listSingletonList2 = IIllI1III1.of(bArr2);
                str = null;
                listSingletonList = listSingletonList2;
                pcmEncoding = -1;
                i2 = -1;
                if (this.dolbyVisionConfigBytes != null) {
                }
                int i52222222222222222222222222222222222 = (this.flagForced ? 2 : 0) | (this.flagDefault ? 1 : 0) | 0;
                ilIIl111lI = new IlIIl111lI();
                if (androidx.media3.common.I1l1IIlI1I.isAudio(strA)) {
                }
                if (this.name != null) {
                }
                IIl1Il11Il iIl1Il11IlBuild2222222222222222222222222222222222 = ilIIl111lI.setId(i).setSampleMimeType(strA).setMaxInputSize(i2).setLanguage(this.language).setSelectionFlags(i52222222222222222222222222222222222).setInitializationData(listSingletonList).setCodecs(str).setDrmInitData(this.drmInitData).build();
                llllllllI1 lllllllli1Track2222222222222222222222222222222222 = ll1iii1lil.track(this.number, i3);
                this.output = lllllllli1Track2222222222222222222222222222222222;
                lllllllli1Track2222222222222222222222222222222222.format(iIl1Il11IlBuild2222222222222222222222222222222222);
                if (IIlI1ll1ll.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 81, 8, 20, 39, 68, 118, 99, 93, 11, 74, 105, 92, 114, 99, 98, 17, 36, 59, 106, 117, 15, 90, 96, 2, 110, 65, 50}), 625230920L)) {
                }
                break;
            default:
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{98, 10, 16, 0, 1, 90, 80, 94, 80, 30, 85, 84, 21, 90, 91, 81, 7, 2, 66, 91, 84, 4, 93, 65, 90, 80, 94, 0, 22, 28}), null);
        }
    }

    public void outputPendingSampleMetadata() throws NoSuchFieldException {
        if (lI1lI11Ill.l1l1l1IIlI(1553)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{85, 80, 22, 52, 36, 92, 102}));
        }
        I1l1IIlI1I i1l1IIlI1I = this.trueHdSampleRechunker;
        if (i1l1IIlI1I != null) {
            i1l1IIlI1I.outputPendingSampleMetadata(this.output, this.cryptoData);
        }
        if (l1lll111II.Il1IIlI1II(1025028522L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{102, 60, 4, 31, 18, 95, 6, 94, 110, 2, 68, 116, 123, 93, 109, 95, 22, 27, 55, 5, 7, 24, 85, 125, 96, 70, 66, 42, 47}));
        }
    }

    public void reset() {
        I1l1IIlI1I i1l1IIlI1I = this.trueHdSampleRechunker;
        if (i1l1IIlI1I != null) {
            i1l1IIlI1I.reset();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean samplesHaveSupplementalData(boolean z) {
        if (lIIlI111II.II1111I11I(7186)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{64, 10, 39, 3, 38, 98, 109, 89, 79, 48, 90, 65, 123, 10, 118}));
        }
        return I1I1lI1II1.a(new byte[]{118, 59, 45, 53, 55, 102}).equals(this.codecId) ? z : this.maxBlockAdditionId > 0;
    }

    private byte[] getHdrStaticInfo() {
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{126, 81, 80}), 350818467L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{113, 20, 15, 42, 4, 116, 92}));
        }
        if (this.primaryRChromaticityX == -1.0f || this.primaryRChromaticityY == -1.0f || this.primaryGChromaticityX == -1.0f || this.primaryGChromaticityY == -1.0f || this.primaryBChromaticityX == -1.0f || this.primaryBChromaticityY == -1.0f || this.whitePointChromaticityX == -1.0f || this.whitePointChromaticityY == -1.0f || this.maxMasteringLuminance == -1.0f || this.minMasteringLuminance == -1.0f) {
            if (IIll1llI1l.Il1IIlI1II(6800)) {
                throw new LinkageError(I1I1lI1II1.a(new byte[]{97, 85, 9, 46, 13}));
            }
            return null;
        }
        byte[] bArr = new byte[25];
        ByteBuffer byteBufferOrder = ByteBuffer.wrap(bArr).order(ByteOrder.LITTLE_ENDIAN);
        byteBufferOrder.put((byte) 0);
        byteBufferOrder.putShort((short) ((this.primaryRChromaticityX * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.primaryRChromaticityY * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.primaryGChromaticityX * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.primaryGChromaticityY * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.primaryBChromaticityX * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.primaryBChromaticityY * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.whitePointChromaticityX * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) ((this.whitePointChromaticityY * 50000.0f) + 0.5f));
        byteBufferOrder.putShort((short) (this.maxMasteringLuminance + 0.5f));
        byteBufferOrder.putShort((short) (this.minMasteringLuminance + 0.5f));
        byteBufferOrder.putShort((short) this.maxContentLuminance);
        byteBufferOrder.putShort((short) this.maxFrameAverageLuminance);
        return bArr;
    }

    private static Pair<String, List<byte[]>> parseFourCcPrivate(III1llIlIl iII1llIlIl) throws l1llI1llII, IllegalAccessException, MalformedURLException, InvalidObjectException, UTFDataFormatException {
        try {
            iII1llIlIl.skipBytes(16);
            long littleEndianUnsignedInt = iII1llIlIl.readLittleEndianUnsignedInt();
            if (littleEndianUnsignedInt == 1482049860) {
                return new Pair<>(I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 83, 89, 79, 28}), null);
            }
            if (littleEndianUnsignedInt == 859189832) {
                return new Pair<>(I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 4, 87, 73, 20}), null);
            }
            if (littleEndianUnsignedInt == 826496599) {
                byte[] data = iII1llIlIl.getData();
                for (int position = iII1llIlIl.getPosition() + 20; position < data.length - 4; position++) {
                    if (data[position] == 0 && data[position + 1] == 0 && data[position + 2] == 1 && data[position + 3] == 15) {
                        Pair<String, List<byte[]>> pair = new Pair<>(I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 64, 70, 90, 85}), Collections.singletonList(Arrays.copyOfRange(data, position, data.length)));
                        if (lIIllIlIl1.Il1IIlI1II(204409410L)) {
                            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{81, 83, 7, 39, 15, 5, 91, 120, 126, 29, 118, 121, 120, 122, 3}));
                        }
                        return pair;
                    }
                }
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 86, 89, 91, 93, 20, 115, 13, 20, 16, 113, 115, 65, 101, 118, 2, 22, 94, 11, 13, 70, 94, 2, 91, 13, 24, 4, 22, 92, 88, 94, 25, 0, 81, 68, 84}), null);
            }
            lIl1llIlll.w(I1I1lI1II1.a(new byte[]{122, 5, 22, 23, 13, 70, 92, 81, 124, 28, 68, 66, 84, 90, 64, 90, 16}), I1I1lI1II1.a(new byte[]{98, 10, 9, 11, 13, 66, 89, 16, Byte.MAX_VALUE, 11, 69, 66, 118, 122, 26, 21, 49, 4, 22, 70, 89, 15, 84, 21, 94, 95, 90, 0, 48, 75, 71, 6, 23, 16, 13, 69, 20, 92, 83, 85, 86, 75, 72, 29, 64, 87, 95, 91, 13, 22, 12}));
            Pair<String, List<byte[]>> pair2 = new Pair<>(I1I1lI1II1.a(new byte[]{65, 13, 6, 0, 13, 26, 79, 29, 76, 10, 91, 94, 90, 78, 90}), null);
            if (!androidx.constraintlayout.widget.lIIlI111II.Il1lII1l1l(5872)) {
                return pair2;
            }
            Log.d(I1I1lI1II1.a(new byte[]{3, 55, 90, 51, 39, 79, 1, 114, 67, 15, 91, 83, 6, 87, 65, 113, 39, 54, 87, 10, 87, 56, 119, 88, 117, 68}), I1I1lI1II1.a(new byte[]{82, 34, 19, 80, 9, 118, 7, 64, 94, 47, 102, 98, 3, 75, 118}));
            return null;
        } catch (ArrayIndexOutOfBoundsException unused) {
            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 114, 90, 23, 19, 33, 113, 16, 17, 65, 92, 69, 87, 67, 0, 68, 86, 86, 23, 86}), null);
        }
    }

    private static List<byte[]> parseVorbisCodecPrivate(byte[] bArr) throws l1llI1llII {
        int i;
        int i2;
        if (II1I11IlI1.l11I11I11l(I1I1lI1II1.a(new byte[]{90, 34, 85, 35, 90, 109, 70, 123, 81, 60, 8, 118, 116, 83, 64, 93, 36, 41, 15, 107, 93, 34, 67, 66, 100, 89, 111, 60, 11, 6}))) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{14}));
        }
        try {
            if (bArr[0] != 2) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 66, 90, 16, 3, 11, 65, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
            }
            int i3 = 0;
            int i4 = 1;
            while (true) {
                i = bArr[i4];
                if ((i & 255) != 255) {
                    break;
                }
                i3 += 255;
                i4++;
            }
            int i5 = i4 + 1;
            int i6 = i3 + (i & 255);
            int i7 = 0;
            while (true) {
                i2 = bArr[i5];
                if ((i2 & 255) != 255) {
                    break;
                }
                i7 += 255;
                i5++;
            }
            int i8 = i5 + 1;
            int i9 = i7 + (i2 & 255);
            if (bArr[i8] != 1) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 66, 90, 16, 3, 11, 65, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
            }
            byte[] bArr2 = new byte[i6];
            System.arraycopy(bArr, i8, bArr2, 0, i6);
            int i10 = i8 + i6;
            if (bArr[i10] != 3) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 66, 90, 16, 3, 11, 65, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
            }
            int i11 = i10 + i9;
            if (bArr[i11] != 5) {
                throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 66, 90, 16, 3, 11, 65, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
            }
            byte[] bArr3 = new byte[bArr.length - i11];
            System.arraycopy(bArr, i11, bArr3, 0, bArr.length - i11);
            ArrayList arrayList = new ArrayList(2);
            arrayList.add(bArr2);
            arrayList.add(bArr3);
            return arrayList;
        } catch (ArrayIndexOutOfBoundsException unused) {
            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 66, 90, 16, 3, 11, 65, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0034  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static boolean parseMsAcmCodecPrivate(III1llIlIl iII1llIlIl) throws l1llI1llII, MalformedURLException {
        boolean z;
        try {
            int littleEndianUnsignedShort = iII1llIlIl.readLittleEndianUnsignedShort();
            if (littleEndianUnsignedShort == 1) {
                return true;
            }
            if (littleEndianUnsignedShort != 65534) {
                return false;
            }
            iII1llIlIl.setPosition(24);
            if (iII1llIlIl.readLong() == I11ll1lIl1.WAVE_SUBFORMAT_PCM.getMostSignificantBits()) {
                z = iII1llIlIl.readLong() == I11ll1lIl1.WAVE_SUBFORMAT_PCM.getLeastSignificantBits();
            }
            if (lIIIIII11I.Il1IIlI1II(3042)) {
                throw new ClassFormatError(I1I1lI1II1.a(new byte[]{94, 84, 16, 86, 21, 64}));
            }
            return z;
        } catch (ArrayIndexOutOfBoundsException unused) {
            throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{114, 22, 16, 10, 16, 21, 71, 81, 75, 23, 89, 94, 82, 25, 121, 102, 77, 32, 33, Byte.MAX_VALUE, 16, 2, 92, 81, 86, 85, 23, 21, 22, 91, 65, 2, 67, 1}), null);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void assertOutputInitialized() throws SyncFailedException {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{109, 21, 45, 83, 87, 69, 115, Byte.MAX_VALUE, 122, 19, 83, 89, 77, 15, 0, 109, 10, 3, 27, 99, 89, 54}), 2595)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{110, 23, 44, 2, 3, 7, 114, 84, 112, 35, 2, 73}));
        }
        ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1.checkNotNull(this.output);
    }

    private byte[] getCodecPrivate(String str) throws l1llI1llII {
        byte[] bArr = this.codecPrivate;
        if (bArr != null) {
            return bArr;
        }
        throw l1llI1llII.createForMalformedContainer(I1I1lI1II1.a(new byte[]{122, 13, 17, 22, 11, 91, 80, 16, 122, 11, 84, 85, 86, 105, 70, 92, 20, 0, 22, 87, 16, 7, 92, 71, 19, 85, 88, 1, 1, 81, 23}) + str, null);
    }
}
