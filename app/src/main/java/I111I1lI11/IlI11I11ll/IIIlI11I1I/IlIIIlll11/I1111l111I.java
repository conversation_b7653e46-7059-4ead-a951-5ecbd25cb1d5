package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.util.Log;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.SyncFailedException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\t\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0006¢\u0006\u0004\b\u0013\u0010\u0014J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\f\u001a\u00020\t8\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\f\u0010\u000bR\u001a\u0010\u000e\u001a\u00020\u00068\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u000e\u0010\bR\u0017\u0010\u0010\u001a\u00020\u00068\u0007¢\u0006\f\n\u0004\b\u0010\u0010\u000f\u001a\u0004\b\u0010\u0010\b"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/I1111l111I;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "a", "Ljava/lang/String;", "b", "I", "c", "p1", "p2", "<init>", "(Ljava/lang/String;II)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class I1111l111I {
    private final String a;
    private final int b;
    private final int c;

    public I1111l111I() {
        this(null, 0, 0, 7, null);
    }

    public boolean equals(Object p0) throws SyncFailedException {
        if (this == p0) {
            return true;
        }
        if (!(p0 instanceof I1111l111I)) {
            return false;
        }
        I1111l111I i1111l111I = (I1111l111I) p0;
        if (!Intrinsics.a((Object) this.a, (Object) i1111l111I.a) || this.b != i1111l111I.b || this.c != i1111l111I.c) {
            return false;
        }
        if (Il11II1llI.I1lllI1llI(471271159L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{0, 17, 47}));
        }
        return true;
    }

    public int hashCode() {
        if (lIIllIlIl1.l1l1Il1I11(I1I1lI1II1.a(new byte[]{70}), 650462377L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{102, 28, 1, 15, 7, 77, 96, 106}));
        }
        return (((this.a.hashCode() * 31) + Integer.hashCode(this.b)) * 31) + Integer.hashCode(this.c);
    }

    public String toString() {
        return I1I1lI1II1.a(new byte[]{101, 1, 15, 10, 20, 80, Byte.MAX_VALUE, 89, 94, 12, 92, 89, 82, 81, 64, 70, 74, 3, 13, 93, 91, 47, 82, 88, 86, 11}) + this.a + I1I1lI1II1.a(new byte[]{27, 68, 1, 13, 3, 69, 67, 85, 75, 42, 69, 93, 87, 92, 70, 8}) + this.b + I1I1lI1II1.a(new byte[]{27, 68, 20, 0, 16, 70, 82, 126, 76, 9, 82, 85, 71, 4}) + this.c + ')';
    }

    public I1111l111I(String str, int i, int i2) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 44, 84, 90, 85}));
        this.a = str;
        this.b = i;
        this.c = i2;
    }

    public /* synthetic */ I1111l111I(String str, int i, int i2, int i3, DefaultConstructorMarker defaultConstructorMarker) {
        this((i3 & 1) != 0 ? "" : str, (i3 & 2) != 0 ? 0 : i, (i3 & 4) != 0 ? 0 : i2);
    }

    public final String a() {
        String str = this.a;
        if (!Il11II1llI.I1II1111ll(I1I1lI1II1.a(new byte[]{100, 17, 24, 34, 86, 64, 14, 86, 104, 48, 89, 125, 89, 91, 102, 108, 45, 21, 5, 0, 90, 42, 73, 108, 114, 94, 5, 53, 32}))) {
            return str;
        }
        Log.i(I1I1lI1II1.a(new byte[]{81, 19, 13, 42, 53, 99}), I1I1lI1II1.a(new byte[]{100, 21, 91}));
        return null;
    }

    /* renamed from: b, reason: from getter */
    public final int getB() {
        return this.b;
    }

    public final int c() {
        if (Il1IIllIll.IIll1I11lI(I1I1lI1II1.a(new byte[]{77, 30, 22, 1, 51, 64, 118, 92, 122, 22, 125, 121, 76, 119, 99, 81, 39, 20, 56, 103, 100, 56, 10}))) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{68, 43, 50, 93, 52, 97, 6, 100, 123, 14, 87, 2, 12, 10, 77, 94, 45, 42, 27, Byte.MAX_VALUE, 125, 57, 86, 67, 112, 80, 123, 53, 52}));
        }
        return this.c;
    }
}
