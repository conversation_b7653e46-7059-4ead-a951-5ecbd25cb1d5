package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.media.content.lIIllIlIl1;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.net.BindException;
import java.net.UnknownHostException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\t\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0006¢\u0006\u0004\b\u0013\u0010\u0014J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\f\u001a\u00020\t8\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\f\u0010\u000bR\u001a\u0010\u000e\u001a\u00020\u00068\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u000e\u0010\bR\u0017\u0010\u0010\u001a\u00020\u00068\u0007¢\u0006\f\n\u0004\b\u0010\u0010\u000f\u001a\u0004\b\u0010\u0010\b"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/llIIl11lIl;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "a", "Ljava/lang/String;", "b", "I", "c", "p1", "p2", "<init>", "(Ljava/lang/String;II)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class llIIl11lIl {
    private final String a;
    private final int b;
    private final int c;

    public llIIl11lIl() {
        this(null, 0, 0, 7, null);
    }

    public boolean equals(Object p0) throws SyncFailedException, UnknownHostException, UTFDataFormatException {
        if (this == p0) {
            if (lIIllIlIl1.I1lIllll1l(306721173L)) {
                throw new SyncFailedException(I1I1lI1II1.a(new byte[]{86, 46, 54, 48, 53, 7, 120, 69, 97, 18, 66, 85, 68, 9, 86, 12}));
            }
            return true;
        }
        if (!(p0 instanceof llIIl11lIl)) {
            return false;
        }
        llIIl11lIl lliil11lil = (llIIl11lIl) p0;
        if (!Intrinsics.a((Object) this.a, (Object) lliil11lil.a)) {
            if (IIlII1IIIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{91, 0, 43, 29, 55, 123, 77, 1, 10, 49, 9, 121, 89, 118, 1, 83, 82, 45, 40, 69, 93, 41, 88, 124, 6, 95, 113, 46, 93, 99, 5}), I1I1lI1II1.a(new byte[]{68, 7, 44, 85, 8, 93, 123, Byte.MAX_VALUE, 95, 48, 70, 97, 108, 11, 97, 122, 32, 3, 49, 89, 2, 37, 91, 67, 82}))) {
                throw new UnknownHostException(I1I1lI1II1.a(new byte[]{118, 87}));
            }
            return false;
        }
        if (this.b != lliil11lil.b || this.c != lliil11lil.c) {
            return false;
        }
        if (Il11II1llI.I1lllI1llI(1756898882L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{99, 47, 53}));
        }
        return true;
    }

    public int hashCode() throws BindException {
        if (lIIlI111II.l111I1ll1l(5783)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{81, 38, 4, 1, 56, 64, 79, 104, 84, 17, 72}));
        }
        return (((this.a.hashCode() * 31) + Integer.hashCode(this.b)) * 31) + Integer.hashCode(this.c);
    }

    public String toString() {
        return I1I1lI1II1.a(new byte[]{101, 1, 15, 10, 20, 80, 121, 95, 77, 1, 67, 24, 87, 86, 91, 94, 44, 0, 15, 87, 13}) + this.a + I1I1lI1II1.a(new byte[]{27, 68, 1, 13, 3, 69, 67, 85, 75, 42, 69, 93, 87, 92, 70, 8}) + this.b + I1I1lI1II1.a(new byte[]{27, 68, 20, 0, 16, 70, 82, 126, 76, 9, 82, 85, 71, 4}) + this.c + ')';
    }

    public llIIl11lIl(String str, int i, int i2) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 44, 84, 90, 85}));
        this.a = str;
        this.b = i;
        this.c = i2;
    }

    public /* synthetic */ llIIl11lIl(String str, int i, int i2, int i3, DefaultConstructorMarker defaultConstructorMarker) {
        this((i3 & 1) != 0 ? "" : str, (i3 & 2) != 0 ? 0 : i, (i3 & 4) != 0 ? 0 : i2);
    }

    public final String a() throws NoSuchAlgorithmException, InstantiationException {
        if (lIIlI111II.llllI1l1II(7533)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{90, 38, 10, 6, 22, 65, 86, 4, 12, 86, 126, 65, 76, 13, 118, 89, 4, 48, 43, 67, 88, 34, 99, Byte.MAX_VALUE, 70, 125, 0, 20, 10, 2}));
        }
        String str = this.a;
        if (ll1l11I1II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{122, 23, 82, 7, 15, 83, 70, 74, 107, 28, 90, 68, 100, 77, 97, 6, 80, 27, 48, 94, 115, 50, 10, 126, 11, 112, 120}), 219502608L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{126, 18, 82, 33, 33, 114, 111}));
        }
        return str;
    }

    public final int b() throws UnrecoverableKeyException {
        int i = this.b;
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.II1lIllIll(3814)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{79, 14, 16, 31, 90, 101, 102, 94, 1, 29, 104, 101, 115, 93, 69, 5, 42, 80, 52, 71, 105, 36, 66, 123, 114}));
        }
        return i;
    }

    public final int c() throws KeyStoreException {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.l1I1ll1lll(6695)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{98, 86, 44, 21, 41, 111, 94, 114, 78, 30, 113, 118}));
        }
        return this.c;
    }
}
