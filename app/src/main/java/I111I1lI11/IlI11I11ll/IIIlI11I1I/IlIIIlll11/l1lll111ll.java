package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.accounts.utils.Ill11ll111;
import android.util.Log;
import androidx.core.location.I1111IIl11;
import androidx.interpolator.view.animation.Il11II1llI;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;
import kotlin.Metadata;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0006¢\u0006\u0004\b\u000e\u0010\u000fJ\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u0011\u0010\f\u001a\u00020\u0006X\u0006¢\u0006\u0006\n\u0004\b\f\u0010\r"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/l1lll111ll;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "a", "I", "<init>", "(I)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class l1lll111ll {
    public final int a;

    public boolean equals(Object p0) throws GeneralSecurityException {
        if (Ill11ll111.l1l1Il1I11(I1I1lI1II1.a(new byte[]{85, 44, 14, 17, 4, 95, 101, 95, 125, 51, 84, 9, 12, 15}), 342809537L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{7, 45, 0, 17, 91, 112, 116, 119, 96, 60, 74, 101, 13, 84, 64, 64, 7, 44, 56, 102, 74, 55}));
        }
        if (this == p0) {
            return true;
        }
        if (!(p0 instanceof l1lll111ll) || this.a != ((l1lll111ll) p0).a) {
            return false;
        }
        if (I1111IIl11.lll1111l11(I1I1lI1II1.a(new byte[]{2, 7, 59, 87, 0, 0, 83, 121, 113, 51, 125, 115, Byte.MAX_VALUE, 112, 86, 97, 0, 84, 9, 81, 98, 88, 92, 12, 102, 123, 100}), 182156441L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{69, 41, 85, 8, 44, 114, 78, 98, 123, 3, 73, 117, 92, 79, 113, 99, 49, 12, 87, 81}));
        }
        return true;
    }

    public int hashCode() {
        return Integer.hashCode(this.a);
    }

    public String toString() {
        String str = I1I1lI1II1.a(new byte[]{103, 8, 3, 11, 55, 69, 83, 81, 77, 1, 117, 70, 80, 87, 64, 29, 18, 13, 3, 92, 121, 5, 14}) + this.a + ')';
        if (!Il11II1llI.l11I11I11l(7326)) {
            return str;
        }
        Log.v(I1I1lI1II1.a(new byte[]{83, 15, 85, 54, 58, 126, 121, 101, 86}), I1I1lI1II1.a(new byte[]{112, 12, 91, 80, 44, 98, 94, 93, 97, 13, 4, 84, 95, 10, 65, 126, 49, 87, 86, 81, 126, 83, 114, 102}));
        return null;
    }

    public l1lll111ll(int i) {
        this.a = i;
    }
}
