package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.media.content.lll1IIII11;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import java.io.EOFException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateEncodingException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\f¢\u0006\u0004\b\u0010\u0010\u0011J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\r\u001a\u00020\f8\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\r\u0010\u000f"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/I11ll1lIl1;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "a", "Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "()Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "<init>", "(Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class I11ll1lIl1 {
    private final WorshipSongsCommonEntity a;

    public boolean equals(Object p0) throws EOFException {
        if (lI11IlI1lI.l1l1Il1I11(I1I1lI1II1.a(new byte[]{102, 41, 14, 7, 51, 116, 92}), 1437724126L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{3, 80, 86, 38, 81, 69}));
        }
        if (this == p0) {
            return true;
        }
        return (p0 instanceof I11ll1lIl1) && Intrinsics.a(this.a, ((I11ll1lIl1) p0).a);
    }

    public int hashCode() {
        return this.a.hashCode();
    }

    public String toString() throws UnrecoverableEntryException {
        if (IllIIIIII1.IlII1Illll(4757)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{0, 34, 20, 60, 45, 121, 15, 6, 72, 17, 116, 89, 118}));
        }
        return I1I1lI1II1.a(new byte[]{96, 11, 16, 22, 10, 92, 71, 96, 85, 5, 73, 117, 67, 92, 90, 65, 74, 3, 7, 83, 94, 92}) + this.a + ')';
    }

    public I11ll1lIl1(WorshipSongsCommonEntity worshipSongsCommonEntity) {
        Intrinsics.checkNotNullParameter(worshipSongsCommonEntity, I1I1lI1II1.a(new byte[]{85, 1, 3, 11}));
        this.a = worshipSongsCommonEntity;
    }

    public final WorshipSongsCommonEntity a() throws CertificateEncodingException {
        if (lll1IIII11.l1l1Il1I11(I1I1lI1II1.a(new byte[]{77, 10, 24, 0, 27, 121, 81, 123, Byte.MAX_VALUE, 33, 104, 86, 111, 85, Byte.MAX_VALUE, 3, 53, 12, 47, 93, 8, 56, 103, 114, 73, 89, 90, 7, 15, 124}), 242833293L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{113, 35, 19, 39, 80, 4, 97, 92, 67}));
        }
        return this.a;
    }
}
