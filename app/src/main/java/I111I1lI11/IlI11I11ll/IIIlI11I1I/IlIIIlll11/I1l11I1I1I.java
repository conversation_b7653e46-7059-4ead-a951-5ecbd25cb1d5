package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.accounts.utils.lIIlI111II;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.constraintlayout.widget.I1IllIll1l;
import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import java.io.UTFDataFormatException;
import java.security.UnrecoverableKeyException;
import java.util.concurrent.BrokenBarrierException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\f¢\u0006\u0004\b\u0010\u0010\u0011J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\r\u001a\u00020\f8\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\r\u0010\u000f"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/I1l11I1I1I;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "a", "Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "()Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "<init>", "(Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class I1l11I1I1I {
    private final WorshipSongsCommonEntity a;

    public boolean equals(Object p0) throws UnrecoverableKeyException, BrokenBarrierException {
        if (IllllI11Il.IlIIl111lI(I1I1lI1II1.a(new byte[]{124, 48, 0, 82, 46, 77, 15, 68, 113, 6}))) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{116, 84, 47, 40, 55, 94, 92, 88, 99, 3, 89, 66, 3, 104, 100, 89, 23, 44, 90, 85, 99, 9, 69, 119, 7, 5, 68}));
        }
        if (this == p0) {
            if (lIIlI111II.IIlI1Il1lI(167021473L)) {
                throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{97, 33, 12, 60, 56, 112, 0, 3, 115, 92, 72, 0, 120, 107, 70, 66, 3, 42}));
            }
            return true;
        }
        if (!(p0 instanceof I1l11I1I1I)) {
            return false;
        }
        if (Intrinsics.a(this.a, ((I1l11I1I1I) p0).a)) {
            return true;
        }
        if (I1IllIll1l.I111IlIl1I(490206161L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{113, 2, 0, 32, 55, 3, 82, 88, Byte.MAX_VALUE, 0, 1, 96, 81, 13, 110}));
        }
        return false;
    }

    public int hashCode() {
        return this.a.hashCode();
    }

    public String toString() {
        String str = I1I1lI1II1.a(new byte[]{96, 11, 16, 22, 10, 92, 71, 118, 88, 18, 95, 66, 92, 77, 81, 112, 20, 4, 12, 70, 24, 3, 86, 84, 93, 11}) + this.a + ')';
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.l11I1Ill11(2557)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{91, 85, 55, 46, 50, Byte.MAX_VALUE, 125, 66, 119, 47, 99, 102, 111, 107}));
        }
        return str;
    }

    public I1l11I1I1I(WorshipSongsCommonEntity worshipSongsCommonEntity) {
        Intrinsics.checkNotNullParameter(worshipSongsCommonEntity, I1I1lI1II1.a(new byte[]{85, 1, 3, 11}));
        this.a = worshipSongsCommonEntity;
    }

    public final WorshipSongsCommonEntity a() throws UTFDataFormatException {
        if (lIIllIlIl1.IlIIlIllI1(I1I1lI1II1.a(new byte[]{118, 82}), I1I1lI1II1.a(new byte[]{98, 5, 90, 28, 84, 6, 7, 84, 108, 83, 2, 117, 12, 64, 98, 77, 59, 7}))) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{100, 11, 53, 46, 41, 0, 64, 67, 104, 87, 5, 81, 68, 84, 70, 123, 51, 24}));
        }
        return this.a;
    }
}
