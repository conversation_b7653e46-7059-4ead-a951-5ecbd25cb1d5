package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.core.location.I111I11Ill;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.security.KeyManagementException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateParsingException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\t\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0006¢\u0006\u0004\b\u0013\u0010\u0014J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\f\u001a\u00020\t8\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\f\u0010\u000bR\u001a\u0010\u000e\u001a\u00020\u00068\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u000e\u0010\bR\u0017\u0010\u0010\u001a\u00020\u00068\u0007¢\u0006\f\n\u0004\b\u0010\u0010\u000f\u001a\u0004\b\u0010\u0010\b"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/lIIIl1Il1l;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "a", "Ljava/lang/String;", "b", "I", "c", "p1", "p2", "<init>", "(Ljava/lang/String;II)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class lIIIl1Il1l {
    private final String a;
    private final int b;
    private final int c;

    public lIIIl1Il1l() {
        this(null, 0, 0, 7, null);
    }

    public boolean equals(Object p0) throws KeyManagementException {
        if (this == p0) {
            if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{125}), 161139033L)) {
                throw new KeyManagementException(I1I1lI1II1.a(new byte[]{1, 47, 80, 9, 86, 76, 94, 121, 10, 85, 4, 68, 0, 107, 82, 65, 55, 10, 48, 0, 94, 54, 116, 87, 121, 124, 118}));
            }
            return true;
        }
        if (!(p0 instanceof lIIIl1Il1l)) {
            return false;
        }
        lIIIl1Il1l liiil1il1l = (lIIIl1Il1l) p0;
        return Intrinsics.a((Object) this.a, (Object) liiil1il1l.a) && this.b == liiil1il1l.b && this.c == liiil1il1l.c;
    }

    public int hashCode() {
        return (((this.a.hashCode() * 31) + Integer.hashCode(this.b)) * 31) + Integer.hashCode(this.c);
    }

    public String toString() {
        String str = I1I1lI1II1.a(new byte[]{101, 1, 15, 10, 20, 80, 117, 95, 86, 15, 93, 81, 71, 82, 28, 87, 13, 14, 9, 124, 81, 12, 86, 8}) + this.a + I1I1lI1II1.a(new byte[]{27, 68, 1, 13, 3, 69, 67, 85, 75, 42, 69, 93, 87, 92, 70, 8}) + this.b + I1I1lI1II1.a(new byte[]{27, 68, 20, 0, 16, 70, 82, 126, 76, 9, 82, 85, 71, 4}) + this.c + ')';
        if (I1I1IIIIl1.I1II1111ll(I1I1lI1II1.a(new byte[]{98, 15, 36, 10, 38, 97, 97, 105, 82, 53, 74, 74}), 2654)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{83, 33, 45, 19, 36, 12, 4, 2}));
        }
        return str;
    }

    public lIIIl1Il1l(String str, int i, int i2) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 44, 84, 90, 85}));
        this.a = str;
        this.b = i;
        this.c = i2;
    }

    public /* synthetic */ lIIIl1Il1l(String str, int i, int i2, int i3, DefaultConstructorMarker defaultConstructorMarker) {
        this((i3 & 1) != 0 ? "" : str, (i3 & 2) != 0 ? 0 : i, (i3 & 4) != 0 ? 0 : i2);
    }

    /* renamed from: a, reason: from getter */
    public final String getA() {
        return this.a;
    }

    public final int b() throws CertificateParsingException {
        if (lIIlI111II.IIlI1Il1lI(160009005L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{92, 10, 46, 80, 1, 76, 77, 87, 124, 14, 99, 4}));
        }
        return this.b;
    }

    public final int c() throws CertificateEncodingException {
        if (I111I11Ill.II1111I11I(I1I1lI1II1.a(new byte[]{122, 16, 4, 38, 50, 96, 124, 92, 124, 14, 103, 1, 69, 107, 83, 5, 43, 38, 59, 103, 115, 13, 112, 90, 1, 116, 71, 60, 61, 66, 103}), I1I1lI1II1.a(new byte[]{1, 14, 38, 1, 53, 126, 92, 65}))) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{70, 33, 9, 4, 4, 5, 77, 115, 79, 42, 85}));
        }
        int i = this.c;
        if (I1I1IIIIl1.l11I11I11l(163859250L)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{96}));
        }
        return i;
    }
}
