package I111I1lI11.IlI11I11ll.IIIlI11I1I.IlIIIlll11;

import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.io.FileNotFoundException;
import java.io.UTFDataFormatException;
import java.io.UnsupportedEncodingException;
import java.security.cert.CertificateException;
import kotlin.Metadata;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0005\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\f¢\u0006\u0004\b\u0010\u0010\u0011J\u001a\u0010\u0004\u001a\u00020\u00032\b\u0010\u0002\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0007\u001a\u00020\u0006HÖ\u0001¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tH×\u0001¢\u0006\u0004\b\n\u0010\u000bR\u0017\u0010\r\u001a\u00020\f8\u0007¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\r\u0010\u000f"}, d2 = {"LI111I1lI11/IlI11I11ll/IIIlI11I1I/IlIIIlll11/I1llllI11I;", "", "p0", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", "", "a", "J", "()J", "<init>", "(J)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final /* data */ class I1llllI11I {
    private final long a;

    public boolean equals(Object p0) throws CertificateException, UTFDataFormatException, FileNotFoundException {
        if (llIl1lII1I.l1l1Il1I11(I1I1lI1II1.a(new byte[]{93, 83, 47, 36, 24, 70, 96, 82, 112, 30, 90, 69, 3, 112, 109, 89, 5, 82, 48, 116, 82, 0}), 606036178L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{71, 54}));
        }
        if (this == p0) {
            return true;
        }
        if (!(p0 instanceof I1llllI11I)) {
            if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{93, 55, 58, 33, 53, 5, 83, 102, 118}), 176306962L)) {
                throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{85, 45, 43, 47}));
            }
            return false;
        }
        if (this.a != ((I1llllI11I) p0).a) {
            return false;
        }
        if (Il11II1llI.I1lllI1llI(264633059L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{112, 28, 8, 35, 46, 5, 103, 86, 15, 37, 121, 86, 115, 106, 5, 64}));
        }
        return true;
    }

    public int hashCode() {
        if (l11Il1lI11.I1II1111ll(7135)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{78, 40, 51, 20, 9, 64, 7, 84, 67, 47, 1, 64, 84, 67, 5, 121, 19, 52, 10, 2, 93, 17}));
        }
        return Long.hashCode(this.a);
    }

    public String toString() {
        return I1I1lI1II1.a(new byte[]{118, 20, 18, 50, 11, 81, 80, 85, 77, 39, 66, 85, 84, 77, 81, 81, 39, 23, 7, 92, 68, 73, 65, 80, 66, 67, 82, 22, 16, 113, 88, 7, 82, 89}) + this.a + ')';
    }

    public I1llllI11I(long j) {
        this.a = j;
    }

    public final long a() throws UnsupportedEncodingException {
        if (lIlIII1I1l.l11I11I11l(2207)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{100, 46, 59, 47, 43, 124, 80, 92, 64, 32, 120}));
        }
        long j = this.a;
        if (lIIlI111II.llIIIl11I1(217815383L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{81, 12, 42, 13, 50, 6, 124, 121, 116, 33, 120, 4, 120, 126, 93, 115, 19, 23, 23, 87}));
        }
        return j;
    }
}
