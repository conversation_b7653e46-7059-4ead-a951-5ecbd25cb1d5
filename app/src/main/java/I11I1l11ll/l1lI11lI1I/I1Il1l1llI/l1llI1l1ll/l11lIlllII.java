package I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll;

import android.media.content.IIl1l1IllI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.net.UnknownServiceException;
import java.security.ProviderException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public enum l11lIlllII {
    BEGIN_ARRAY,
    END_ARRAY,
    BEGIN_OBJECT,
    END_OBJECT,
    NAME,
    STRING,
    NUMBER,
    BOOLEAN,
    NULL,
    END_DOCUMENT;

    public static l11lIlllII valueOf(String str) {
        l11lIlllII l11lilllii = (l11lIlllII) Enum.valueOf(l11lIlllII.class, str);
        if (IIlI1ll1ll.IlIIl111lI(I1I1lI1II1.a(new byte[]{103, 49, 91, 93, 46, 71, 116, 8, 86, 54, 83, 1, 89}), 9233)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{113, 2, 3, 84, 38}));
        }
        return l11lilllii;
    }

    /* renamed from: values, reason: to resolve conflict with enum method */
    public static l11lIlllII[] valuesCustom() throws UnknownServiceException {
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{1, 54, 0, 8, 21, 87, 94, 116, 96, 18, 101, 74, 125, 117, 77, 90, 46, 2, 56, 123, 2, 4, 91, 96, 107, 121, 114, 9, 86, 81}), 262458751L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{110, 5, 14, 14, 10, 4, 115, 4, 106, 39, 81, 116, 70, 83, 109}));
        }
        return (l11lIlllII[]) values().clone();
    }
}
