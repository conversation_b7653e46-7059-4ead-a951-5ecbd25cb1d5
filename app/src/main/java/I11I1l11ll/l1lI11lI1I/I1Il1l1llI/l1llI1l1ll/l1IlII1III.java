package I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll;

import android.support.v4.graphics.drawable.IlIIlI11I1;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class l1IlII1III {
    static String a(int i, int[] iArr, String[] strArr, int[] iArr2) {
        if (IlIIlI11I1.I1lllI1llI(327047447L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{114, 82, 7, 9, 87, 125, 90, 120, 88, 42, 114, 81, 67, 1, 64, 124, 23, 21}));
        }
        StringBuilder sb = new StringBuilder("$");
        for (int i2 = 0; i2 < i; i2++) {
            int i3 = iArr[i2];
            if (i3 == 1 || i3 == 2) {
                sb.append('[').append(iArr2[i2]).append(']');
            } else if (i3 == 3 || i3 == 4 || i3 == 5) {
                sb.append('.');
                String str = strArr[i2];
                if (str != null) {
                    sb.append(str);
                }
            }
        }
        return sb.toString();
    }
}
