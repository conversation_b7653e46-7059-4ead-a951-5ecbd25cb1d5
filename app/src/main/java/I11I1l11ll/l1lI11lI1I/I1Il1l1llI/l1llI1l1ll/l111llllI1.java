package I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll;

import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.I1Il1I11I1;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.III1l1I11I;
import android.media.content.lll1IIII11;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.Closeable;
import java.io.IOException;
import java.io.StreamCorruptedException;
import java.util.Arrays;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class l111llllI1 implements Closeable {
    private static final String[] g = new String[128];
    int a;
    int[] b = new int[32];
    String[] c = new String[32];
    int[] d = new int[32];
    boolean e;
    boolean f;

    public abstract int a(l11Il1l11I l11il1l11i) throws IOException;

    public abstract void a() throws IOException;

    public abstract void b() throws IOException;

    public abstract void c() throws IOException;

    public abstract void d() throws IOException;

    public abstract boolean e() throws IOException;

    public abstract l11lIlllII f() throws IOException;

    public abstract String g() throws IOException;

    public abstract void h() throws IOException;

    public abstract String i() throws IOException;

    public abstract boolean j() throws IOException;

    public abstract double k() throws IOException;

    public abstract int l() throws IOException;

    public abstract void m() throws IOException;

    static /* synthetic */ void a(I1Il1I11I1 i1Il1I11I1, String str) throws IOException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{67, 47, 10, 6, 80, Byte.MAX_VALUE, 65}), 437692796L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{121, 51, 43, 53, 55, 80, 122, 105, 15, 92, 87, 9, 121, 76, 99, 67}));
        }
        b(i1Il1I11I1, str);
        if (lll1IIII11.l1ll11I11l(I1I1lI1II1.a(new byte[]{113, 18}))) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{123, 3, 15, 20, 82, 70, 88, 98, 93, 23, 83, 117, 83, 119, 91, 84, 0, 2, 1, 96, 74, 41, 114, 121, 117, 123}));
        }
    }

    static {
        for (int i = 0; i <= 31; i++) {
            g[i] = String.format(I1I1lI1II1.a(new byte[]{107, 17, 71, 85, 86, 77}), Integer.valueOf(i));
        }
        String[] strArr = g;
        strArr[34] = I1I1lI1II1.a(new byte[]{107, 70});
        strArr[92] = I1I1lI1II1.a(new byte[]{107, 56});
        strArr[9] = I1I1lI1II1.a(new byte[]{107, 16});
        strArr[8] = I1I1lI1II1.a(new byte[]{107, 6});
        strArr[10] = I1I1lI1II1.a(new byte[]{107, 10});
        strArr[13] = I1I1lI1II1.a(new byte[]{107, 22});
        strArr[12] = I1I1lI1II1.a(new byte[]{107, 2});
    }

    public static l111llllI1 a(III1l1I11I iII1l1I11I) {
        return new I1IIIlllll(iII1l1I11I);
    }

    l111llllI1() {
    }

    final void a(int i) {
        int i2 = this.a;
        int[] iArr = this.b;
        if (i2 == iArr.length) {
            if (i2 == 256) {
                throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{121, 1, 17, 17, 11, 91, 80, 16, 77, 11, 95, 16, 81, 92, 81, 69, 66, 0, 22, 18}) + n());
            }
            this.b = Arrays.copyOf(iArr, iArr.length * 2);
            String[] strArr = this.c;
            this.c = (String[]) Arrays.copyOf(strArr, strArr.length * 2);
            int[] iArr2 = this.d;
            this.d = Arrays.copyOf(iArr2, iArr2.length * 2);
        }
        int[] iArr3 = this.b;
        int i3 = this.a;
        this.a = i3 + 1;
        iArr3[i3] = i;
        if (lI1lllIII1.IlII1Illll(2814)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{92, 32, 46, 81, 11, 97, 101, 7, 78, 0, 86, 88, 80, 72, 94, 7, 12, 6, 18, 4, 103, 22, 86, 3, 124, 111, 115, 8, 14, 98, 92, 37}));
        }
    }

    final ll1ll1I1I1 a(String str) throws ll1ll1I1I1 {
        throw new ll1ll1I1I1(str + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
    }

    public final String n() throws InstantiationException {
        String strA = l1IlII1III.a(this.a, this.b, this.c, this.d);
        if (lIIlI111II.lII11llIIl(1714047693L)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{120, 29, 36, 47, 22, 66, 112, 89, 105, 3, Byte.MAX_VALUE, 113, 95, 65, 93, 89, 54, 52, 45, Byte.MAX_VALUE, 89, 23}));
        }
        return strA;
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x003a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static void b(I1Il1I11I1 i1Il1I11I1, String str) throws IOException {
        String strA;
        String[] strArr = g;
        i1Il1I11I1.h(34);
        int length = str.length();
        int i = 0;
        for (int i2 = 0; i2 < length; i2++) {
            char cCharAt = str.charAt(i2);
            if (cCharAt < 128) {
                strA = strArr[cCharAt];
                if (strA != null) {
                    if (i < i2) {
                        i1Il1I11I1.b(str, i, i2);
                    }
                    i1Il1I11I1.b(strA);
                    i = i2 + 1;
                }
            } else {
                if (cCharAt == 8232) {
                    strA = I1I1lI1II1.a(new byte[]{107, 17, 80, 85, 80, 13});
                } else if (cCharAt == 8233) {
                    strA = I1I1lI1II1.a(new byte[]{107, 17, 80, 85, 80, 12});
                }
                if (i < i2) {
                }
                i1Il1I11I1.b(strA);
                i = i2 + 1;
            }
        }
        if (i < length) {
            i1Il1I11I1.b(str, i, length);
        }
        i1Il1I11I1.h(34);
        if (Il11II1llI.I111IlIl1I(120)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{110, 41, 20, 49, 0, 123, 6, 101}));
        }
    }
}
