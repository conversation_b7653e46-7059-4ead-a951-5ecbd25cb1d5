package I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll;

import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.III1l1I11I;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.lIIllIIlll;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.ll1l1Illl1;
import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIIIII11I;
import android.accounts.utils.lIIlI111II;
import android.media.content.II1I11IlI1;
import android.media.content.IIl1l1IllI;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.util.Log;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1111IIl11;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.StreamCorruptedException;
import java.net.BindException;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CRLException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateExpiredException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CancellationException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class I1IIIlllll extends l111llllI1 {
    private static final lIIllIIlll g = lIIllIIlll.encodeUtf8(I1I1lI1II1.a(new byte[]{16, 56}));
    private static final lIIllIIlll h = lIIllIIlll.encodeUtf8(I1I1lI1II1.a(new byte[]{21, 56}));
    private static final lIIllIIlll i = lIIllIIlll.encodeUtf8(I1I1lI1II1.a(new byte[]{76, 25, 57, 56, 88, 25, 23, 58, 48, 105, 60, 31, 105, 2, 23, 8}));
    private static final lIIllIIlll j = lIIllIIlll.encodeUtf8("\n\r");
    private static final lIIllIIlll k = lIIllIIlll.encodeUtf8(I1I1lI1II1.a(new byte[]{29, 75}));
    private final III1l1I11I l;
    private final ll1l1Illl1 m;
    private int n = 0;
    private long o;
    private int p;
    private String q;

    I1IIIlllll(III1l1I11I iII1l1I11I) {
        if (iII1l1I11I == null) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{68, 11, 23, 23, 1, 80, 23, 13, 4, 68, 94, 69, 89, 85}));
        }
        this.l = iII1l1I11I;
        this.m = iII1l1I11I.b();
        a(6);
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void a() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 3) {
            a(1);
            this.d[this.a - 1] = 0;
            this.n = 0;
            if (I111lIl11I.lll1111l11(I1I1lI1II1.a(new byte[]{80, 33, 36, 1, 40, 80, 124, 116, 107, 2, 81, 93, 88, 109, 71, 94, 32, 87}), 2338)) {
                throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{90, 21, 90, 33, 82, 102, 117, 87, 75, 35, Byte.MAX_VALUE, 73, 70, 10, 100, 96, 8, 42, 56, 121}));
            }
            return;
        }
        throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 38, 117, 119, 124, 119, 107, 116, 48, 51, 35, 107, 16, 3, 70, 65, 19, 65, 86, 22, 68}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void b() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 4) {
            this.a--;
            int[] iArr = this.d;
            int i2 = this.a - 1;
            iArr[i2] = iArr[i2] + 1;
            this.n = 0;
            return;
        }
        throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 33, 126, 116, 106, 120, 102, 103, 35, 56, 66, 80, 69, 21, 19, 66, 82, 69, 23}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void c() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, KeyManagementException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (II1lllllI1.l1l1Il1I11(I1I1lI1II1.a(new byte[]{114, 51, 42, 39, 0, 99, 15, 124, 74, 40, 70, 73, 83, 96, 93, 125, 38, 4, 11, 72, 115, 86, 88, 124, 119, 64, 120}), I1I1lI1II1.a(new byte[]{92, 43, 53, 32, 58, 87}))) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{120, 87, 4, 8, 43, 124}));
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO != 1) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 38, 117, 119, 124, 119, 107, 122, 32, 43, 39, 113, 100, 65, 81, 64, 71, 22, 64, 4, 23, 18}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
        a(3);
        this.n = 0;
        if (IIlI1ll1ll.I1II1111ll(196257694L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{86, 32, 18, 36, 3, 123, 5, 1, 84, 32, 1, 5, 81}));
        }
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void d() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (lI11IlI1lI.I11II1I1I1(I1I1lI1II1.a(new byte[]{114, 55, 85, 9, 55, 80, 65, 65, 75, 61, 103, 102, 109}), 9923)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{67, 28, 15}));
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO != 2) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 33, 126, 116, 106, 118, 118, Byte.MAX_VALUE, 39, 34, 54, 18, 82, 20, 71, 21, 68, 87, 68, 69}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
        this.a--;
        this.c[this.a] = null;
        int[] iArr = this.d;
        int i2 = this.a - 1;
        iArr[i2] = iArr[i2] + 1;
        this.n = 0;
        if (lIIIIII11I.I1lllI1llI(585437815L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{6, 13, 9, 80, 82, 123, 77, 67, 96, 12, 65, 113, 123, 80, 81, 111, 13, 18, 38, 1, 93, 13, 95, 3, 11}));
        }
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public boolean e() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        return (iO == 2 || iO == 4 || iO == 18) ? false : true;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public l11lIlllII f() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        switch (iO) {
            case 1:
                return l11lIlllII.BEGIN_OBJECT;
            case 2:
                return l11lIlllII.END_OBJECT;
            case 3:
                return l11lIlllII.BEGIN_ARRAY;
            case 4:
                l11lIlllII l11lilllii = l11lIlllII.END_ARRAY;
                if (l11Il1lI11.l11I11I11l(I1I1lI1II1.a(new byte[]{1, 0, 84, 63, 11, 2, 114, 82, 13, 11, 104, 86, 115, 88, 115, 88, 6, 32}))) {
                    throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{5, 45, 35, 42, 52, 95, 91, 92, 73, 14, 84, 69, 0, 88, 120, 112, 50, 59}));
                }
                return l11lilllii;
            case 5:
            case 6:
                return l11lIlllII.BOOLEAN;
            case 7:
                return l11lIlllII.NULL;
            case 8:
            case 9:
            case 10:
            case 11:
                l11lIlllII l11lilllii2 = l11lIlllII.STRING;
                if (lIIlI111II.I11II1111l(204910063L)) {
                    throw new NumberFormatException(I1I1lI1II1.a(new byte[]{84, 48, 26, 20, 81, 79, 5, 87, 75, 52, 1, 96, 109, 82, 92, 4, 12, 41, 87, 89, 105, 34, 124, 87, 71, 119, 112, 31, 87, 6}));
                }
                return l11lilllii2;
            case 12:
            case 13:
            case 14:
            case 15:
                l11lIlllII l11lilllii3 = l11lIlllII.NAME;
                if (androidx.interpolator.view.animation.lIIlI111II.lIll1IIl11(3933)) {
                    throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{68, 15, 90, 34, 13, 122, 97, 118, 126, 32, 103, 91, 7, 111}));
                }
                return l11lilllii3;
            case 16:
            case 17:
                return l11lIlllII.NUMBER;
            case 18:
                return l11lIlllII.END_DOCUMENT;
            default:
                throw new AssertionError();
        }
    }

    private int o() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.Il11lIlI1I(9260)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{89, 32, 26, 7, 37, 101, 115, 74, 84, 3, 96, 124, 126, 118, 95, 120, 52, 14, 5, 75, 81, 37, 100, 108, 125, 64, 1, 4, 81}));
        }
        int i2 = this.b[this.a - 1];
        if (i2 == 1) {
            this.b[this.a - 1] = 2;
        } else if (i2 == 2) {
            int iA = a(true);
            this.m.k();
            if (iA != 44) {
                if (iA != 59) {
                    if (iA != 93) {
                        throw a(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 88, 70, 71, 3, 24}));
                    }
                    this.n = 4;
                    if (IIlI1Il1lI.Il1IIlI1II(1662155993L)) {
                        throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{122, 2, 44, 84, 11, 4, 68, 100, 82, 37, 100}));
                    }
                    return 4;
                }
                t();
            }
        } else {
            if (i2 == 3 || i2 == 5) {
                this.b[this.a - 1] = 4;
                if (i2 == 5) {
                    int iA2 = a(true);
                    this.m.k();
                    if (iA2 != 44) {
                        if (iA2 != 59) {
                            if (iA2 != 125) {
                                throw a(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 86, 86, 95, 7, 2, 22}));
                            }
                            this.n = 2;
                            if (l1lI1I1l11.IlIIl111lI(I1I1lI1II1.a(new byte[]{109, 17, 8, 47, 33, 12, 112, 100, 94, 60, 95, 64, 71, 64, 126, 88, 11, 36, 8, 71}), 9998)) {
                                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{69, 49, 45, 48, 17, 1, 65, 64, 67, 80, 104, 103, 115, 117, 77}));
                            }
                            return 2;
                        }
                        t();
                    }
                }
                int iA3 = a(true);
                if (iA3 == 34) {
                    this.m.k();
                    this.n = 13;
                    if (llIlII1IlI.I11II1I1I1(I1I1lI1II1.a(new byte[]{91, 87, 54, 34, 41, 0, 97}))) {
                        throw new ClassFormatError(I1I1lI1II1.a(new byte[]{109, 21, 1, 82, 54, 80, 111, 97, 78, 54, 64, 103, 90, 83, 4, 116, 41, 55, 16, 120}));
                    }
                    return 13;
                }
                if (iA3 == 39) {
                    this.m.k();
                    t();
                    this.n = 12;
                    if (IIl1l1IllI.l11I11I11l(I1I1lI1II1.a(new byte[]{84, 2, 10, 11, 90, 91, 86, 104, 81, 3, 3, 3, 93, 9, 12, 1, 42, 20}), 10685)) {
                        throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{94}));
                    }
                    return 12;
                }
                if (iA3 != 125) {
                    t();
                    if (!b((char) iA3)) {
                        throw a(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 10, 81, 93, 80}));
                    }
                    this.n = 14;
                    if (I1111IIl11.IlII1Illll(327901994L)) {
                        throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{82, 48, 58, 10, 5, 120, 99, 6, 95, 15, 71, 68}));
                    }
                    return 14;
                }
                if (i2 == 5) {
                    throw a(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 10, 81, 93, 80}));
                }
                this.m.k();
                this.n = 2;
                if (IlIIlI11I1.IlII1Illll(181460326L)) {
                    throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{4, 0, 36, 34, 32, 84, 90, 6, 107, 38, 124, 7}));
                }
                return 2;
            }
            if (i2 == 4) {
                this.b[this.a - 1] = 5;
                int iA4 = a(true);
                this.m.k();
                if (iA4 != 58) {
                    if (iA4 != 61) {
                        throw a(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 67, 10, 23}));
                    }
                    t();
                    if (this.l.c(1L) && this.m.d(0L) == 62) {
                        this.m.k();
                    }
                }
            } else if (i2 == 6) {
                this.b[this.a - 1] = 7;
            } else if (i2 == 7) {
                if (a(false) == -1) {
                    this.n = 18;
                    return 18;
                }
                t();
            } else if (i2 == 8) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 48, 80, 86, 84, 92, 22, 16, 89, 70, 25, 87, 89, 13, 18, 7, 86}));
            }
        }
        int iA5 = a(true);
        if (iA5 == 34) {
            this.m.k();
            this.n = 9;
            return 9;
        }
        if (iA5 == 39) {
            t();
            this.m.k();
            this.n = 8;
            return 8;
        }
        if (iA5 != 44 && iA5 != 59) {
            if (iA5 == 91) {
                this.m.k();
                this.n = 3;
                if (II1lllllI1.IIll1I11lI(I1I1lI1II1.a(new byte[]{115, 34, 47, 2, 3, 77, 118, 117, 77, 3, 90, 100, 69, 65, 113, 100, 16, 48, 5, 70, 124, 51, 100, 115, 80, 87, 113, 39, 3, 5, 15}), 3563)) {
                    throw new CRLException(I1I1lI1II1.a(new byte[]{86, 16, 53, 12, 12, 123}));
                }
                return 3;
            }
            if (iA5 != 93) {
                if (iA5 == 123) {
                    this.m.k();
                    this.n = 1;
                    if (llIlI11III.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{64, 30, 18, 10, 44, 124, 116, 123, 85, 53, 116, 113, 94, 88, 89, 97, 15, 88, 18, 83, 91, 44, 95, 111, 10, 82, 109, 45, 80, 96}), 7091)) {
                        throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{122, 42, 37, 82, 32, 68, 2, 5, 9, 47, 3, 7, 113, 96, 126, 91, 8, 6, 55, 126, 96, 19, 123, 125, 89, 78, 116, 28}));
                    }
                    return 1;
                }
                int iP = p();
                if (iP != 0) {
                    return iP;
                }
                int iQ = q();
                if (iQ != 0) {
                    return iQ;
                }
                if (!b(this.m.d(0L))) {
                    throw a(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 18, 81, 92, 64, 92}));
                }
                t();
                this.n = 10;
                return 10;
            }
            if (i2 == 1) {
                this.m.k();
                this.n = 4;
                if (I111lIl11I.l1ll11I11l(I1I1lI1II1.a(new byte[]{88, 34, 5, 2, 90, 80, 85, 67, 78, 42, 97, 0, 116, 120, 121, 118, 15, 17, 23, 1, 83, 48, 4}))) {
                    throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{98, 83, 39, 44, 13, 4, 77, 97, 106, 45, 0, 7, 120, 82, 77, 101, 42, 18, 6, 85, 120, 42, 71, 86, 82, 96, 7, 55}));
                }
                return 4;
            }
        }
        if (i2 != 1 && i2 != 2) {
            throw a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 70, 84, 85, 65, 80}));
        }
        t();
        this.n = 7;
        return 7;
    }

    private int p() throws NoSuchAlgorithmException, IOException {
        String strA;
        String strA2;
        byte bD = this.m.d(0L);
        int i2 = 5;
        if (bD == 116 || bD == 84) {
            strA = I1I1lI1II1.a(new byte[]{67, 22, 23, 0});
            strA2 = I1I1lI1II1.a(new byte[]{99, 54, 55, 32});
        } else if (bD == 102 || bD == 70) {
            strA = I1I1lI1II1.a(new byte[]{81, 5, 14, 22, 7});
            strA2 = I1I1lI1II1.a(new byte[]{113, 37, 46, 54, 39});
            i2 = 6;
        } else {
            if (bD != 110 && bD != 78) {
                return 0;
            }
            strA = I1I1lI1II1.a(new byte[]{89, 17, 14, 9});
            strA2 = I1I1lI1II1.a(new byte[]{121, 49, 46, 41});
            i2 = 7;
        }
        int length = strA.length();
        int i3 = 1;
        while (i3 < length) {
            int i4 = i3 + 1;
            if (!this.l.c(i4)) {
                return 0;
            }
            byte bD2 = this.m.d(i3);
            if (bD2 != strA.charAt(i3) && bD2 != strA2.charAt(i3)) {
                return 0;
            }
            i3 = i4;
        }
        if (this.l.c(length + 1) && b(this.m.d(length))) {
            if (llIlII1IlI.llII1lIIlI(225532591L)) {
                throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{125, 11, 55, 0, 39, 93, 4, 88, 11, 50, 64, 86, 126, 8, 69, 126, 3, 32, 33, Byte.MAX_VALUE}));
            }
            return 0;
        }
        this.m.i(length);
        this.n = i2;
        return i2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:46:0x0081, code lost:
    
        if (b(r11) != false) goto L72;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x0083, code lost:
    
        if (r6 != 2) goto L63;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x0085, code lost:
    
        if (r7 == false) goto L63;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x008b, code lost:
    
        if (r8 != Long.MIN_VALUE) goto L52;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x008d, code lost:
    
        if (r10 == false) goto L63;
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x0091, code lost:
    
        if (r8 != 0) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0093, code lost:
    
        if (r10 != false) goto L63;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0095, code lost:
    
        if (r10 == false) goto L57;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x0098, code lost:
    
        r8 = -r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0099, code lost:
    
        r16.o = r8;
        r16.m.i(r5);
        r16.n = 16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x00b7, code lost:
    
        if (android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{84, 23, 52, 61, 33, 4, 83, 64, 77, 12, 1, 101, 118, 64, 78, 7, 42, 49, 19, 10, 2, 53, 87, 113, 84, 93, 122, 54, 12, 4, 101, 20}), 621251373) != false) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x00b9, code lost:
    
        return 16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x00ca, code lost:
    
        throw new java.lang.NumberFormatException(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{86, 3, 38, 22, 7, 121, 78, 117, 96}));
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x00cb, code lost:
    
        if (r6 == 2) goto L70;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x00ce, code lost:
    
        if (r6 == 4) goto L70;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x00d1, code lost:
    
        if (r6 != 7) goto L69;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x00d4, code lost:
    
        return 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x00d5, code lost:
    
        r16.p = r5;
        r16.n = 17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x00db, code lost:
    
        return 17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x00dc, code lost:
    
        return 0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private int q() throws NoSuchAlgorithmException, IOException {
        char c;
        boolean z = true;
        long j2 = 0;
        int i2 = 0;
        char c2 = 0;
        boolean z2 = false;
        boolean z3 = true;
        while (true) {
            int i3 = i2 + 1;
            if (!this.l.c(i3)) {
                break;
            }
            byte bD = this.m.d(i2);
            if (bD != 43) {
                if (bD == 69 || bD == 101) {
                    if (c2 != 2 && c2 != 4) {
                        if (IIIlIl1I1l.l11I11I11l(1075213516L)) {
                            Log.w(I1I1lI1II1.a(new byte[]{78, 33}), I1I1lI1II1.a(new byte[]{126, 44, 9, 2, 11, 94, 116, 89, 12, 50, 86}));
                        }
                        return 0;
                    }
                    c2 = 5;
                } else if (bD == 45) {
                    c = 6;
                    if (c2 == 0) {
                        c2 = 1;
                        z2 = true;
                    } else if (c2 != 5) {
                        return 0;
                    }
                } else if (bD != 46) {
                    if (bD < 48 || bD > 57) {
                        break;
                    }
                    if (c2 == z || c2 == 0) {
                        j2 = -(bD - 48);
                        c2 = 2;
                    } else if (c2 == 2) {
                        if (j2 == 0) {
                            return 0;
                        }
                        long j3 = (10 * j2) - (bD - 48);
                        z3 &= j2 > -922337203685477580L || (j2 == -922337203685477580L && j3 < j2);
                        j2 = j3;
                    } else if (c2 == 3) {
                        c2 = 4;
                    } else if (c2 == 5 || c2 == 6) {
                        c2 = 7;
                    }
                } else {
                    if (c2 != 2) {
                        return 0;
                    }
                    c2 = 3;
                }
                i2 = i3;
                z = true;
            } else {
                c = 6;
                if (c2 != 5) {
                    return 0;
                }
            }
            c2 = c;
            i2 = i3;
            z = true;
        }
    }

    private boolean b(int i2) throws IOException {
        if (lI1l1I1l1l.l1l1l1IIlI(190532359L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{121, 43, 6, 81, 6, 93, 6, 122, 12, 2, 99, Byte.MAX_VALUE, 97, 112, 82, 88, 52, 47, 10, 113, 91, 18, 126}));
        }
        if (i2 == 9 || i2 == 10 || i2 == 12 || i2 == 13 || i2 == 32) {
            return false;
        }
        if (i2 != 35) {
            if (i2 == 44) {
                return false;
            }
            if (i2 != 47 && i2 != 61) {
                if (i2 == 123 || i2 == 125 || i2 == 58) {
                    return false;
                }
                if (i2 != 59) {
                    switch (i2) {
                        case 91:
                        case 93:
                            return false;
                        case 92:
                            break;
                        default:
                            if (I1I1IIIIl1.l1l1l1IIlI(103)) {
                                throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{1, 35, 58, 15, 5, 3, 69, 102, 104, 61, 90, 82, 90, 83, 78, 69, 43, 25, 11, 92, 121, 85, 99, 100, 4, 93, 118, 51, 16}));
                            }
                            return true;
                    }
                }
            }
        }
        t();
        return false;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public String g() throws NoSuchAlgorithmException, UnrecoverableKeyException, TimeoutException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        String strA;
        if (IIlII1IIIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{98, 15, 41, 45, 20, 102, 69, 67, 99, 84, 91, 106, 121, 108, 64, 4, 12, 21, 6, 2}), I1I1lI1II1.a(new byte[]{65}))) {
            throw new BindException(I1I1lI1II1.a(new byte[]{2, 33, 38, 20, 59, 71, 115, 106, 83, 11, 65, 115, 83, 96, 0, 7, 55, 46, 37, 1}));
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 14) {
            strA = r();
        } else if (iO == 13) {
            strA = a(h);
        } else if (iO == 12) {
            strA = a(g);
        } else {
            if (iO != 15) {
                throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 94, 84, 84, 81, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
            }
            strA = this.q;
        }
        this.n = 0;
        this.c[this.a - 1] = strA;
        return strA;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public int a(l11Il1l11I l11il1l11i) throws NoSuchAlgorithmException, UnrecoverableKeyException, TimeoutException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO < 12 || iO > 15) {
            return -1;
        }
        if (iO == 15) {
            int iA = a(this.q, l11il1l11i);
            if (IIlI1ll1ll.l111l1I1Il(I1I1lI1II1.a(new byte[]{93, 48, 80, 31, 6, 70, 94, 92, 95, 34, 122, 102, 82, 81, 81, 97, 48, 57, 41, 95, 89, 0, 87, 76, 98, 117, 97, 48, 1}), 276720567L)) {
                throw new StackOverflowError(I1I1lI1II1.a(new byte[]{82, 83}));
            }
            return iA;
        }
        int iA2 = this.l.a(l11il1l11i.b);
        if (iA2 != -1) {
            this.n = 0;
            this.c[this.a - 1] = l11il1l11i.a[iA2];
            return iA2;
        }
        String str = this.c[this.a - 1];
        String strG = g();
        int iA3 = a(strG, l11il1l11i);
        if (iA3 == -1) {
            this.n = 15;
            this.q = strG;
            this.c[this.a - 1] = str;
        }
        if (Il1I1lllIl.l111IIlII1(I1I1lI1II1.a(new byte[]{110, 86, 45, 48, 91, 109, 103, 0, 107, 30, 7, 4}), 226784417L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{95, 52, 14, 63, 87, 7, 79}));
        }
        return iA3;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void h() throws CertPathBuilderException, NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (this.f) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 67, 82, 13, 64, 16, 64, 87, 81, 77, 18, 4, 1, 70, 85, 5, 19}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69}) + n());
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 14) {
            s();
        } else if (iO == 13) {
            b(h);
        } else if (iO == 12) {
            b(g);
        } else if (iO != 15) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 94, 84, 84, 81, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
        this.n = 0;
        this.c[this.a - 1] = I1I1lI1II1.a(new byte[]{89, 17, 14, 9});
    }

    private int a(String str, l11Il1l11I l11il1l11i) throws UnknownHostException {
        int length = l11il1l11i.a.length;
        for (int i2 = 0; i2 < length; i2++) {
            if (str.equals(l11il1l11i.a[i2])) {
                this.n = 0;
                this.c[this.a - 1] = str;
                return i2;
            }
        }
        if (l1lI1I1l11.Ill1lIIlIl(8461)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{86, 11, 43, 51, 58}));
        }
        return -1;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public String i() throws SignatureException, TimeoutException, IOException, BrokenBarrierException, ReflectiveOperationException, CertificateExpiredException, InvalidAlgorithmParameterException, NoSuchAlgorithmException, UnrecoverableKeyException, InvalidKeyException, CRLException {
        String strF;
        if (I1I1IIIIl1.IlII1Illll(728097341L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{124, 87, 21, 2, 46}));
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 10) {
            strF = r();
        } else if (iO == 9) {
            strF = a(h);
        } else if (iO == 8) {
            strF = a(g);
        } else if (iO == 11) {
            strF = this.q;
            this.q = null;
        } else if (iO == 16) {
            strF = Long.toString(this.o);
        } else {
            if (iO != 17) {
                throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 67, 65, 75, 93, 91, 5, 65, 0, 71, 68, 65, 68, 84, 64, 22}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
            }
            strF = this.m.f(this.p);
        }
        this.n = 0;
        int[] iArr = this.d;
        int i2 = this.a - 1;
        iArr[i2] = iArr[i2] + 1;
        return strF;
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public boolean j() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 5) {
            this.n = 0;
            int[] iArr = this.d;
            int i2 = this.a - 1;
            iArr[i2] = iArr[i2] + 1;
            return true;
        }
        if (iO == 6) {
            this.n = 0;
            int[] iArr2 = this.d;
            int i3 = this.a - 1;
            iArr2[i3] = iArr2[i3] + 1;
            return false;
        }
        throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 82, 90, 86, 88, 80, 3, 15, 66, 80, 69, 21, 19, 66, 82, 69, 23}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public double k() throws NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, NumberFormatException, ReflectiveOperationException, CertPathValidatorException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (IllllI11Il.IlIIlIllI1(I1I1lI1II1.a(new byte[]{84, 46, 10, 45, 32, 70, 122, 100, 14, 40, 3, 65, 96, 82, 96, 66, 32, 35, 19, 125, 86, 86, 3, 101, 73, 2, 100, 29, 41, 68}), 379787912L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{92, 22, 50, 18, 9, 70, 124, 74, 91, 18, 0, 93, 123, 1, 12, 94, 48, 82, 21, 74}));
        }
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 16) {
            this.n = 0;
            int[] iArr = this.d;
            int i2 = this.a - 1;
            iArr[i2] = iArr[i2] + 1;
            return this.o;
        }
        if (iO == 17) {
            this.q = this.m.f(this.p);
        } else if (iO == 9) {
            this.q = a(h);
        } else if (iO == 8) {
            this.q = a(g);
        } else if (iO == 10) {
            this.q = r();
        } else if (iO != 11) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 84, 90, 76, 86, 89, 7, 65, 0, 71, 68, 65, 68, 84, 64, 22}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
        this.n = 11;
        try {
            double d = Double.parseDouble(this.q);
            if (!this.e && (Double.isNaN(d) || Double.isInfinite(d))) {
                throw new ll1ll1I1I1(I1I1lI1II1.a(new byte[]{125, 55, 45, 43, 66, 83, 88, 66, 91, 13, 84, 67, 21, 119, 85, 123, 66, 0, 12, 86, 16, 8, 93, 83, 90, 88, 94, 17, 13, 87, 68, 89, 23}) + d + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
            }
            this.q = null;
            this.n = 0;
            int[] iArr2 = this.d;
            int i3 = this.a - 1;
            iArr2[i3] = iArr2[i3] + 1;
            return d;
        } catch (NumberFormatException unused) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 84, 90, 76, 86, 89, 7, 65, 0, 71, 68, 65, 68, 84, 64, 22}) + this.q + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
    }

    private String a(lIIllIIlll liilliilll) throws NoSuchAlgorithmException, TimeoutException, IOException, BrokenBarrierException {
        StringBuilder sb = null;
        while (true) {
            long jC = this.l.c(liilliilll);
            if (jC == -1) {
                throw a(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 74, 64, 71, 11, 15, 5}));
            }
            if (this.m.d(jC) != 92) {
                if (sb == null) {
                    String strF = this.m.f(jC);
                    this.m.k();
                    return strF;
                }
                sb.append(this.m.f(jC));
                this.m.k();
                return sb.toString();
            }
            if (sb == null) {
                sb = new StringBuilder();
            }
            sb.append(this.m.f(jC));
            this.m.k();
            sb.append(w());
        }
    }

    private String r() throws UnrecoverableKeyException, IOException, BrokenBarrierException {
        if (Il1I1lllIl.lll1111l11(I1I1lI1II1.a(new byte[]{1, 15, 10, 35, 43, 84, 91, 72, 107, 38, 102, 2, 98, 0, 98, 112, 52, 47, 43, 84, 5, 32, 68, 125, 102, 94}), 4533)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{84, 22, 19, 84, 54, 4, 83, 123, 78, 20, 97, 66, 7}));
        }
        long jC = this.l.c(i);
        String strF = jC != -1 ? this.m.f(jC) : this.m.s();
        if (lIIllIlIl1.IlII1Illll(267901629L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{14, 1, 48, 38, 16, 71, 95, 94, 106, 8, 83, 85, 114, 76, 119, 0, 90}));
        }
        return strF;
    }

    private void b(lIIllIIlll liilliilll) throws CertPathBuilderException, NoSuchAlgorithmException, IOException {
        while (true) {
            long jC = this.l.c(liilliilll);
            if (jC == -1) {
                throw a(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 74, 64, 71, 11, 15, 5}));
            }
            if (this.m.d(jC) == 92) {
                this.m.i(jC + 1);
                w();
            } else {
                this.m.i(jC + 1);
                if (android.support.v4.graphics.drawable.lIIlI111II.Il1lII1l1l(8759)) {
                    throw new CancellationException(I1I1lI1II1.a(new byte[]{100, 14, 46, 86, 13, 97, 98, 118, 75, 15, 8, 73, 81, 8, 68, 13, 7, 50, 0}));
                }
                return;
            }
        }
    }

    private void s() throws NoSuchAlgorithmException, IOException {
        long jC = this.l.c(i);
        ll1l1Illl1 ll1l1illl1 = this.m;
        if (jC == -1) {
            jC = ll1l1illl1.a();
        }
        ll1l1illl1.i(jC);
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public int l() throws NoSuchAlgorithmException, TimeoutException, IOException, InvalidKeyException, BrokenBarrierException, NumberFormatException, ReflectiveOperationException, KeyStoreException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        String strA;
        int iO = this.n;
        if (iO == 0) {
            iO = o();
        }
        if (iO == 16) {
            long j2 = this.o;
            int i2 = (int) j2;
            if (j2 != i2) {
                throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 94, 16, 92, 87, 64, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + this.o + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
            }
            this.n = 0;
            int[] iArr = this.d;
            int i3 = this.a - 1;
            iArr[i3] = iArr[i3] + 1;
            return i2;
        }
        if (iO == 17) {
            this.q = this.m.f(this.p);
        } else if (iO == 9 || iO == 8) {
            if (iO == 9) {
                strA = a(h);
            } else {
                strA = a(g);
            }
            this.q = strA;
            try {
                int i4 = Integer.parseInt(strA);
                this.n = 0;
                int[] iArr2 = this.d;
                int i5 = this.a - 1;
                iArr2[i5] = iArr2[i5] + 1;
                if (II1I11IlI1.Ill1lIIlIl(2307)) {
                    throw new KeyStoreException(I1I1lI1II1.a(new byte[]{100, 8, 84, 81, 1, 6, 122, 2, 64, 3}));
                }
                return i4;
            } catch (NumberFormatException unused) {
            }
        } else if (iO != 11) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 94, 16, 92, 87, 64, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
        this.n = 11;
        try {
            double d = Double.parseDouble(this.q);
            int i6 = (int) d;
            if (i6 != d) {
                throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 94, 16, 92, 87, 64, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + this.q + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
            }
            this.q = null;
            this.n = 0;
            int[] iArr3 = this.d;
            int i7 = this.a - 1;
            iArr3[i7] = iArr3[i7] + 1;
            return i6;
        } catch (NumberFormatException unused2) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 94, 16, 92, 87, 64, 21, 0, 20, 22, 18, 71, 0, 64, 21}) + this.q + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
        }
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public void close() throws NoSuchAlgorithmException, IOException {
        this.n = 0;
        this.b[0] = 8;
        this.a = 1;
        this.m.v();
        this.l.close();
    }

    @Override // I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll.l111llllI1
    public void m() throws CertPathBuilderException, NoSuchAlgorithmException, IOException, InvalidKeyException, BrokenBarrierException, ReflectiveOperationException, CRLException, CertificateExpiredException, InvalidAlgorithmParameterException {
        if (this.f) {
            throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 67, 82, 13, 64, 16, 64, 87, 81, 77, 18, 4, 1, 70, 85, 5, 19}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69}) + n());
        }
        int i2 = 0;
        do {
            int iO = this.n;
            if (iO == 0) {
                iO = o();
            }
            if (iO == 3) {
                a(1);
            } else if (iO == 1) {
                a(3);
            } else {
                if (iO == 4) {
                    i2--;
                    if (i2 < 0) {
                        throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 70, 84, 85, 65, 80, 66, 3, 23, 70, 16, 22, 82, 70, 19}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
                    }
                    this.a--;
                } else if (iO == 2) {
                    i2--;
                    if (i2 < 0) {
                        throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 70, 84, 85, 65, 80, 66, 3, 23, 70, 16, 22, 82, 70, 19}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
                    }
                    this.a--;
                } else if (iO == 14 || iO == 10) {
                    s();
                } else if (iO == 9 || iO == 13) {
                    b(h);
                } else if (iO == 8 || iO == 12) {
                    b(g);
                } else if (iO == 17) {
                    this.m.i(this.p);
                } else if (iO == 18) {
                    throw new Il1IllIl1I(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 5, 16, 70, 84, 85, 65, 80, 66, 3, 23, 70, 16, 22, 82, 70, 19}) + f() + I1I1lI1II1.a(new byte[]{23, 5, 22, 69, 18, 84, 67, 88, 25}) + n());
                }
                this.n = 0;
            }
            i2++;
            this.n = 0;
        } while (i2 != 0);
        int[] iArr = this.d;
        int i3 = this.a - 1;
        iArr[i3] = iArr[i3] + 1;
        this.c[this.a - 1] = I1I1lI1II1.a(new byte[]{89, 17, 14, 9});
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0026, code lost:
    
        r8.m.i(r3 - 1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0032, code lost:
    
        if (r1 != 47) goto L53;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x003c, code lost:
    
        if (r8.l.c(2) != false) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0044, code lost:
    
        if (androidx.versionedparcelable.custom.entities.II1I11IlI1.I1lllI1llI(7554) != false) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0046, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0056, code lost:
    
        throw new java.io.FileNotFoundException(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{86, 53, 33, 22, 0}));
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0057, code lost:
    
        t();
        r5 = r8.m.d(1L);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0064, code lost:
    
        if (r5 == 42) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0066, code lost:
    
        if (r5 == 47) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x006e, code lost:
    
        if (androidx.constraintlayout.widget.Il1lII1l1l.Il1IIlI1II(8401) == false) goto L31;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0070, code lost:
    
        android.util.Log.d(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{77, 39, 44, 86, 18, 122, 117, 8, 99}), l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{115, 29, 91, 93, 91, 80, 125, 72, 0, 30, 6, 73, 64, 125}));
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0087, code lost:
    
        return 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0088, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x0089, code lost:
    
        r8.m.k();
        r8.m.k();
        u();
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0098, code lost:
    
        r8.m.k();
        r8.m.k();
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x00a6, code lost:
    
        if (v() == false) goto L57;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00b7, code lost:
    
        throw a(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 90, 91, 88, 15, 4, 12, 70}));
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x00ba, code lost:
    
        if (r1 != 35) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x00bc, code lost:
    
        t();
        u();
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x00cb, code lost:
    
        if (androidx.core.location.lIIlI111II.IIlI1Il1lI(790327883) != false) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x00cd, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x00dc, code lost:
    
        throw new java.security.InvalidKeyException(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{93, 17, 43, 43, 32, 84, 110, 68, 12, 28, 123, 96, 88, 13, 96, 4, 82, 48, 38, 99}));
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private int a(boolean z) throws NoSuchAlgorithmException, IOException, InvalidKeyException {
        while (true) {
            int i2 = 0;
            while (true) {
                int i3 = i2 + 1;
                if (!this.l.c(i3)) {
                    if (z) {
                        throw new EOFException(I1I1lI1II1.a(new byte[]{114, 10, 6, 69, 13, 83, 23, 89, 87, 20, 69, 68}));
                    }
                    return -1;
                }
                byte bD = this.m.d(i2);
                if (bD != 10 && bD != 32 && bD != 13 && bD != 9) {
                    break;
                }
                i2 = i3;
            }
        }
    }

    private void t() throws IOException {
        if (!this.e) {
            throw a(I1I1lI1II1.a(new byte[]{98, 23, 7, 69, 40, 70, 88, 94, 107, 1, 81, 84, 80, 75, 26, 70, 7, 21, 46, 87, 94, 8, 86, 91, 71, 30, 67, 23, 17, 87, 30, 67, 67, 11, 66, 4, 1, 86, 82, 64, 77, 68, 93, 81, 89, 95, 91, 71, 15, 4, 6, 18, 122, 50, 124, 123}));
        }
        if (l1lI1I1l11.l11I11I11l(269339090L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{94, 12, 86, 32, 21, 124, 70, 68, 78, 1, 3, 89, 92, 106, 3, 94, 0, 20, 90, 104}));
        }
    }

    private void u() throws NoSuchAlgorithmException, IOException {
        long jC = this.l.c(j);
        ll1l1Illl1 ll1l1illl1 = this.m;
        ll1l1illl1.i(jC != -1 ? jC + 1 : ll1l1illl1.a());
        if (androidx.versionedparcelable.custom.entities.II1I11IlI1.I111IlIl1I(I1I1lI1II1.a(new byte[]{67, 20, 15, 34, 55, 87, 80, 67, 78, 38, 81}), 344220001L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{95, 17, 24, 18, 5, 66, 92, 86, 84, 40, 0, 84, 91, 117, 121, 92, 26, 87, 12}));
        }
    }

    private boolean v() throws NoSuchAlgorithmException, IOException, InvalidKeyException {
        if (lI1l1I1l1l.IlII1Illll(I1I1lI1II1.a(new byte[]{110, 93, 32, 28, 56, 1, 117, 104, 84, 22, 115, 123, 79, 1, 89}), 184180614L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{125, 1, 20, 32, 48, 79, 94, 82, 64, 20, 70, 97, 122, 12, 124, 120, 46, 57, 91, 99, 121, 15, 68, 80, 125, 99, 69, 46, 23, 96}));
        }
        long jB = this.l.b(k);
        boolean z = jB != -1;
        ll1l1Illl1 ll1l1illl1 = this.m;
        ll1l1illl1.i(z ? jB + r1.size() : ll1l1illl1.a());
        if (I1IllIll1l.I1lllI1llI(310296518L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{116, 17, 90, 93, 9, 68, 7, 1, 82, 48, 86, 101, 120, 118, 12, 86, 85, 48, 1, 87, 3, 37, 113}));
        }
        return z;
    }

    public String toString() {
        return I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 48, 80, 86, 84, 92, 22, 24}) + this.l + I1I1lI1II1.a(new byte[]{30});
    }

    private char w() throws CertPathBuilderException, NoSuchAlgorithmException, IOException {
        int i2;
        int i3;
        if (!this.l.c(1L)) {
            throw a(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 92, 71, 86, 3, 17, 7, 18, 67, 4, 66, 64, 86, 88, 84, 0}));
        }
        byte bK = this.m.k();
        if (bK == 10 || bK == 34 || bK == 39 || bK == 47 || bK == 92) {
            return (char) bK;
        }
        if (bK == 98) {
            if (II1I11IlI1.Il1IIlI1II(5499)) {
                throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{68, 21, 91, 41, 91, 93, 113, 84, 120, 92, 91, 120, 112, 125, 69, 124, 91, 38, 40, 3, 97, 14, 123, 89, 95}));
            }
            return '\b';
        }
        if (bK == 102) {
            if (lIIII1l1lI.l11I11I11l(9138)) {
                throw new ClassFormatError(I1I1lI1II1.a(new byte[]{0, 44, 1, 87, 81, 97, 67, 84, 72, 22, 103, 69, 82, 96, 83, 79, 39, 47, 42, 103, 90, 82, 122, 3, 102, 115, 14, 93, 3, 118, 86, 15}));
            }
            return '\f';
        }
        if (bK == 110) {
            return '\n';
        }
        if (bK == 114) {
            return '\r';
        }
        if (bK == 116) {
            return '\t';
        }
        if (bK == 117) {
            if (!this.l.c(4L)) {
                throw new EOFException(I1I1lI1II1.a(new byte[]{98, 10, 22, 0, 16, 88, 94, 94, 88, 16, 85, 84, 21, 92, 71, 86, 3, 17, 7, 18, 67, 4, 66, 64, 86, 88, 84, 0, 68, 83, 67, 67, 71, 5, 22, 13, 66}) + n());
            }
            char c = 0;
            for (int i4 = 0; i4 < 4; i4++) {
                byte bD = this.m.d(i4);
                char c2 = (char) (c << 4);
                if (bD < 48 || bD > 57) {
                    if (bD >= 97 && bD <= 102) {
                        i2 = bD - 97;
                    } else {
                        if (bD < 65 || bD > 70) {
                            throw a(I1I1lI1II1.a(new byte[]{107, 17}) + this.m.f(4L));
                        }
                        i2 = bD - 65;
                    }
                    i3 = i2 + 10;
                } else {
                    i3 = bD - 48;
                }
                c = (char) (c2 + i3);
            }
            this.m.i(4L);
            return c;
        }
        if (this.e) {
            return (char) bK;
        }
        throw a(I1I1lI1II1.a(new byte[]{126, 10, 20, 4, 14, 92, 83, 16, 92, 23, 83, 81, 69, 92, 20, 70, 7, 16, 23, 87, 94, 2, 86, 15, 19, 106}) + ((char) bK));
    }
}
