package I11I1l11ll.l1lI11lI1I.I1Il1l1llI.l1llI1l1ll;

import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.IIIll1l1l1;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.lIIllIIlll;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.ll1l1Illl1;
import android.media.content.lIIllIlIl1;
import java.io.IOException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l11Il1l11I {
    final String[] a;
    final IIIll1l1l1 b;

    private l11Il1l11I(String[] strArr, IIIll1l1l1 iIIll1l1l1) {
        this.a = strArr;
        this.b = iIIll1l1l1;
    }

    public static l11Il1l11I a(String... strArr) {
        try {
            lIIllIIlll[] liilliilllArr = new lIIllIIlll[strArr.length];
            ll1l1Illl1 ll1l1illl1 = new ll1l1Illl1();
            for (int i = 0; i < strArr.length; i++) {
                l111llllI1.a(ll1l1illl1, strArr[i]);
                ll1l1illl1.k();
                liilliilllArr[i] = ll1l1illl1.r();
            }
            l11Il1l11I l11il1l11i = new l11Il1l11I((String[]) strArr.clone(), IIIll1l1l1.a(liilliilllArr));
            if (lIIllIlIl1.I1lllI1llI(653365148L)) {
                throw new LinkageError(I1I1lI1II1.a(new byte[]{15, 1, 20, 18, 39, 98, 126, 1, 115, 85, 118, Byte.MAX_VALUE, 79, 110, 108, 97, 43, 8, 10}));
            }
            return l11il1l11i;
        } catch (IOException e) {
            throw new AssertionError(e);
        }
    }
}
