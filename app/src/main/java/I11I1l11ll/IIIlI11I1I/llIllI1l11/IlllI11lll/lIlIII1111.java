package I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll;

import android.accounts.utils.lIIIIII11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.SyncFailedException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class lIlIII1111 {
    private File a;

    protected abstract InputStream a(File file, String str) throws IOException;

    protected abstract boolean b(File file, String str) throws IOException;

    public lIlIII1111(File file) {
        this.a = file;
    }

    public final InputStream a(String str) throws IOException {
        if (IlIIlI11I1.Il1IIlI1II(277551167L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{112, 86, 39, 6}));
        }
        InputStream inputStreamA = a(this.a, str);
        if (lIIIIII11I.Ill1lIIlIl(500)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{97, 30, 36, 17, 48, 89, 114, 104, 105, 45, 65, 96, 111, 78, 94, 81, 85, 84, 3, 98, 105, 50}));
        }
        return inputStreamA;
    }

    public final boolean b(String str) throws IOException {
        return b(this.a, str);
    }

    public lIlIII1111() {
    }
}
