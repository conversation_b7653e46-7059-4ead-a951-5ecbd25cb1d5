package I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll;

import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import l1IlIl1IlI.IIIIIII1Il.lIlll111II.I1Il11I1lI.l1IIIl1III;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class llIllIlll1 extends lIlIII1111 {
    public llIllIlll1(File file) {
        super(file);
    }

    @Override // I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111
    protected final InputStream a(File file, String str) throws IOException {
        File file2 = new File(file, I1I1lI1II1.a(new byte[]{69, 1, 17}) + File.separator + str);
        if (!file2.getCanonicalPath().startsWith(file.getCanonicalPath())) {
            throw new IOException(I1I1lI1II1.a(new byte[]{81, 13, 14, 0, 66, 91, 88, 68, 25, 2, 95, 69, 91, 93}));
        }
        l1IIIl1III.d(I1I1lI1II1.a(new byte[]{80, 1, 1, 14, 13, 24, 83, 85, 91, 17, 87, 29, 65, 88, 83}), I1I1lI1II1.a(new byte[]{121, 11, 16, 8, 3, 89, 113, 89, 85, 1, 124, 95, 84, 93, 81, 71, 78, 65, 4, 91, 92, 4, 9}), file2.getAbsolutePath());
        FileInputStream fileInputStream = new FileInputStream(file2.getCanonicalFile());
        if (llIlII1IlI.I1lIllll1l(259320869L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{100, 29, 91}));
        }
        return fileInputStream;
    }

    @Override // I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111
    protected final boolean b(File file, String str) throws IOException {
        return new File(file, I1I1lI1II1.a(new byte[]{69, 1, 17}) + File.separator + str).exists();
    }
}
