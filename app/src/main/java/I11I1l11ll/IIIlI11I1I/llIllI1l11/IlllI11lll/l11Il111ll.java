package I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll;

import android.support.v4.graphics.drawable.IllllI11Il;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import l1IlIl1IlI.IIIIIII1Il.lIlll111II.I1Il11I1lI.l1IIIl1III;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l11Il111ll extends lIlIII1111 {
    public l11Il111ll(File file) {
        super(file);
    }

    @Override // I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111
    protected final InputStream a(File file, String str) throws IOException {
        l1IIIl1III.d(I1I1lI1II1.a(new byte[]{80, 1, 1, 14, 13, 24, 83, 85, 91, 17, 87, 29, 65, 88, 83}), I1I1lI1II1.a(new byte[]{122, 29, 35, 23, 1, 93, 94, 70, 92, 34, 89, 92, 80, 117, 91, 84, 6, 4, 16, 30, 16, 7, 90, 89, 86, 12}), new File(str).getCanonicalPath());
        if (IllllI11Il.I1II1111ll(376451444L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{0, 92}));
        }
        return null;
    }

    @Override // I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111
    protected final boolean b(File file, String str) throws IOException {
        new File(str).getCanonicalPath();
        return false;
    }
}
