package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import android.media.content.lll1IIII11;
import android.os.AsyncTask;
import android.util.Log;
import androidx.core.location.I1111IIl11;
import androidx.core.location.l1l1I111I1;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.security.KeyManagementException;
import java.security.cert.CertStoreException;
import java.util.concurrent.ThreadPoolExecutor;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class l11Il111ll extends AsyncTask<Object, Void, String> {
    private lIII1IlIlI a;
    protected final IlIlIIll11 d;

    public l11Il111ll(IlIlIIll11 ilIlIIll11) {
        this.d = ilIlIIll11;
    }

    public void a(lIII1IlIlI liii1ilili) throws KeyManagementException {
        if (lll1IIII11.I11II1I1I1(224094245L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{67, 28, 20, 63, 52, 113, 96, 118, 85, 1, Byte.MAX_VALUE, 118, 124, 109, 123, 76, 16, 49, 33, 6, 98, 20, 92, 124, 125, 65, 83, 4, 34, 10}));
        }
        this.a = liii1ilili;
    }

    protected void a(String str) throws CertStoreException {
        lIII1IlIlI liii1ilili = this.a;
        if (liii1ilili != null) {
            liii1ilili.a(this);
        }
        if (lIlIII1I1l.llII1lIIlI(219283435L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{5, 21, 5, 0, 82, 64, 102, 3, 82, 47, 93, 0, 70, 123, 97, Byte.MAX_VALUE, 41, 27}));
        }
    }

    public void a(ThreadPoolExecutor threadPoolExecutor) {
        if (I1I1IIIIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{4, 46, 40, 83, 24, 80, 93, 74, 86, 52, 113, 93, 101, 65}), 5448)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{77, 1, 84, 11, 21, 125, 64, 1, 83, 10, 85, 89, 71, 99, 1, 103, 85, 84, 90, 69, 65, 17, 92, 13, 126, 125, 68, 7}));
        }
        executeOnExecutor(threadPoolExecutor, new Object[0]);
        if (I1111IIl11.Il1IIlI1II(1000511894L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{102, 17, 38, 41, 90, 125, 67, 115, 64, 20, 89, 99, 13, 12, 113, 126, 44, 7, 0, 1, 8, 2, 75}));
        }
    }

    @Override // android.os.AsyncTask
    protected /* synthetic */ void onPostExecute(String str) throws CertStoreException {
        if (l1l1I111I1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{90, 1, 20, 2, 83, 77, 80, 119, 91, 30, 66, 124, 121, 94, 66, 124, 0, 50, 84, 99, 92, 39, 103, 4, 70, 111, 80, 0}), 1468)) {
            Log.v(I1I1lI1II1.a(new byte[]{115, 23, 40, 86, 54, 68, 117, 65, 116, 23, 1, 121, 1, 93, 76, 0, 82, 16, 90, 93, 2, 89, 101, Byte.MAX_VALUE, 10, 68, 90, 54, 44, 98}), I1I1lI1II1.a(new byte[]{95, 19, 37, 10, 20, 113, 0, 81, 87, 14, 124, 85, 111, 111, 91, 65, 1, 54, 35, 123}));
        } else {
            a(str);
        }
    }
}
