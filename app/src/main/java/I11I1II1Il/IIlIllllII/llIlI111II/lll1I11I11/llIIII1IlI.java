package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import android.accounts.utils.Ill11ll111;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.text.TextUtils;
import androidx.core.location.lI1lI11Ill;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.FileNotFoundException;
import java.io.NotActiveException;
import java.io.SyncFailedException;
import java.net.BindException;
import java.security.InvalidAlgorithmParameterException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertStoreException;
import java.util.HashSet;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class llIIII1IlI extends lIlIII1111 {
    public llIIII1IlI(IlIlIIll11 ilIlIIll11, HashSet<String> hashSet, JSONObject jSONObject, long j) {
        super(ilIlIIll11, hashSet, jSONObject, j);
    }

    private void b(String str) throws SyncFailedException, BindException, InvalidAlgorithmParameterException {
        IIlIIlIIIl.II1llllllI.I11ll1IlII.IllIlll111.lIlIII1111 liliii1111A = IIlIIlIIIl.II1llllllI.I11ll1IlII.IllIlll111.lIlIII1111.a();
        if (liliii1111A != null) {
            for (I1I1IIllll.l1IlI11I1l.l1Il1I1l1I.lIllllI1lI.lIlIII1111 liliii1111 : liliii1111A.b()) {
                if (((lIlIII1111) this).a.contains(liliii1111.getAdSessionId())) {
                    liliii1111.getAdSessionStatePublisher().a(str, this.c);
                }
            }
        }
        if (lI1lI11Ill.I1lllI1llI(1626758228L)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{7, 15, 38, 42, 11, 4, 102, 67, 13, 6, 74, 121, 124}));
        }
    }

    protected String a(Object... objArr) throws NotActiveException {
        if (android.support.v4.graphics.drawable.l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{96, 41, 48, 47, 39, 90, 116, 82, 108, 85, 103, 125, 94, 12, 3, 7, 91, 21, 1, 65}), 3285)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{92, 28, 12, 32, 51, 76, 78, 119, 124, 16}));
        }
        if (II1lIlIlll.I1l1I1111l.Il1IIIlI1l.IIll11lI1l.l11Il111ll.b(this.b, this.d.b())) {
            return null;
        }
        this.d.a(this.b);
        return this.b.toString();
    }

    @Override // I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11.l11Il111ll
    /* renamed from: a */
    protected void onPostExecute(String str) throws SyncFailedException, BindException, InvalidAlgorithmParameterException, CertStoreException {
        if (!TextUtils.isEmpty(str)) {
            b(str);
        }
        super.onPostExecute(str);
        if (l1lI1I1l11.Il1IIlI1II(195602191L)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{116, 0, 4, 44, 4, 91, 84, 66, 8, 46, 1, 1, 69, 1, 112, 13, 53}));
        }
    }

    @Override // android.os.AsyncTask
    protected /* synthetic */ String doInBackground(Object[] objArr) throws UnrecoverableKeyException, NotActiveException {
        String strA = a(objArr);
        if (II1I11IlI1.Il1IIlI1II(I1I1lI1II1.a(new byte[]{101, 34, 43, 23, 0, 12}), 243169396L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{88, 53, 14, 33, 13}));
        }
        return strA;
    }

    @Override // I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11.l11Il111ll, android.os.AsyncTask
    protected /* synthetic */ void onPostExecute(String str) throws SyncFailedException, BindException, FileNotFoundException, InvalidAlgorithmParameterException, CertStoreException {
        if (IllllI11Il.l11I11I11l(6267)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{5, 84, 87, 32, 80, 115}));
        }
        onPostExecute(str);
        if (Ill11ll111.l1ll11I11l(I1I1lI1II1.a(new byte[]{85, 85, 83, 86, 21, 89, 65, 72, 122, 17, 94, 118, 79, 90, 69, 94, 23, 89, 5, 98}), 1468979002L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{90, 21, 20, 49, 16, 113, 3, 1, 11, 1, 69, 104, 99, 1, 91, 109, 43, 9, 59}));
        }
    }
}
