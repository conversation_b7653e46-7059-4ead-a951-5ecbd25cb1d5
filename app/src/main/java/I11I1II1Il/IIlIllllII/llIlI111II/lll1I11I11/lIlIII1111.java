package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import java.util.HashSet;
import org.json.JSONObject;

/* loaded from: classes.dex */
public abstract class lIlIII1111 extends l11Il111ll {
    protected final HashSet<String> a;
    protected final JSONObject b;
    protected final long c;

    public lIlIII1111(IlIlIIll11 ilIlIIll11, HashSet<String> hashSet, JSONObject jSONObject, long j) {
        super(ilIlIIll11);
        this.a = new HashSet<>(hashSet);
        this.b = jSONObject;
        this.c = j;
    }
}
