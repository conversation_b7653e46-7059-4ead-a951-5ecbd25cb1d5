package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.IOException;
import java.io.StreamCorruptedException;
import java.security.InvalidParameterException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class l1lllIll1I extends l11Il111ll {
    public l1lllIll1I(IlIlIIll11 ilIlIIll11) {
        super(ilIlIIll11);
    }

    protected String a(Object... objArr) throws StreamCorruptedException {
        if (llIlII1IlI.lll1111l11(I1I1lI1II1.a(new byte[]{95, 23, 23, 2}), 4859)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{97, 9, 53, 82, 55, Byte.MAX_VALUE, 92, 124, 113, 17, 116, 116, 124, 94, 85, 99, 53, 11, 85, 91, 93, 47, 95, 82, 124}));
        }
        this.d.a(null);
        return null;
    }

    @Override // android.os.AsyncTask
    protected /* synthetic */ String doInBackground(Object[] objArr) throws IOException {
        if (llIlII1IlI.I1lllI1llI(3393)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{91, 92, 22, 48}));
        }
        String strA = a(objArr);
        if (lI11IlI1lI.I1lllI1llI(2936)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{94, 41, 80, 63, 22, 103}));
        }
        return strA;
    }
}
