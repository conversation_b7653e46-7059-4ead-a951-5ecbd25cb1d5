package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.io.ObjectStreamException;
import java.io.SyncFailedException;
import java.net.BindException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateExpiredException;
import java.util.HashSet;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class IllIIll11l extends lIlIII1111 {
    public IllIIll11l(IlIlIIll11 ilIlIIll11, HashSet<String> hashSet, JSONObject jSONObject, long j) {
        super(ilIlIIll11, hashSet, jSONObject, j);
    }

    private void b(String str) throws SyncFailedException, BindException {
        IIlIIlIIIl.II1llllllI.I11ll1IlII.IllIlll111.lIlIII1111 liliii1111A = IIlIIlIIIl.II1llllllI.I11ll1IlII.IllIlll111.lIlIII1111.a();
        if (liliii1111A != null) {
            for (I1I1IIllll.l1IlI11I1l.l1Il1I1l1I.lIllllI1lI.lIlIII1111 liliii1111 : liliii1111A.b()) {
                if (((lIlIII1111) this).a.contains(liliii1111.getAdSessionId())) {
                    liliii1111.getAdSessionStatePublisher().b(str, this.c);
                }
            }
        }
    }

    protected String a(Object... objArr) throws CertificateExpiredException {
        if (IllllI11Il.II1111I11I(I1I1lI1II1.a(new byte[]{78, 85, 82, 42, 56, 90, 67, 83, 65, 61, Byte.MAX_VALUE, 69, 90, 0, 124, 108, 85, 14, 91, 121}), I1I1lI1II1.a(new byte[]{1, 14, 81, 85, 38, 67, 77, 89, 86, 16, 100, 103, 71, 74, 12, 13, 26, 50, 39, 72, 94, 35}))) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{3, 55, 33, 60, 43, 94, 77, 96, 82, 22, 106, 103}));
        }
        return this.b.toString();
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11.l11Il111ll, android.os.AsyncTask
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void onPostExecute(String str) throws SyncFailedException, BindException, CertStoreException {
        b(str);
        super.onPostExecute(str);
    }

    @Override // android.os.AsyncTask
    protected /* synthetic */ String doInBackground(Object[] objArr) throws ObjectStreamException, CertificateExpiredException {
        if (lIIlI111II.IIlIl1Illl(7476)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{86, 47, 47, 31, 47, 113}));
        }
        String strA = a(objArr);
        if (IIlI1Il1lI.I1lI11IIll(I1I1lI1II1.a(new byte[]{14, 46, 87, 46, 26, 77, 113, 8, 122, 11, 87, 95, 112}), 207892425L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{66, 87, 13, 52, 13, 84, 102, 82, 97, 45, 100, 6, 81, Byte.MAX_VALUE, 13}));
        }
        return strA;
    }
}
