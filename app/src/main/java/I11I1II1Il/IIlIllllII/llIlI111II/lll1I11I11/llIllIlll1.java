package I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11;

import android.accounts.utils.lIIIIII11I;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.util.ArrayDeque;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class llIllIlll1 implements lIII1IlIlI {
    private final BlockingQueue<Runnable> a;
    private final ThreadPoolExecutor b;
    private final ArrayDeque<l11Il111ll> c = new ArrayDeque<>();
    private l11Il111ll d = null;

    public llIllIlll1() {
        LinkedBlockingQueue linkedBlockingQueue = new LinkedBlockingQueue();
        this.a = linkedBlockingQueue;
        this.b = new ThreadPoolExecutor(1, 1, 1L, TimeUnit.SECONDS, linkedBlockingQueue);
    }

    private void a() {
        l11Il111ll l11il111llPoll = this.c.poll();
        this.d = l11il111llPoll;
        if (l11il111llPoll != null) {
            l11il111llPoll.a(this.b);
        }
    }

    @Override // I11I1II1Il.IIlIllllII.llIlI111II.lll1I11I11.lIII1IlIlI
    public void a(l11Il111ll l11il111ll) {
        this.d = null;
        a();
    }

    public void b(l11Il111ll l11il111ll) throws KeyManagementException, UnsupportedEncodingException {
        if (lIIIIII11I.IllIlI1l1I(I1I1lI1II1.a(new byte[]{94, 83, 53, 42, 17, 101, 83, 8, 15, 3}), 200262041L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{67, 1, 22, 21, 81, 13, 68, 123, 116, 62, 90, 83, 0, 91, 98, 93, 82, 80, 8, 113, 71, 15, 100, 102}));
        }
        l11il111ll.a(this);
        this.c.add(l11il111ll);
        if (this.d == null) {
            a();
        }
        if (l1l1IllI11.IllIlI1l1I(I1I1lI1II1.a(new byte[]{78, 43, 0, 86, 35, 112, 15, 119, 87, 3, 4, 106, 125, 114, 88, 102, 44, 47, 14, 64, 90, 39, 95, 67, 102}), 282777866L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{15, 20, 16, 29, 14, 102, 90, 5, 114, 42, 0, 119, 3, 64}));
        }
    }
}
