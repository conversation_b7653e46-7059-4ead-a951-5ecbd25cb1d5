package I11I1II1Il.Il11Ill1ll.l1lI11lI1I.l1ll1I1I11;

import IIIlI1II1I.I1IlllI1lI.l11Il1lIll.lIIlIl1lIl.Il111lllll;
import IIIlI1II1I.I1IlllI1lI.l11Il1lIll.lIIlIl1lIl.l1II1llIl1;
import IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.I1l1IIl1II;
import IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.Il111I1111;
import IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.l1lllI111I;
import IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lll1l1lI1I;
import IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lIIIIII11I;
import android.content.Context;
import android.media.content.II1I11IlI1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup$LayoutParams;
import android.widget.RelativeLayout;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.lIIlI111II;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.NotActiveException;
import java.io.SyncFailedException;
import java.net.SocketTimeoutException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import java.util.concurrent.CancellationException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lll1II11ll.Il111lll1I.I1lIl1lIlI.l1lllIl1II.lI1I1I1I1I;

/* loaded from: classes.dex */
public abstract class IllIlllIII extends RelativeLayout implements lllI111lII {
    protected View N;
    protected Il111lllll O;
    protected lllI111lII P;

    /* JADX WARN: Multi-variable type inference failed */
    protected IllIlllIII(View view) {
        this(view, view instanceof lllI111lII ? (lllI111lII) view : null);
    }

    protected IllIlllIII(View view, lllI111lII llli111lii) {
        super(view.getContext(), null, 0);
        this.N = view;
        this.P = llli111lii;
        if ((this instanceof I1l1IIl1II) && (llli111lii instanceof l1lllI111I) && llli111lii.getSpinnerStyle() == Il111lllll.e) {
            llli111lii.getView().setScaleY(-1.0f);
            return;
        }
        if (this instanceof l1lllI111I) {
            lllI111lII llli111lii2 = this.P;
            if ((llli111lii2 instanceof I1l1IIl1II) && llli111lii2.getSpinnerStyle() == Il111lllll.e) {
                llli111lii.getView().setScaleY(-1.0f);
            }
        }
    }

    protected IllIlllIII(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
    }

    public boolean equals(Object obj) throws CertificateNotYetValidException {
        if (lII1llllI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{124, 7, 43, 32, 17, 98, 7, 70, 82, 40, 126, 91, 123, 94, 97, 82, 39, 4, 3, 120, 2, 52, 73, 92, 95, 66, 93, 1, 84, 81}), 471524125L)) {
            Log.v(I1I1lI1II1.a(new byte[]{15, 7, 54, 44}), I1I1lI1II1.a(new byte[]{14, 2, 27, 87, 50, Byte.MAX_VALUE, 67, 67, 77, 40, 84, 115, 93, 0, 101, 116, 13, 8, 13, 117, 81, 19, 124, 92}));
            return false;
        }
        if (!super.equals(obj)) {
            if (obj instanceof lllI111lII) {
                return getView() == ((lllI111lII) obj).getView();
            }
            if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{117, 16, 5, 36, 59, 3, 7, 87, 116, 53, 81, 83}), 1437)) {
                throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{85, 18, 1}));
            }
            return false;
        }
        if (l11Il111ll.Ill1lIIlIl(211623571L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{123, 55, 45, 10}));
        }
        return true;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public View getView() {
        View view = this.N;
        if (view == null) {
            view = this;
        }
        if (II1I11IlI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{91, 80, 82, 48, 39, 70, 118, Byte.MAX_VALUE, 110, 18, 65, 123, 3, 8, 117, 115, 10, 35, 10, 89, 97, 5, 2}), 4012)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{95, 8, 87, 6, 24}));
        }
        return view;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public int a(Il111I1111 il111I1111, boolean z) throws SyncFailedException {
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            if (lIIIIII11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{126, 86, 39, 42, 32, 120, 2}), 234597261L)) {
                throw new NullPointerException(I1I1lI1II1.a(new byte[]{113, 83, 80, 14, 49, 124, 65, 113}));
            }
            return 0;
        }
        int iA = llli111lii.a(il111I1111, z);
        if (llIl1lII1I.IIll1I11lI(I1I1lI1II1.a(new byte[]{122, 62, 50, 87, 59, 1, 114, 0, 119, 0, 1, 71, Byte.MAX_VALUE, 120, 76, 115, 16, 18, 20, 67, 116}))) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{78, 41, 41, 46, 48, 102, 80, 4, 109, 80, 104, 90, 66, 13, 83, 68, 59, 55, 42, 95, 8, 32, 89, 71, 84, 76, 102}));
        }
        return iA;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void setPrimaryColors(int... iArr) {
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            return;
        }
        llli111lii.setPrimaryColors(iArr);
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public Il111lllll getSpinnerStyle() throws SocketTimeoutException, CertificateExpiredException, CloneNotSupportedException {
        if (lIIlI111II.llIIlI1llI(250163589L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{126, 10, 23, 39, 20, 115, 99, 102, 124, 93, 70, 7, 5, 116, 78, 100, 26, 9, 48}));
        }
        Il111lllll il111lllll = this.O;
        if (il111lllll != null) {
            if (IIll1llI1l.Il1IIlI1II(8897)) {
                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{121, 53, 10, 47, 84, 116, 101, 81, 126, 8, 87, 73, 68, 94, 88, 64, 10, 34, 17, 96, 116, 81, 82, 83, 75, 68, 65}));
            }
            return il111lllll;
        }
        lllI111lII llli111lii = this.P;
        if (llli111lii != null && llli111lii != this) {
            return llli111lii.getSpinnerStyle();
        }
        View view = this.N;
        if (view != null) {
            ViewGroup$LayoutParams layoutParams = view.getLayoutParams();
            if (layoutParams instanceof lI1I1I1I1I) {
                Il111lllll il111lllll2 = ((lI1I1I1I1I) layoutParams).b;
                this.O = il111lllll2;
                if (il111lllll2 != null) {
                    return il111lllll2;
                }
            }
            if (layoutParams != null && (layoutParams.height == 0 || layoutParams.height == -1)) {
                for (Il111lllll il111lllll3 : Il111lllll.f) {
                    if (il111lllll3.i) {
                        this.O = il111lllll3;
                        return il111lllll3;
                    }
                }
            }
        }
        Il111lllll il111lllll4 = Il111lllll.a;
        this.O = il111lllll4;
        if (llIlII1IlI.l1ll11I11l(I1I1lI1II1.a(new byte[]{90, 6, 40, 13, 59, 115, 79, 2, 90, 23, 82, 113, 109, 1, 122, 67, 58, 20, 0}), 5508)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{66, 41, 21, 52, 37, 82}));
        }
        return il111lllll4;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void a(lll1l1lI1I lll1l1li1i, int i, int i2) throws NotActiveException {
        if (IllllI11Il.IIlIl1Illl(I1I1lI1II1.a(new byte[]{120, 30, 49, 85, 90, 93, Byte.MAX_VALUE, 119, 10, 21, 84, 106, 112}), I1I1lI1II1.a(new byte[]{123, 15, 11, 0, 12, 86, 92, 88, 73, 16, 88, 99}))) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{100, 37, 39, 42, 17}));
        }
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            View view = this.N;
            if (view != null) {
                ViewGroup$LayoutParams layoutParams = view.getLayoutParams();
                if (layoutParams instanceof lI1I1I1I1I) {
                    lll1l1li1i.a(this, ((lI1I1I1I1I) layoutParams).a);
                }
            }
        } else {
            llli111lii.a(lll1l1li1i, i, i2);
        }
        if (androidx.core.location.IllIlllIII.I111IlIl1I(3828)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{100, 92, 53, 86, 86, 79, 114, 105, 110, 35, 96, 86, 96, 12, 65}));
        }
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public boolean b() {
        lllI111lII llli111lii = this.P;
        return (llli111lii == null || llli111lii == this || !llli111lii.b()) ? false : true;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void a(float f, int i, int i2) throws UnrecoverableKeyException {
        if (lIIIIII11I.IIll1I11lI(I1I1lI1II1.a(new byte[]{15, 47, 5, 93, 20, 101, 94, 96, 67}))) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{98, 43, 37, 82, 38, 67, 99, 89, 105, 28, 101, 65, 1, 111, 117, 67, 17, 39, 5, 11, 71, 18}));
        }
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            return;
        }
        llli111lii.a(f, i, i2);
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void a(boolean z, float f, int i, int i2, int i3) {
        if (IIlI1Il1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{78, 38, 50, 13, 80, 91, 117, 88, 88, 9, 92, 114}), I1I1lI1II1.a(new byte[]{4, 20, 16, 36, 54, 108, 80, 74, 15, 54, 83, 88, 64, 110, 71, 119, 52, 15, 52, 5, 94, 81, 101, 13}))) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{4, 38, 5, 80, 14, 81, 94, 67, 119, 32, 87, 8, 0, 124, 3, 103}));
        }
        lllI111lII llli111lii = this.P;
        if (llli111lii != null && llli111lii != this) {
            llli111lii.a(z, f, i, i2, i3);
        }
        if (lI11IlI1lI.l11I11I11l(2214)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{0, 42, 22, 63, 21, 101, 92, 2, 86, 48, 87, 105, 68, 112, 124, 113, 91, 10, 26, 91, 100, 17, 118, 89, 0, 123, 89, 85, 92}));
        }
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void b(Il111I1111 il111I1111, int i, int i2) {
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            return;
        }
        llli111lii.b(il111I1111, i, i2);
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public void a(Il111I1111 il111I1111, int i, int i2) {
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            return;
        }
        llli111lii.a(il111I1111, i, i2);
    }

    @Override // lI11II1I11.IIIl1I111I.IllIlll111.III1111lll.l1l11lI11I
    public void a(Il111I1111 il111I1111, l1II1llIl1 l1ii1llil1, l1II1llIl1 l1ii1llil12) throws TimeoutException {
        if (l111Il1lI1.III11111Il(I1I1lI1II1.a(new byte[]{102, 11, 48, 0, 5, 82, 111, 64, 111, 45, 124, 68, 124, 85, 101, 108, 7, 89, 90, 95, 100, 50, 92, 90, 99, 67, 96, 51}))) {
            Log.i(I1I1lI1II1.a(new byte[]{99, 38, 18, 1, 26, 70, 90, 100, 123}), I1I1lI1II1.a(new byte[]{14, 6, 17, 86, 20, 6}));
            return;
        }
        lllI111lII llli111lii = this.P;
        if (llli111lii == null || llli111lii == this) {
            return;
        }
        if ((this instanceof I1l1IIl1II) && (llli111lii instanceof l1lllI111I)) {
            if (l1ii1llil1.isFooter) {
                l1ii1llil1 = l1ii1llil1.toHeader();
            }
            if (l1ii1llil12.isFooter) {
                l1ii1llil12 = l1ii1llil12.toHeader();
            }
        } else if ((this instanceof l1lllI111I) && (llli111lii instanceof I1l1IIl1II)) {
            if (l1ii1llil1.isHeader) {
                l1ii1llil1 = l1ii1llil1.toFooter();
            }
            if (l1ii1llil12.isHeader) {
                l1ii1llil12 = l1ii1llil12.toFooter();
            }
        }
        lllI111lII llli111lii2 = this.P;
        if (llli111lii2 != null) {
            llli111lii2.a(il111I1111, l1ii1llil1, l1ii1llil12);
        }
    }

    public boolean a(boolean z) {
        lllI111lII llli111lii = this.P;
        boolean z2 = (llli111lii instanceof I1l1IIl1II) && ((I1l1IIl1II) llli111lii).a(z);
        if (IIIlIll111.I1lllI1llI(196082465L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{86, 33, 86, 48, 35, 126, 101, 87, 79, 42, 66, 69, 96, 87, 117, 88, 53, 44, 22, 3, 9, 47, 66, 4, 124, 96, 67, 42, 52}));
        }
        return z2;
    }

    @Override // IIl1Il1Ill.llIl1I1llI.IIIlI11I1I.I1lII1I1l1.lllI111lII
    public boolean a(int i, float f, boolean z) throws CertificateExpiredException {
        if (I1lllI11II.I1lllI1llI(375325547L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{103, 42, 84, 36}));
        }
        if (IlIIlI11I1.Ill1lIIlIl(5903)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{85, 12, 86, 11, 18, 13, 80, 100, 72, 17, 67, 87, 67, 108, 86, 108, 12, 0, 15, 91, 94, 42, 84, 71, 4, 94, 111, 47, 9, 94, 118, 52}));
        }
        return false;
    }
}
