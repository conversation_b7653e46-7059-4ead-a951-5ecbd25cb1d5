package I11I1II1Il.Il11Ill1ll.l1lI11lI1I.l1ll1I1I11;

import android.graphics.PointF;
import android.view.View;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.l1l1I111I1;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.IOException;
import java.security.DigestException;
import java.security.InvalidParameterException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1II1I1I.llIIl1l1l1.IIIl1I111I.II1I11llII.lI1111llI1;
import lI11II1I11.IIIl1I111I.IllIlll111.III1111lll.l11I1Il1ll;

/* loaded from: classes.dex */
public class l1Il1II1l1 implements l11I1Il1ll {
    public PointF a;
    public l11I1Il1ll b;
    public boolean c = true;

    @Override // lI11II1I11.IIIl1I111I.IllIlll111.III1111lll.l11I1Il1ll
    public boolean a(View view) {
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{85, 55, 49, 92, 44, 7, 111, Byte.MAX_VALUE, 119, 8, 97, 6, 69, 113, 64, 98, 43, 47, 47, 89, 4, 8, 124, 98, 113, 102, 95}))) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{125}));
        }
        l11I1Il1ll l11i1il1ll = this.b;
        return l11i1il1ll != null ? l11i1il1ll.a(view) : lI1111llI1.a(view, this.a);
    }

    @Override // lI11II1I11.IIIl1I111I.IllIlll111.III1111lll.l11I1Il1ll
    public boolean b(View view) throws DigestException, IOException {
        l11I1Il1ll l11i1il1ll = this.b;
        if (l11i1il1ll != null) {
            boolean zB = l11i1il1ll.b(view);
            if (Il1lII1l1l.Ill1lIIlIl(8620)) {
                throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{83, 50, 7, 2, 37, 89, 109, 117, 106, 48, 118, 64, 114, 112, 66, 98, 18}));
            }
            return zB;
        }
        boolean zA = lI1111llI1.a(view, this.a, this.c);
        if (II1I11IlI1.IlII1Illll(I1I1lI1II1.a(new byte[]{0, 18, 41, 42, 45, 98, 82, 84, 87, 15, 118, 96, 1, 75, 78, 83, 18, 49}), 211343545L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{80, 11, 53, 22, 58, 77, 113, 9, 8}));
        }
        return zA;
    }
}
