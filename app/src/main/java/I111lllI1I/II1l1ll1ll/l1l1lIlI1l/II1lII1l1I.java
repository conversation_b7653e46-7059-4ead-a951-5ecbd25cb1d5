package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import IlIlI11III.I1lIl11III.lI1l111ll1.l1l11I11II.lI11IIlIll;
import IlIlI11III.I1lIl11III.lI1l111ll1.l1l11I11II.ll1I1l1lII;
import android.accounts.utils.IIIlIl1I1l;
import android.media.content.II1I11IlI1;
import android.media.content.Il1llIl111;
import android.util.Log;
import android.view.ViewParent;
import androidx.fragment.app.I1llI11IIl;
import androidx.fragment.app.IllIIll11l;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView$AdapterDataObserver;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.I11IlIIll1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;

/* loaded from: classes.dex */
class II1lII1l1I {
    private RecyclerView$AdapterDataObserver mDataObserver;
    private lIIllIlIl1 mLifecycleObserver;
    private ll1I1l1lII mPageChangeCallback;
    private long mPrimaryItemId = -1;
    private lI11IIlIll mViewPager;
    final /* synthetic */ Il111I11Il this$0;

    II1lII1l1I(Il111I11Il il111I11Il) {
        this.this$0 = il111I11Il;
    }

    void register(RecyclerView recyclerView) {
        this.mViewPager = inferViewPager(recyclerView);
        llI1l1111l lli1l1111l = new llI1l1111l(this);
        this.mPageChangeCallback = lli1l1111l;
        this.mViewPager.registerOnPageChangeCallback(lli1l1111l);
        lll1l1lI1I lll1l1li1i = new lll1l1lI1I(this);
        this.mDataObserver = lll1l1li1i;
        this.this$0.registerAdapterDataObserver(lll1l1li1i);
        this.mLifecycleObserver = new IllIl11IIl(this);
        this.this$0.mLifecycle.addObserver(this.mLifecycleObserver);
    }

    void unregister(RecyclerView recyclerView) {
        inferViewPager(recyclerView).unregisterOnPageChangeCallback(this.mPageChangeCallback);
        this.this$0.unregisterAdapterDataObserver(this.mDataObserver);
        this.this$0.mLifecycle.removeObserver(this.mLifecycleObserver);
        this.mViewPager = null;
    }

    void updateFragmentMaxLifecycle(boolean z) {
        if (this.this$0.shouldDelayFragmentTransactions()) {
            if (II1I11IlI1.III111l111(I1I1lI1II1.a(new byte[]{121, 33, 18, 60, 43, 95, 114, 5, 106, 13, 91, 81, 68, 64, 99, 7, 56, 38, 44, 99}), 250341819L)) {
                throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{93, 10, 45, 36, 91, 122, 69, 97, 14, 43, 97, 88, 71}));
            }
            return;
        }
        if (this.mViewPager.getScrollState() != 0) {
            return;
        }
        if (this.this$0.mFragments.isEmpty() || this.this$0.getItemCount() == 0) {
            if (IIIlIl1I1l.I1II1111ll(3459)) {
                Log.e(I1I1lI1II1.a(new byte[]{79, 29, 38, 0, 20, 119, 126, 1, 64}), I1I1lI1II1.a(new byte[]{98, 44, 24, 43, 24, 0, 71, 95, 122, 15, 89, 69, 100, 74, 99, 82, 17, 27, 44, 95, Byte.MAX_VALUE, 80, 101, 96, 123, 87, 79, 54, 22, 74, 116, 44}));
                return;
            }
            return;
        }
        int currentItem = this.mViewPager.getCurrentItem();
        if (currentItem >= this.this$0.getItemCount()) {
            return;
        }
        long itemId = this.this$0.getItemId(currentItem);
        if (itemId == this.mPrimaryItemId && !z) {
            if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{81, 92, 81, 19, 80, 77, 101, 67, 85, 45, 6, 126, 86, 75, 88, 76, 42, 5, 54, 87, 124, 14}), 181647941L)) {
                throw new RuntimeException(I1I1lI1II1.a(new byte[]{121, 92, 14, 47, 22, Byte.MAX_VALUE, 79, 73, 11, 1, 66, 99, 119, 79, 83, 91, 51, 32, 7, 69, 9, 54, 67, 91, 66, 89, 98, 49}));
            }
            return;
        }
        IllIIll11l illIIll11l = this.this$0.mFragments.get(itemId);
        if (illIIll11l == null || !illIIll11l.isAdded()) {
            return;
        }
        this.mPrimaryItemId = itemId;
        I1llI11IIl i1llI11IIlBeginTransaction = this.this$0.mFragmentManager.beginTransaction();
        IllIIll11l illIIll11l2 = null;
        for (int i = 0; i < this.this$0.mFragments.size(); i++) {
            long jKeyAt = this.this$0.mFragments.keyAt(i);
            IllIIll11l illIIll11lValueAt = this.this$0.mFragments.valueAt(i);
            if (illIIll11lValueAt.isAdded()) {
                if (jKeyAt != this.mPrimaryItemId) {
                    i1llI11IIlBeginTransaction.setMaxLifecycle(illIIll11lValueAt, I11IlIIll1.STARTED);
                } else {
                    illIIll11l2 = illIIll11lValueAt;
                }
                illIIll11lValueAt.setMenuVisibility(jKeyAt == this.mPrimaryItemId);
            }
        }
        if (illIIll11l2 != null) {
            i1llI11IIlBeginTransaction.setMaxLifecycle(illIIll11l2, I11IlIIll1.RESUMED);
        }
        if (i1llI11IIlBeginTransaction.isEmpty()) {
            return;
        }
        i1llI11IIlBeginTransaction.commitNow();
    }

    private lI11IIlIll inferViewPager(RecyclerView recyclerView) {
        ViewParent parent = recyclerView.getParent();
        if (parent instanceof lI11IIlIll) {
            return (lI11IIlIll) parent;
        }
        throw new IllegalStateException(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 50, 89, 85, 66, 105, 85, 82, 7, 19, 80, 18, 89, 15, 64, 65, 82, 88, 84, 0, 74, 18, 112, 12, 67, 94, 66}) + parent);
    }
}
