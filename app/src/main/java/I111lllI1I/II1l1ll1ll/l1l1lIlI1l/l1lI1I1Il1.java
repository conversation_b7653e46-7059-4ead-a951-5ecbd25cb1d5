package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import android.view.ViewGroup;
import android.view.ViewGroup$LayoutParams;
import android.widget.FrameLayout;
import androidx.recyclerview.widget.RecyclerView$ViewHolder;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIll1l1l11;

/* loaded from: classes.dex */
public final class l1lI1I1Il1 extends RecyclerView$ViewHolder {
    private l1lI1I1Il1(FrameLayout frameLayout) {
        super(frameLayout);
    }

    static l1lI1I1Il1 create(ViewGroup viewGroup) {
        if (lII1llllI1.l111IIlII1(I1I1lI1II1.a(new byte[]{84, 17, 12, 36, 49, 109, 89, 121, 95, 49, 86, 81, 65, 87}), I1I1lI1II1.a(new byte[]{77, 1, 42, 3, 4, 0, 91, 106, 80, 60, 105, 83, 118, 114, 115, 92, 56, 36, 3, 120, 81, 8}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{115, 8, 51, 14, 22, 2, 79, 5, 116, 86, 88, 3, 87, 14, 3, 114, 4, 55, 83, 91, 119, 39, 112, 108}));
        }
        FrameLayout frameLayout = new FrameLayout(viewGroup.getContext());
        frameLayout.setLayoutParams(new ViewGroup$LayoutParams(-1, -1));
        frameLayout.setId(lIll1l1l11.generateViewId());
        frameLayout.setSaveEnabled(false);
        return new l1lI1I1Il1(frameLayout);
    }

    FrameLayout getContainer() {
        return (FrameLayout) this.itemView;
    }
}
