package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.util.concurrent.TimeoutException;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIll1l1l11;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lll1llIIll;

/* loaded from: classes.dex */
class IIIll1l1l1 implements lIIllIlIl1 {
    final /* synthetic */ Il111I11Il this$0;
    final /* synthetic */ l1lI1I1Il1 val$holder;

    IIIll1l1l1(Il111I11Il il111I11Il, l1lI1I1Il1 l1li1i1il1) {
        this.this$0 = il111I11Il;
        this.val$holder = l1li1i1il1;
    }

    @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1
    public void onStateChanged(lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws GeneralSecurityException, TimeoutException, UnknownHostException {
        if (this.this$0.shouldDelayFragmentTransactions()) {
            return;
        }
        li1liill1i.getLifecycle().removeObserver(this);
        if (lIll1l1l11.isAttachedToWindow(this.val$holder.getContainer())) {
            this.this$0.placeFragmentInViewHolder(this.val$holder);
        }
    }
}
