package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1Ill1lIII;
import androidx.recyclerview.widget.RecyclerView$AdapterDataObserver;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.StreamCorruptedException;
import java.net.NoRouteToHostException;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public abstract class I1111l11ll extends RecyclerView$AdapterDataObserver {
    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public abstract void onChanged();

    private I1111l11ll() {
    }

    /* synthetic */ I1111l11ll(I1lIlIIIl1 i1lIlIIIl1) {
        this();
    }

    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public final void onItemRangeChanged(int i, int i2) {
        onChanged();
    }

    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public final void onItemRangeChanged(int i, int i2, Object obj) throws KeyManagementException, NoRouteToHostException {
        if (llIlII1IlI.Il1IIlI1II(5246)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{80, 22, 6, 84, 13, 101, 95, 116, 107, 86, 117, 7, 12, 99, 110, 70}));
        }
        onChanged();
        if (I1Ill1lIII.l11I11I11l(545451377L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{85, 40, 43, 45, 85, 67, 80, 5, 104, 62, 100, 101, 116, 112, 114, 112, 3, 50, 51, 97, 83, 9}));
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public final void onItemRangeInserted(int i, int i2) {
        onChanged();
    }

    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public final void onItemRangeRemoved(int i, int i2) throws StreamCorruptedException {
        if (l111Il1lI1.llII1lIIlI(I1I1lI1II1.a(new byte[]{125, 60, 44, 50, 52, 108, 118, 120, 10, 10, 122, 9, 79, 82, 13, 87, 83, 0, 53, 11}))) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{102, 45, 37, 31, 11, 3, 65, 124, 126, 19, 83}));
        }
        onChanged();
    }

    @Override // androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public final void onItemRangeMoved(int i, int i2, int i3) {
        onChanged();
    }
}
