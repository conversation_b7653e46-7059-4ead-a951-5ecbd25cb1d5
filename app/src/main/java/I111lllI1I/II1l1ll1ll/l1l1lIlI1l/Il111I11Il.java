package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.llllIllIII;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lIIIIII11I;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.FrameLayout;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.IllIlllIII;
import androidx.fragment.app.Fragment$SavedState;
import androidx.fragment.app.IllIIll11l;
import androidx.fragment.app.l1IlII1III;
import androidx.fragment.app.lII11llIIl;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView$Adapter;
import androidx.recyclerview.widget.RecyclerView$ViewHolder;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import com.ironsource.mediationsdk.logger.IronSourceError;
import java.io.ObjectStreamException;
import java.io.UTFDataFormatException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateParsingException;
import java.util.Iterator;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIll1l1l11;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.I11IlIIll1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.llll1I1lll;
import lllIl1I1II.I1Il1l1llI.IIlIllllII.Il1lIIlll1;
import lllIl1I1II.I1Il1l1llI.IIlIllllII.l111l1llIl;

/* loaded from: classes.dex */
public abstract class Il111I11Il extends RecyclerView$Adapter<l1lI1I1Il1> implements III1I1IIII {
    private static final long GRACE_WINDOW_TIME_MS = 10000;
    private static final String KEY_PREFIX_FRAGMENT = I1I1lI1II1.a(new byte[]{81, 71});
    private static final String KEY_PREFIX_STATE = I1I1lI1II1.a(new byte[]{68, 71});
    final lII11llIIl mFragmentManager;
    private II1lII1l1I mFragmentMaxLifecycleEnforcer;
    final Il1lIIlll1<IllIIll11l> mFragments;
    private boolean mHasStaleFragments;
    boolean mIsInGracePeriod;
    private final Il1lIIlll1<Integer> mItemIdToViewHolder;
    final llll1I1lll mLifecycle;
    private final Il1lIIlll1<Fragment$SavedState> mSavedStates;

    public abstract IllIIll11l createFragment(int i);

    public final boolean onFailedToRecycleView(l1lI1I1Il1 l1li1i1il1) throws NoSuchMethodException {
        if (IIlI1Il1lI.IlIllIll1I(255958462L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{81, 46, 58, 38, 44, 95, 100, 123, 8, 41, 4, 4, 122, 94}));
        }
        return true;
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public /* synthetic */ void onBindViewHolder(RecyclerView$ViewHolder recyclerView$ViewHolder, int i) throws CertPathBuilderException, BrokenBarrierException, CertificateEncodingException {
        if (I1I1IIIIl1.l1l1l1IIlI(IronSourceError.ERROR_PLACEMENT_CAPPED)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{90, 93, 45, 7, 83, 77}));
        }
        onBindViewHolder((l1lI1I1Il1) recyclerView$ViewHolder, i);
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public /* synthetic */ RecyclerView$ViewHolder onCreateViewHolder(ViewGroup viewGroup, int i) throws UTFDataFormatException {
        if (I1IllIll1l.Ill1lIIlIl(213319437L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{6, 53, 58, 80, 46, 83, 78, 72, 88, 0, 125, 67, 93, 86, 87, 96, 26, 49, 42, 96, 89, 4}));
        }
        l1lI1I1Il1 l1li1i1il1OnCreateViewHolder = onCreateViewHolder(viewGroup, i);
        if (!I111I11Ill.IlIIlIllI1(I1I1lI1II1.a(new byte[]{112}), 169633956L)) {
            return l1li1i1il1OnCreateViewHolder;
        }
        Log.w(I1I1lI1II1.a(new byte[]{66, 7, 51, 41, 15, 90, 93, 66, 123, 37, 92, 9, 121, 13, 67, 95, 9, 36, 14, 69, 121, 34, 0, 86, 89, 84, 5, 21, 80, 68, 116, 5}), I1I1lI1II1.a(new byte[]{71}));
        return null;
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public /* synthetic */ boolean onFailedToRecycleView(RecyclerView$ViewHolder recyclerView$ViewHolder) throws ReflectiveOperationException, UnknownServiceException {
        if (Il1I1lllIl.I1lllI1llI(222617699L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{80, 47, 5, 49, 81, 108, 14, 2, 111, 9, 96}));
        }
        boolean zOnFailedToRecycleView = onFailedToRecycleView((l1lI1I1Il1) recyclerView$ViewHolder);
        if (IIIlIll111.l11I11I11l(I1I1lI1II1.a(new byte[]{89, 38, 40, 4, 82, 94, 113, 68, 92, 10, 105, 113, 115, 85, 77, 5, 32, 39, 35, 75, 101, 88, 105, 87, 2}), 6060)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{125, 84, 5, 15, 26, 109, 93, 125, 109, 39, 104, 90, 76, 118, 101, 102, 49, 18}));
        }
        return zOnFailedToRecycleView;
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public /* synthetic */ void onViewAttachedToWindow(RecyclerView$ViewHolder recyclerView$ViewHolder) throws GeneralSecurityException, TimeoutException, BrokenBarrierException, UnknownHostException {
        onViewAttachedToWindow((l1lI1I1Il1) recyclerView$ViewHolder);
        if (Ill11ll111.lI11llll1I(I1I1lI1II1.a(new byte[]{95, 13, 44, 43, 33, 80, 68, 69, 124, 17, 85, 97, 3}), 509837169L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{94, 53, 9, 2, 32, 111, 86, 89}));
        }
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public /* synthetic */ void onViewRecycled(RecyclerView$ViewHolder recyclerView$ViewHolder) {
        onViewRecycled((l1lI1I1Il1) recyclerView$ViewHolder);
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{1}), 9735)) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{99, 85, 9, 19}));
        }
    }

    public Il111I11Il(l1IlII1III l1ilii1iii) {
        this(l1ilii1iii.getSupportFragmentManager(), l1ilii1iii.getLifecycle());
    }

    public Il111I11Il(IllIIll11l illIIll11l) {
        this(illIIll11l.getChildFragmentManager(), illIIll11l.getLifecycle());
    }

    public Il111I11Il(lII11llIIl lii11lliil, llll1I1lll llll1i1lll) throws CertificateParsingException {
        this.mFragments = new Il1lIIlll1<>();
        this.mSavedStates = new Il1lIIlll1<>();
        this.mItemIdToViewHolder = new Il1lIIlll1<>();
        this.mIsInGracePeriod = false;
        this.mHasStaleFragments = false;
        this.mFragmentManager = lii11lliil;
        this.mLifecycle = llll1i1lll;
        super.setHasStableIds(true);
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public void onAttachedToRecyclerView(RecyclerView recyclerView) {
        if (llIlI11III.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{85, 5, 86, 54, 21, 95, 110, 83, 93, 39, 68, 64, 84, 104, 112, 95, 37, 41, 6, 123, 121}), 4649)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{0, 2, 33, 40, 36, 102, 83, 74, 113, 2, 71, 83, 123, 72, 12, 6, 42, 50, 24, 87, 9, 9, 65, 94, 66, 5}));
        }
        llllIllIII.checkArgument(this.mFragmentMaxLifecycleEnforcer == null);
        II1lII1l1I iI1lII1l1I = new II1lII1l1I(this);
        this.mFragmentMaxLifecycleEnforcer = iI1lII1l1I;
        iI1lII1l1I.register(recyclerView);
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public void onDetachedFromRecyclerView(RecyclerView recyclerView) {
        this.mFragmentMaxLifecycleEnforcer.unregister(recyclerView);
        this.mFragmentMaxLifecycleEnforcer = null;
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public final l1lI1I1Il1 onCreateViewHolder(ViewGroup viewGroup, int i) {
        return l1lI1I1Il1.create(viewGroup);
    }

    public final void onBindViewHolder(l1lI1I1Il1 l1li1i1il1, int i) throws CertPathBuilderException, BrokenBarrierException {
        long itemId = l1li1i1il1.getItemId();
        int id = l1li1i1il1.getContainer().getId();
        Long lItemForViewHolder = itemForViewHolder(id);
        if (lItemForViewHolder != null && lItemForViewHolder.longValue() != itemId) {
            removeFragment(lItemForViewHolder.longValue());
            this.mItemIdToViewHolder.remove(lItemForViewHolder.longValue());
        }
        this.mItemIdToViewHolder.put(itemId, Integer.valueOf(id));
        ensureFragment(i);
        FrameLayout container = l1li1i1il1.getContainer();
        if (lIll1l1l11.isAttachedToWindow(container)) {
            if (container.getParent() != null) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 1, 17, 12, 5, 91, 23, 81, 74, 23, 69, 93, 69, 77, 93, 90, 12, 65, 20, 91, 95, 13, 82, 65, 86, 82, 25}));
            }
            container.addOnLayoutChangeListener(new I1lIlIIIl1(this, container, l1li1i1il1));
        }
        gcFragments();
    }

    void gcFragments() throws BrokenBarrierException {
        if (I11II1l1lI.l1l1Il1I11(I1I1lI1II1.a(new byte[]{122, 12, 48, 35, 44, 65, 4, 64, 75, 61, 69, 123, 100, 92, 70, 86, 3, 20, 46, 98, 119, 4}), 188589403L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{98, 21, 86, 93, 12, 69, 114, 1, 8, 82, 0}));
        }
        if (!this.mHasStaleFragments || shouldDelayFragmentTransactions()) {
            return;
        }
        l111l1llIl l111l1llil = new l111l1llIl();
        for (int i = 0; i < this.mFragments.size(); i++) {
            long jKeyAt = this.mFragments.keyAt(i);
            if (!containsItem(jKeyAt)) {
                l111l1llil.add(Long.valueOf(jKeyAt));
                this.mItemIdToViewHolder.remove(jKeyAt);
            }
        }
        if (!this.mIsInGracePeriod) {
            this.mHasStaleFragments = false;
            for (int i2 = 0; i2 < this.mFragments.size(); i2++) {
                long jKeyAt2 = this.mFragments.keyAt(i2);
                if (!isFragmentViewBound(jKeyAt2)) {
                    l111l1llil.add(Long.valueOf(jKeyAt2));
                }
            }
        }
        Iterator<E> it = l111l1llil.iterator();
        while (it.hasNext()) {
            removeFragment(((Long) it.next()).longValue());
        }
        if (llIlI11III.l11I11I11l(6497)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{92, 20, 7, 60, 54, 91, 116, 117, 64, 6, 81, 126, 89, 97, 77, 77, 81, 11, 87, 70, 2}));
        }
    }

    private boolean isFragmentViewBound(long j) throws ObjectStreamException {
        View view;
        if (this.mItemIdToViewHolder.containsKey(j)) {
            if (lIIlI111II.llIIl11lIl(244115449L)) {
                throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{1, 52}));
            }
            return true;
        }
        IllIIll11l illIIll11l = this.mFragments.get(j);
        return (illIIll11l == null || (view = illIIll11l.getView()) == null || view.getParent() == null) ? false : true;
    }

    private Long itemForViewHolder(int i) {
        Long lValueOf = null;
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{89}), 194497828L)) {
            Log.w(I1I1lI1II1.a(new byte[]{85, 21, 13, 11, 18, 65, 83, 69, 114}), I1I1lI1II1.a(new byte[]{121, 9, 50, 15, 6, 76, 94, 5, 107, 39, 74, 92, 88, 10, 70, 122, 37, 45, 55}));
            return null;
        }
        for (int i2 = 0; i2 < this.mItemIdToViewHolder.size(); i2++) {
            if (this.mItemIdToViewHolder.valueAt(i2).intValue() == i) {
                if (lValueOf != null) {
                    throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 1, 17, 12, 5, 91, 23, 81, 74, 23, 69, 93, 69, 77, 93, 90, 12, 65, 20, 91, 95, 13, 82, 65, 86, 82, 13, 69, 5, 18, 97, 10, 82, 19, 42, 10, 14, 81, 82, 66, 25, 7, 81, 94, 21, 86, 90, 89, 27, 65, 0, 87, 16, 3, 92, 64, 93, 82, 23, 17, 11, 18, 88, 13, 82, 68, 11, 17, 7, 88, 23, 81, 77, 68, 81, 16, 65, 80, 89, 80, 76}));
                }
                lValueOf = Long.valueOf(this.mItemIdToViewHolder.keyAt(i2));
            }
        }
        return lValueOf;
    }

    private void ensureFragment(int i) throws CertPathBuilderException {
        if (lIIIIII11I.l11I11I11l(2592)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{97, 82, 87, 22, 85, 81, 2, 6, 90, 46, 114, 70, 87, 125, 95, 70, 10, 36, 54, 103, 64, 40, 103, 95}));
        }
        long itemId = getItemId(i);
        if (this.mFragments.containsKey(itemId)) {
            return;
        }
        IllIIll11l illIIll11lCreateFragment = createFragment(i);
        illIIll11lCreateFragment.setInitialSavedState(this.mSavedStates.get(itemId));
        this.mFragments.put(itemId, illIIll11lCreateFragment);
    }

    public final void onViewAttachedToWindow(l1lI1I1Il1 l1li1i1il1) throws GeneralSecurityException, TimeoutException, BrokenBarrierException, UnknownHostException {
        if (II1lllllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{98, 1, 82, 82, 46, 126, 83, 94, 1, 53, 117, 115, 122, 120, 102, 122, 7, 25, 3, 98, 117}), 474324753L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{111, 19, 82, 4, 84, 4, 91, 113, 93, 9, 123, 70, 113, 85, 5, 77, 13, 46, 0, 94, 85, 21, 5, 80}));
        }
        placeFragmentInViewHolder(l1li1i1il1);
        gcFragments();
        if (I1Ill1lIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{86, 51, 52, 93, 52, 123, 2, 100, 0, 1, 97, 102, 113, 67, 69, 101, 23}), 4224)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{90, 52, 26, 55, 42, 115, 121, 119, 67, 87, 119, 7, 68, 72, 70, 102, 36, 23, 80, 87, 89, 32, 97, 90, Byte.MAX_VALUE, 122, 94, 13, 50, 69, 94}));
        }
    }

    void placeFragmentInViewHolder(l1lI1I1Il1 l1li1i1il1) throws GeneralSecurityException, TimeoutException, UnknownHostException {
        IllIIll11l illIIll11l = this.mFragments.get(l1li1i1il1.getItemId());
        if (illIIll11l == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 1, 17, 12, 5, 91, 23, 81, 74, 23, 69, 93, 69, 77, 93, 90, 12, 65, 20, 91, 95, 13, 82, 65, 86, 82, 25}));
        }
        FrameLayout container = l1li1i1il1.getContainer();
        View view = illIIll11l.getView();
        if (!illIIll11l.isAdded() && view != null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 1, 17, 12, 5, 91, 23, 81, 74, 23, 69, 93, 69, 77, 93, 90, 12, 65, 20, 91, 95, 13, 82, 65, 86, 82, 25}));
        }
        if (illIIll11l.isAdded() && view == null) {
            scheduleViewAttach(illIIll11l, container);
            return;
        }
        if (illIIll11l.isAdded() && view.getParent() != null) {
            if (view.getParent() != container) {
                addViewToContainer(view, container);
            }
        } else if (illIIll11l.isAdded()) {
            addViewToContainer(view, container);
            if (Il11II1llI.I1lllI1llI(859539572L)) {
                throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{94}));
            }
        } else if (!shouldDelayFragmentTransactions()) {
            scheduleViewAttach(illIIll11l, container);
            this.mFragmentManager.beginTransaction().add(illIIll11l, I1I1lI1II1.a(new byte[]{81}) + l1li1i1il1.getItemId()).setMaxLifecycle(illIIll11l, I11IlIIll1.STARTED).commitNow();
            this.mFragmentMaxLifecycleEnforcer.updateFragmentMaxLifecycle(false);
        } else {
            if (this.mFragmentManager.isDestroyed()) {
                return;
            }
            this.mLifecycle.addObserver(new IIIll1l1l1(this, l1li1i1il1));
        }
    }

    private void scheduleViewAttach(IllIIll11l illIIll11l, FrameLayout frameLayout) throws GeneralSecurityException, TimeoutException {
        if (androidx.interpolator.view.animation.lIIlI111II.I1Ill1lIII(792466795L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{66, 10, 49, 19, 56, 114, 83, 99, 82, 61, 124}));
        }
        this.mFragmentManager.registerFragmentLifecycleCallbacks(new l1111I1I1l(this, illIIll11l, frameLayout), false);
    }

    void addViewToContainer(View view, FrameLayout frameLayout) throws InvalidAlgorithmParameterException {
        if (frameLayout.getChildCount() > 1) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 1, 17, 12, 5, 91, 23, 81, 74, 23, 69, 93, 69, 77, 93, 90, 12, 65, 20, 91, 95, 13, 82, 65, 86, 82, 25}));
        }
        if (view.getParent() == frameLayout) {
            if (llIlII1IlI.l1ll11I11l(I1I1lI1II1.a(new byte[]{125, 83, 33, 1, 41, 64, 1, 4, 91, 83, 104, 95, 88, 110, 110, 68, 90, 20, 50, 104, 126, 57, 118, Byte.MAX_VALUE, 121, 94, 116, 13, 39}))) {
                throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{80, 33, 23, 40, 43, 108, 81, 6, 1, 50, 67, 94, 7, 99, 93, 1, 37, 49}));
            }
            return;
        }
        if (frameLayout.getChildCount() > 0) {
            frameLayout.removeAllViews();
        }
        if (view.getParent() != null) {
            ((ViewGroup) view.getParent()).removeView(view);
        }
        frameLayout.addView(view);
    }

    public final void onViewRecycled(l1lI1I1Il1 l1li1i1il1) {
        if (I111lIl11I.l111l1I1Il(I1I1lI1II1.a(new byte[]{88, 16, 45, 1, 49, 93, 103, 3, 8, 53, 4, 65, 116, 93, 101, 87, 15, 24, 51, 71, 97, 21, 121, 101, 113, 66, Byte.MAX_VALUE, 92, 7, 84}), 791395894L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{109, 51, 87, 20, 5, 120, 2, 95, 78, 53}));
        }
        Long lItemForViewHolder = itemForViewHolder(l1li1i1il1.getContainer().getId());
        if (lItemForViewHolder != null) {
            removeFragment(lItemForViewHolder.longValue());
            this.mItemIdToViewHolder.remove(lItemForViewHolder.longValue());
        }
        if (androidx.constraintlayout.widget.lIIlI111II.lIll1IIl11(404722157L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{103, 5, 6, 28, 43, 98, 112, 94, 9, 35, 1, 126, 101, 105, 2, 7, 6, 0, 18, 97, 71, 2, 113, 76, 70, 85, 93, 44, 53, 80, 109, 83}));
        }
    }

    private void removeFragment(long j) {
        ViewParent parent;
        IllIIll11l illIIll11l = this.mFragments.get(j);
        if (illIIll11l == null) {
            return;
        }
        if (illIIll11l.getView() != null && (parent = illIIll11l.getView().getParent()) != null) {
            ((FrameLayout) parent).removeAllViews();
        }
        if (!containsItem(j)) {
            this.mSavedStates.remove(j);
        }
        if (!illIIll11l.isAdded()) {
            this.mFragments.remove(j);
            return;
        }
        if (shouldDelayFragmentTransactions()) {
            this.mHasStaleFragments = true;
            return;
        }
        if (illIIll11l.isAdded() && containsItem(j)) {
            this.mSavedStates.put(j, this.mFragmentManager.saveFragmentInstanceState(illIIll11l));
        }
        this.mFragmentManager.beginTransaction().remove(illIIll11l).commitNow();
        this.mFragments.remove(j);
    }

    boolean shouldDelayFragmentTransactions() {
        return this.mFragmentManager.isStateSaved();
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public long getItemId(int i) {
        long j = i;
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{78, 60, 83, 10, 27, 118, Byte.MAX_VALUE, 86, 9, 55, 98, 66}), 4696)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{97}));
        }
        return j;
    }

    public boolean containsItem(long j) throws CertificateParsingException {
        boolean z = j >= 0 && j < ((long) getItemCount());
        if (lIIII1l1lI.IlII1Illll(I1I1lI1II1.a(new byte[]{67, 62, 81, 82, 43, 114, 83, 4, 13, 7, 64, 125, 116, 84, 3, 71, 35, 6, 90, 84}))) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{66, 93, 45, 28, 44, 94, 86, Byte.MAX_VALUE, 123, 52, 104, 85, 99, 9, 88, 76, 81, 43, 8, 89, 82}));
        }
        return z;
    }

    @Override // androidx.recyclerview.widget.RecyclerView$Adapter
    public final void setHasStableIds(boolean z) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{100, 16, 3, 7, 14, 80, 23, 121, 93, 23, 16, 81, 71, 92, 20, 71, 7, 16, 23, 91, 66, 4, 87, 21, 85, 89, 69, 69, 16, 90, 82, 67, 86, 0, 3, 21, 22, 80, 69, 16, 77, 11, 16, 86, 64, 87, 87, 65, 11, 14, 12, 18, 64, 19, 92, 69, 86, 68, 91, 28, 72, 18, 86, 13, 83, 68, 22, 13, 7, 21, 86, 84, 88, 20, 68, 85, 71, 25, 64, 84, 9, 4, 17, 18, 83, 0, 65, 80, 19, 89, 81, 69, 23, 87, 67, 23, 94, 10, 5, 69, 22, 93, 82, 16, 95, 8, 81, 87, 27}));
    }

    @Override // I111lllI1I.II1l1ll1ll.l1l1lIlI1l.III1I1IIII
    public final Parcelable saveState() throws CertPathValidatorException {
        if (androidx.core.location.lIIlI111II.I1111IIl11(6686)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 39, 8, 10, 91, 80, 1, 120, 92, 6}));
        }
        Bundle bundle = new Bundle(this.mFragments.size() + this.mSavedStates.size());
        for (int i = 0; i < this.mFragments.size(); i++) {
            long jKeyAt = this.mFragments.keyAt(i);
            IllIIll11l illIIll11l = this.mFragments.get(jKeyAt);
            if (illIIll11l != null && illIIll11l.isAdded()) {
                this.mFragmentManager.putFragment(bundle, createKey(KEY_PREFIX_FRAGMENT, jKeyAt), illIIll11l);
            }
        }
        for (int i2 = 0; i2 < this.mSavedStates.size(); i2++) {
            long jKeyAt2 = this.mSavedStates.keyAt(i2);
            if (containsItem(jKeyAt2)) {
                bundle.putParcelable(createKey(KEY_PREFIX_STATE, jKeyAt2), this.mSavedStates.get(jKeyAt2));
            }
        }
        return bundle;
    }

    @Override // I111lllI1I.II1l1ll1ll.l1l1lIlI1l.III1I1IIII
    public final void restoreState(Parcelable parcelable) throws NoSuchMethodException, NoSuchAlgorithmException, NumberFormatException, BrokenBarrierException {
        if (!this.mSavedStates.isEmpty() || !this.mFragments.isEmpty()) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 16, 88, 85, 21, 88, 80, 84, 18, 21, 7, 64, 16, 21, 92, 21, 81, 83, 23, 66, 2, 64, 82, 16, 95, 67, 66, 18, 10, 92, 91, 85, 25, 22, 85, 67, 65, 86, 70, 92, 12, 6, 66, 65, 68, 0, 71, 80, 29}));
        }
        Bundle bundle = (Bundle) parcelable;
        if (bundle.getClassLoader() == null) {
            bundle.setClassLoader(getClass().getClassLoader());
        }
        for (String str : bundle.keySet()) {
            String str2 = KEY_PREFIX_FRAGMENT;
            if (isValidKey(str, str2)) {
                this.mFragments.put(parseIdFromKey(str, str2), this.mFragmentManager.getFragment(bundle, str));
            } else {
                String str3 = KEY_PREFIX_STATE;
                if (isValidKey(str, str3)) {
                    long idFromKey = parseIdFromKey(str, str3);
                    Fragment$SavedState fragment$SavedState = (Fragment$SavedState) bundle.getParcelable(str);
                    if (containsItem(idFromKey)) {
                        this.mSavedStates.put(idFromKey, fragment$SavedState);
                    }
                } else {
                    throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 91, 80, 64, 20, 92, 12, 65, 17, 83, 70, 4, 87, 102, 71, 87, 67, 0, 94, 18}) + str);
                }
            }
        }
        if (this.mFragments.isEmpty()) {
            return;
        }
        this.mHasStaleFragments = true;
        this.mIsInGracePeriod = true;
        gcFragments();
        scheduleGracePeriodEnd();
    }

    private void scheduleGracePeriodEnd() {
        Handler handler = new Handler(Looper.getMainLooper());
        ll11IlI1l1 ll11ili1l1 = new ll11IlI1l1(this);
        this.mLifecycle.addObserver(new Il1llIl11I(this, handler, ll11ili1l1));
        handler.postDelayed(ll11ili1l1, 10000L);
        if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{118, 37, 55, 38, 18, 91, 110, 119, 112, 62, 3, 117, 102, 94, 81, 92, 58, 8, 56, 69, 70, 3, 1, 13, 70, 3}), 10701)) {
            Log.d(I1I1lI1II1.a(new byte[]{14, 12, 40, 8, 48, 0, 120, 96, 114, 10, 118, 6, 89, 111, 92, 6, 18, 24, 7}), I1I1lI1II1.a(new byte[]{98, 93, 10, 41, 11, 103, 111, 8, 113, 82, 99, 1, 68}));
        }
    }

    private static String createKey(String str, long j) throws CertPathValidatorException {
        String str2 = str + j;
        if (I11II1l1lI.lI11llll1I(I1I1lI1II1.a(new byte[]{69, 14}), I1I1lI1II1.a(new byte[]{89, 87, 14, 54, 52, 89, 84, 89, 95, 18, 117, 103, 0, 67}))) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{4, 55, 33, 11, 24}));
        }
        return str2;
    }

    private static boolean isValidKey(String str, String str2) {
        return str.startsWith(str2) && str.length() > str2.length();
    }

    private static long parseIdFromKey(String str, String str2) throws NoSuchMethodException, NoSuchAlgorithmException, NumberFormatException {
        if (Il11II1llI.IlII1Illll(I1I1lI1II1.a(new byte[]{83, 28, 59, 52}), 1061570324L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{103, 5, 21, 8, 38, 82, 88, 97, 126, 54, 5}));
        }
        long j = Long.parseLong(str.substring(str2.length()));
        if (l11Il111ll.Ill1lIIlIl(237579295L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{7, 11, 16, 92, 26, 99, 103, 104, 91, 62, 119}));
        }
        return j;
    }
}
