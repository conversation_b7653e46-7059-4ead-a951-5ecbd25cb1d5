package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import androidx.fragment.app.IllIIll11l;
import androidx.fragment.app.lII11llIIl;
import androidx.fragment.app.lIlII1IIl1;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertPathBuilderException;

/* loaded from: classes.dex */
class l1111I1I1l extends lIlII1IIl1 {
    final /* synthetic */ Il111I11Il this$0;
    final /* synthetic */ FrameLayout val$container;
    final /* synthetic */ IllIIll11l val$fragment;

    l1111I1I1l(Il111I11Il il111I11Il, IllIIll11l illIIll11l, FrameLayout frameLayout) {
        this.this$0 = il111I11Il;
        this.val$fragment = illIIll11l;
        this.val$container = frameLayout;
    }

    @Override // androidx.fragment.app.lIlII1IIl1
    public void onFragmentViewCreated(lII11llIIl lii11lliil, IllIIll11l illIIll11l, View view, Bundle bundle) throws CertPathBuilderException, InvalidAlgorithmParameterException {
        if (illIIll11l == this.val$fragment) {
            lii11lliil.unregisterFragmentLifecycleCallbacks(this);
            this.this$0.addViewToContainer(view, this.val$container);
        }
    }
}
