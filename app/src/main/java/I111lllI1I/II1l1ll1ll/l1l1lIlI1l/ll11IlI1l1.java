package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import java.util.concurrent.BrokenBarrierException;

/* loaded from: classes.dex */
class ll11IlI1l1 implements Runnable {
    final /* synthetic */ Il111I11Il this$0;

    ll11IlI1l1(Il111I11Il il111I11Il) {
        this.this$0 = il111I11Il;
    }

    @Override // java.lang.Runnable
    public void run() throws BrokenBarrierException {
        this.this$0.mIsInGracePeriod = false;
        this.this$0.gcFragments();
    }
}
