package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import android.os.Handler;
import androidx.core.location.I111I11Ill;
import java.security.UnrecoverableKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lll1llIIll;

/* loaded from: classes.dex */
class Il1llIl11I implements lIIllIlIl1 {
    final /* synthetic */ Il111I11Il this$0;
    final /* synthetic */ Handler val$handler;
    final /* synthetic */ Runnable val$runnable;

    Il1llIl11I(Il111I11Il il111I11Il, Handler handler, Runnable runnable) {
        this.this$0 = il111I11Il;
        this.val$handler = handler;
        this.val$runnable = runnable;
    }

    @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1
    public void onStateChanged(lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws UnrecoverableKeyException {
        if (I111I11Ill.l111IIlII1(I1I1lI1II1.a(new byte[]{113, 9, 81, 13, 55, 125, 4, 117, 74, 30, 122, 84, 111, 9, 98, 6, 37, 2, 38}), 206499634L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{94, 62, 45, 31}));
        }
        if (lll1lliill == lll1llIIll.ON_DESTROY) {
            this.val$handler.removeCallbacks(this.val$runnable);
            li1liill1i.getLifecycle().removeObserver(this);
        }
    }
}
