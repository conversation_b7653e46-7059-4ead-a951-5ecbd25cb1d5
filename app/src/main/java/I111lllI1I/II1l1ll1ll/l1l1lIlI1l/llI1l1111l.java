package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import IlIlI11III.I1lIl11III.lI1l111ll1.l1l11I11II.ll1I1l1lII;
import androidx.interpolator.view.animation.lIIlI111II;
import java.io.StreamCorruptedException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llI1l1111l extends ll1I1l1lII {
    final /* synthetic */ II1lII1l1I this$1;

    llI1l1111l(II1lII1l1I iI1lII1l1I) {
        this.this$1 = iI1lII1l1I;
    }

    @Override // IlIlI11III.I1lIl11III.lI1l111ll1.l1l11I11II.ll1I1l1lII
    public void onPageScrollStateChanged(int i) {
        this.this$1.updateFragmentMaxLifecycle(false);
    }

    @Override // IlIlI11III.I1lIl11III.lI1l111ll1.l1l11I11II.ll1I1l1lII
    public void onPageSelected(int i) throws StreamCorruptedException {
        if (lIIlI111II.I1lll11llI(2242)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{69, 86, 81, 43, 16, 111}));
        }
        this.this$1.updateFragmentMaxLifecycle(false);
    }
}
