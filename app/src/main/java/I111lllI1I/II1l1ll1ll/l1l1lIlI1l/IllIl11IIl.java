package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import android.support.v4.graphics.drawable.lIIlI111II;
import java.security.cert.CertificateNotYetValidException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lll1llIIll;

/* loaded from: classes.dex */
class IllIl11IIl implements lIIllIlIl1 {
    final /* synthetic */ II1lII1l1I this$1;

    IllIl11IIl(II1lII1l1I iI1lII1l1I) {
        this.this$1 = iI1lII1l1I;
    }

    @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1
    public void onStateChanged(lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws CertificateNotYetValidException, BrokenBarrierException {
        if (android.support.v4.graphics.drawable.lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{113, 14, 5, 10, 37, 65, 67, 95, 77, 43, 99, 120, 79, 88}), 9043)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{15, 0, 48, 49, 19, 112, 95, 69, 83, 55, 123, 87, 12, 105, 80, 87, 49, 11, 44, 95, 82, 86, 98, 76, 2, 92, 113, 2, 41, 92}));
        }
        this.this$1.updateFragmentMaxLifecycle(false);
        if (lIIlI111II.I11II1111l(207279245L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{124, 46, 38, 34, 83, 98, 98, 70, 104, 33, 120, 114, 109, 67, 70, 65, 43, 38, 54, 68, 125, 14, 122, 84}));
        }
    }
}
