package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

/* loaded from: classes.dex */
class lll1l1lI1I extends I1111l11ll {
    final /* synthetic */ II1lII1l1I this$1;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lll1l1lI1I(II1lII1l1I iI1lII1l1I) {
        super(null);
        this.this$1 = iI1lII1l1I;
    }

    @Override // I111lllI1I.II1l1ll1ll.l1l1lIlI1l.I1111l11ll, androidx.recyclerview.widget.RecyclerView$AdapterDataObserver
    public void onChanged() {
        this.this$1.updateFragmentMaxLifecycle(true);
    }
}
