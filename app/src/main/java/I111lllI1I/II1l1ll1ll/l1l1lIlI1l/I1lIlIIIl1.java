package I111lllI1I.II1l1ll1ll.l1l1lIlI1l;

import android.view.View;
import android.view.View$OnLayoutChangeListener;
import android.widget.FrameLayout;
import androidx.constraintlayout.widget.lIIlI111II;
import java.net.UnknownHostException;
import java.security.AccessControlException;
import java.security.GeneralSecurityException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1lIlIIIl1 implements View$OnLayoutChangeListener {
    final /* synthetic */ Il111I11Il this$0;
    final /* synthetic */ FrameLayout val$container;
    final /* synthetic */ l1lI1I1Il1 val$holder;

    I1lIlIIIl1(Il111I11Il il111I11Il, FrameLayout frameLayout, l1lI1I1Il1 l1li1i1il1) {
        this.this$0 = il111I11Il;
        this.val$container = frameLayout;
        this.val$holder = l1li1i1il1;
    }

    @Override // android.view.View$OnLayoutChangeListener
    public void onLayoutChange(View view, int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8) throws GeneralSecurityException, TimeoutException, UnknownHostException {
        if (this.val$container.getParent() != null) {
            this.val$container.removeOnLayoutChangeListener(this);
            this.this$0.placeFragmentInViewHolder(this.val$holder);
        }
        if (lIIlI111II.llI1llI1l1(523042240L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{116, 19, 83, 20, 3, 102, 78, 3, 15, 55, 72, 125, 64, 12, 97, 67, 1, 19, 52, 68, 124, 36, 107, 99}));
        }
    }
}
