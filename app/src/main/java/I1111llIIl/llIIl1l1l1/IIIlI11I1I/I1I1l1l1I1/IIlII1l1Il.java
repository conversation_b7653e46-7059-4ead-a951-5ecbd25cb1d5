package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import android.accounts.utils.IIIlIl1I1l;
import android.media.content.lll1IIII11;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.util.Log;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.media3.common.II1ll1I1lI;
import androidx.media3.common.IIll1I11lI;
import androidx.media3.common.Il11IllI1l;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.l11llI1lI1;
import androidx.media3.common.l1I1l1Il11;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.io.FileNotFoundException;
import java.io.InvalidObjectException;
import java.io.NotSerializableException;
import java.io.SyncFailedException;
import java.net.NoRouteToHostException;
import java.net.UnknownHostException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;

/* loaded from: classes.dex */
public final class IIlII1l1Il {
    private IIlII1l1Il() {
    }

    public static IlI11Ill11 addAdGroupToAdPlaybackState(IlI11Ill11 ilI11Ill11, long j, long j2, long... jArr) throws SyncFailedException, NotSerializableException, CertificateEncodingException {
        long mediaPeriodPositionUsForContent = getMediaPeriodPositionUsForContent(j, -1, ilI11Ill11);
        int i = ilI11Ill11.removedAdGroupCount;
        while (i < ilI11Ill11.adGroupCount && ilI11Ill11.getAdGroup(i).timeUs != Long.MIN_VALUE && ilI11Ill11.getAdGroup(i).timeUs <= mediaPeriodPositionUsForContent) {
            i++;
        }
        IlI11Ill11 ilI11Ill11WithContentResumeOffsetUs = ilI11Ill11.withNewAdGroup(i, mediaPeriodPositionUsForContent).withIsServerSideInserted(i, true).withAdCount(i, jArr.length).withAdDurationsUs(i, jArr).withContentResumeOffsetUs(i, j2);
        IlI11Ill11 ilI11Ill11WithSkippedAd = ilI11Ill11WithContentResumeOffsetUs;
        for (int i2 = 0; i2 < jArr.length && jArr[i2] == 0; i2++) {
            ilI11Ill11WithSkippedAd = ilI11Ill11WithSkippedAd.withSkippedAd(i, i2);
        }
        return correctFollowingAdGroupTimes(ilI11Ill11WithSkippedAd, i, llIIlI1llI.sum(jArr), j2);
    }

    public static long getStreamPositionUs(II1ll1I1lI iI1ll1I1lI, IlI11Ill11 ilI11Ill11) throws InvalidObjectException, CertificateException, NoSuchProviderException, NoRouteToHostException {
        l1I1l1Il11 currentTimeline = iI1ll1I1lI.getCurrentTimeline();
        if (currentTimeline.isEmpty()) {
            return -9223372036854775807L;
        }
        Il11IllI1l period = currentTimeline.getPeriod(iI1ll1I1lI.getCurrentPeriodIndex(), new Il11IllI1l());
        if (!llIIlI1llI.areEqual(period.getAdsId(), ilI11Ill11.adsId)) {
            if (IIlIIlIII1.l11I11I11l(3061)) {
                throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{68, 60, 49, 48, 50, 68, 102, 9, 0, 44, 115, 116, 115, 85, 67, 120, 17, 25, 46, 122}));
            }
            return -9223372036854775807L;
        }
        if (iI1ll1I1lI.isPlayingAd()) {
            long streamPositionUsForAd = getStreamPositionUsForAd(llIIlI1llI.msToUs(iI1ll1I1lI.getCurrentPosition()), iI1ll1I1lI.getCurrentAdGroupIndex(), iI1ll1I1lI.getCurrentAdIndexInAdGroup(), ilI11Ill11);
            if (lll1IIII11.llII1lIIlI(219310808L)) {
                throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{112, 54, 41, 41, 59, 13, 64, 115, 106}));
            }
            return streamPositionUsForAd;
        }
        long streamPositionUsForContent = getStreamPositionUsForContent(llIIlI1llI.msToUs(iI1ll1I1lI.getCurrentPosition()) - period.getPositionInWindowUs(), -1, ilI11Ill11);
        if (IIIlIll111.Il1IIlI1II(7035)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{98, 50, 46, 51, 44, 94, 91, 124, 65, 15, 83, 68, 96, 78, 119, 82, 38, 57, 80, 113, 86, 45, 91}));
        }
        return streamPositionUsForContent;
    }

    public static long getStreamPositionUs(long j, IIll1I11lI iIll1I11lI, IlI11Ill11 ilI11Ill11) {
        if (iIll1I11lI.isAd()) {
            return getStreamPositionUsForAd(j, iIll1I11lI.adGroupIndex, iIll1I11lI.adIndexInAdGroup, ilI11Ill11);
        }
        return getStreamPositionUsForContent(j, iIll1I11lI.nextAdGroupIndex, ilI11Ill11);
    }

    public static long getMediaPeriodPositionUs(long j, IIll1I11lI iIll1I11lI, IlI11Ill11 ilI11Ill11) {
        if (iIll1I11lI.isAd()) {
            return getMediaPeriodPositionUsForAd(j, iIll1I11lI.adGroupIndex, iIll1I11lI.adIndexInAdGroup, ilI11Ill11);
        }
        return getMediaPeriodPositionUsForContent(j, iIll1I11lI.nextAdGroupIndex, ilI11Ill11);
    }

    public static long getStreamPositionUsForAd(long j, int i, int i2, IlI11Ill11 ilI11Ill11) throws CertificateNotYetValidException, NoSuchProviderException {
        int i3;
        if (IIlI1ll1ll.llll111lI1(I1I1lI1II1.a(new byte[]{7, 23, 4, 36, 83, 83, 66, 95, 76, 83, 106, 9, 13, 9, 98, 114, 11, 27, 46, 88, 89, 43, 96, 96, 120, 110}))) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{100, 23, 4, 18}));
        }
        l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i);
        long j2 = j + adGroup.timeUs;
        int i4 = ilI11Ill11.removedAdGroupCount;
        while (true) {
            i3 = 0;
            if (i4 >= i) {
                break;
            }
            l11llI1lI1 adGroup2 = ilI11Ill11.getAdGroup(i4);
            while (i3 < getAdCountInGroup(ilI11Ill11, i4)) {
                j2 += adGroup2.durationsUs[i3];
                i3++;
            }
            j2 -= adGroup2.contentResumeOffsetUs;
            i4++;
        }
        if (i2 < getAdCountInGroup(ilI11Ill11, i)) {
            while (i3 < i2) {
                j2 += adGroup.durationsUs[i3];
                i3++;
            }
        }
        if (l111Il1lI1.I1lIllll1l(I1I1lI1II1.a(new byte[]{123, 42}))) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{88, 47, 39, 29, 47, 65, 122}));
        }
        return j2;
    }

    public static long getMediaPeriodPositionUsForAd(long j, int i, int i2, IlI11Ill11 ilI11Ill11) {
        int i3;
        l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i);
        long j2 = j - adGroup.timeUs;
        int i4 = ilI11Ill11.removedAdGroupCount;
        while (true) {
            i3 = 0;
            if (i4 >= i) {
                break;
            }
            l11llI1lI1 adGroup2 = ilI11Ill11.getAdGroup(i4);
            while (i3 < getAdCountInGroup(ilI11Ill11, i4)) {
                j2 -= adGroup2.durationsUs[i3];
                i3++;
            }
            j2 += adGroup2.contentResumeOffsetUs;
            i4++;
        }
        if (i2 < getAdCountInGroup(ilI11Ill11, i)) {
            while (i3 < i2) {
                j2 -= adGroup.durationsUs[i3];
                i3++;
            }
        }
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{95, 39, 82}), 372777188L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{83, 54, 22, 85, 49, 13, 88, 100, 13, 38, 94, 121, 13, 77, 115, 122}));
        }
        return j2;
    }

    public static long getStreamPositionUsForContent(long j, int i, IlI11Ill11 ilI11Ill11) {
        int i2 = i == -1 ? ilI11Ill11.adGroupCount : i;
        long j2 = 0;
        for (int i3 = ilI11Ill11.removedAdGroupCount; i3 < i2; i3++) {
            l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i3);
            if (adGroup.timeUs == Long.MIN_VALUE || adGroup.timeUs > j) {
                break;
            }
            long j3 = adGroup.timeUs + j2;
            for (int i4 = 0; i4 < getAdCountInGroup(ilI11Ill11, i3); i4++) {
                j2 += adGroup.durationsUs[i4];
            }
            j2 -= adGroup.contentResumeOffsetUs;
            if (adGroup.timeUs + adGroup.contentResumeOffsetUs > j) {
                long jMax = Math.max(j3, j + j2);
                if (!I1IllIll1l.l11I11I11l(222083034L)) {
                    return jMax;
                }
                Log.v(I1I1lI1II1.a(new byte[]{70, 82, 10, 54, 46, 100, 112, 105, 77}), I1I1lI1II1.a(new byte[]{70}));
                return 0L;
            }
        }
        return j + j2;
    }

    public static long getMediaPeriodPositionUsForContent(long j, int i, IlI11Ill11 ilI11Ill11) {
        if (i == -1) {
            i = ilI11Ill11.adGroupCount;
        }
        long j2 = 0;
        for (int i2 = ilI11Ill11.removedAdGroupCount; i2 < i; i2++) {
            l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i2);
            if (adGroup.timeUs == Long.MIN_VALUE || adGroup.timeUs > j - j2) {
                break;
            }
            for (int i3 = 0; i3 < getAdCountInGroup(ilI11Ill11, i2); i3++) {
                j2 += adGroup.durationsUs[i3];
            }
            j2 -= adGroup.contentResumeOffsetUs;
            long j3 = j - j2;
            if (adGroup.timeUs + adGroup.contentResumeOffsetUs > j3) {
                long jMax = Math.max(adGroup.timeUs, j3);
                if (!IIlI1ll1ll.lI11llll1I(I1I1lI1II1.a(new byte[]{101, 23, 91, 39, 87, 79, 91, 3, 115, 42}), I1I1lI1II1.a(new byte[]{69, 80, 33, 43, 41}))) {
                    return jMax;
                }
                Log.v(I1I1lI1II1.a(new byte[]{82, 5, 35, 48, 15, 80, 68}), I1I1lI1II1.a(new byte[]{112, 52, 10, 93, 80, 118, 68, 118, 64, 33, 124, 103, 122, 87, 108, 113, 9, 13, 33, 7, 81, 6, 7, 113, 118}));
                return 0L;
            }
        }
        return j - j2;
    }

    public static int getAdCountInGroup(IlI11Ill11 ilI11Ill11, int i) throws UnknownHostException {
        l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i);
        int i2 = adGroup.count == -1 ? 0 : adGroup.count;
        if (IIlI1Il1lI.l11I11I11l(179403542L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{78, 20, 53, 60, 23}));
        }
        return i2;
    }

    private static IlI11Ill11 correctFollowingAdGroupTimes(IlI11Ill11 ilI11Ill11, int i, long j, long j2) throws FileNotFoundException {
        long j3 = (-j) + j2;
        while (true) {
            i++;
            if (i >= ilI11Ill11.adGroupCount) {
                break;
            }
            long j4 = ilI11Ill11.getAdGroup(i).timeUs;
            if (j4 != Long.MIN_VALUE) {
                ilI11Ill11 = ilI11Ill11.withAdGroupTimeUs(i, j4 + j3);
            }
        }
        if (IIIlIl1I1l.Ill1lIIlIl(560)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{123, 49, 82, 86, 82, 6, 79, 71}));
        }
        return ilI11Ill11;
    }
}
