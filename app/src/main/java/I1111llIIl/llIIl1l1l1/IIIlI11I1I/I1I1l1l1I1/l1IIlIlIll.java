package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import IIIl11ll1l.l1lI11lI1I.IIIl1llIlI.lI1lIll11I.I1IIIIIlI1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I11l111l1l;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II;
import android.net.Uri;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.media3.common.l1I1l1Il11;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.net.UnknownHostException;
import java.security.DigestException;
import java.security.ProviderException;
import java.security.cert.CertificateEncodingException;
import java.util.ArrayList;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l1IIlIlIll {
    private final List<I11l111l1l> activeMediaPeriods = new ArrayList();
    private llIl1I11II adMediaSource;
    private Uri adUri;
    private final I1IIl111I1 id;
    final /* synthetic */ lIllI1I1Il this$0;
    private l1I1l1Il11 timeline;

    public l1IIlIlIll(lIllI1I1Il lilli1i1il, I1IIl111I1 i1IIl111I1) {
        this.this$0 = lilli1i1il;
        this.id = i1IIl111I1;
    }

    public void initializeWithMediaSource(llIl1I11II llil1i11ii, Uri uri) {
        this.adMediaSource = llil1i11ii;
        this.adUri = uri;
        for (int i = 0; i < this.activeMediaPeriods.size(); i++) {
            I11l111l1l i11l111l1l = this.activeMediaPeriods.get(i);
            i11l111l1l.setMediaSource(llil1i11ii);
            i11l111l1l.setPrepareListener(new l111I111Il(this.this$0, uri));
        }
        this.this$0.prepareChildSource(this.id, llil1i11ii);
    }

    public I1IIlllI1I createMediaPeriod(I1IIl111I1 i1IIl111I1, I1IIIIIlI1 i1IIIIIlI1, long j) {
        I11l111l1l i11l111l1l = new I11l111l1l(i1IIl111I1, i1IIIIIlI1, j);
        this.activeMediaPeriods.add(i11l111l1l);
        llIl1I11II llil1i11ii = this.adMediaSource;
        if (llil1i11ii != null) {
            i11l111l1l.setMediaSource(llil1i11ii);
            i11l111l1l.setPrepareListener(new l111I111Il(this.this$0, (Uri) lIlII1IIl1.checkNotNull(this.adUri)));
        }
        l1I1l1Il11 l1i1l1il11 = this.timeline;
        if (l1i1l1il11 != null) {
            i11l111l1l.createPeriod(new I1IIl111I1(l1i1l1il11.getUidOfPeriod(0), i1IIl111I1.windowSequenceNumber));
        }
        if (Il1lII1l1l.Ill1lIIlIl(7906)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{111, 8, 23, 10, 91, 91, 3, 83, 65, 43, 121, 88, 6, 12, 77, 67, 23, 4}));
        }
        return i11l111l1l;
    }

    public void handleSourceInfoRefresh(l1I1l1Il11 l1i1l1il11) {
        if (IIlI1ll1ll.l111l1I1Il(I1I1lI1II1.a(new byte[]{89}), 304541322L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{118, 1, 58, 22, 85, 125, 117, 118, 15, 6, 0, 117}));
        }
        lIlII1IIl1.checkArgument(l1i1l1il11.getPeriodCount() == 1);
        if (this.timeline == null) {
            Object uidOfPeriod = l1i1l1il11.getUidOfPeriod(0);
            for (int i = 0; i < this.activeMediaPeriods.size(); i++) {
                I11l111l1l i11l111l1l = this.activeMediaPeriods.get(i);
                i11l111l1l.createPeriod(new I1IIl111I1(uidOfPeriod, i11l111l1l.id.windowSequenceNumber));
            }
        }
        this.timeline = l1i1l1il11;
        if (ll1l11I1II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{109, 80, 91, 43, 58, 103, 82, 83, 106, 43, 113, 9, 111, 81, 110, 103, 10, 80, 41, 80, 9, 14, 67, 112, 90, 85, 0, 60, 6, 64, 91}), 347350511L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{123, 60, 12, 11, 50, 125, 101, 65, 125, 17, 113, 105, 3, 106, 117, 111, 83, 3, 12, 69}));
        }
    }

    public long getDurationUs() throws UnknownHostException {
        l1I1l1Il11 l1i1l1il11 = this.timeline;
        long durationUs = l1i1l1il11 == null ? -9223372036854775807L : l1i1l1il11.getPeriod(0, this.this$0.period).getDurationUs();
        if (Il1I1lllIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{114}), I1I1lI1II1.a(new byte[]{86, 40, 6, 63, 50}))) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{68}));
        }
        return durationUs;
    }

    public void releaseMediaPeriod(I11l111l1l i11l111l1l) {
        this.activeMediaPeriods.remove(i11l111l1l);
        i11l111l1l.releasePeriod();
        if (ll1l11I1II.I111IlIl1I(I1I1lI1II1.a(new byte[]{95, 53, 53, 22, 7, 93, 1, 86, 80}), 395163942L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{102, 33, 85, 29, 80, 12, 125, 101, 108, 47, Byte.MAX_VALUE, 94, 111, 97, 94, 89, 46, 10, 83, 80, 122, 0, 123, 119, 98, 90, 80, 11}));
        }
    }

    public void release() {
        if (hasMediaSource()) {
            lIllI1I1Il.access$700(this.this$0, this.id);
        }
    }

    public boolean hasMediaSource() {
        return this.adMediaSource != null;
    }

    public boolean isInactive() throws CertificateEncodingException {
        boolean zIsEmpty = this.activeMediaPeriods.isEmpty();
        if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{92, 86, 3, 39, 58, 12, Byte.MAX_VALUE, 1, 94, 50, 66, 65, 12, 94, 90, 89, 15, 43, 20, 126}), 2529)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{125, 38, 33, 81, 33, 124, 15, 102, 91, 49, 117, 94, 84, 91, 103, 102, 27, 19, 16, 80, 126, 42, 80, 102, 66, 89, 64, 84, 22, 96}));
        }
        return zIsEmpty;
    }
}
