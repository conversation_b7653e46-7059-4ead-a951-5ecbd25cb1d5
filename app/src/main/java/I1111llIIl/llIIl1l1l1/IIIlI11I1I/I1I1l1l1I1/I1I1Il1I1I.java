package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1llIll1lI;
import android.accounts.utils.lIIlI111II;
import android.media.content.lIIllIlIl1;
import androidx.media3.common.Il11IllI1l;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.l1I1l1Il11;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* loaded from: classes.dex */
public final class I1I1Il1I1I extends l1llIll1lI {
    private final IlI11Ill11 adPlaybackState;

    public I1I1Il1I1I(l1I1l1Il11 l1i1l1il11, IlI11Ill11 ilI11Ill11) {
        super(l1i1l1il11);
        lIlII1IIl1.checkState(l1i1l1il11.getPeriodCount() == 1);
        lIlII1IIl1.checkState(l1i1l1il11.getWindowCount() == 1);
        this.adPlaybackState = ilI11Ill11;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1llIll1lI, androidx.media3.common.l1I1l1Il11
    public Il11IllI1l getPeriod(int i, Il11IllI1l il11IllI1l, boolean z) throws ClassNotFoundException {
        if (lIIllIlIl1.l11I11I11l(174002068L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{114, 30, 90, 92, 44, 84, 122, 67, 96, 93, 70, 103, 91, 111}));
        }
        this.timeline.getPeriod(i, il11IllI1l, z);
        il11IllI1l.set(il11IllI1l.id, il11IllI1l.uid, il11IllI1l.windowIndex, il11IllI1l.durationUs == -9223372036854775807L ? this.adPlaybackState.contentDurationUs : il11IllI1l.durationUs, il11IllI1l.getPositionInWindowUs(), this.adPlaybackState, il11IllI1l.isPlaceholder);
        if (lIIlI111II.II1lIllIll(7623)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{117}));
        }
        return il11IllI1l;
    }
}
