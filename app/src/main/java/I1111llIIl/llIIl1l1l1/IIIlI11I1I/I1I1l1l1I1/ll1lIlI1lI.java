package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llII1ll111;
import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.lIIll11Ill;
import IIl11IllI1.IIIII1I11I.I111IlIl1l.Il1IIIlI1l.Il1IlllI1I;
import IIl11IllI1.IIIII1I11I.I111IlIl1l.Il1IIIlI1l.lI11IIllII;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1111IIl11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIIlI11lll;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llI1l11IlI;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1lII1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llll1lI1II;
import android.accounts.utils.I1lllI11II;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import android.util.Pair;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.I111I11Ill;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.IIl1Il11Il;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.StreamKey;
import androidx.media3.common.l11I11I11l;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.EOFException;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.NotActiveException;
import java.io.UnsupportedEncodingException;
import java.net.BindException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.PortUnreachableException;
import java.security.DigestException;
import java.security.ProviderException;
import java.security.SignatureException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CRLException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;
import llIl11II1I.l11IIllI1I.I1Il1l1I1I.Il1lIIlIll.l1IlI1Il11;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class ll1lIlI1lI implements IIIlI11lll {
    private final I1IIlllI1I actualMediaPeriod;
    private IlI11Ill11 adPlaybackState;
    private boolean hasStartedPreparing;
    private boolean isPrepared;
    private Il1I1111l1 loadingPeriod;
    private final Object periodUid;
    private final List<Il1I1111l1> mediaPeriods = new ArrayList();
    private final Map<Long, Pair<I1111IIl11, llIl1lII1I>> activeLoads = new HashMap();
    public l1IlI1Il11[] trackSelections = new l1IlI1Il11[0];
    public l1l1IllI11[] sampleStreams = new l1l1IllI11[0];
    public llIl1lII1I[] lastDownstreamFormatChangeData = new llIl1lII1I[0];

    static /* synthetic */ List access$200(ll1lIlI1lI ll1lili1li) {
        if (IllIlllIII.l11I11I11l(7008)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{100, 46, 18, 6, 38, 101, 6, 126, 64, 60, 1, 124, 5, 86, 122, 123}));
        }
        List<Il1I1111l1> list = ll1lili1li.mediaPeriods;
        if (androidx.core.location.llIl1lII1I.IlIIl111lI(I1I1lI1II1.a(new byte[]{97, 41, 17, 84, 59, 71, 66, 83, 124, 49, Byte.MAX_VALUE, 0, 1, 110, 114, 108, 44, 12, 36, 5, 8, 87, 86, 76, 123, 110}), 165588962L)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{109, 83, 22, 8, 33, 4, 100, 5, 92}));
        }
        return list;
    }

    public ll1lIlI1lI(I1IIlllI1I i1IIlllI1I, Object obj, IlI11Ill11 ilI11Ill11) {
        this.actualMediaPeriod = i1IIlllI1I;
        this.periodUid = obj;
        this.adPlaybackState = ilI11Ill11;
    }

    public void updateAdPlaybackState(IlI11Ill11 ilI11Ill11) {
        this.adPlaybackState = ilI11Ill11;
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{93, 16, 54, 8, 48, 113, 67, 121, 84, 38, 86, 89, 66, 81, 120}), 387152685L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{81, 10, 90, 2, 27, 109, 103, 113, 93, 60, 104, 70, 12, 73, 93, 82}));
        }
    }

    public void add(Il1I1111l1 il1I1111l1) {
        this.mediaPeriods.add(il1I1111l1);
    }

    public void remove(Il1I1111l1 il1I1111l1) throws CertificateException {
        if (IIll1llI1l.Ill1lIIlIl(9354)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{96, 53, 56, 48, 52, 94, 2, 1, 64, 61, 64, 88, 126, 9, 97, 12, 38, 22, 52, 91, 4, 16, 2, 5, 114, 113, 14}));
        }
        if (il1I1111l1.equals(this.loadingPeriod)) {
            this.loadingPeriod = null;
            this.activeLoads.clear();
        }
        this.mediaPeriods.remove(il1I1111l1);
    }

    public boolean isUnused() throws CertPathValidatorException, CloneNotSupportedException {
        if (lIIlI111II.lI1lIIll11(408201414L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{71, 84, 42, 43, 37, 83, 67, Byte.MAX_VALUE, 14, 39, 9, 68, Byte.MAX_VALUE, 75, 120, 121, 56, 85, 90, 71, 88, 48, 87, 113, 70, 110, 68, 15, 83, 69, 70, 36}));
        }
        boolean zIsEmpty = this.mediaPeriods.isEmpty();
        if (androidx.core.location.lIIlI111II.ll1I111ll1(769)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{98, 11, 5, 81, 12}));
        }
        return zIsEmpty;
    }

    public void release(llIl1I11II llil1i11ii) throws SignatureException {
        llil1i11ii.releasePeriod(this.actualMediaPeriod);
        if (androidx.interpolator.view.animation.lIIlI111II.llI1llI1l1(234753993L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{1}));
        }
    }

    public boolean canReuseMediaPeriod(I1IIl111I1 i1IIl111I1, long j) throws CRLException, EOFException {
        if (l1lI1I1l11.I1lIllll1l(357311142L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{79, 34, 58, 21, 58, 7, 95, 117, 111, 45}));
        }
        Il1I1111l1 il1I1111l1 = (Il1I1111l1) lIIll11Ill.b(this.mediaPeriods);
        boolean z = IIlII1l1Il.getStreamPositionUs(j, i1IIl111I1, this.adPlaybackState) == IIlII1l1Il.getStreamPositionUs(I1Illl11II.getMediaPeriodEndPositionUs(il1I1111l1, this.adPlaybackState), il1I1111l1.mediaPeriodId, this.adPlaybackState);
        if (androidx.core.location.I1111IIl11.llll111lI1(I1I1lI1II1.a(new byte[]{5, 42, 48, 51, 35, 93, 82}), 7360)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{118, 34, 19, 18, 53, 71, 65, 124, 93, 80, 71, 123, 0, 117, 114, 69, 12, 16, 40, 100}));
        }
        return z;
    }

    public Il1I1111l1 getMediaPeriodForEvent(llIl1lII1I llil1lii1i) {
        if (I111I11Ill.IlIllIll1I(174972501L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{118, 19, 55, 80, 14, 1, 112, 90, 72, 29, 84, 81, 89, 110, 69, 80, 13, 42, 8, 67, 84, 51, 114, 116, 3}));
        }
        if (llil1lii1i == null || llil1lii1i.mediaStartTimeMs == -9223372036854775807L) {
            return null;
        }
        for (int i = 0; i < this.mediaPeriods.size(); i++) {
            Il1I1111l1 il1I1111l1 = this.mediaPeriods.get(i);
            long mediaPeriodPositionUs = IIlII1l1Il.getMediaPeriodPositionUs(llIIlI1llI.msToUs(llil1lii1i.mediaStartTimeMs), il1I1111l1.mediaPeriodId, this.adPlaybackState);
            long mediaPeriodEndPositionUs = I1Illl11II.getMediaPeriodEndPositionUs(il1I1111l1, this.adPlaybackState);
            if (mediaPeriodPositionUs >= 0 && mediaPeriodPositionUs < mediaPeriodEndPositionUs) {
                return il1I1111l1;
            }
        }
        return null;
    }

    public void prepare(Il1I1111l1 il1I1111l1, long j) throws PortUnreachableException {
        il1I1111l1.lastStartPositionUs = j;
        if (this.hasStartedPreparing) {
            if (this.isPrepared) {
                ((IIIlI11lll) lIlII1IIl1.checkNotNull(il1I1111l1.callback)).onPrepared(il1I1111l1);
            }
        } else {
            this.hasStartedPreparing = true;
            this.actualMediaPeriod.prepare(this, IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState));
            if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{113, 28, 45, 81, 8, 91, 83, 84, 91, 22, 64, 106, 76, 1, 126, 116, 91, 43, 19, 113, 6, 17, 65, 13, 85, 124, 83, 14, 34, 117, 122, 21}), 231505980L)) {
                throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{83, 81, 10, 21, 36, 98, 85, 90, 1, 40, 83, 8, 80, 122, 6, 97}));
            }
        }
    }

    public void maybeThrowPrepareError() throws IOException {
        if (androidx.recyclerview.widget.content.adapter.l1l1IllI11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{122, 42, 55, 53, 50, 109, 115, 72, 86, 87, 101, 68, 2, 122}), 5837)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{123, 32, 58, 22, 91, 82}));
        }
        this.actualMediaPeriod.maybeThrowPrepareError();
    }

    public llll1lI1II getTrackGroups() {
        return this.actualMediaPeriod.getTrackGroups();
    }

    public List<StreamKey> getStreamKeys(List<l1IlI1Il11> list) throws PortUnreachableException {
        List<StreamKey> streamKeys = this.actualMediaPeriod.getStreamKeys(list);
        if (l1lll111II.I1lllI1llI(7050)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{112, 46, 1, 82}));
        }
        return streamKeys;
    }

    public boolean continueLoading(Il1I1111l1 il1I1111l1, long j) throws InvalidClassException {
        Il1I1111l1 il1I1111l12 = this.loadingPeriod;
        if (il1I1111l12 != null && !il1I1111l1.equals(il1I1111l12)) {
            for (Pair<I1111IIl11, llIl1lII1I> pair : this.activeLoads.values()) {
                il1I1111l12.mediaSourceEventDispatcher.loadCompleted((I1111IIl11) pair.first, I1Illl11II.access$400(il1I1111l12, (llIl1lII1I) pair.second, this.adPlaybackState));
                il1I1111l1.mediaSourceEventDispatcher.loadStarted((I1111IIl11) pair.first, I1Illl11II.access$400(il1I1111l1, (llIl1lII1I) pair.second, this.adPlaybackState));
            }
        }
        this.loadingPeriod = il1I1111l1;
        boolean zContinueLoading = this.actualMediaPeriod.continueLoading(getStreamPositionUsWithNotYetStartedHandling(il1I1111l1, j));
        if (lII1llllI1.l111IIlII1(I1I1lI1II1.a(new byte[]{15, 37, 37, 7, 46, 1, 94, 122, 80, 84, 99, 89, 2, 112, 115, Byte.MAX_VALUE, 86, 5, 90, 0, 81, 37, 107, 69, 84, 101}), I1I1lI1II1.a(new byte[]{121, 37, 56, 19, 20, 97, 96, 3, 73, 51, 4, 95, 70, 117, 92, 70, 48, 56, 22, 94, 72, 25, 126, Byte.MAX_VALUE, 99, 96, 103, 9, 3, 74, 120}))) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{14, 47, 90, 13, 50, 109, 97, 124, 86, 47, 86, 68, 86, 67, 71, 102, 13, 23, 43, 86, 120, 18, 105, 95, 116, 67, 111}));
        }
        return zContinueLoading;
    }

    public boolean isLoading(Il1I1111l1 il1I1111l1) {
        return il1I1111l1.equals(this.loadingPeriod) && this.actualMediaPeriod.isLoading();
    }

    public long getBufferedPositionUs(Il1I1111l1 il1I1111l1) {
        return getMediaPeriodPositionUsWithEndOfSourceHandling(il1I1111l1, this.actualMediaPeriod.getBufferedPositionUs());
    }

    public long getNextLoadPositionUs(Il1I1111l1 il1I1111l1) {
        return getMediaPeriodPositionUsWithEndOfSourceHandling(il1I1111l1, this.actualMediaPeriod.getNextLoadPositionUs());
    }

    public long seekToUs(Il1I1111l1 il1I1111l1, long j) {
        if (II1lllllI1.llll111lI1(I1I1lI1II1.a(new byte[]{7, 87, 54, 10, 17, 70, 121, 66, 106, 34, 105, 64, 81, 93, 82, 4, 6, 57, 1}), 1025028522L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{70, 42, 39, 6, 12, Byte.MAX_VALUE, 109, 86, 65, 60, 126, 85, 5, 72, 98, 109, 26, 16, 33}));
        }
        return IIlII1l1Il.getMediaPeriodPositionUs(this.actualMediaPeriod.seekToUs(IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState)), il1I1111l1.mediaPeriodId, this.adPlaybackState);
    }

    public long getAdjustedSeekPositionUs(Il1I1111l1 il1I1111l1, long j, lI11IIllII li11iillii) throws InterruptedException {
        long mediaPeriodPositionUs = IIlII1l1Il.getMediaPeriodPositionUs(this.actualMediaPeriod.getAdjustedSeekPositionUs(IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState), li11iillii), il1I1111l1.mediaPeriodId, this.adPlaybackState);
        if (android.accounts.utils.lIIlI111II.l1IIl11Ill(5097)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{65, 15, 9}));
        }
        return mediaPeriodPositionUs;
    }

    public void discardBuffer(Il1I1111l1 il1I1111l1, long j, boolean z) {
        this.actualMediaPeriod.discardBuffer(IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState), z);
    }

    public void reevaluateBuffer(Il1I1111l1 il1I1111l1, long j) throws ClassNotFoundException {
        this.actualMediaPeriod.reevaluateBuffer(getStreamPositionUsWithNotYetStartedHandling(il1I1111l1, j));
        if (androidx.interpolator.view.animation.lIIlI111II.l111I1ll1l(9811)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{82, 83, 36, 10, 41, 7, 5, 121, 122, 5, 0, 5, 83, 9, 67, 76, 35, 18, 82, 118, 126, 36}));
        }
    }

    public long readDiscontinuity(Il1I1111l1 il1I1111l1) throws CertificateNotYetValidException {
        if (androidx.core.location.I1111IIl11.l11I11I11l(646869988L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{91, 41, 86, 44, 80, 3, 114, 97, 109, 13, 3, 100, 94, 97, 112, 96, 10, 43, 52, 71, 104, 50, 64, 70, 70, 122, 103, 38, 1}));
        }
        if (!il1I1111l1.equals(this.mediaPeriods.get(0))) {
            if (Il1l11I11I.I11II1I1I1(I1I1lI1II1.a(new byte[]{86, 16, 9, 49, 81, 90, 77, 71, 86, 29, 82, 66, Byte.MAX_VALUE, 9, 121, 84, 47, 46, 11, 11, 95, 32, 122, 88, 102, 81, 86, 53, 22, 93, 67, 33}), 7604)) {
                throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{89, 21, 18, 46, 84, 90, 14, 91, 104, 62, 69, 97, 112, 111, 67, 89, 38, 17, 85, 101, 70, 4, 81, 68, 80, 93, 81, 36, 47}));
            }
            return -9223372036854775807L;
        }
        long discontinuity = this.actualMediaPeriod.readDiscontinuity();
        if (discontinuity == -9223372036854775807L) {
            return -9223372036854775807L;
        }
        return IIlII1l1Il.getMediaPeriodPositionUs(discontinuity, il1I1111l1.mediaPeriodId, this.adPlaybackState);
    }

    public long selectTracks(Il1I1111l1 il1I1111l1, l1IlI1Il11[] l1ili1il11Arr, boolean[] zArr, l1l1IllI11[] l1l1illi11Arr, boolean[] zArr2, long j) throws UnsupportedEncodingException {
        if (lI11IlI1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{88, 93, 82, 86, 4, 114, 71, 89, 12}), 263838247L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{124, 83, 4, 42, 23}));
        }
        il1I1111l1.lastStartPositionUs = j;
        if (!il1I1111l1.equals(this.mediaPeriods.get(0))) {
            for (int i = 0; i < l1ili1il11Arr.length; i++) {
                l1IlI1Il11 l1ili1il11 = l1ili1il11Arr[i];
                boolean z = true;
                if (l1ili1il11 != null) {
                    if (zArr[i] && l1l1illi11Arr[i] != null) {
                        z = false;
                    }
                    zArr2[i] = z;
                    if (z) {
                        l1l1illi11Arr[i] = llIIlI1llI.areEqual(this.trackSelections[i], l1ili1il11) ? new IIll1IIlII(il1I1111l1, i) : new llI1l11IlI();
                    }
                } else {
                    l1l1illi11Arr[i] = null;
                    zArr2[i] = true;
                }
            }
            return j;
        }
        this.trackSelections = (l1IlI1Il11[]) Arrays.copyOf(l1ili1il11Arr, l1ili1il11Arr.length);
        long streamPositionUs = IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState);
        l1l1IllI11[] l1l1illi11Arr2 = this.sampleStreams;
        l1l1IllI11[] l1l1illi11Arr3 = l1l1illi11Arr2.length == 0 ? new l1l1IllI11[l1ili1il11Arr.length] : (l1l1IllI11[]) Arrays.copyOf(l1l1illi11Arr2, l1l1illi11Arr2.length);
        long jSelectTracks = this.actualMediaPeriod.selectTracks(l1ili1il11Arr, zArr, l1l1illi11Arr3, zArr2, streamPositionUs);
        this.sampleStreams = (l1l1IllI11[]) Arrays.copyOf(l1l1illi11Arr3, l1l1illi11Arr3.length);
        this.lastDownstreamFormatChangeData = (llIl1lII1I[]) Arrays.copyOf(this.lastDownstreamFormatChangeData, l1l1illi11Arr3.length);
        for (int i2 = 0; i2 < l1l1illi11Arr3.length; i2++) {
            if (l1l1illi11Arr3[i2] == null) {
                l1l1illi11Arr[i2] = null;
                this.lastDownstreamFormatChangeData[i2] = null;
            } else if (l1l1illi11Arr[i2] == null || zArr2[i2]) {
                l1l1illi11Arr[i2] = new IIll1IIlII(il1I1111l1, i2);
                this.lastDownstreamFormatChangeData[i2] = null;
            }
        }
        return IIlII1l1Il.getMediaPeriodPositionUs(jSelectTracks, il1I1111l1.mediaPeriodId, this.adPlaybackState);
    }

    public int readData(Il1I1111l1 il1I1111l1, int i, Il1IlllI1I il1IlllI1I, llII1ll111 llii1ll111, int i2) throws UnrecoverableKeyException {
        int data = ((l1l1IllI11) llIIlI1llI.castNonNull(this.sampleStreams[i])).readData(il1IlllI1I, llii1ll111, i2 | 1 | 4);
        long mediaPeriodPositionUsWithEndOfSourceHandling = getMediaPeriodPositionUsWithEndOfSourceHandling(il1I1111l1, llii1ll111.timeUs);
        if ((data == -4 && mediaPeriodPositionUsWithEndOfSourceHandling == Long.MIN_VALUE) || (data == -3 && getBufferedPositionUs(il1I1111l1) == Long.MIN_VALUE && !llii1ll111.waitingForKeys)) {
            maybeNotifyDownstreamFormatChanged(il1I1111l1, i);
            llii1ll111.clear();
            llii1ll111.addFlag(4);
            return -4;
        }
        if (data == -4) {
            maybeNotifyDownstreamFormatChanged(il1I1111l1, i);
            ((l1l1IllI11) llIIlI1llI.castNonNull(this.sampleStreams[i])).readData(il1IlllI1I, llii1ll111, i2);
            llii1ll111.timeUs = mediaPeriodPositionUsWithEndOfSourceHandling;
        }
        if (I1lllI11II.Ill1lIIlIl(948)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{116, 23, 33, 87, 47, 112, 122, 9, 8, 86, 6, 105, 122, 96, 4, 1, 39, 18, 44, 86, 105, 7, 97, 70, 64, 110, 110}));
        }
        return data;
    }

    public int skipData(Il1I1111l1 il1I1111l1, int i, long j) {
        int iSkipData = ((l1l1IllI11) llIIlI1llI.castNonNull(this.sampleStreams[i])).skipData(IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState));
        if (android.accounts.utils.lIIlI111II.IIlIl1Illl(3937)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{84, 84, 32, 48, 22, 5, 115, 94, 65, 29, 113, 93, 6, 97, 85, 102}));
        }
        return iSkipData;
    }

    public boolean isReady(int i) throws BindException {
        if (II1I11IlI1.Il1IIlI1II(I1I1lI1II1.a(new byte[]{92}), 184142006L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{81, 61, 37, 12, 86, 2, 97, 97}));
        }
        return ((l1l1IllI11) llIIlI1llI.castNonNull(this.sampleStreams[i])).isReady();
    }

    public void maybeThrowError(int i) throws IOException {
        ((l1l1IllI11) llIIlI1llI.castNonNull(this.sampleStreams[i])).maybeThrowError();
    }

    public void onDownstreamFormatChanged(Il1I1111l1 il1I1111l1, llIl1lII1I llil1lii1i) throws CertPathBuilderException, DigestException, BindException, NotActiveException, CertificateEncodingException {
        int iFindMatchingStreamIndex = findMatchingStreamIndex(llil1lii1i);
        if (iFindMatchingStreamIndex != -1) {
            this.lastDownstreamFormatChangeData[iFindMatchingStreamIndex] = llil1lii1i;
            il1I1111l1.hasNotifiedDownstreamFormatChange[iFindMatchingStreamIndex] = true;
        }
        if (IllllI11Il.I11II1I1I1(271923678L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{124, 23, 41, 36, 42, 67, 124, 82, 64, 81, 89, 7, 67, 108, 95, 1, 23, 52, 36, 1, 126, 10, 87, 4, 5, 113, 64, 39, 54, 121, 121}));
        }
    }

    public void onLoadStarted(I1111IIl11 i1111IIl11, llIl1lII1I llil1lii1i) throws NoSuchFieldException {
        this.activeLoads.put(Long.valueOf(i1111IIl11.loadTaskId), Pair.create(i1111IIl11, llil1lii1i));
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{96, 38, 24, 19, 47, 99, 125, 115, 85, 53, 0, 2, 66, 116, 119, Byte.MAX_VALUE, 11}), 173503741L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{111, 14, 91, 36, 56, 12, Byte.MAX_VALUE, 104}));
        }
    }

    public void onLoadFinished(I1111IIl11 i1111IIl11) {
        if (IIll1llI1l.Il1IIlI1II(3310)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{116, 46, 15, 18, 22, 77, 122, 1, 94, 60, 3, 99, 76, 91, 7, 13, 54, 5, 56, 67, 126, 42, 81, 1, 0, 91}));
        }
        this.activeLoads.remove(Long.valueOf(i1111IIl11.loadTaskId));
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIIlI11lll
    public void onPrepared(I1IIlllI1I i1IIlllI1I) throws NoRouteToHostException {
        this.isPrepared = true;
        for (int i = 0; i < this.mediaPeriods.size(); i++) {
            Il1I1111l1 il1I1111l1 = this.mediaPeriods.get(i);
            if (il1I1111l1.callback != null) {
                il1I1111l1.callback.onPrepared(il1I1111l1);
            }
        }
        if (lIIllIlIl1.llII1lIIlI(I1I1lI1II1.a(new byte[]{1, 44, 33, 41, 90, 82, Byte.MAX_VALUE, 121, 85, 16, Byte.MAX_VALUE, 101, 95}))) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{112, 46, 46, 83, 48, 80, 89, 73, 106, 37, 68, 82, 76, 112, 83, 70}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IlI1l1Il1l
    public void onContinueLoadingRequested(I1IIlllI1I i1IIlllI1I) {
        if (IllIIIIII1.l1l1l1IIlI(161109480L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{123, 93, 6, 35, 35, 80}));
        }
        Il1I1111l1 il1I1111l1 = this.loadingPeriod;
        if (il1I1111l1 == null) {
            return;
        }
        ((IIIlI11lll) lIlII1IIl1.checkNotNull(il1I1111l1.callback)).onContinueLoadingRequested(this.loadingPeriod);
    }

    private long getStreamPositionUsWithNotYetStartedHandling(Il1I1111l1 il1I1111l1, long j) {
        if (j < il1I1111l1.lastStartPositionUs) {
            return IIlII1l1Il.getStreamPositionUs(il1I1111l1.lastStartPositionUs, il1I1111l1.mediaPeriodId, this.adPlaybackState) - (il1I1111l1.lastStartPositionUs - j);
        }
        return IIlII1l1Il.getStreamPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState);
    }

    private long getMediaPeriodPositionUsWithEndOfSourceHandling(Il1I1111l1 il1I1111l1, long j) {
        if (j == Long.MIN_VALUE) {
            return Long.MIN_VALUE;
        }
        long mediaPeriodPositionUs = IIlII1l1Il.getMediaPeriodPositionUs(j, il1I1111l1.mediaPeriodId, this.adPlaybackState);
        long j2 = mediaPeriodPositionUs < I1Illl11II.getMediaPeriodEndPositionUs(il1I1111l1, this.adPlaybackState) ? mediaPeriodPositionUs : Long.MIN_VALUE;
        if (!IIlI1Il1lI.lll1111l11(I1I1lI1II1.a(new byte[]{114, 19, 26, 9, 5, 125, 123, 92, 11, 60, 68, 81, 96, 110, 97, 103, 36, 36, 1, 118, 3, 34, 103, 2, 89, 89, 71, 17, 52, 3, 93, 49}), 293844938L)) {
            return j2;
        }
        Log.v(I1I1lI1II1.a(new byte[]{99, 40, 81, 38, 85, 98, 117, 6, 1, 51, 7, 87, 101, 119, 97, 95, 3, 8, 16, 106, 99, 42}), I1I1lI1II1.a(new byte[]{90, 7, 54, 81, 4, 70, 0, 126, 10, 92, 97, 69, 66, 105, 80, 101, 52, 59, 35, 118, 96, 14, 95, 86, 70, 67, 111, 42, 85, 116, 79, 8}));
        return 0L;
    }

    private int findMatchingStreamIndex(llIl1lII1I llil1lii1i) throws CertPathBuilderException, DigestException, BindException, NotActiveException, CertificateEncodingException {
        if (llil1lii1i.trackFormat == null) {
            if (I1IllIll1l.l11I11I11l(231536490L)) {
                throw new BindException(I1I1lI1II1.a(new byte[]{69, 38, 26, 16, 13, 94, 88, 92, 123, 29, 67, 73, 70, 125, 66, 123, 45, 42, 59, 11, 117, 54, 82, 101, 126, 69, 0, 44, 14, 98, 81, 13}));
            }
            return -1;
        }
        int i = 0;
        loop0: while (true) {
            l1IlI1Il11[] l1ili1il11Arr = this.trackSelections;
            if (i >= l1ili1il11Arr.length) {
                return -1;
            }
            l1IlI1Il11 l1ili1il11 = l1ili1il11Arr[i];
            if (l1ili1il11 != null) {
                l11I11I11l trackGroup = l1ili1il11.getTrackGroup();
                boolean z = llil1lii1i.trackType == 0 && trackGroup.equals(getTrackGroups().get(0));
                for (int i2 = 0; i2 < trackGroup.length; i2++) {
                    IIl1Il11Il format = trackGroup.getFormat(i2);
                    if (format.equals(llil1lii1i.trackFormat) || (z && format.id != null && format.id.equals(llil1lii1i.trackFormat.id))) {
                        break loop0;
                    }
                }
            }
            i++;
        }
        if (lI1lllIII1.IlII1Illll(7232)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{121, 3, 36, 43, 10, 122, 70, 98, 113, 62, 69, 125, 101, 15, 6, 109}));
        }
        return i;
    }

    private void maybeNotifyDownstreamFormatChanged(Il1I1111l1 il1I1111l1, int i) {
        if (il1I1111l1.hasNotifiedDownstreamFormatChange[i] || this.lastDownstreamFormatChangeData[i] == null) {
            return;
        }
        il1I1111l1.hasNotifiedDownstreamFormatChange[i] = true;
        il1I1111l1.mediaSourceEventDispatcher.downstreamFormatChanged(I1Illl11II.access$400(il1I1111l1, this.lastDownstreamFormatChangeData[i], this.adPlaybackState));
    }
}
