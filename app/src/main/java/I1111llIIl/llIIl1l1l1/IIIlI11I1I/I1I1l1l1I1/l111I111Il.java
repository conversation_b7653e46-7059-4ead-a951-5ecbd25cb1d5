package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1111IIl11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1II1lIl1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import android.net.Uri;
import android.os.SystemClock;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.IOException;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.I1lIlIIIl1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l111I111Il implements I1II1lIl1I {
    private final Uri adUri;
    final /* synthetic */ lIllI1I1Il this$0;

    public l111I111Il(lIllI1I1Il lilli1i1il, Uri uri) {
        this.this$0 = lilli1i1il;
        this.adUri = uri;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1II1lIl1I
    public void onPrepareComplete(final I1IIl111I1 i1IIl111I1) {
        lIllI1I1Il.access$200(this.this$0).post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.l111I111Il$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() throws CertPathValidatorException {
                this.f$0.lambda$onPrepareComplete$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-l111I111Il(i1IIl111I1);
            }
        });
        if (l1lI1I1l11.I111IlIl1I(262812099L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{83, 28, 52, 44, 13, 108, 85, 4, 113, 9, 0, 123, 101, 110, 71, 99}));
        }
    }

    /* synthetic */ void lambda$onPrepareComplete$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-l111I111Il(I1IIl111I1 i1IIl111I1) throws CertPathValidatorException {
        lIllI1I1Il.access$400(this.this$0).handlePrepareComplete(this.this$0, i1IIl111I1.adGroupIndex, i1IIl111I1.adIndexInAdGroup);
        if (lIIllIlIl1.Il1IIlI1II(520871297L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{117, 52, 21, 53, 0, 69, 117, 89, 91, 6, 120, 101, 12, 78, 108, 65, 38, 48, 6, 5, 106, 82, 65, 81, 87, 67, 66}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1II1lIl1I
    public void onPrepareError(final I1IIl111I1 i1IIl111I1, final IOException iOException) {
        lIllI1I1Il.access$300(this.this$0, i1IIl111I1).loadError(new I1111IIl11(I1111IIl11.getNewId(), new I1lIlIIIl1(this.adUri), SystemClock.elapsedRealtime()), 6, (IOException) Il1III1Il1.createForAd(iOException), true);
        lIllI1I1Il.access$200(this.this$0).post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.l111I111Il$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$onPrepareError$1$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-l111I111Il(i1IIl111I1, iOException);
            }
        });
        if (androidx.core.location.I1111IIl11.llII1lIIlI(I1I1lI1II1.a(new byte[]{67, 85, 6, 85, 33, Byte.MAX_VALUE, 4, 119, 108, 60}))) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{111, 80, 91, 35, 51, 5, 99, 8, 124, 7}));
        }
    }

    /* synthetic */ void lambda$onPrepareError$1$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-l111I111Il(I1IIl111I1 i1IIl111I1, IOException iOException) {
        lIllI1I1Il.access$400(this.this$0).handlePrepareError(this.this$0, i1IIl111I1.adGroupIndex, i1IIl111I1.adIndexInAdGroup, iOException);
        if (Il1I1lllIl.Ill1lIIlIl(7795)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{123, 2, 4, 81, 16, 109, 88, 9, 123, 47, 114, 93, 125, 116, 122, 114, 48, 51, 33, 94}));
        }
    }
}
