package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1111IIl11;
import android.accounts.utils.IIIlIl1I1l;
import android.os.Handler;
import android.os.SystemClock;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.IlI11Ill11;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.IOException;
import java.io.UTFDataFormatException;
import java.net.SocketTimeoutException;
import java.security.AccessControlException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.I1lIlIIIl1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class ll1I1l1l1l implements II1l1lIllI {
    private final Handler playerHandler = llIIlI1llI.createHandlerForCurrentLooper();
    private volatile boolean stopped;
    final /* synthetic */ lIllI1I1Il this$0;

    public ll1I1l1l1l(lIllI1I1Il lilli1i1il) {
        this.this$0 = lilli1i1il;
    }

    public void stop() {
        this.stopped = true;
        this.playerHandler.removeCallbacksAndMessages(null);
        if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{121, 62, 90, 51, 46, 116, 79, 92, 1, 23, 67, 121, 118, 126, 117, 92, 0, 20, 51, 70, 1, 12, 3, 92, 99, 113, 2, 92, 35}), 9522)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{14, 7, 54}));
        }
    }

    @Override // I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.II1l1lIllI
    public void onAdPlaybackState(final IlI11Ill11 ilI11Ill11) throws InterruptedException, SocketTimeoutException {
        if (IIIlIl1I1l.l11I11I11l(197693134L)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{83, 49, 14, 16, 33, 92, 124, 83, 108, 40, 8, 69, 102, 9}));
        }
        if (!this.stopped) {
            this.playerHandler.post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.ll1I1l1l1l$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() throws CertificateException, InvalidAlgorithmParameterException {
                    this.f$0.lambda$onAdPlaybackState$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-ll1I1l1l1l(ilI11Ill11);
                }
            });
        } else if (lI11IlI1lI.IlII1Illll(254807670L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{82, 12, 39, 47, 82, 96, 64, 105, 118, 84, 9, 116, 90, 96, 91, 103, 32, 83, 40, 1}));
        }
    }

    /* synthetic */ void lambda$onAdPlaybackState$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-ll1I1l1l1l(IlI11Ill11 ilI11Ill11) throws CertificateException, InvalidAlgorithmParameterException {
        if (II1I11IlI1.IlII1Illll(I1I1lI1II1.a(new byte[]{117, 50, 26, 6, 46, Byte.MAX_VALUE, 112, 100, 78, 61, 69, 83, 93, 73, 113, 122, 6, 40, 45, 118, 120, 16, 97, 99, 91, 120, 83, 11, 86}), 199920219L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{114, 55, 20, 52, 26, 115, 0, 83}));
        }
        if (!this.stopped) {
            lIllI1I1Il.access$100(this.this$0, ilI11Ill11);
        } else if (l11Il1lI11.I111IlIl1I(I1I1lI1II1.a(new byte[]{114, 48, 11, 52, 36, 114, 115, 1, 112, 83, 95, 68, 124, 85, 7, 69, 90, 55, 59, 69, 3, 9, 74, 12, 1, 100, 92, 31}), 7268)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 60, 5}));
        }
    }

    @Override // I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.II1l1lIllI
    public void onAdLoadError(Il1III1Il1 il1III1Il1, I1lIlIIIl1 i1lIlIIIl1) {
        if (this.stopped) {
            return;
        }
        lIllI1I1Il.access$000(this.this$0, null).loadError(new I1111IIl11(I1111IIl11.getNewId(), i1lIlIIIl1, SystemClock.elapsedRealtime()), 6, (IOException) il1III1Il1, true);
        if (Il1IIllIll.IIll1I11lI(I1I1lI1II1.a(new byte[]{113, 62, 46, 83, 3, 124, 4, 85, 90, 15, 5, 121, 71, 82, 81, 124, 39, 12, 40, 71, 103, 7, 82, 112, 68, 95, 82}))) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{71, 82, 32, 2, 40, 125, 85, 93, 81, 43, 100, 125, 2, 116, 12}));
        }
    }
}
