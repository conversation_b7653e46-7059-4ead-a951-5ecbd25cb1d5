package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import IIl11IllI1.IIIII1I11I.I111IlIl1l.Il1IIIlI1l.lI11IIllII;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l111l1II;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIIlI11lll;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llll1lI1II;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIIIII11I;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.lI1lI11Ill;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.media3.common.StreamKey;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.UnsupportedEncodingException;
import java.net.PortUnreachableException;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyException;
import java.security.SignatureException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateEncodingException;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I11II1l1lI;
import llIl11II1I.l11IIllI1I.I1Il1l1I1I.Il1lIIlIll.l1IlI1Il11;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class Il1I1111l1 implements I1IIlllI1I {
    public IIIlI11lll callback;
    public final I11II1l1lI drmEventDispatcher;
    public boolean[] hasNotifiedDownstreamFormatChange = new boolean[0];
    public long lastStartPositionUs;
    public final I1IIl111I1 mediaPeriodId;
    public final I1l111l1II mediaSourceEventDispatcher;
    public final ll1lIlI1lI sharedPeriod;

    public Il1I1111l1(ll1lIlI1lI ll1lili1li, I1IIl111I1 i1IIl111I1, I1l111l1II i1l111l1II, I11II1l1lI i11II1l1lI) {
        this.sharedPeriod = ll1lili1li;
        this.mediaPeriodId = i1IIl111I1;
        this.mediaSourceEventDispatcher = i1l111l1II;
        this.drmEventDispatcher = i11II1l1lI;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public void prepare(IIIlI11lll iIIlI11lll, long j) throws PortUnreachableException {
        this.callback = iIIlI11lll;
        this.sharedPeriod.prepare(this, j);
        if (I1lllI11II.Il1IIlI1II(5845)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{82, 48, 90, 9, 14, 88, 102, 69, 96, 93, 0, 124, 7, 97, 12, 80, 11, 53, 19, 75, 73, 50, 85, 82, 10, 120, 112, 81, 54, 117, 110, 19}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public void maybeThrowPrepareError() throws IOException {
        this.sharedPeriod.maybeThrowPrepareError();
        if (l11Il111ll.IlII1Illll(187542579L)) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{83, 19, 8, 60, 27, 13, 5, 64, 115, 55, 90, 114, 112, 76}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public llll1lI1II getTrackGroups() throws CertPathBuilderException {
        if (lI1l1I1l1l.I1lllI1llI(687093816L)) {
            Log.w(I1I1lI1II1.a(new byte[]{109, 18, 51, 44, 80, 77, 118, 105, 105, 6, 84, 124}), I1I1lI1II1.a(new byte[]{80, 8, 13, 41, 22, 64, 115, 73, 77, 84, 100, 92, 81, 96, 82, 123, 52, 0, 6, 67, 93, 84, 82, 120, 85, 81, 113, 31, 32, 93, 95}));
            return null;
        }
        llll1lI1II trackGroups = this.sharedPeriod.getTrackGroups();
        if (llIl1lII1I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{71, 39, 10, 1, 40, 109, 122, 1, 9, 18, 94, 122, 69}), 1137887880L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{3}));
        }
        return trackGroups;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public List<StreamKey> getStreamKeys(List<l1IlI1Il11> list) throws KeyException {
        if (ll1l11I1II.l11I11I11l(I1I1lI1II1.a(new byte[]{123, 6, 87, 45, 8, 5, 69, 125, 64, 30, 119, 114, 6, 67, 64, 108, 48, 32, 36}))) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{92, 33, 7, 11, 86, 87, 99, 93, 73, 9, 99, 124, 67, 67, 96, 13, 16, 87}));
        }
        return this.sharedPeriod.getStreamKeys(list);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public long selectTracks(l1IlI1Il11[] l1ili1il11Arr, boolean[] zArr, l1l1IllI11[] l1l1illi11Arr, boolean[] zArr2, long j) throws ReflectiveOperationException, UnsupportedEncodingException, CertificateEncodingException {
        if (l1lll111II.l11I11I11l(2599)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{121, 21, 17, 15, 42, 69}));
        }
        if (this.hasNotifiedDownstreamFormatChange.length == 0) {
            this.hasNotifiedDownstreamFormatChange = new boolean[l1l1illi11Arr.length];
        }
        long jSelectTracks = this.sharedPeriod.selectTracks(this, l1ili1il11Arr, zArr, l1l1illi11Arr, zArr2, j);
        if (IIlIIlIII1.l11I11I11l(5826)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{90, 61, 40, 3, 14, 115, 126, 101, 9, 20, 126, 65, 96}));
        }
        return jSelectTracks;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public void discardBuffer(long j, boolean z) throws IllegalAccessException {
        this.sharedPeriod.discardBuffer(this, j, z);
        if (lIIIIII11I.l11I11I11l(8640)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{86, 61, 10, 84, 1, 79, 89, 124, 81, 30, 123, 113, 67, 96, 66, 86, 84, 54, 80, 96, 1, 10, 64, 122}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public long readDiscontinuity() {
        return this.sharedPeriod.readDiscontinuity(this);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public long seekToUs(long j) {
        return this.sharedPeriod.seekToUs(this, j);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I
    public long getAdjustedSeekPositionUs(long j, lI11IIllII li11iillii) throws KeyException, InterruptedException {
        long adjustedSeekPositionUs = this.sharedPeriod.getAdjustedSeekPositionUs(this, j, li11iillii);
        if (lIIlI111II.I1IlI11II1(9633)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{121, 84, 49, 10, 14, 67, 120, 8, 97, 21, 93}));
        }
        return adjustedSeekPositionUs;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIllIII11I
    public long getBufferedPositionUs() {
        long bufferedPositionUs = this.sharedPeriod.getBufferedPositionUs(this);
        if (!androidx.versionedparcelable.custom.entities.lIIlI111II.Il1IIIIlll(4092)) {
            return bufferedPositionUs;
        }
        Log.w(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 54, 50, 54, 52, 100}), I1I1lI1II1.a(new byte[]{71, 19, 32, 8, 10, 0}));
        return 0L;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIllIII11I
    public long getNextLoadPositionUs() throws SignatureException {
        if (l1IIll1I1l.Il1IIlI1II(9437)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{65, 86, 9, 92, 24, 126, 114, 85, 88, 83, 5, 100, 102, 82, 78, 65, 45, 59, 87, 112, 66, 19, 81, 124, 10, 126, 83, 7, 15}));
        }
        long nextLoadPositionUs = this.sharedPeriod.getNextLoadPositionUs(this);
        if (Il1lII1l1l.IlII1Illll(2138)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{92, 60, 49, 51}));
        }
        return nextLoadPositionUs;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIllIII11I
    public boolean continueLoading(long j) throws InvalidClassException {
        boolean zContinueLoading = this.sharedPeriod.continueLoading(this, j);
        if (android.accounts.utils.lIIlI111II.lI11IlI1lI(700797322L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{115, 14, 1}));
        }
        return zContinueLoading;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIllIII11I
    public boolean isLoading() throws InvalidAlgorithmParameterException {
        boolean zIsLoading = this.sharedPeriod.isLoading(this);
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{123, 19, 43, 23, 59, 68, 123, 9, 64, 14, 100, 65, 4, 73, 112, 118}))) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{103}));
        }
        return zIsLoading;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.IIllIII11I
    public void reevaluateBuffer(long j) throws ClassNotFoundException {
        this.sharedPeriod.reevaluateBuffer(this, j);
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{125, 55, 86, 40, 43, 109, 6, 105}), 162045645L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{96, 19, 40, 10, 48, 124, 125, 6, 116, 0, 113, 88, 69, 118, 101, Byte.MAX_VALUE, 0, 44, 81, 6, 120, 44, 90, 102, 125, 78, 86}));
        }
    }
}
