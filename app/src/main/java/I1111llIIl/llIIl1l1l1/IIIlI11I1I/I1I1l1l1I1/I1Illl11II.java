package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.III1l11Ill;
import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.lIII1IlIlI;
import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.lIIll11Ill;
import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.ll1III1lIl0;
import IIIl11ll1l.l1lI11lI1I.IIIl1llIlI.lI1lIll11I.I1IIIIIlI1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1111IIl11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l111l1II;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1III111I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1lII1I;
import android.media.content.II1I11IlI1;
import android.media.content.lll1IIII11;
import android.os.Handler;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.util.Log;
import android.util.Pair;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.media3.common.Il1I1lllI1;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.l11llI1lI1;
import androidx.media3.common.l1I1l1Il11;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.EOFException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.io.NotActiveException;
import java.io.ObjectStreamException;
import java.io.SyncFailedException;
import java.io.UnsupportedEncodingException;
import java.net.BindException;
import java.security.DigestException;
import java.security.GeneralSecurityException;
import java.security.InvalidKeyException;
import java.security.InvalidParameterException;
import java.security.KeyException;
import java.security.SignatureException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.util.List;
import java.util.Map$Entry;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I11II1l1lI;
import l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.II1lI1llII;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;

/* loaded from: classes.dex */
public final class I1Illl11II extends I111I1lI1I implements II1III111I, II1ll1l1ll, I1I1II1lIl {
    private final II1l11I1Il adPlaybackStateUpdater;
    private l1I1l1Il11 contentTimeline;
    private ll1lIlI1lI lastUsedMediaPeriod;
    private final llIl1I11II mediaSource;
    private Handler playbackHandler;
    private final lIII1IlIlI<Pair<Long, Object>, ll1lIlI1lI> mediaPeriods = I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.ll11IlI1l1.create();
    private III1l11Ill<Object, IlI11Ill11> adPlaybackStates = III1l11Ill.of();
    private final I1l111l1II mediaSourceEventDispatcherWithoutId = createEventDispatcher(null);
    private final I11II1l1lI drmEventDispatcherWithoutId = createDrmEventDispatcher(null);

    static /* synthetic */ llIl1lII1I access$400(Il1I1111l1 il1I1111l1, llIl1lII1I llil1lii1i, IlI11Ill11 ilI11Ill11) throws ObjectStreamException {
        if (l11Il1lI11.I1lIllll1l(1292)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{70, 16, 16, 80, 10, 7, 68, 93, 97, 11, 7, 65, 4, 93, 101, Byte.MAX_VALUE, 15, 84, 6, 124, 3, 53, 5, 121, 114, 15, 88, 36, 93}));
        }
        return correctMediaLoadData(il1I1111l1, llil1lii1i, ilI11Ill11);
    }

    public I1Illl11II(llIl1I11II llil1i11ii, II1l11I1Il iI1l11I1Il) {
        this.mediaSource = llil1i11ii;
        this.adPlaybackStateUpdater = iI1l11I1Il;
    }

    public void setAdPlaybackStates(final III1l11Ill<Object, IlI11Ill11> iII1l11Ill) throws SyncFailedException {
        lIlII1IIl1.checkArgument(!iII1l11Ill.isEmpty());
        Object objCheckNotNull = lIlII1IIl1.checkNotNull(iII1l11Ill.values().asList().get(0).adsId);
        ll1III1lIl0<Map$Entry<Object, IlI11Ill11>> it = iII1l11Ill.entrySet().iterator();
        while (it.hasNext()) {
            Map$Entry<Object, IlI11Ill11> next = it.next();
            Object key = next.getKey();
            IlI11Ill11 value = next.getValue();
            lIlII1IIl1.checkArgument(llIIlI1llI.areEqual(objCheckNotNull, value.adsId));
            IlI11Ill11 ilI11Ill11 = this.adPlaybackStates.get(key);
            if (ilI11Ill11 != null) {
                for (int i = value.removedAdGroupCount; i < value.adGroupCount; i++) {
                    l11llI1lI1 adGroup = value.getAdGroup(i);
                    lIlII1IIl1.checkArgument(adGroup.isServerSideInserted);
                    if (i < ilI11Ill11.adGroupCount && IIlII1l1Il.getAdCountInGroup(value, i) < IIlII1l1Il.getAdCountInGroup(ilI11Ill11, i)) {
                        l11llI1lI1 adGroup2 = value.getAdGroup(i + 1);
                        lIlII1IIl1.checkArgument(adGroup.contentResumeOffsetUs + adGroup2.contentResumeOffsetUs == ilI11Ill11.getAdGroup(i).contentResumeOffsetUs);
                        lIlII1IIl1.checkArgument(adGroup.timeUs + adGroup.contentResumeOffsetUs == adGroup2.timeUs);
                    }
                    if (adGroup.timeUs == Long.MIN_VALUE) {
                        lIlII1IIl1.checkArgument(IIlII1l1Il.getAdCountInGroup(value, i) == 0);
                    }
                }
            }
        }
        synchronized (this) {
            Handler handler = this.playbackHandler;
            if (handler == null) {
                this.adPlaybackStates = iII1l11Ill;
            } else {
                handler.post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.I1Illl11II$$ExternalSyntheticLambda0
                    @Override // java.lang.Runnable
                    public final void run() {
                        this.f$0.lambda$setAdPlaybackStates$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-I1Illl11II(iII1l11Ill);
                    }
                });
            }
        }
    }

    /* synthetic */ void lambda$setAdPlaybackStates$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-I1Illl11II(III1l11Ill iII1l11Ill) {
        IlI11Ill11 ilI11Ill11;
        for (ll1lIlI1lI ll1lili1li : this.mediaPeriods.values()) {
            IlI11Ill11 ilI11Ill112 = (IlI11Ill11) iII1l11Ill.get(ll1lili1li.periodUid);
            if (ilI11Ill112 != null) {
                ll1lili1li.updateAdPlaybackState(ilI11Ill112);
            }
        }
        ll1lIlI1lI ll1lili1li2 = this.lastUsedMediaPeriod;
        if (ll1lili1li2 != null && (ilI11Ill11 = (IlI11Ill11) iII1l11Ill.get(ll1lili1li2.periodUid)) != null) {
            this.lastUsedMediaPeriod.updateAdPlaybackState(ilI11Ill11);
        }
        this.adPlaybackStates = iII1l11Ill;
        if (this.contentTimeline != null) {
            refreshSourceInfo(new I1111l11ll(this.contentTimeline, iII1l11Ill));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public Il1I1lllI1 getMediaItem() {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{92}), 172228103L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{70, 81, 41, 39, 35, 114, 125}));
        }
        return this.mediaSource.getMediaItem();
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void prepareSourceInternal(II1lI1llII iI1lI1llII) {
        Handler handlerCreateHandlerForCurrentLooper = llIIlI1llI.createHandlerForCurrentLooper();
        synchronized (this) {
            this.playbackHandler = handlerCreateHandlerForCurrentLooper;
        }
        this.mediaSource.addEventListener(handlerCreateHandlerForCurrentLooper, this);
        this.mediaSource.addDrmEventListener(handlerCreateHandlerForCurrentLooper, this);
        this.mediaSource.prepareSource(this, iI1lI1llII, getPlayerId());
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public void maybeThrowSourceInfoRefreshError() throws IOException {
        this.mediaSource.maybeThrowSourceInfoRefreshError();
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void enableInternal() {
        if (androidx.core.location.llIl1lII1I.IIll1I11lI(I1I1lI1II1.a(new byte[]{95, 2, 1, 44, 0}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{114, 54, 35, 1, 37, 68, 116, 120, 104, 50, 87, 90, 116, 119, 81, 80, 36, 84, 38, 1, 99, 18, 126, 71, 3, 70, 78, 42, 7, 3, 88}));
        }
        this.mediaSource.enable(this);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void disableInternal() throws SignatureException, EOFException {
        releaseLastUsedMediaPeriod();
        this.mediaSource.disable(this);
        if (IIlIIlIII1.I1lllI1llI(1748158097L)) {
            Log.i(I1I1lI1II1.a(new byte[]{93, 10, 19, 16, 46, 1, 109, 103, 115, 38, 116, 71, 92, 8, 12, 7, 86, 37, 4, 85, 93, 35, 80, 4, 6, 120, 126, 49, 14}), I1I1lI1II1.a(new byte[]{94, 35, 48, 52, 44, 79, 97, 88, 85, 42, 1, 103}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1III111I
    public void onSourceInfoRefreshed(llIl1I11II llil1i11ii, l1I1l1Il11 l1i1l1il11) throws NoSuchMethodException {
        if (l111Il1lI1.l11I11I11l(801504967L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{81, 8, 15, 36, 51, 94, 109, 101}));
        }
        this.contentTimeline = l1i1l1il11;
        II1l11I1Il iI1l11I1Il = this.adPlaybackStateUpdater;
        if ((iI1l11I1Il == null || !iI1l11I1Il.onAdPlaybackStateUpdateRequested(l1i1l1il11)) && !this.adPlaybackStates.isEmpty()) {
            refreshSourceInfo(new I1111l11ll(l1i1l1il11, this.adPlaybackStates));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void releaseSourceInternal() throws SignatureException, EOFException {
        releaseLastUsedMediaPeriod();
        this.contentTimeline = null;
        synchronized (this) {
            this.playbackHandler = null;
        }
        this.mediaSource.releaseSource(this);
        this.mediaSource.removeEventListener(this);
        this.mediaSource.removeDrmEventListener(this);
        if (lIIlI111II.IIlI1Il1lI(4694)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{115, 34, 81, 85, 86, 92, 67, 97, 120, 53, 5, 96, 89, 75, 126, 87, 19, 84, 48, 117, 64, 34, 73, 93, 107, 100}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public I1IIlllI1I createPeriod(I1IIl111I1 i1IIl111I1, I1IIIIIlI1 i1IIIIIlI1, long j) throws SignatureException {
        ll1lIlI1lI ll1lili1li;
        Pair<Long, Object> pair = new Pair<>(Long.valueOf(i1IIl111I1.windowSequenceNumber), i1IIl111I1.periodUid);
        ll1lIlI1lI ll1lili1li2 = this.lastUsedMediaPeriod;
        boolean z = false;
        if (ll1lili1li2 != null) {
            if (ll1lili1li2.periodUid.equals(i1IIl111I1.periodUid)) {
                ll1lili1li = this.lastUsedMediaPeriod;
                this.mediaPeriods.put(pair, ll1lili1li);
                z = true;
            } else {
                this.lastUsedMediaPeriod.release(this.mediaSource);
                ll1lili1li = null;
            }
            this.lastUsedMediaPeriod = null;
        } else {
            ll1lili1li = null;
        }
        if (ll1lili1li == null && ((ll1lili1li = (ll1lIlI1lI) lIIll11Ill.b(this.mediaPeriods.get((lIII1IlIlI<Pair<Long, Object>, ll1lIlI1lI>) pair), (Object) null)) == null || !ll1lili1li.canReuseMediaPeriod(i1IIl111I1, j))) {
            IlI11Ill11 ilI11Ill11 = (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(i1IIl111I1.periodUid));
            ll1lIlI1lI ll1lili1li3 = new ll1lIlI1lI(this.mediaSource.createPeriod(new I1IIl111I1(i1IIl111I1.periodUid, i1IIl111I1.windowSequenceNumber), i1IIIIIlI1, IIlII1l1Il.getStreamPositionUs(j, i1IIl111I1, ilI11Ill11)), i1IIl111I1.periodUid, ilI11Ill11);
            this.mediaPeriods.put(pair, ll1lili1li3);
            ll1lili1li = ll1lili1li3;
        }
        Il1I1111l1 il1I1111l1 = new Il1I1111l1(ll1lili1li, i1IIl111I1, createEventDispatcher(i1IIl111I1), createDrmEventDispatcher(i1IIl111I1));
        ll1lili1li.add(il1I1111l1);
        if (z && ll1lili1li.trackSelections.length > 0) {
            il1I1111l1.seekToUs(j);
        }
        return il1I1111l1;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public void releasePeriod(I1IIlllI1I i1IIlllI1I) throws SignatureException, CertificateException {
        Il1I1111l1 il1I1111l1 = (Il1I1111l1) i1IIlllI1I;
        il1I1111l1.sharedPeriod.remove(il1I1111l1);
        if (il1I1111l1.sharedPeriod.isUnused()) {
            this.mediaPeriods.remove(new Pair(Long.valueOf(il1I1111l1.mediaPeriodId.windowSequenceNumber), il1I1111l1.mediaPeriodId.periodUid), il1I1111l1.sharedPeriod);
            if (this.mediaPeriods.isEmpty()) {
                this.lastUsedMediaPeriod = il1I1111l1.sharedPeriod;
            } else {
                il1I1111l1.sharedPeriod.release(this.mediaSource);
            }
        }
        if (l111Il1lI1.I11II1I1I1(I1I1lI1II1.a(new byte[]{124, 13, 26, 17, 1, 4, 115, 72, 72, 17, 120, 74, 13, 77, 7, 122, 46, 13, 48, 85, 115, 82, 99, 83, 65, 0, 98}), 1437)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{109, 50, 40, 42, 48, 98}));
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmSessionAcquired(int i, I1IIl111I1 i1IIl111I1, int i2) throws ObjectStreamException, IllegalAccessException {
        if (IIlI1Il1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{15, 85, 7, 16, 42, 125, 66, 124, 97, 45, 98}), 1364051931L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{89, 93, 24, 47, 21, 126, 109, 89, 78, 16, 94, 104, 115, 109, 97, 87, 17}));
        }
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, true);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmSessionAcquired(i2);
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmSessionAcquired(i2);
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmKeysLoaded(int i, I1IIl111I1 i1IIl111I1) throws IllegalAccessException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, false);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmKeysLoaded();
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmKeysLoaded();
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmSessionManagerError(int i, I1IIl111I1 i1IIl111I1, Exception exc) throws IllegalAccessException, CertificateException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, false);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmSessionManagerError(exc);
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmSessionManagerError(exc);
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmKeysRestored(int i, I1IIl111I1 i1IIl111I1) throws IllegalAccessException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, false);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmKeysRestored();
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmKeysRestored();
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmKeysRemoved(int i, I1IIl111I1 i1IIl111I1) throws IllegalAccessException, InterruptedException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, false);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmKeysRemoved();
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmKeysRemoved();
        }
    }

    @Override // l1ll1lIlll.l11Il1lIll.lI1lII1l1I.I1lIl11III.I1I1II1lIl
    public void onDrmSessionReleased(int i, I1IIl111I1 i1IIl111I1) throws IllegalAccessException {
        if (l1IIll1I1l.Il1IIlI1II(142)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{2, 62, 53, 49, 27, 7, 103, 74, 106, 50, 100, 105, 12, 78, 87, 122, 17, 34, 27, 101, 72, 40, 118, 66, 10, 64, 3, 6, 17, 102, 93}));
        }
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, null, false);
        if (mediaPeriodForEvent == null) {
            this.drmEventDispatcherWithoutId.drmSessionReleased();
        } else {
            mediaPeriodForEvent.drmEventDispatcher.drmSessionReleased();
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onLoadStarted(int i, I1IIl111I1 i1IIl111I1, I1111IIl11 i1111IIl11, llIl1lII1I llil1lii1i) throws IllegalAccessException, NoSuchFieldException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, true);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.loadStarted(i1111IIl11, llil1lii1i);
        } else {
            mediaPeriodForEvent.sharedPeriod.onLoadStarted(i1111IIl11, llil1lii1i);
            mediaPeriodForEvent.mediaSourceEventDispatcher.loadStarted(i1111IIl11, correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onLoadCompleted(int i, I1IIl111I1 i1IIl111I1, I1111IIl11 i1111IIl11, llIl1lII1I llil1lii1i) throws IllegalAccessException, InvalidKeyException {
        if (llIlI11III.I1lllI1llI(9125)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{4, 49, 53, 20, 15, 121, 120, 96}));
        }
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, true);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.loadCompleted(i1111IIl11, llil1lii1i);
        } else {
            mediaPeriodForEvent.sharedPeriod.onLoadFinished(i1111IIl11);
            mediaPeriodForEvent.mediaSourceEventDispatcher.loadCompleted(i1111IIl11, correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onLoadCanceled(int i, I1IIl111I1 i1IIl111I1, I1111IIl11 i1111IIl11, llIl1lII1I llil1lii1i) throws IllegalAccessException, GeneralSecurityException {
        if (II1I11IlI1.IlIIlIllI1(I1I1lI1II1.a(new byte[]{71, 15, 16, 93, 44, 112, 98, 106, 75, 62, 69, 113, 101, 78}), 173025299L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{93, 52, 48, 49, 53, 92, 121, 123, 122, 86, 8, 120, 122, Byte.MAX_VALUE, 88, 6, 90, 8, 54, 91}));
        }
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, true);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.loadCanceled(i1111IIl11, llil1lii1i);
        } else {
            mediaPeriodForEvent.sharedPeriod.onLoadFinished(i1111IIl11);
            mediaPeriodForEvent.mediaSourceEventDispatcher.loadCanceled(i1111IIl11, correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onLoadError(int i, I1IIl111I1 i1IIl111I1, I1111IIl11 i1111IIl11, llIl1lII1I llil1lii1i, IOException iOException, boolean z) throws IllegalAccessException, InterruptedIOException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, true);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.loadError(i1111IIl11, llil1lii1i, iOException, z);
        } else {
            if (z) {
                mediaPeriodForEvent.sharedPeriod.onLoadFinished(i1111IIl11);
            }
            mediaPeriodForEvent.mediaSourceEventDispatcher.loadError(i1111IIl11, correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))), iOException, z);
        }
        if (lll1IIII11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{117, 0, 58, 47, 45, 64, 69, 86, 82, 35, 5, 83, 91, 67, 117, 6, 38, 44, 48, 86, 105, 27, 4, 97, 88, 110, 126, 33, 5, 96}), 7026)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{117, 0, 48, 92, 11}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onUpstreamDiscarded(int i, I1IIl111I1 i1IIl111I1, llIl1lII1I llil1lii1i) throws IllegalAccessException, UnsupportedEncodingException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, false);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.upstreamDiscarded(llil1lii1i);
        } else {
            mediaPeriodForEvent.mediaSourceEventDispatcher.upstreamDiscarded(correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))));
        }
        if (IIIlIll111.Il1IIlI1II(5441)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{81, 33, 84, 21, 84, 123, 5, 105, 119, 51, 8, 2, 76, 81, 80, 94, 58, 34, 52, 75, Byte.MAX_VALUE, 27, 80}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.II1ll1l1ll
    public void onDownstreamFormatChanged(int i, I1IIl111I1 i1IIl111I1, llIl1lII1I llil1lii1i) throws CertPathBuilderException, IllegalAccessException, DigestException, BindException, NotActiveException, CertificateEncodingException {
        Il1I1111l1 mediaPeriodForEvent = getMediaPeriodForEvent(i1IIl111I1, llil1lii1i, false);
        if (mediaPeriodForEvent == null) {
            this.mediaSourceEventDispatcherWithoutId.downstreamFormatChanged(llil1lii1i);
        } else {
            mediaPeriodForEvent.sharedPeriod.onDownstreamFormatChanged(mediaPeriodForEvent, llil1lii1i);
            mediaPeriodForEvent.mediaSourceEventDispatcher.downstreamFormatChanged(correctMediaLoadData(mediaPeriodForEvent, llil1lii1i, (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(mediaPeriodForEvent.mediaPeriodId.periodUid))));
        }
    }

    private void releaseLastUsedMediaPeriod() throws SignatureException, EOFException {
        ll1lIlI1lI ll1lili1li = this.lastUsedMediaPeriod;
        if (ll1lili1li != null) {
            ll1lili1li.release(this.mediaSource);
            this.lastUsedMediaPeriod = null;
        }
        if (androidx.core.location.llIl1lII1I.l1l1l1IIlI(1575694069L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{93, 9, 7, 85, 37, 64, 65, 120, 115, 45, 113, 8, 118, 90, 119, 97, 45, 17, 33, 101, 5, 51, 102, 2, 70, 115, 89, 10, 81}));
        }
    }

    private Il1I1111l1 getMediaPeriodForEvent(I1IIl111I1 i1IIl111I1, llIl1lII1I llil1lii1i, boolean z) throws IllegalAccessException {
        Il1I1111l1 il1I1111l1;
        if (i1IIl111I1 == null) {
            return null;
        }
        List<ll1lIlI1lI> list = this.mediaPeriods.get((lIII1IlIlI<Pair<Long, Object>, ll1lIlI1lI>) new Pair<>(Long.valueOf(i1IIl111I1.windowSequenceNumber), i1IIl111I1.periodUid));
        if (list.isEmpty()) {
            return null;
        }
        if (z) {
            ll1lIlI1lI ll1lili1li = (ll1lIlI1lI) lIIll11Ill.b(list);
            if (ll1lili1li.loadingPeriod == null) {
                il1I1111l1 = (Il1I1111l1) lIIll11Ill.b(ll1lIlI1lI.access$200(ll1lili1li));
            } else {
                il1I1111l1 = ll1lili1li.loadingPeriod;
            }
            if (lI11IlI1lI.IlIllIll1I(348729433L)) {
                throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{15, 18, 35, 63, 4, 12, 82, 87, 107, 42, 1, 71, 125, 80, 67, 88}));
            }
            return il1I1111l1;
        }
        for (int i = 0; i < list.size(); i++) {
            Il1I1111l1 mediaPeriodForEvent = list.get(i).getMediaPeriodForEvent(llil1lii1i);
            if (mediaPeriodForEvent != null) {
                return mediaPeriodForEvent;
            }
        }
        return (Il1I1111l1) ll1lIlI1lI.access$200(list.get(0)).get(0);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static long getMediaPeriodEndPositionUs(Il1I1111l1 il1I1111l1, IlI11Ill11 ilI11Ill11) throws KeyException {
        if (lII1llllI1.l11I11I11l(1370435949L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{126, 22, 81, 83, 56, 84, 91, 69, 104, 62, 9, 114, 125, 64, 92, 67, 36, 16, 43, 75, 69, 37, 80, 92, 64}));
        }
        I1IIl111I1 i1IIl111I1 = il1I1111l1.mediaPeriodId;
        if (i1IIl111I1.isAd()) {
            l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i1IIl111I1.adGroupIndex);
            if (adGroup.count == -1) {
                return 0L;
            }
            return adGroup.durationsUs[i1IIl111I1.adIndexInAdGroup];
        }
        if (i1IIl111I1.nextAdGroupIndex == -1) {
            return Long.MAX_VALUE;
        }
        l11llI1lI1 adGroup2 = ilI11Ill11.getAdGroup(i1IIl111I1.nextAdGroupIndex);
        long j = adGroup2.timeUs != Long.MIN_VALUE ? adGroup2.timeUs : Long.MAX_VALUE;
        if (IIlII1IIIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{97, 15, 36, 12, 17, 124, 118, 122, 86, 80}), I1I1lI1II1.a(new byte[]{82, 60, 15, 38, 91, 77, 2, 4, 104, 7, 124, 69, 100, 114, 118, 115, 11, 11, 37, 87, 71, 2, 84, 97, 105, 110, 126, 23, 23, 4, 99, 86}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{2, 9, 47, 48, 36, 102, 111, 122, 107, 5, 100, 118, 126, 79, 67, 88, 9, 20, 8, 80, 125, 54, 84, 113, 120}));
        }
        return j;
    }

    private static llIl1lII1I correctMediaLoadData(Il1I1111l1 il1I1111l1, llIl1lII1I llil1lii1i, IlI11Ill11 ilI11Ill11) {
        llIl1lII1I llil1lii1i2 = new llIl1lII1I(llil1lii1i.dataType, llil1lii1i.trackType, llil1lii1i.trackFormat, llil1lii1i.trackSelectionReason, llil1lii1i.trackSelectionData, correctMediaLoadDataPositionMs(llil1lii1i.mediaStartTimeMs, il1I1111l1, ilI11Ill11), correctMediaLoadDataPositionMs(llil1lii1i.mediaEndTimeMs, il1I1111l1, ilI11Ill11));
        if (I1IllIll1l.llII1lIIlI(I1I1lI1II1.a(new byte[]{80, 93, 85, 1, 8, 100, 68, 113, 92, 29, 0, 124, 4, 111, 126, 5, 42, 80, 86}))) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{103, 48, 32, 83, 11, 119, 3, 101, 112, 3, 99, 96, 3, 113, 88, 84, 21, 87, 46, 88, 87}));
        }
        return llil1lii1i2;
    }

    private static long correctMediaLoadDataPositionMs(long j, Il1I1111l1 il1I1111l1, IlI11Ill11 ilI11Ill11) throws CertificateNotYetValidException {
        long mediaPeriodPositionUsForContent;
        if (j == -9223372036854775807L) {
            if (!IIIlIll111.Il1IIlI1II(4630)) {
                return -9223372036854775807L;
            }
            Log.i(I1I1lI1II1.a(new byte[]{0, 2, 47, 0, 43, 114, 71, 102, 82, 8, 94, 125, 71, 10, 86, 119, 55, 38}), I1I1lI1II1.a(new byte[]{111, 39, 33, 80, 81, 99, 84, 115, 65, 87, 72, 85, 95, 96, 108}));
            return 0L;
        }
        long jMsToUs = llIIlI1llI.msToUs(j);
        I1IIl111I1 i1IIl111I1 = il1I1111l1.mediaPeriodId;
        if (i1IIl111I1.isAd()) {
            mediaPeriodPositionUsForContent = IIlII1l1Il.getMediaPeriodPositionUsForAd(jMsToUs, i1IIl111I1.adGroupIndex, i1IIl111I1.adIndexInAdGroup, ilI11Ill11);
        } else {
            mediaPeriodPositionUsForContent = IIlII1l1Il.getMediaPeriodPositionUsForContent(jMsToUs, -1, ilI11Ill11);
        }
        long jUsToMs = llIIlI1llI.usToMs(mediaPeriodPositionUsForContent);
        if (lI1lI11Ill.Il1IIlI1II(596367578L)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{79, 20, 51, 12, 39, 97, 5, 96, 82, 50, 3, 3, 82, 9, 67, 86, 39, 11, 43, 92, 99, 59, 10, 76, 70, 90, 78, 3, 10}));
        }
        return jUsToMs;
    }
}
