package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import I1IlIl1llI.l1IIII1I1I.lI1lII1l1I.l1ll1lIlll.III1l11Ill;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1llIll1lI;
import android.accounts.utils.Ill11ll111;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.media3.common.Il11IllI1l;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.l1I1l1Il11;
import androidx.media3.common.l1II1llIl1;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.StreamCorruptedException;
import java.io.SyncFailedException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class I1111l11ll extends l1llIll1lI {
    private final III1l11Ill<Object, IlI11Ill11> adPlaybackStates;

    public I1111l11ll(l1I1l1Il11 l1i1l1il11, III1l11Ill<Object, IlI11Ill11> iII1l11Ill) {
        super(l1i1l1il11);
        lIlII1IIl1.checkState(l1i1l1il11.getWindowCount() == 1);
        Il11IllI1l il11IllI1l = new Il11IllI1l();
        for (int i = 0; i < l1i1l1il11.getPeriodCount(); i++) {
            l1i1l1il11.getPeriod(i, il11IllI1l, true);
            lIlII1IIl1.checkState(iII1l11Ill.containsKey(lIlII1IIl1.checkNotNull(il11IllI1l.uid)));
        }
        this.adPlaybackStates = iII1l11Ill;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1llIll1lI, androidx.media3.common.l1I1l1Il11
    public l1II1llIl1 getWindow(int i, l1II1llIl1 l1ii1llil1, long j) throws SyncFailedException, StreamCorruptedException, ClassNotFoundException, CertificateException {
        if (Ill11ll111.IIll1I11lI(271293836L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{116, 6, 85, 39, 37, 93, 68, 125, 73, 50, 100, Byte.MAX_VALUE, 90, 105, 68, 12, 11, 34, 3, 87, 88, 49, 7, 102, 126, 69, 99, 55, 34}));
        }
        super.getWindow(i, l1ii1llil1, j);
        Il11IllI1l il11IllI1l = new Il11IllI1l();
        IlI11Ill11 ilI11Ill11 = (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(lIlII1IIl1.checkNotNull(getPeriod(l1ii1llil1.firstPeriodIndex, il11IllI1l, true).uid)));
        long mediaPeriodPositionUsForContent = IIlII1l1Il.getMediaPeriodPositionUsForContent(l1ii1llil1.positionInFirstPeriodUs, -1, ilI11Ill11);
        if (l1ii1llil1.durationUs != -9223372036854775807L) {
            Il11IllI1l period = super.getPeriod(l1ii1llil1.lastPeriodIndex, il11IllI1l, true);
            long j2 = period.positionInWindowUs;
            IlI11Ill11 ilI11Ill112 = (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(period.uid));
            Il11IllI1l period2 = getPeriod(l1ii1llil1.lastPeriodIndex, il11IllI1l);
            l1ii1llil1.durationUs = period2.positionInWindowUs + IIlII1l1Il.getMediaPeriodPositionUsForContent(l1ii1llil1.durationUs - j2, -1, ilI11Ill112);
        } else if (ilI11Ill11.contentDurationUs != -9223372036854775807L) {
            l1ii1llil1.durationUs = ilI11Ill11.contentDurationUs - mediaPeriodPositionUsForContent;
        }
        l1ii1llil1.positionInFirstPeriodUs = mediaPeriodPositionUsForContent;
        if (IIlI1Il1lI.l11I11I11l(972273872L)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{96, 83, 18, 39, 26, 70, 118, 81, 126, 16, 95, 4, 101, 113, 99, 12, 1, 40}));
        }
        return l1ii1llil1;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1llIll1lI, androidx.media3.common.l1I1l1Il11
    public Il11IllI1l getPeriod(int i, Il11IllI1l il11IllI1l, boolean z) throws StreamCorruptedException, CertificateEncodingException {
        if (Il1I1lllIl.I1lI11IIll(I1I1lI1II1.a(new byte[]{64, 44, 32, 63, 42, 4, 113, 117, 115, 52, 8, 88, 115, 112, 115, 102, 38, 13, 1, 106, 106, 20, 99, 86, 73, 124, 84}), 259780997L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{103, 15}));
        }
        super.getPeriod(i, il11IllI1l, true);
        IlI11Ill11 ilI11Ill11 = (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(il11IllI1l.uid));
        long j = il11IllI1l.durationUs;
        long mediaPeriodPositionUsForContent = j == -9223372036854775807L ? ilI11Ill11.contentDurationUs : IIlII1l1Il.getMediaPeriodPositionUsForContent(j, -1, ilI11Ill11);
        Il11IllI1l il11IllI1l2 = new Il11IllI1l();
        long mediaPeriodPositionUsForContent2 = 0;
        for (int i2 = 0; i2 < i + 1; i2++) {
            this.timeline.getPeriod(i2, il11IllI1l2, true);
            IlI11Ill11 ilI11Ill112 = (IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackStates.get(il11IllI1l2.uid));
            if (i2 == 0) {
                mediaPeriodPositionUsForContent2 = -IIlII1l1Il.getMediaPeriodPositionUsForContent(-il11IllI1l2.getPositionInWindowUs(), -1, ilI11Ill112);
            }
            if (i2 != i) {
                mediaPeriodPositionUsForContent2 += IIlII1l1Il.getMediaPeriodPositionUsForContent(il11IllI1l2.durationUs, -1, ilI11Ill112);
            }
        }
        il11IllI1l.set(il11IllI1l.id, il11IllI1l.uid, il11IllI1l.windowIndex, mediaPeriodPositionUsForContent, mediaPeriodPositionUsForContent2, ilI11Ill11, il11IllI1l.isPlaceholder);
        if (IllIIIIII1.I1II1111ll(2515)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{109, 85, 59, 39, 50, 91, 100, 2, 76, 43}));
        }
        return il11IllI1l;
    }
}
