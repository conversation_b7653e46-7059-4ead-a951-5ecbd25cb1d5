package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llII1ll111;
import IIl11IllI1.IIIII1I11I.I111IlIl1l.Il1IIIlI1l.Il1IlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.IOException;
import java.security.UnrecoverableKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IIll1IIlII implements l1l1IllI11 {
    private final Il1I1111l1 mediaPeriod;
    private final int streamIndex;

    public IIll1IIlII(Il1I1111l1 il1I1111l1, int i) {
        this.mediaPeriod = il1I1111l1;
        this.streamIndex = i;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11
    public boolean isReady() {
        return this.mediaPeriod.sharedPeriod.isReady(this.streamIndex);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11
    public void maybeThrowError() throws IOException {
        if (IllIIIIII1.I1lIllll1l(4843)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{88, 53, 48, 87, 19, 90, 121, 67, 109, 54, 94, 119, 97}));
        }
        this.mediaPeriod.sharedPeriod.maybeThrowError(this.streamIndex);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11
    public int readData(Il1IlllI1I il1IlllI1I, llII1ll111 llii1ll111, int i) throws UnrecoverableKeyException {
        int data = this.mediaPeriod.sharedPeriod.readData(this.mediaPeriod, this.streamIndex, il1IlllI1I, llii1ll111, i);
        if (I1IllIll1l.llII1lIIlI(I1I1lI1II1.a(new byte[]{99, 33, 11, 32, 50, 114, 116, 6, 95, 29}))) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{123, 30, 24, 38, 39, 122, 126, 95, 77, 52, 66, 106, 124, 105, 124, 79, 80, 38, 13}));
        }
        return data;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1l1IllI11
    public int skipData(long j) {
        return this.mediaPeriod.sharedPeriod.skipData(this.mediaPeriod, this.streamIndex, j);
    }
}
