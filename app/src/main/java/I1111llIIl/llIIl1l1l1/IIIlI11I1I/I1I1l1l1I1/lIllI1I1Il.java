package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import IIIl11ll1l.l1lI11lI1I.IIIl1llIlI.lI1lIll11I.I1IIIIIlI1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I11l111l1l;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIl111I1;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1IIlllI1I;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l111l1II;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l1ll1I11;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.l1IlllllII;
import Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II;
import android.accounts.utils.IIIlIl1I1l;
import android.media.content.IIl1l1IllI;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.support.v4.graphics.drawable.Il1IIllIll;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.I1111IIl11;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.lI1lI11Ill;
import androidx.core.location.llIl1lII1I;
import androidx.media3.common.II1IIl1I1I;
import androidx.media3.common.III1I11I1l;
import androidx.media3.common.Il11IllI1l;
import androidx.media3.common.Il1I1lllI1;
import androidx.media3.common.IlI11Ill11;
import androidx.media3.common.l11llI1lI1;
import androidx.media3.common.l1I1l1Il11;
import androidx.media3.common.lll1IIII11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.security.DigestException;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.util.Arrays;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.I1lIlIIIl1;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.II1lI1llII;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* loaded from: classes.dex */
public final class lIllI1I1Il extends I1l1ll1I11<I1IIl111I1> {
    private static final I1IIl111I1 CHILD_SOURCE_MEDIA_PERIOD_ID = new I1IIl111I1(new Object());
    private final l1IlllllII adMediaSourceFactory;
    private IlI11Ill11 adPlaybackState;
    private final I1lIlIIIl1 adTagDataSpec;
    private final II1IIl1I1I adViewProvider;
    private final Object adsId;
    private final IIl1II1lII adsLoader;
    private ll1I1l1l1l componentListener;
    private final llIl1I11II contentMediaSource;
    private l1I1l1Il11 contentTimeline;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final Il11IllI1l period = new Il11IllI1l();
    private l1IIlIlIll[][] adMediaSourceHolders = new l1IIlIlIll[0][];

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l1ll1I11
    protected /* synthetic */ I1IIl111I1 getMediaPeriodIdForChildMediaPeriodId(I1IIl111I1 i1IIl111I1, I1IIl111I1 i1IIl111I12) {
        I1IIl111I1 mediaPeriodIdForChildMediaPeriodId = getMediaPeriodIdForChildMediaPeriodId(i1IIl111I1, i1IIl111I12);
        if (IIl1l1IllI.IlII1Illll(374685196L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{103, 23, 10, 12, 19, 67, 91, 88, 86, 2, 98, 69, 119, 109}));
        }
        return mediaPeriodIdForChildMediaPeriodId;
    }

    static /* synthetic */ I1l111l1II access$000(lIllI1I1Il lilli1i1il, I1IIl111I1 i1IIl111I1) throws CertPathBuilderException {
        if (l1IIll1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{89, 42, 3, 8, 36, 102, 70, 73, 87, 85, 92, 101, 108, 90, 13, 12, 18, 59, 5, 68, 121, 10, 122}), 9346)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{126, 20, 22, 12, 53, 68, 3, 114, 110, 93, 113}));
        }
        I1l111l1II i1l111l1IICreateEventDispatcher = lilli1i1il.createEventDispatcher(i1IIl111I1);
        if (llIlI11III.I1II1111ll(I1I1lI1II1.a(new byte[]{84, 10, 9, 40, 5, 2, 70, 106, 76, 87, 123, 92, 114, 10, 126, 6}))) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{114, 18, 52, 31}));
        }
        return i1l111l1IICreateEventDispatcher;
    }

    static /* synthetic */ void access$100(lIllI1I1Il lilli1i1il, IlI11Ill11 ilI11Ill11) throws CertificateException, InvalidAlgorithmParameterException {
        lilli1i1il.onAdPlaybackState(ilI11Ill11);
        if (lIIlI111II.I1Ill1lIII(267005909L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{121, 54, 59, 40}));
        }
    }

    static /* synthetic */ Handler access$200(lIllI1I1Il lilli1i1il) throws NoSuchProviderException {
        Handler handler = lilli1i1il.mainHandler;
        if (Il1lII1l1l.IlII1Illll(1530)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{98, 50, 37, 23, 5, 126, 114, 72, 80, 45, 71}));
        }
        return handler;
    }

    static /* synthetic */ I1l111l1II access$300(lIllI1I1Il lilli1i1il, I1IIl111I1 i1IIl111I1) throws BrokenBarrierException {
        if (androidx.interpolator.view.animation.lIIlI111II.l11llI1lI1(246651535L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{71, 7, 42, 0, 83, 109, 95, 97, 11, 6, 66, Byte.MAX_VALUE, 6, 81, 102, 71, 47, 43, 53, 68, 124, 37, 89, 120, 81, 65, 110, 44, 41, 118, 86, 87}));
        }
        I1l111l1II i1l111l1IICreateEventDispatcher = lilli1i1il.createEventDispatcher(i1IIl111I1);
        if (llIl1lII1I.IlIIl111lI(I1I1lI1II1.a(new byte[]{70, 51, 41, 20, 14, 115, 88, 68, 95, 32, 84, 65, 87, 82, 115, 7, 6, 41, 27, 86, 121, 7, 86, 70, 89, 100, 110, 63, 40, 83, 111, 90}), 894096126L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{124, 3, 13, 48, 80, 65, 124, 68, 64, 6, 123, 1, 124, 84, 3, 99, 51, 53, 39, 118, 85, 4, 1, 13, 10, 71, 3, 7}));
        }
        return i1l111l1IICreateEventDispatcher;
    }

    static /* synthetic */ IIl1II1lII access$400(lIllI1I1Il lilli1i1il) {
        if (Il1lII1l1l.IlII1Illll(482)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{15, 21, 43, 61, 39, 97, 5, 68, 14, 32, 81, 103, 68, 92, 96, 3, 27, 46}));
        }
        return lilli1i1il.adsLoader;
    }

    static /* synthetic */ void access$700(lIllI1I1Il lilli1i1il, Object obj) throws DigestException {
        if (l1l1IllI11.III111l111(I1I1lI1II1.a(new byte[]{111, 17, 82, 93, 55, 68, 89, 88, 88, 20, 65, 3, 93, 88, 94, Byte.MAX_VALUE, 17, 2, 23, 102, 81, 52, 70, 116, 86, 79, 15, 52, 40, 90, 113}), I1I1lI1II1.a(new byte[]{67, 43}))) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{120, 20, 45, 38, 21, 3, 65, 71, 116, 80, 116, 5, 5, 78, 112, 64, 80, 48, 17, 72, 96, 46, 64, 98, 1, 108, 93, 47, 41, 119, 86, 16}));
        }
        lilli1i1il.releaseChildSource(obj);
        if (Il1IIllIll.l1l1l1IIlI(1732641901L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{5, 61, 83, 9, 20, 64, 85, 98, 90, 60, 93, 74, 101, 77, 119, 93, 58}));
        }
    }

    public lIllI1I1Il(llIl1I11II llil1i11ii, I1lIlIIIl1 i1lIlIIIl1, Object obj, l1IlllllII l1illlllii, IIl1II1lII iIl1II1lII, II1IIl1I1I iI1IIl1I1I) {
        this.contentMediaSource = llil1i11ii;
        this.adMediaSourceFactory = l1illlllii;
        this.adsLoader = iIl1II1lII;
        this.adViewProvider = iI1IIl1I1I;
        this.adTagDataSpec = i1lIlIIIl1;
        this.adsId = obj;
        iIl1II1lII.setSupportedContentTypes(l1illlllii.getSupportedTypes());
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public Il1I1lllI1 getMediaItem() {
        if (Il1l11I11I.llll111lI1(I1I1lI1II1.a(new byte[]{71, 51, 15, 21, 36, 93, 83, 73, 94, 46, 7, 2, 118, 74}), 8160)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{98, 34, 91, 41, 37, 118, 125}));
        }
        return this.contentMediaSource.getMediaItem();
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l1ll1I11, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void prepareSourceInternal(II1lI1llII iI1lI1llII) {
        super.prepareSourceInternal(iI1lI1llII);
        final ll1I1l1l1l ll1i1l1l1l = new ll1I1l1l1l(this);
        this.componentListener = ll1i1l1l1l;
        prepareChildSource(CHILD_SOURCE_MEDIA_PERIOD_ID, this.contentMediaSource);
        this.mainHandler.post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.lIllI1I1Il$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$prepareSourceInternal$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-lIllI1I1Il(ll1i1l1l1l);
            }
        });
    }

    /* synthetic */ void lambda$prepareSourceInternal$0$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-lIllI1I1Il(ll1I1l1l1l ll1i1l1l1l) {
        this.adsLoader.start(this, this.adTagDataSpec, this.adsId, this.adViewProvider, ll1i1l1l1l);
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public I1IIlllI1I createPeriod(I1IIl111I1 i1IIl111I1, I1IIIIIlI1 i1IIIIIlI1, long j) throws InvalidAlgorithmParameterException {
        if (((IlI11Ill11) lIlII1IIl1.checkNotNull(this.adPlaybackState)).adGroupCount > 0 && i1IIl111I1.isAd()) {
            int i = i1IIl111I1.adGroupIndex;
            int i2 = i1IIl111I1.adIndexInAdGroup;
            l1IIlIlIll[][] l1iililillArr = this.adMediaSourceHolders;
            l1IIlIlIll[] l1iililillArr2 = l1iililillArr[i];
            if (l1iililillArr2.length <= i2) {
                l1iililillArr[i] = (l1IIlIlIll[]) Arrays.copyOf(l1iililillArr2, i2 + 1);
            }
            l1IIlIlIll l1iililill = this.adMediaSourceHolders[i][i2];
            if (l1iililill == null) {
                l1iililill = new l1IIlIlIll(this, i1IIl111I1);
                this.adMediaSourceHolders[i][i2] = l1iililill;
                maybeUpdateAdMediaSources();
            }
            return l1iililill.createMediaPeriod(i1IIl111I1, i1IIIIIlI1, j);
        }
        I11l111l1l i11l111l1l = new I11l111l1l(i1IIl111I1, i1IIIIIlI1, j);
        i11l111l1l.setMediaSource(this.contentMediaSource);
        i11l111l1l.createPeriod(i1IIl111I1);
        return i11l111l1l;
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.llIl1I11II
    public void releasePeriod(I1IIlllI1I i1IIlllI1I) throws NoSuchAlgorithmException {
        if (I1111IIl11.IIll1I11lI(I1I1lI1II1.a(new byte[]{77, 20, 87, 33, 43, 108, 88, 114, 73, 46, 73}))) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{79, 6, 91, 47, 7, 95, 77, 97, 108, 17, 70, 96, 112, 109, 4, 70, 50, 52}));
        }
        I11l111l1l i11l111l1l = (I11l111l1l) i1IIlllI1I;
        I1IIl111I1 i1IIl111I1 = i11l111l1l.id;
        if (i1IIl111I1.isAd()) {
            l1IIlIlIll l1iililill = (l1IIlIlIll) lIlII1IIl1.checkNotNull(this.adMediaSourceHolders[i1IIl111I1.adGroupIndex][i1IIl111I1.adIndexInAdGroup]);
            l1iililill.releaseMediaPeriod(i11l111l1l);
            if (l1iililill.isInactive()) {
                l1iililill.release();
                this.adMediaSourceHolders[i1IIl111I1.adGroupIndex][i1IIl111I1.adIndexInAdGroup] = null;
            }
        } else {
            i11l111l1l.releasePeriod();
        }
        if (llIl1lII1I.Ill1lIIlIl(2508)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{99, 54, 56, 0, 49, 121, 4, 66, 94, 21, 86, 4, 1, 91, 82, 2, 32, 39}));
        }
    }

    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l1ll1I11, Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I111I1lI1I
    protected void releaseSourceInternal() {
        super.releaseSourceInternal();
        final ll1I1l1l1l ll1i1l1l1l = (ll1I1l1l1l) lIlII1IIl1.checkNotNull(this.componentListener);
        this.componentListener = null;
        ll1i1l1l1l.stop();
        this.contentTimeline = null;
        this.adPlaybackState = null;
        this.adMediaSourceHolders = new l1IIlIlIll[0][];
        this.mainHandler.post(new Runnable() { // from class: I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1.lIllI1I1Il$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$releaseSourceInternal$1$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-lIllI1I1Il(ll1i1l1l1l);
            }
        });
    }

    /* synthetic */ void lambda$releaseSourceInternal$1$I1111llIIl-llIIl1l1l1-IIIlI11I1I-I1I1l1l1I1-lIllI1I1Il(ll1I1l1l1l ll1i1l1l1l) {
        this.adsLoader.stop(this, ll1i1l1l1l);
        if (androidx.core.location.lIIlI111II.IlIlII11Il(8664)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{111, 9, 10, 21, 27, 113, 85, 1, 119, 81, 120, 118, 114, 12, 119, 103, 50, 20, 0}));
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // Ill1IIIllI.Il1IIIlI1l.IlI1lIlIll.IlllIllI1l.I1l1ll1I11
    public void onChildSourceInfoRefreshed(I1IIl111I1 i1IIl111I1, llIl1I11II llil1i11ii, l1I1l1Il11 l1i1l1il11) throws CertificateException, CertStoreException {
        if (i1IIl111I1.isAd()) {
            ((l1IIlIlIll) lIlII1IIl1.checkNotNull(this.adMediaSourceHolders[i1IIl111I1.adGroupIndex][i1IIl111I1.adIndexInAdGroup])).handleSourceInfoRefresh(l1i1l1il11);
        } else {
            lIlII1IIl1.checkArgument(l1i1l1il11.getPeriodCount() == 1);
            this.contentTimeline = l1i1l1il11;
        }
        maybeUpdateSourceInfo();
        if (lI1lI11Ill.Il1IIlI1II(270167443L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{97, 60, 48, 42, 6, 3, 3, 113, 116}));
        }
    }

    protected I1IIl111I1 getMediaPeriodIdForChildMediaPeriodId(I1IIl111I1 i1IIl111I1, I1IIl111I1 i1IIl111I12) {
        if (!i1IIl111I1.isAd()) {
            i1IIl111I1 = i1IIl111I12;
        }
        if (I1IllIll1l.IlIIl111lI(I1I1lI1II1.a(new byte[]{84, 44, 17, 0, 55, 2, 89, 88, 8, 55, 126, 74, 98, 116, 2, 89, 49, 85, 18, 92, 97, 34, 119, 98, 113}), 213916825L)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 1, 81, 23, 51, 118}));
        }
        return i1IIl111I1;
    }

    private void onAdPlaybackState(IlI11Ill11 ilI11Ill11) throws CertificateException, InvalidAlgorithmParameterException {
        if (this.adPlaybackState == null) {
            l1IIlIlIll[][] l1iililillArr = new l1IIlIlIll[ilI11Ill11.adGroupCount][];
            this.adMediaSourceHolders = l1iililillArr;
            Arrays.fill(l1iililillArr, new l1IIlIlIll[0]);
        } else {
            lIlII1IIl1.checkState(ilI11Ill11.adGroupCount == this.adPlaybackState.adGroupCount);
        }
        this.adPlaybackState = ilI11Ill11;
        maybeUpdateAdMediaSources();
        maybeUpdateSourceInfo();
    }

    private void maybeUpdateAdMediaSources() throws InvalidAlgorithmParameterException {
        Uri uri;
        if (IIIlIl1I1l.l11I11I11l(775330488L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{71, 43, 21, 45, 19, 123, 90, 0, 15, 34, 122, 98}));
        }
        IlI11Ill11 ilI11Ill11 = this.adPlaybackState;
        if (ilI11Ill11 == null) {
            return;
        }
        for (int i = 0; i < this.adMediaSourceHolders.length; i++) {
            int i2 = 0;
            while (true) {
                l1IIlIlIll[] l1iililillArr = this.adMediaSourceHolders[i];
                if (i2 < l1iililillArr.length) {
                    l1IIlIlIll l1iililill = l1iililillArr[i2];
                    l11llI1lI1 adGroup = ilI11Ill11.getAdGroup(i);
                    if (l1iililill != null && !l1iililill.hasMediaSource() && i2 < adGroup.uris.length && (uri = adGroup.uris[i2]) != null) {
                        lll1IIII11 uri2 = new lll1IIII11().setUri(uri);
                        III1I11I1l iII1I11I1l = this.contentMediaSource.getMediaItem().localConfiguration;
                        if (iII1I11I1l != null) {
                            uri2.setDrmConfiguration(iII1I11I1l.drmConfiguration);
                        }
                        l1iililill.initializeWithMediaSource(this.adMediaSourceFactory.createMediaSource(uri2.build()), uri);
                    }
                    i2++;
                }
            }
        }
    }

    private void maybeUpdateSourceInfo() throws CertificateException {
        l1I1l1Il11 l1i1l1il11 = this.contentTimeline;
        IlI11Ill11 ilI11Ill11 = this.adPlaybackState;
        if (ilI11Ill11 != null && l1i1l1il11 != null) {
            if (ilI11Ill11.adGroupCount == 0) {
                refreshSourceInfo(l1i1l1il11);
            } else {
                this.adPlaybackState = this.adPlaybackState.withAdDurationsUs(getAdDurationsUs());
                refreshSourceInfo(new I1I1Il1I1I(l1i1l1il11, this.adPlaybackState));
            }
        }
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.l1llI1llII(179825884L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{77, 5, 19, 48, 54, 1, 86, 8, 0, 48, 97, 87, 87, 87, 70, 83}));
        }
    }

    private long[][] getAdDurationsUs() throws CertPathValidatorException {
        long[][] jArr = new long[this.adMediaSourceHolders.length][];
        int i = 0;
        while (true) {
            l1IIlIlIll[][] l1iililillArr = this.adMediaSourceHolders;
            if (i >= l1iililillArr.length) {
                break;
            }
            jArr[i] = new long[l1iililillArr[i].length];
            int i2 = 0;
            while (true) {
                l1IIlIlIll[] l1iililillArr2 = this.adMediaSourceHolders[i];
                if (i2 < l1iililillArr2.length) {
                    l1IIlIlIll l1iililill = l1iililillArr2[i2];
                    jArr[i][i2] = l1iililill == null ? -9223372036854775807L : l1iililill.getDurationUs();
                    i2++;
                }
            }
            i++;
        }
        if (IllIIIIII1.I1lIllll1l(1726)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{116, 55, 32}));
        }
        return jArr;
    }
}
