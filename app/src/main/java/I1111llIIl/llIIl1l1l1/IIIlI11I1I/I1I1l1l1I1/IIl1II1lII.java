package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import androidx.media3.common.II1IIl1I1I;
import androidx.media3.common.II1ll1I1lI;
import java.io.IOException;
import l1llIIII1I.IIIII1I11I.l1l1lIlI1l.l11l1l1l11.I1lIlIIIl1;

/* loaded from: classes.dex */
public interface IIl1II1lII {
    void handlePrepareComplete(lIllI1I1Il lilli1i1il, int i, int i2);

    void handlePrepareError(lIllI1I1Il lilli1i1il, int i, int i2, IOException iOException);

    void release();

    void setPlayer(II1ll1I1lI iI1ll1I1lI);

    void setSupportedContentTypes(int... iArr);

    void start(lIllI1I1Il lilli1i1il, I1lIlIIIl1 i1lIlIIIl1, Object obj, II1IIl1I1I iI1IIl1I1I, II1l1lIllI iI1l1lIllI);

    void stop(lIllI1I1Il lilli1i1il, II1l1lIllI iI1l1lIllI);
}
