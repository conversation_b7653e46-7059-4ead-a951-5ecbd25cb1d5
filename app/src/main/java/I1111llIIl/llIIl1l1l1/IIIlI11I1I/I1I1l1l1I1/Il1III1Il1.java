package I1111llIIl.llIIl1l1l1.IIIlI11I1I.I1I1l1l1I1;

import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import java.io.IOException;
import java.security.UnrecoverableKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* loaded from: classes.dex */
public final class Il1III1Il1 extends IOException {
    public static final int TYPE_AD = 0;
    public static final int TYPE_AD_GROUP = 1;
    public static final int TYPE_ALL_ADS = 2;
    public static final int TYPE_UNEXPECTED = 3;
    public final int type;

    public static Il1III1Il1 createForAd(Exception exc) {
        return new Il1III1Il1(0, exc);
    }

    public static Il1III1Il1 createForAdGroup(Exception exc, int i) {
        return new Il1III1Il1(1, new IOException(I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 92, 95, 84, 93, 20, 84, 6, 65, 5, 64, 95, 20, 67, 21}) + i, exc));
    }

    public static Il1III1Il1 createForAllAds(Exception exc) {
        Il1III1Il1 il1III1Il1 = new Il1III1Il1(2, exc);
        if (Il1I1lllIl.IllIlI1l1I(I1I1lI1II1.a(new byte[]{88, 35, 17, 18, 49, 100, 67, 4, 88, 83, 87, 3, 87, 88, 93, 108, 21, 32, 85, 93}), 394188666L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{126, 11, 87, 0, 32}));
        }
        return il1III1Il1;
    }

    public static Il1III1Il1 createForUnexpected(RuntimeException runtimeException) {
        return new Il1III1Il1(3, runtimeException);
    }

    private Il1III1Il1(int i, Exception exc) {
        super(exc);
        this.type = i;
    }

    public RuntimeException getRuntimeExceptionForUnexpected() throws UnrecoverableKeyException {
        lIlII1IIl1.checkState(this.type == 3);
        RuntimeException runtimeException = (RuntimeException) lIlII1IIl1.checkNotNull(getCause());
        if (IIl1l1IllI.I1lllI1llI(4468)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{86, 33, 44, 28, 4, 69, 80, 99, 95, 52, 91, 104, 90, 83, 93, 89, 48, 8, 16, 99, 115, 0}));
        }
        return runtimeException;
    }
}
