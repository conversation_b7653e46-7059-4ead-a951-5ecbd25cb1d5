package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.view.View;
import androidx.interpolator.view.animation.lI11IlI1lI;
import java.security.cert.CertificateException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.sequences.i;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\u001a\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0001*\u00020\u0000¢\u0006\u0004\b\u0002\u0010\u0003\u001a\u001b\u0010\u0006\u001a\u00020\u0005*\u00020\u00002\b\u0010\u0004\u001a\u0004\u0018\u00010\u0001¢\u0006\u0004\b\u0006\u0010\u0007"}, d2 = {"Landroid/view/View;", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "get", "(Landroid/view/View;)LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "p0", "", "set", "(Landroid/view/View;LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;)V"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class lII1111lIl {
    public static final void set(View view, l1IlIl1llI l1ilil1lli) {
        Intrinsics.checkNotNullParameter(view, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        view.setTag(I1lI11l1l1.view_tree_saved_state_registry_owner, l1ilil1lli);
    }

    public static final l1IlIl1llI get(View view) throws CertificateException {
        if (lI11IlI1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{83, 32, 44, 35, 47, 100, 93, 98, 80, 92, 68, 8, 114, 117, 126, 66, 83, 40}), 189770888L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{121, 10, 80, 4, 45, 123, 114, 71, 13, 7, 89, 106, 116, 8, 78, 80, 18, 45, 50, 125, 90}));
        }
        Intrinsics.checkNotNullParameter(view, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        return (l1IlIl1llI) i.b(i.d(i.a(view, llIlI11III.INSTANCE), llIIl1lI11.INSTANCE));
    }
}
