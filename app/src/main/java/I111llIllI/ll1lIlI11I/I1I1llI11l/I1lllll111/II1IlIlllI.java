package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H&¢\u0006\u0004\b\u0005\u0010\u0006ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001À\u0006\u0001"}, d2 = {"LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/II1IlIlllI;", "", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "p0", "", "onRecreated", "(LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface II1IlIlllI {
    void onRecreated(l1IlIl1llI p0);
}
