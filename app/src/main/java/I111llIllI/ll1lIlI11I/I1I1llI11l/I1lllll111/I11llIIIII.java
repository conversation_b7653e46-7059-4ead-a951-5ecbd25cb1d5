package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.os.Bundle;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.concurrent.TimeoutException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010#\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\r¢\u0006\u0004\b\u000e\u0010\u000fJ\u0015\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0005\u0010\u0006J\u000f\u0010\b\u001a\u00020\u0007H\u0017¢\u0006\u0004\b\b\u0010\tR\u001a\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00020\n8\u0002X\u0083\u0004¢\u0006\u0006\n\u0004\b\u000b\u0010\f"}, d2 = {"LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/I11llIIIII;", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/llI1l11111;", "", "p0", "", "add", "(Ljava/lang/String;)V", "Landroid/os/Bundle;", "saveState", "()Landroid/os/Bundle;", "", "classes", "Ljava/util/Set;", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1II1llIl1;", "<init>", "(LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1II1llIl1;)V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class I11llIIIII implements llI1l11111 {
    private final Set<String> classes;

    public I11llIIIII(l1II1llIl1 l1ii1llil1) throws TimeoutException {
        Intrinsics.checkNotNullParameter(l1ii1llil1, I1I1lI1II1.a(new byte[]{69, 1, 5, 12, 17, 65, 69, 73}));
        this.classes = new LinkedHashSet();
        l1ii1llil1.registerSavedStateProvider(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 72, 23, 23, 81, 70, 80, 93, 71, 65, 3, 21, 7, 28, 98, 4, 64, 65, 82, 68, 67, 0, 22}), this);
    }

    @Override // I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111.llI1l11111
    public Bundle saveState() {
        Bundle bundle = new Bundle();
        bundle.putStringArrayList(I1I1lI1II1.a(new byte[]{84, 8, 3, 22, 17, 80, 68, 111, 77, 11, 111, 66, 80, 74, 64, 90, 16, 4}), new ArrayList<>(this.classes));
        return bundle;
    }

    public final void add(String p0) {
        if (IIlII1IIIl.IllIlI1l1I(I1I1lI1II1.a(new byte[]{14, 61, 14, 2, 48, 126, 83, 95, 97, 0}), 170126744L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{98, 8, 16, 54, 41, 112, 94}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{84, 8, 3, 22, 17, 123, 86, 93, 92}));
        this.classes.add(p0);
    }
}
