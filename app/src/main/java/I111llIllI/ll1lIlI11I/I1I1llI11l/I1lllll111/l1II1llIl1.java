package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import II1I1Il1ll.llIIIll1ll.lIl1I1111l.l1I1l11Il1.ll1ll1lIl1;
import II1I1Il1ll.llIIIll1ll.lIl1I1111l.l1I1l11Il1.lll1I1I11l;
import android.os.Bundle;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.Log;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.interpolator.view.animation.llIlII1IlI;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.security.KeyStoreException;
import java.util.Iterator;
import java.util.Map$Entry;
import java.util.concurrent.TimeoutException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lll1llIIll;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.llll1I1lll;

@Metadata(d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000 )2\u00020\u0001:\u0003()*B\u0007\b\u0000¢\u0006\u0002\u0010\u0002J\u0012\u0010\u0014\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0015\u001a\u00020\u0007H\u0007J\u0010\u0010\u0016\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0015\u001a\u00020\u0007J\u0015\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0001¢\u0006\u0002\b\u001bJ\u0017\u0010\u001c\u001a\u00020\u00182\b\u0010\u001d\u001a\u0004\u0018\u00010\u0013H\u0001¢\u0006\u0002\b\u001eJ\u0010\u0010\u001f\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\u0013H\u0007J\u0018\u0010!\u001a\u00020\u00182\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\bH\u0007J\u0018\u0010#\u001a\u00020\u00182\u000e\u0010$\u001a\n\u0012\u0006\b\u0001\u0012\u00020&0%H\u0007J\u0010\u0010'\u001a\u00020\u00182\u0006\u0010\u0015\u001a\u00020\u0007H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e¢\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u0006X\u0082\u0004¢\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u00020\u0004X\u0080\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u000b\"\u0004\b\f\u0010\rR \u0010\u000f\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u00048G@BX\u0086\u000e¢\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000bR\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e¢\u0006\u0002\n\u0000¨\u0006+"}, d2 = {"Landroidx/savedstate/SavedStateRegistry;", "", "()V", "attached", "", "components", "Landroidx/arch/core/internal/SafeIterableMap;", "", "Landroidx/savedstate/SavedStateRegistry$SavedStateProvider;", "isAllowingSavingState", "isAllowingSavingState$savedstate_release", "()Z", "setAllowingSavingState$savedstate_release", "(Z)V", "<set-?>", "isRestored", "recreatorProvider", "Landroidx/savedstate/Recreator$SavedStateProvider;", "restoredState", "Landroid/os/Bundle;", "consumeRestoredStateForKey", "key", "getSavedStateProvider", "performAttach", "", "lifecycle", "Landroidx/lifecycle/Lifecycle;", "performAttach$savedstate_release", "performRestore", "savedState", "performRestore$savedstate_release", "performSave", "outBundle", "registerSavedStateProvider", "provider", "runOnNextRecreation", "clazz", "Ljava/lang/Class;", "Landroidx/savedstate/SavedStateRegistry$AutoRecreated;", "unregisterSavedStateProvider", "AutoRecreated", "Companion", "SavedStateProvider", "savedstate_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class l1II1llIl1 {
    private boolean attached;
    private final ll1ll1lIl1<String, llI1l11111> components = new ll1ll1lIl1<>();
    private boolean isAllowingSavingState = true;
    private boolean isRestored;
    private I11llIIIII recreatorProvider;
    private Bundle restoredState;

    @Deprecated
    private static final String SAVED_COMPONENTS_KEY = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 72, 23, 8, 89, 86, 80, 90, 77, 86, 14, 4, 76, 112, 69, 15, 87, 89, 82, 84, 91, 0, 55, 83, 65, 6, 83, 55, 22, 4, 22, 80, 101, 85, 94, 13, 67, 68, 71, 64, 26, 94, 7, 24});
    private static final l1l1IllI11 Companion = new l1l1IllI11(null);

    public final boolean isRestored() {
        if (llIlII1IlI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{113, 47, 24, 80, 26, 82, 100, 104, 1}), 171790250L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{77, 93, 86, 14, 43, 115, 81, 84, 10, 22, 115, 3, 119, 112, 103, 64, 8, 88, 42, 124, 74, 80, 67, 115, 121, 0, 121, 0}));
        }
        boolean z = this.isRestored;
        if (androidx.versionedparcelable.custom.entities.llIlI11III.IlII1Illll(188832640L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{123, 21, 46, 63, 37, 4, 68, 115, 88, 39, 82, 123, 77, 1, 102, 69, 3, 84, 81, 66, 105}));
        }
        return z;
    }

    public final boolean isAllowingSavingState$savedstate_release() throws UnsupportedEncodingException {
        boolean z = this.isAllowingSavingState;
        if (IIlIIlIII1.I1lllI1llI(388780457L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{67, 83, 32, 3, 24, 111, 81, 81, 77, 17, 95, 2, 95, 96, 80, 122, 16, 14, 84, 83, 101}));
        }
        return z;
    }

    public final void setAllowingSavingState$savedstate_release(boolean z) {
        this.isAllowingSavingState = z;
    }

    public final Bundle consumeRestoredStateForKey(String key) throws SocketTimeoutException, KeyStoreException {
        if (IllllI11lI.l11I11I11l(I1I1lI1II1.a(new byte[]{116, 10, 21, 50, 17, 122, 77, 85, 13, 93, 92, 120, 119, 99}), 186349054L)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{94, 92, 85, 3, 84, 96, 2, 99}));
        }
        Intrinsics.checkNotNullParameter(key, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        if (!this.isRestored) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{110, 11, 23, 69, 1, 84, 89, 16, 90, 11, 94, 67, 64, 84, 81, 103, 7, 18, 22, 93, 66, 4, 87, 102, 71, 87, 67, 0, 34, 93, 69, 40, 82, 29, 66, 10, 12, 89, 78, 16, 88, 2, 68, 85, 71, 25, 71, 64, 18, 4, 16, 28, 95, 15, 112, 71, 86, 87, 67, 0, 68, 93, 81, 67, 84, 11, 16, 23, 7, 70, 71, 95, 87, 0, 89, 94, 82, 25, 87, 90, 15, 17, 13, 92, 85, 15, 71}).toString());
        }
        Bundle bundle = this.restoredState;
        if (bundle == null) {
            return null;
        }
        Bundle bundle2 = bundle != null ? bundle.getBundle(key) : null;
        Bundle bundle3 = this.restoredState;
        if (bundle3 != null) {
            bundle3.remove(key);
        }
        Bundle bundle4 = this.restoredState;
        boolean z = false;
        if (bundle4 != null && !bundle4.isEmpty()) {
            z = true;
        }
        if (!z) {
            this.restoredState = null;
        }
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{99, 83, 82, 17, 46, 66}), 2516)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{115, 1, 56, 84, 15, 103, 126, 5, 81, 29, 88}));
        }
        return bundle2;
    }

    public final void registerSavedStateProvider(String str, llI1l11111 lli1l11111) throws TimeoutException {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        Intrinsics.checkNotNullParameter(lli1l11111, I1I1lI1II1.a(new byte[]{71, 22, 13, 19, 11, 81, 82, 66}));
        if (!(this.components.putIfAbsent(str, lli1l11111) == null)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{100, 5, 20, 0, 6, 102, 67, 81, 77, 1, 96, 66, 90, 79, 93, 81, 7, 19, 66, 69, 89, 21, 91, 21, 71, 94, 82, 69, 3, 91, 65, 6, 89, 68, 9, 0, 27, 21, 94, 67, 25, 5, 92, 66, 80, 88, 80, 76, 66, 19, 7, 85, 89, 18, 71, 80, 65, 83, 83}).toString());
        }
        if (l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{99, 21}), 1976)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{1, 47, 22, 61, 27, 98, 3, 113, 88, 12, 120, 3, 115, 118, 123, 100, 46, 14, 5, 0, 91, 41, 125, 90, 97, 111, 7, 47}));
        }
    }

    public final llI1l11111 getSavedStateProvider(String str) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        Iterator<Map$Entry<String, llI1l11111>> it = this.components.iterator();
        while (it.hasNext()) {
            Map$Entry<String, llI1l11111> next = it.next();
            Intrinsics.checkNotNullExpressionValue(next, I1I1lI1II1.a(new byte[]{84, 11, 15, 21, 13, 91, 82, 94, 77, 23}));
            String key = next.getKey();
            llI1l11111 value = next.getValue();
            if (Intrinsics.a((Object) key, (Object) str)) {
                return value;
            }
        }
        return null;
    }

    public final void unregisterSavedStateProvider(String key) {
        if (IIlIIlIII1.l11I11I11l(8553)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{69, 30, 56, 14, 39, 82, 14, 95, 93, 85}));
        }
        Intrinsics.checkNotNullParameter(key, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        this.components.remove(key);
    }

    public final void runOnNextRecreation(Class<? extends II1IlIlllI> clazz) throws NoSuchMethodException, SecurityException {
        Intrinsics.checkNotNullParameter(clazz, I1I1lI1II1.a(new byte[]{84, 8, 3, 31, 24}));
        if (!this.isAllowingSavingState) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{116, 5, 12, 69, 12, 90, 67, 16, 73, 1, 66, 86, 90, 75, 89, 21, 22, 9, 11, 65, 16, 0, 80, 65, 90, 89, 89, 69, 5, 84, 67, 6, 69, 68, 13, 11, 49, 84, 65, 85, 112, 10, 67, 68, 84, 87, 87, 80, 49, 21, 3, 70, 85}).toString());
        }
        I11llIIIII i11llIIIII = this.recreatorProvider;
        if (i11llIIIII == null) {
            i11llIIIII = new I11llIIIII(this);
        }
        this.recreatorProvider = i11llIIIII;
        try {
            clazz.getDeclaredConstructor(new Class[0]);
            I11llIIIII i11llIIIII2 = this.recreatorProvider;
            if (i11llIIIII2 != null) {
                String name = clazz.getName();
                Intrinsics.checkNotNullExpressionValue(name, I1I1lI1II1.a(new byte[]{84, 8, 3, 31, 24, 27, 89, 81, 84, 1}));
                i11llIIIII2.add(name);
            }
        } catch (NoSuchMethodException e) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{116, 8, 3, 22, 17, 21}) + clazz.getSimpleName() + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 95, 81, 79, 1, 16, 84, 80, 95, 85, 64, 14, 21, 66, 81, 95, 15, 64, 65, 65, 67, 84, 17, 11, 64, 23, 10, 89, 68, 13, 23, 6, 80, 69, 16, 77, 11, 16, 82, 80, 25, 85, 64, 22, 14, 15, 83, 68, 8, 80, 84, 95, 90, 78, 69, 22, 87, 84, 17, 82, 5, 22, 0, 6}), e);
        }
    }

    public final void performAttach$savedstate_release(llll1I1lll llll1i1lll) {
        Intrinsics.checkNotNullParameter(llll1i1lll, I1I1lI1II1.a(new byte[]{91, 13, 4, 0, 1, 76, 84, 92, 92}));
        if (!(!this.attached)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{100, 5, 20, 0, 6, 102, 67, 81, 77, 1, 98, 85, 82, 80, 71, 65, 16, 24, 66, 69, 81, 18, 19, 84, 95, 68, 82, 4, 0, 75, 23, 2, 67, 16, 3, 6, 10, 80, 83, 30}).toString());
        }
        llll1i1lll.addObserver(new lIIllIlIl1() { // from class: I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111.l1II1llIl1$$ExternalSyntheticLambda0
            @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1
            public final void onStateChanged(lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws InterruptedException {
                l1II1llIl1.performAttach$lambda$4(this.f$0, li1liill1i, lll1lliill);
            }
        });
        this.attached = true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void performAttach$lambda$4(l1II1llIl1 l1ii1llil1, lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws InterruptedException {
        Intrinsics.checkNotNullParameter(l1ii1llil1, I1I1lI1II1.a(new byte[]{67, 12, 11, 22, 70, 5}));
        Intrinsics.checkNotNullParameter(li1liill1i, I1I1lI1II1.a(new byte[]{11, 5, 12, 10, 12, 76, 90, 95, 76, 23, 16, 64, 84, 75, 85, 88, 7, 21, 7, 64, 16, 81, 13}));
        Intrinsics.checkNotNullParameter(lll1lliill, I1I1lI1II1.a(new byte[]{82, 18, 7, 11, 22}));
        if (lll1lliill == lll1llIIll.ON_START) {
            l1ii1llil1.isAllowingSavingState = true;
        } else if (lll1lliill == lll1llIIll.ON_STOP) {
            l1ii1llil1.isAllowingSavingState = false;
        }
        if (ll1l11I1II.Il1IIlI1II(I1I1lI1II1.a(new byte[]{80, 1, 86, 93, 85, 66, 102, 64, 0, 38, 94, 4, 86, 125, 96, 3, 8, 20, 11, 125, 100, 16, 3, 5, 99, 14, 5, 7, 9, 102, 83}), 2354)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{101, 33, 46, 8, 3, 119, 110, 113, 124, 17, 72, 9, 84, 67, 124, 120, 12, 51, 41, 118, 88, 3, 66, 4, 80, 69, 100, 82, 93, 67, 0}));
        }
    }

    public final void performRestore$savedstate_release(Bundle savedState) {
        if (IIIlIll111.Il1IIlI1II(6939)) {
            Log.v(I1I1lI1II1.a(new byte[]{101, 16, 12, 23, 87, 114, 15, 73, 126, 37, 93, 116, 97, 83}), I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 28, 17, 36, 37, 92, 90, 94, 11, 50, 72, 85, 87, 10, 100, 80, 53, 20, 11, 97, 102}));
        } else {
            if (!this.attached) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{110, 11, 23, 69, 15, 64, 68, 68, 25, 7, 81, 92, 89, 25, 68, 80, 16, 7, 13, 64, 93, 32, 71, 65, 82, 85, 95, 77, 77, 18, 85, 6, 81, 11, 16, 0, 66, 86, 86, 92, 85, 13, 94, 87, 21, 73, 81, 71, 4, 14, 16, 95, 98, 4, 64, 65, 92, 68, 82, 77, 38, 71, 89, 7, 91, 1, 75, 75}).toString());
            }
            if (!(!this.isRestored)) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{100, 5, 20, 0, 6, 102, 67, 81, 77, 1, 98, 85, 82, 80, 71, 65, 16, 24, 66, 69, 81, 18, 19, 84, 95, 68, 82, 4, 0, 75, 23, 17, 82, 23, 22, 10, 16, 80, 83, 30}).toString());
            }
            this.restoredState = savedState != null ? savedState.getBundle(SAVED_COMPONENTS_KEY) : null;
            this.isRestored = true;
        }
    }

    public final void performSave(Bundle outBundle) {
        Intrinsics.checkNotNullParameter(outBundle, I1I1lI1II1.a(new byte[]{88, 17, 22, 39, 23, 91, 83, 92, 92}));
        Bundle bundle = new Bundle();
        Bundle bundle2 = this.restoredState;
        if (bundle2 != null) {
            bundle.putAll(bundle2);
        }
        lll1I1I11l lll1i1i11lIteratorWithAdditions = this.components.iteratorWithAdditions();
        Intrinsics.checkNotNullExpressionValue(lll1i1i11lIteratorWithAdditions, I1I1lI1II1.a(new byte[]{67, 12, 11, 22, 76, 86, 88, 93, 73, 11, 94, 85, 91, 77, 71, 27, 11, 21, 7, 64, 81, 21, 92, 71, 100, 95, 67, 13, 37, 86, 83, 10, 67, 13, 13, 11, 17, 29, 30}));
        lll1I1I11l lll1i1i11l = lll1i1i11lIteratorWithAdditions;
        while (lll1i1i11l.hasNext()) {
            Map$Entry next = lll1i1i11l.next();
            bundle.putBundle((String) next.getKey(), ((llI1l11111) next.getValue()).saveState());
        }
        if (!bundle.isEmpty()) {
            outBundle.putBundle(SAVED_COMPONENTS_KEY, bundle);
        }
        if (ll1l11I1II.Il1IIlI1II(I1I1lI1II1.a(new byte[]{115, 85, 16, 0, 36, 97, 81, 115, 126, 33, 91, 3, 2, 113, 93}), 8170)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{121, 87, 13, 38, 19, 80, 110, 121, 74, 23, 106, 122, 68, 116, 78, 100, 1, 34, 19, 98, 72, 13}));
        }
    }
}
