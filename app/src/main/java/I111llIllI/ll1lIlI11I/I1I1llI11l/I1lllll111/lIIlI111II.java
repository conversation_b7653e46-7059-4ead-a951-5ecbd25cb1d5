package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.net.NoRouteToHostException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0007\u0010\bJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007¢\u0006\u0004\b\u0005\u0010\u0006"}, d2 = {"LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/lIIlI111II;", "", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "p0", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1I1l1Il1I;", "create", "(LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;)LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1I1l1Il1I;", "<init>", "()V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class lIIlI111II {
    public /* synthetic */ lIIlI111II(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    private lIIlI111II() {
    }

    public final l1I1l1Il1I create(l1IlIl1llI p0) throws NoRouteToHostException {
        if (l1lll111II.IlII1Illll(I1I1lI1II1.a(new byte[]{125, 16, 53, 44, 54, 95, 115, 121, 99, 54, 125, 93, 67, 94}))) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{64, 6, 91, 18, 32, 113, 117, 70, 117, 62, 122, 5, 88, 73, 123}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{88, 19, 12, 0, 16}));
        l1I1l1Il1I l1i1l1il1i = new l1I1l1Il1I(p0, null);
        if (android.support.v4.graphics.drawable.lIIlI111II.IIll1l1lII(2983)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{102, 87, 37, 16, 38, 69, 101, 118, 79, 85, 122, 9, 7, 85, 86, 92, 47, 4, 87, 86, 70, 53, 118}));
        }
        return l1i1l1il1i;
    }
}
