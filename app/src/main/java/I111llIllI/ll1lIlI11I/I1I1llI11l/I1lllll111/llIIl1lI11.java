package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.view.View;
import androidx.interpolator.view.animation.IllllI11lI;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u0004\u0018\u00010\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\n¢\u0006\u0002\b\u0004"}, d2 = {"<anonymous>", "Landroidx/savedstate/SavedStateRegistryOwner;", "view", "Landroid/view/View;", "invoke"}, k = 3, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
final class llIIl1lI11 extends s implements Function1<View, l1IlIl1llI> {
    public static final llIIl1lI11 INSTANCE = new llIIl1lI11();

    llIIl1lI11() {
        super(1);
    }

    @Override // kotlin.jvm.functions.Function1
    public final l1IlIl1llI invoke(View view) {
        Intrinsics.checkNotNullParameter(view, I1I1lI1II1.a(new byte[]{65, 13, 7, 18}));
        Object tag = view.getTag(I1lI11l1l1.view_tree_saved_state_registry_owner);
        l1IlIl1llI l1ilil1lli = tag instanceof l1IlIl1llI ? (l1IlIl1llI) tag : null;
        if (IllllI11lI.l11I11I11l(I1I1lI1II1.a(new byte[]{113, 12, 82, 7, 56, 12, 15, 122, 116, 0, 126, 101, 97, 124}), 195276079L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{100, 84, 44, 28, 18, 1, 95, 72, 96, 53, 8}));
        }
        return l1ilil1lli;
    }
}
