package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.media.content.II1I11IlI1;
import android.os.Bundle;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.net.SocketTimeoutException;
import java.security.KeyStoreException;
import java.security.cert.CertPathBuilderException;
import java.util.ArrayList;
import java.util.Iterator;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lll1llIIll;

@Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0000\u0018\u0000 \u000e2\u00020\u0001:\u0002\u000e\u000fB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016J\u0010\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0010"}, d2 = {"Landroidx/savedstate/Recreator;", "Landroidx/lifecycle/LifecycleEventObserver;", "owner", "Landroidx/savedstate/SavedStateRegistryOwner;", "(Landroidx/savedstate/SavedStateRegistryOwner;)V", "onStateChanged", "", "source", "Landroidx/lifecycle/LifecycleOwner;", "event", "Landroidx/lifecycle/Lifecycle$Event;", "reflectiveNew", "className", "", "Companion", "SavedStateProvider", "savedstate_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class IlIIlIl1II implements lIIllIlIl1 {
    public static final String CLASSES_KEY = I1I1lI1II1.a(new byte[]{84, 8, 3, 22, 17, 80, 68, 111, 77, 11, 111, 66, 80, 74, 64, 90, 16, 4});
    public static final String COMPONENT_KEY = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 72, 23, 23, 81, 70, 80, 93, 71, 65, 3, 21, 7, 28, 98, 4, 64, 65, 82, 68, 67, 0, 22});
    public static final IllIIIIII1 Companion = new IllIIIIII1(null);
    private final l1IlIl1llI owner;

    public IlIIlIl1II(l1IlIl1llI l1ilil1lli) {
        Intrinsics.checkNotNullParameter(l1ilil1lli, I1I1lI1II1.a(new byte[]{88, 19, 12, 0, 16}));
        this.owner = l1ilil1lli;
    }

    @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIIllIlIl1
    public void onStateChanged(lI1lIIll1I li1liill1i, lll1llIIll lll1lliill) throws CertPathBuilderException, IllegalAccessException, NoSuchMethodException, SocketTimeoutException, InstantiationException, SecurityException, KeyStoreException, IllegalArgumentException, InvocationTargetException {
        if (II1I11IlI1.lll1111l11(I1I1lI1II1.a(new byte[]{79, 21, 58, 28, 6, 3, 5, 121, Byte.MAX_VALUE, 37, 123, 73, 86, 106, 68, 5, 49, 3, 5, 68}), 8293)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{67, 20}));
        }
        Intrinsics.checkNotNullParameter(li1liill1i, I1I1lI1II1.a(new byte[]{68, 11, 23, 23, 1, 80}));
        Intrinsics.checkNotNullParameter(lll1lliill, I1I1lI1II1.a(new byte[]{82, 18, 7, 11, 22}));
        if (lll1lliill != lll1llIIll.ON_CREATE) {
            throw new AssertionError(I1I1lI1II1.a(new byte[]{121, 1, 26, 17, 66, 80, 65, 85, 87, 16, 16, 93, 64, 74, 64, 21, 0, 4, 66, 125, 126, 62, 112, 103, 118, 119, 99, 32}));
        }
        li1liill1i.getLifecycle().removeObserver(this);
        Bundle bundleConsumeRestoredStateForKey = this.owner.getSavedStateRegistry().consumeRestoredStateForKey(COMPONENT_KEY);
        if (bundleConsumeRestoredStateForKey == null) {
            return;
        }
        ArrayList<String> stringArrayList = bundleConsumeRestoredStateForKey.getStringArrayList(CLASSES_KEY);
        if (stringArrayList == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{117, 17, 12, 1, 14, 80, 23, 71, 80, 16, 88, 16, 71, 92, 71, 65, 13, 19, 7, 86, 16, 18, 71, 84, 71, 83, 23, 3, 11, 64, 23, 23, 95, 1, 66, 6, 13, 88, 71, 95, 87, 1, 94, 68, 21, 27, 85, 91, 6, 19, 13, 91, 84, 25, 29, 70, 82, 64, 82, 1, 23, 70, 86, 23, 82, 74, 48, 0, 17, 65, 86, 66, 77, 1, 66, 18, 21, 84, 65, 70, 22, 65, 1, 93, 94, 21, 82, 92, 93, 22, 91, 12, 23, 70, 23, 12, 81, 68, 17, 17, 16, 92, 89, 87, 74, 68, 82, 73, 21, 77, 92, 80, 66, 10, 7, 75, 16, 67, 80, 89, 82, 69, 68, 0, 23, 109, 67, 12, 104, 22, 7, 22, 22, 90, 69, 85, 27}));
        }
        Iterator<String> it = stringArrayList.iterator();
        while (it.hasNext()) {
            reflectiveNew(it.next());
        }
        if (lII1llllI1.lI11llll1I(I1I1lI1II1.a(new byte[]{115, 17, 82, 4, 43, 0, 69, 74, 117, 52, 124, 118, 92, 88, 113, 100, 90, 19, 48, 122}), 548174380L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{99, 0, 0, 31, 82, 115, 15, 105, 0, 47, 84, 86, 64, 88, 121, 98, 0}));
        }
    }

    private final void reflectiveNew(String className) throws IllegalAccessException, NoSuchMethodException, InstantiationException, SecurityException, IllegalArgumentException, InvocationTargetException {
        if (android.accounts.utils.lIIlI111II.Il1lII1l1l(223808775L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{120, 18, 15, 42, 32, 13}));
        }
        try {
            Class<? extends U> clsAsSubclass = Class.forName(className, false, IlIIlIl1II.class.getClassLoader()).asSubclass(II1IlIlllI.class);
            Intrinsics.checkNotNullExpressionValue(clsAsSubclass, I1I1lI1II1.a(new byte[]{76, 110, 66, 69, 66, 21, 23, 16, 25, 68, 16, 16, 21, 25, 20, 21, 66, 65, 33, 94, 81, 18, 64, 27, -47, -74, -111, 6, 8, 83, 68, 16, 25, 14, 3, 19, 3, 28, 61, 16, 25, 68, 16, 16, 21, 25, 20, 21, 66, 65, 66, 79}));
            try {
                Constructor declaredConstructor = clsAsSubclass.getDeclaredConstructor(new Class[0]);
                declaredConstructor.setAccessible(true);
                try {
                    Object objNewInstance = declaredConstructor.newInstance(new Object[0]);
                    Intrinsics.checkNotNullExpressionValue(objNewInstance, I1I1lI1II1.a(new byte[]{76, 110, 66, 69, 66, 21, 23, 16, 25, 68, 16, 16, 21, 25, 20, 21, 66, 65, 1, 93, 94, 18, 71, 71, -47, -74, -111, 18, 45, 92, 68, 23, 86, 10, 1, 0, 74, 28, 61, 16, 25, 68, 16, 16, 21, 25, 20, 21, 66, 65, 66, 79}));
                    ((II1IlIlllI) objNewInstance).onRecreated(this.owner);
                    if (androidx.constraintlayout.widget.lIIlI111II.l1llI1llII(3969)) {
                        throw new InstantiationError(I1I1lI1II1.a(new byte[]{110, 34, 23, 92, 46, 90, 118, 85, 91, 86, 69, 114, 100, 86, 121, 65, 19, 47, 51, 5, 98, 46, 96, 76, 126, 97, 97, 28}));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 89, 94, 70, 77, 85, 91, 22, 8, 3, 70, 85, 65}) + className, e);
                }
            } catch (NoSuchMethodException e2) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{116, 8, 3, 22, 17, 21}) + clsAsSubclass.getSimpleName() + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 95, 81, 79, 1, 16, 84, 80, 95, 85, 64, 14, 21, 66, 81, 95, 15, 64, 65, 65, 67, 84, 17, 11, 64, 23, 10, 89, 68, 13, 23, 6, 80, 69, 16, 77, 11, 16, 82, 80, 25, 85, 64, 22, 14, 15, 83, 68, 8, 80, 84, 95, 90, 78, 69, 22, 87, 84, 17, 82, 5, 22, 0, 6}), e2);
            }
        } catch (ClassNotFoundException e3) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{116, 8, 3, 22, 17, 21}) + className + I1I1lI1II1.a(new byte[]{23, 19, 3, 22, 12, 18, 67, 16, 95, 11, 69, 94, 81}), e3);
        }
    }
}
