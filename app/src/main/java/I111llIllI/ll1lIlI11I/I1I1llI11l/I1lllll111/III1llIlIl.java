package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.view.View;
import java.security.cert.CertificateException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0001*\u00020\u0000¢\u0006\u0004\b\u0002\u0010\u0003"}, d2 = {"Landroid/view/View;", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "findViewTreeSavedStateRegistryOwner", "(Landroid/view/View;)LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class III1llIlIl {
    public static final /* synthetic */ l1IlIl1llI findViewTreeSavedStateRegistryOwner(View view) throws CertificateException {
        Intrinsics.checkNotNullParameter(view, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        l1IlIl1llI l1ilil1lli = lII1111lIl.get(view);
        if (androidx.interpolator.view.animation.lIIlI111II.llI1llI1l1(179752290L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{70, 19}));
        }
        return l1ilil1lli;
    }
}
