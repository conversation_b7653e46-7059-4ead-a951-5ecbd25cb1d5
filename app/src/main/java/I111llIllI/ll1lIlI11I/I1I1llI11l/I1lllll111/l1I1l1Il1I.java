package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import android.os.Bundle;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.IIlIIlIII1;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.io.EOFException;
import java.io.InvalidClassException;
import java.security.DigestException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.I11IlIIll1;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.llll1I1lll;

@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \u00122\u00020\u0001:\u0001\u0012B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\b\u0010\u000b\u001a\u00020\fH\u0007J\u0012\u0010\r\u001a\u00020\f2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0007J\u0010\u0010\u0010\u001a\u00020\f2\u0006\u0010\u0011\u001a\u00020\u000fH\u0007R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000R\u0011\u0010\u0007\u001a\u00020\b¢\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n¨\u0006\u0013"}, d2 = {"Landroidx/savedstate/SavedStateRegistryController;", "", "owner", "Landroidx/savedstate/SavedStateRegistryOwner;", "(Landroidx/savedstate/SavedStateRegistryOwner;)V", "attached", "", "savedStateRegistry", "Landroidx/savedstate/SavedStateRegistry;", "getSavedStateRegistry", "()Landroidx/savedstate/SavedStateRegistry;", "performAttach", "", "performRestore", "savedState", "Landroid/os/Bundle;", "performSave", "outBundle", "Companion", "savedstate_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class l1I1l1Il1I {
    public static final lIIlI111II Companion = new lIIlI111II(null);
    private boolean attached;
    private final l1IlIl1llI owner;
    private final l1II1llIl1 savedStateRegistry;

    public /* synthetic */ l1I1l1Il1I(l1IlIl1llI l1ilil1lli, DefaultConstructorMarker defaultConstructorMarker) {
        this(l1ilil1lli);
    }

    public static final l1I1l1Il1I create(l1IlIl1llI l1ilil1lli) {
        if (IIlIIlIII1.I1lllI1llI(292426579L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{7, 30, 85, 52, 6, 69, 14, 101, 113, 62, 0, 6, 100, 106, 109, 5, 55, 32, 11, 115, 85, 4, Byte.MAX_VALUE, 108, 106, 84, 85, 92}));
        }
        return Companion.create(l1ilil1lli);
    }

    private l1I1l1Il1I(l1IlIl1llI l1ilil1lli) {
        this.owner = l1ilil1lli;
        this.savedStateRegistry = new l1II1llIl1();
    }

    public final l1II1llIl1 getSavedStateRegistry() {
        if (androidx.versionedparcelable.custom.entities.llIlI11III.l1l1l1IIlI(326621842L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{77, 8, 21, 36, 55, 7, 121, 101, 110, 47, 101, 6, 126, 93, 115, 98, 49, 0, 91, 92, 8, 85, 86, 118, 86, 87, 117, 2, 55, 107}));
        }
        l1II1llIl1 l1ii1llil1 = this.savedStateRegistry;
        if (I11II1l1lI.IlII1Illll(187182920L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{88, 61, 58, 9, 11, 1, 98}));
        }
        return l1ii1llil1;
    }

    public final void performAttach() {
        if (I1111IIl11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{14, 34, 13, 16, 38, 125, 112, 7, 117, 7, 116, 119, 68, 115, 66, 13, 6, 39, 8, 68, 71, 55, 103, 101, 68, 2}), 423349129L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{126, 8, 6, 16, 15, 68, 125, 81, 105, 23, 123, 7, 87, 76, 67, 5, 7, 18, 17, 124, 82, 22, 96, 118, 112, 81, 4}));
        }
        llll1I1lll lifecycle = this.owner.getLifecycle();
        if (!(lifecycle.getCurrentState() == I11IlIIll1.INITIALIZED)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{101, 1, 17, 17, 3, 71, 67, 85, 75, 68, 93, 69, 70, 77, 20, 87, 7, 65, 1, 64, 85, 0, 71, 80, 87, 22, 88, 11, 8, 75, 23, 7, 66, 22, 11, 11, 5, 21, 88, 71, 87, 1, 66, 23, 70, 25, 93, 91, 11, 21, 11, 83, 92, 8, 73, 84, 71, 95, 88, 11, 68, 65, 67, 2, 80, 1}).toString());
        }
        lifecycle.addObserver(new IlIIlIl1II(this.owner));
        this.savedStateRegistry.performAttach$savedstate_release(lifecycle);
        this.attached = true;
        if (androidx.core.location.lIIlI111II.l1l11llIl1(265794082L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{118, 41, 10, 3, 23, 12, 120, 72, 119, 14, 123, 119, 98, 92, 103, 86, 12, 83, 46, 72, 103, 49, 91, 70, 124, 70, 91, 55, 2, 112, 121}));
        }
    }

    public final void performRestore(Bundle savedState) {
        if (I1I1IIIIl1.l11I11I11l(231079690L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{4, 54, 50, 10, 58, 70, 88, 74, 92, 54, 120, 119, 80, 123, 100, 1, 83, 5, 58, 2, 99, 16, 66, 97, 68, 96}));
        }
        if (!this.attached) {
            performAttach();
        }
        llll1I1lll lifecycle = this.owner.getLifecycle();
        if (!(!lifecycle.getCurrentState().isAtLeast(I11IlIIll1.STARTED))) {
            throw new IllegalStateException((I1I1lI1II1.a(new byte[]{71, 1, 16, 3, 13, 71, 90, 98, 92, 23, 68, 95, 71, 92, 20, 86, 3, 15, 12, 93, 68, 65, 81, 80, 19, 85, 86, 9, 8, 87, 83, 67, 64, 12, 7, 11, 66, 90, 64, 94, 92, 22, 16, 89, 70, 25}) + lifecycle.getCurrentState()).toString());
        }
        this.savedStateRegistry.performRestore$savedstate_release(savedState);
        if (I1Ill1lIII.I1lIllll1l(4527)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{101, 10, 87, 0, 14, 5, 80, 0, 81, 45, 103, 93, 7, 105, 98, 100, 40, 82, 43, 75, 87}));
        }
    }

    public final void performSave(Bundle outBundle) {
        Intrinsics.checkNotNullParameter(outBundle, I1I1lI1II1.a(new byte[]{88, 17, 22, 39, 23, 91, 83, 92, 92}));
        this.savedStateRegistry.performSave(outBundle);
    }
}
