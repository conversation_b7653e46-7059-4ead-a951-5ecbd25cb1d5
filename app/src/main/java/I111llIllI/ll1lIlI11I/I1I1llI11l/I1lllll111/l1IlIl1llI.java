package I111llIllI.ll1lIlI11I.I1I1llI11l.I1lllll111;

import kotlin.Metadata;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lI1lIIll1I;

@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001R\u0014\u0010\u0005\u001a\u00020\u00028'X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0003\u0010\u0004ø\u0001\u0000\u0082\u0002\u0006\n\u0004\b!0\u0001À\u0006\u0001"}, d2 = {"LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1IlIl1llI;", "LllIlIllII1/llIIl1l1l1/l1Il1I1l1I/lI11ll11I1/lI1lIIll1I;", "LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1II1llIl1;", "getSavedStateRegistry", "()LI111llIllI/ll1lIlI11I/I1I1llI11l/I1lllll111/l1II1llIl1;", "savedStateRegistry"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface l1IlIl1llI extends lI1lIIll1I {
    l1II1llIl1 getSavedStateRegistry();
}
