package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.Ill11ll111;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import android.content.res.XmlResourceParser;
import android.graphics.Shader;
import android.media.content.II1I11IlI1;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Xml;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.EOFException;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.AccessControlException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public final class l1ll11l111 {
    private static final String LOG_TAG = I1I1lI1II1.a(new byte[]{116, 11, 15, 21, 14, 80, 79, 115, 86, 8, 95, 66, 118, 86, 89, 69, 3, 21});
    private int mColor;
    private final ColorStateList mColorStateList;
    private final Shader mShader;

    private l1ll11l111(Shader shader, ColorStateList colorStateList, int i) {
        this.mShader = shader;
        this.mColorStateList = colorStateList;
        this.mColor = i;
    }

    static l1ll11l111 from(Shader shader) {
        if (I1Ill1lIII.I1II1111ll(I1I1lI1II1.a(new byte[]{5, 21, 32, 34, 16, 5, 6, 64, 80, 93, 91, 2, 89, 74, 112, 1, 81, 23, 43, 71, 91, 87}))) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{101, 30, 17}));
        }
        return new l1ll11l111(shader, null, 0);
    }

    static l1ll11l111 from(ColorStateList colorStateList) throws EOFException {
        l1ll11l111 l1ll11l111Var = new l1ll11l111(null, colorStateList, colorStateList.getDefaultColor());
        if (lIIlI111II.II1lllllII(3921)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{100, 46, 4, 39, 0, 12, 93, 87, 15, 0, 67}));
        }
        return l1ll11l111Var;
    }

    static l1ll11l111 from(int i) throws NoSuchAlgorithmException, UnknownHostException {
        if (II1I11IlI1.III11111Il(182440175L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{90, 41, 27, 17, 86, 5, 90, 86, 11, 14}));
        }
        l1ll11l111 l1ll11l111Var = new l1ll11l111(null, null, i);
        if (lI11IlI1lI.l1l1Il1I11(I1I1lI1II1.a(new byte[]{125, 19, 36, 44, 20, 103, 66, 65, 119, 11, 9, 0, 88, 76, 69, 70}), 213890782L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{126, 1}));
        }
        return l1ll11l111Var;
    }

    public Shader getShader() {
        return this.mShader;
    }

    public int getColor() {
        if (IIlI1Il1lI.ll1I1lII11(I1I1lI1II1.a(new byte[]{1, 34, 4, 21, 41, 116, 64, 119, 84, 20, 67}))) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{5, 41, 26, 54, 43, 64, 91, 92, 67, 35}));
        }
        int i = this.mColor;
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{126, 8, 7, 43, 51, 109, 69, 120, 82, 38, 81, 84, 5, 81, 89, 6, 84, 13}), 276198536L)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{120, 52, 44, 52, 26, 111, 100, 90, 67, 3, 123, 118, 111, 73, 2, 12, 27, 36, 42, 84, 90}));
        }
        return i;
    }

    public void setColor(int i) throws ClassNotFoundException, CertPathValidatorException {
        if (android.accounts.utils.lIIlI111II.I1111IIl11(161568777L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{14, 81, 54, 3, 86, 1, 116, 103, 88, 14, 104, 115, 69, 108, 93, 111, 42, 52, 24, 68, 65, 3, 100, 114, 84, 83, 116, 44, 60, 124}));
        }
        this.mColor = i;
        if (androidx.core.location.lIIlI111II.l1llI1llII(542)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{67, 46, 4, 63, 35, 115, 97, 104, 10, 43, 103, 99, Byte.MAX_VALUE, 125, 7, 6, 14, 48, 47, 11, 2, 9, 101, 4, 122, 100, 2, 60, 48, 91, 15}));
        }
    }

    public boolean isGradient() {
        return this.mShader != null;
    }

    public boolean isStateful() throws SocketTimeoutException {
        ColorStateList colorStateList;
        boolean z = this.mShader == null && (colorStateList = this.mColorStateList) != null && colorStateList.isStateful();
        if (l1l1IllI11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{80, 92, 46, 36, 90, 118, 100, 99, 0, 87, 88, 68, 99, 126, 82, 125, 8, 16, 52, 86, 95, 45, 96, 102, 119, 70, 70, 81, 3, 102}), IronSourceConstants.RV_OPERATIONAL_LOAD_AD)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{79}));
        }
        return z;
    }

    public boolean onStateChanged(int[] iArr) throws CertificateParsingException {
        if (l1lI1I1l11.ll1I1lII11(I1I1lI1II1.a(new byte[]{101, 1, 12, 38, 0, 100, 68, 9, 95, 53, 98, 100, 115, 77, 114, 91, 59, 46, 56, 64, 101, 50}), 9140)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{91, 60, 5, 28, 55, 87}));
        }
        if (isStateful()) {
            ColorStateList colorStateList = this.mColorStateList;
            int colorForState = colorStateList.getColorForState(iArr, colorStateList.getDefaultColor());
            if (colorForState != this.mColor) {
                this.mColor = colorForState;
                return true;
            }
        }
        return false;
    }

    public boolean willDraw() {
        return isGradient() || this.mColor != 0;
    }

    public static l1ll11l111 inflate(Resources resources, int i, Resources$Theme resources$Theme) {
        if (lIIII1l1lI.I1lllI1llI(9845)) {
            Log.v(I1I1lI1II1.a(new byte[]{122, 12, 80}), I1I1lI1II1.a(new byte[]{89, 9, 43, 19, 91}));
            return null;
        }
        try {
            l1ll11l111 l1ll11l111VarCreateFromXml = createFromXml(resources, i, resources$Theme);
            if (IlIIlI11I1.Il1IIlI1II(290829148L)) {
                throw new NullPointerException(I1I1lI1II1.a(new byte[]{69, 21, 48, 4, 42, 82, 14}));
            }
            return l1ll11l111VarCreateFromXml;
        } catch (Exception e) {
            Log.e(LOG_TAG, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 89, 94, 83, 85, 85, 65, 7, 65, 33, 93, 93, 17, 95, 80, 75, 117, 88, 9, 11, 64, 25}), e);
            if (Ill11ll111.l1Il11I1Il(I1I1lI1II1.a(new byte[]{82, 21, 24, 11, 43, 70, 109, 89, 64, 82, 6, 7, 71, 107, 67, 103, 17, 37, 16}), 710719615L)) {
                throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{117, 5, 82, 50, 52, 84, 110, 83, 116, 43, 103, 3, 91, 124, 86, 102, 23, 57, 32, 112, 96, 14, 68, 12, 7, 101, 101}));
            }
            return null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:21:0x0055  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static l1ll11l111 createFromXml(Resources resources, int i, Resources$Theme resources$Theme) throws XmlPullParserException, Resources$NotFoundException, IOException, CertificateException {
        int next;
        char c;
        if (lIIllIlIl1.l11I11I11l(183584000L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{82, 93, 40, 2, 17, 97, 111, Byte.MAX_VALUE, 120, 15, 89, 82, 71, 75, 3, 91, 33, 5, 58, 0, 89, 36}));
        }
        XmlResourceParser xml = resources.getXml(i);
        AttributeSet attributeSetAsAttributeSet = Xml.asAttributeSet(xml);
        do {
            next = xml.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next != 2) {
            throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{121, 11, 66, 22, 22, 84, 69, 68, 25, 16, 81, 87, 21, 95, 91, 64, 12, 5}));
        }
        String name = xml.getName();
        int iHashCode = name.hashCode();
        if (iHashCode != 89650992) {
            c = (iHashCode == 1191572447 && name.equals(I1I1lI1II1.a(new byte[]{68, 1, 14, 0, 1, 65, 88, 66}))) ? (char) 0 : (char) 65535;
        } else if (name.equals(I1I1lI1II1.a(new byte[]{80, 22, 3, 1, 11, 80, 89, 68}))) {
            c = 1;
        }
        if (c == 0) {
            return from(I11llIIIII.createFromXmlInner(resources, xml, attributeSetAsAttributeSet, resources$Theme));
        }
        if (c == 1) {
            return from(I1l111l1II.createFromXmlInner(resources, xml, attributeSetAsAttributeSet, resources$Theme));
        }
        throw new XmlPullParserException(xml.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 23, 11, 17, 64, 71, 64, 86, 22, 68, 85, 81, 25, 87, 90, 15, 17, 14, 87, 72, 65, 80, 90, 95, 89, 69, 69, 16, 83, 80, 67}) + name);
    }
}
