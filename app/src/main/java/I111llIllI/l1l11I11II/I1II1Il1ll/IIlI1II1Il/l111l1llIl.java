package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import I1IlIlll1l.I1lIIlll1l.lIl1llI11l.lIll1Ill11.lllIlIl1ll;
import II1lIlIlll.l111ll1lll.l11I1lIIl1.IllIlll111.lI1IIIl1I1;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.TypedArray;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.Base64;
import android.util.Xml;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.NotSerializableException;
import java.security.cert.CRLException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public class l111l1llIl {
    private static final int DEFAULT_TIMEOUT_MILLIS = 500;
    public static final int FETCH_STRATEGY_ASYNC = 1;
    public static final int FETCH_STRATEGY_BLOCKING = 0;
    public static final int INFINITE_TIMEOUT_VALUE = -1;
    private static final int ITALIC = 1;
    private static final int NORMAL_WEIGHT = 400;

    public static I1I1Il1I1I parse(XmlPullParser xmlPullParser, Resources resources) throws XmlPullParserException, IOException {
        int next;
        if (IIIlIll111.l11I11I11l(I1I1lI1II1.a(new byte[]{116, 86, 27, 53, 49, 94, 102, 72, 106, 2, 89, 2, 67, 76, 97, 119, 86, 34}), 3659)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{95, 86}));
        }
        do {
            next = xmlPullParser.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next == 2) {
            return readFamilies(xmlPullParser, resources);
        }
        throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{121, 11, 66, 22, 22, 84, 69, 68, 25, 16, 81, 87, 21, 95, 91, 64, 12, 5}));
    }

    private static I1I1Il1I1I readFamilies(XmlPullParser xmlPullParser, Resources resources) throws XmlPullParserException, CertificateParsingException, IOException, CRLException {
        xmlPullParser.require(2, null, I1I1lI1II1.a(new byte[]{81, 11, 12, 17, 79, 83, 86, 93, 80, 8, 73}));
        if (xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{81, 11, 12, 17, 79, 83, 86, 93, 80, 8, 73}))) {
            I1I1Il1I1I family = readFamily(xmlPullParser, resources);
            if (I1IllIll1l.llll111lI1(I1I1lI1II1.a(new byte[]{99, 85, 32, 44, 36, 6}), 275419170L)) {
                throw new CRLException(I1I1lI1II1.a(new byte[]{95, 92, 46, 38, 36, 82, 92, 0, 110, 20, 106, 114, 95, 108, 85, 98, 85, 51, 58, 89, 99, 34, 126, 126, 112, 122}));
            }
            return family;
        }
        skip(xmlPullParser);
        return null;
    }

    private static I1I1Il1I1I readFamily(XmlPullParser xmlPullParser, Resources resources) throws XmlPullParserException, CertificateParsingException, IOException {
        if (IllllI11Il.IlIIlIllI1(I1I1lI1II1.a(new byte[]{80, 80, 3, 21, 7, 96, 4, 86, 9, 46, 115, 85}), 765199864L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{92, 83, 0, 29, 14, 86, 91, 104, 67, 82, 125, 118, 118, 122, 77, 7, 35, 40, 40, 66, 84, 59, 74, 119, 65, 126, 101, 50, 40, 112, 115}));
        }
        TypedArray typedArrayObtainAttributes = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), lllIlIl1ll.FontFamily);
        String string = typedArrayObtainAttributes.getString(lllIlIl1ll.FontFamily_fontProviderAuthority);
        String string2 = typedArrayObtainAttributes.getString(lllIlIl1ll.FontFamily_fontProviderPackage);
        String string3 = typedArrayObtainAttributes.getString(lllIlIl1ll.FontFamily_fontProviderQuery);
        int resourceId = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.FontFamily_fontProviderCerts, 0);
        int integer = typedArrayObtainAttributes.getInteger(lllIlIl1ll.FontFamily_fontProviderFetchStrategy, 1);
        int integer2 = typedArrayObtainAttributes.getInteger(lllIlIl1ll.FontFamily_fontProviderFetchTimeout, 500);
        String string4 = typedArrayObtainAttributes.getString(lllIlIl1ll.FontFamily_fontProviderSystemFontFamily);
        typedArrayObtainAttributes.recycle();
        if (string != null && string2 != null && string3 != null) {
            while (xmlPullParser.next() != 3) {
                skip(xmlPullParser);
            }
            lI11lIIII1 li11liiii1 = new lI11lIIII1(new lI1IIIl1I1(string, string2, string3, readCerts(resources, resourceId)), integer, integer2, string4);
            if (Il1llIl111.Ill1lIIlIl(188812346L)) {
                throw new InvalidClassException(I1I1lI1II1.a(new byte[]{68, 83, 16, 49, 26, 116, 83, 93, 80, 45, 84, 6, 82, 86, 92, 64, 86, 37, 27, 113, 101, 44, Byte.MAX_VALUE, 122, 117, 83, 118, 9}));
            }
            return li11liiii1;
        }
        ArrayList arrayList = new ArrayList();
        while (xmlPullParser.next() != 3) {
            if (xmlPullParser.getEventType() == 2) {
                if (xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{81, 11, 12, 17}))) {
                    arrayList.add(readFont(xmlPullParser, resources));
                } else {
                    skip(xmlPullParser);
                }
            }
        }
        if (arrayList.isEmpty()) {
            return null;
        }
        return new II1Il1llIl((l1llll11Il[]) arrayList.toArray(new l1llll11Il[0]));
    }

    private static int getType(TypedArray typedArray, int i) throws CertPathValidatorException {
        if (l11Il111ll.IlII1Illll(296873756L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{89, 62, 24, 8, 80, 80, 124, 72, Byte.MAX_VALUE, 8, 0, 102, 0, 99, 123, 91, 20, 21, 14, 113, 99}));
        }
        return I1lll11llI.getType(typedArray, i);
    }

    public static List<List<byte[]>> readCerts(Resources resources, int i) throws Resources$NotFoundException {
        if (i == 0) {
            List<List<byte[]>> listEmptyList = Collections.emptyList();
            if (lI1lI11Ill.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{112, 15, 52, 17, 86, 124, 91, 87, 92, 51, 65, 95, 12, 110, 89, 7}))) {
                throw new InstantiationError(I1I1lI1II1.a(new byte[]{110, 52, 22, 8, 37, 118, 91, 126, 13, 46, 115, 69, 7, 104, 109, 67}));
            }
            return listEmptyList;
        }
        TypedArray typedArrayObtainTypedArray = resources.obtainTypedArray(i);
        try {
            if (typedArrayObtainTypedArray.length() == 0) {
                List<List<byte[]>> listEmptyList2 = Collections.emptyList();
                typedArrayObtainTypedArray.recycle();
                if (llIlII1IlI.ll1I1lII11(I1I1lI1II1.a(new byte[]{1, 92, 26, 4}))) {
                    throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{82, 18, 35, 6, 36, 71, 103, 101, 93, 80, 81, 74, 5, 93, 85, 2, 59, 6, 0, 74, 74, 59, 84, 115, 0, 76, 120, 84, 29}));
                }
                return listEmptyList2;
            }
            ArrayList arrayList = new ArrayList();
            if (getType(typedArrayObtainTypedArray, 0) == 1) {
                for (int i2 = 0; i2 < typedArrayObtainTypedArray.length(); i2++) {
                    int resourceId = typedArrayObtainTypedArray.getResourceId(i2, 0);
                    if (resourceId != 0) {
                        arrayList.add(toByteArrayList(resources.getStringArray(resourceId)));
                    }
                }
            } else {
                arrayList.add(toByteArrayList(resources.getStringArray(i)));
            }
            return arrayList;
        } finally {
            typedArrayObtainTypedArray.recycle();
        }
    }

    private static List<byte[]> toByteArrayList(String[] strArr) {
        ArrayList arrayList = new ArrayList();
        for (String str : strArr) {
            arrayList.add(Base64.decode(str, 0));
        }
        return arrayList;
    }

    private static l1llll11Il readFont(XmlPullParser xmlPullParser, Resources resources) throws XmlPullParserException, IOException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        TypedArray typedArrayObtainAttributes = resources.obtainAttributes(Xml.asAttributeSet(xmlPullParser), lllIlIl1ll.FontFamilyFont);
        if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.FontFamilyFont_fontWeight)) {
            i = lllIlIl1ll.FontFamilyFont_fontWeight;
        } else {
            i = lllIlIl1ll.FontFamilyFont_android_fontWeight;
        }
        int i6 = typedArrayObtainAttributes.getInt(i, 400);
        if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.FontFamilyFont_fontStyle)) {
            i2 = lllIlIl1ll.FontFamilyFont_fontStyle;
        } else {
            i2 = lllIlIl1ll.FontFamilyFont_android_fontStyle;
        }
        boolean z = 1 == typedArrayObtainAttributes.getInt(i2, 0);
        if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.FontFamilyFont_ttcIndex)) {
            i3 = lllIlIl1ll.FontFamilyFont_ttcIndex;
        } else {
            i3 = lllIlIl1ll.FontFamilyFont_android_ttcIndex;
        }
        if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.FontFamilyFont_fontVariationSettings)) {
            i4 = lllIlIl1ll.FontFamilyFont_fontVariationSettings;
        } else {
            i4 = lllIlIl1ll.FontFamilyFont_android_fontVariationSettings;
        }
        String string = typedArrayObtainAttributes.getString(i4);
        int i7 = typedArrayObtainAttributes.getInt(i3, 0);
        if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.FontFamilyFont_font)) {
            i5 = lllIlIl1ll.FontFamilyFont_font;
        } else {
            i5 = lllIlIl1ll.FontFamilyFont_android_font;
        }
        int resourceId = typedArrayObtainAttributes.getResourceId(i5, 0);
        String string2 = typedArrayObtainAttributes.getString(i5);
        typedArrayObtainAttributes.recycle();
        while (xmlPullParser.next() != 3) {
            skip(xmlPullParser);
        }
        l1llll11Il l1llll11il = new l1llll11Il(string2, i6, z, string, i7, resourceId);
        if (IIIlIll111.IlII1Illll(1085846037L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{112, 11, 54, 87, 22, 95, 124, 90, 72, 48, 106, 82, 6, 64, 69, 0, 17, 82, 9}));
        }
        return l1llll11il;
    }

    private static void skip(XmlPullParser xmlPullParser) throws XmlPullParserException, IOException {
        int i = 1;
        while (i > 0) {
            int next = xmlPullParser.next();
            if (next == 2) {
                i++;
            } else if (next == 3) {
                i--;
            }
        }
        if (Il1l11I11I.ll1I1lII11(I1I1lI1II1.a(new byte[]{121, 40, 11, 48, 59, 77, 2, 4, 77, 16, 100, 104, Byte.MAX_VALUE}), 7602)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{88, 38, 51, 36, 12, 121, 86, 98}));
        }
    }

    private l111l1llIl() {
    }
}
