package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lIIlI111II;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.media.content.lIIllIlIl1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.FileNotFoundException;
import java.security.NoSuchAlgorithmException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class Il11IlIllI {
    final Resources mResources;
    final Resources$Theme mTheme;

    Il11IlIllI(Resources resources, Resources$Theme resources$Theme) {
        this.mResources = resources;
        this.mTheme = resources$Theme;
    }

    public boolean equals(Object obj) throws NoSuchAlgorithmException, FileNotFoundException {
        if (lIIllIlIl1.IlII1Illll(254549244L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{96, 53, 13, 18, 46, 118, 14, 121, 126, 3, 90, 106, 1, 114, 88, 84, 38, 46, 33, 103, 85, 4, Byte.MAX_VALUE, 103, 112, 6, 118, 81, 0, 11, 4}));
        }
        if (this == obj) {
            if (llIlII1IlI.I1lIllll1l(1329976448L)) {
                throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{121, 39, 86, 16, 23, 88, 14, 101, 88, 3, 6, 81, 90, 106, 12}));
            }
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            if (I1lllI11II.llll111lI1(I1I1lI1II1.a(new byte[]{77, 39, 1, 21, 5}), 894096126L)) {
                throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{68, 22, 59, 4, 50, 89, 111, 87, 97}));
            }
            return false;
        }
        Il11IlIllI il11IlIllI = (Il11IlIllI) obj;
        boolean z = this.mResources.equals(il11IlIllI.mResources) && IIIl11Illl.equals(this.mTheme, il11IlIllI.mTheme);
        if (lIIlI111II.IIl1l1llII(257)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{117, 62, 48, 1, 80, 82, 111, 96, 120, 44, 117, 89, 116, 12, 86, 13, 32, 49, 1, 96, 67, 51, 103, 5, 4, 96}));
        }
        return z;
    }

    public int hashCode() {
        return IIIl11Illl.hash(this.mResources, this.mTheme);
    }
}
