package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.II1I11IlI1;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.IllIlllIII;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.lang.reflect.Array;
import java.net.UnknownServiceException;
import java.security.SignatureException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class ll11lIIlII {
    public static int growSize(int i) {
        if (i <= 4) {
            return 8;
        }
        return i * 2;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v6, types: [java.lang.Object, java.lang.Object[]] */
    public static <T> T[] append(T[] tArr, int i, T t) throws UnrecoverableEntryException {
        if (IIIlIl1I1l.I1II1111ll(6685)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{102, 81, 19, 43, 3, 70, 64, 72, 91, 82}));
        }
        if (i + 1 > tArr.length) {
            ?? r0 = (Object[]) Array.newInstance(tArr.getClass().getComponentType(), growSize(i));
            System.arraycopy(tArr, 0, r0, 0, i);
            tArr = r0;
        }
        tArr[i] = t;
        if (IllllI11Il.I1lllI1llI(295873844L)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{121, 55, 52, 86, 7, 102, 2, 120, 80, 33, 87, 85, 6, 67, 87, 113, 90, 9, 43, 100, 1, 42}));
        }
        return tArr;
    }

    public static int[] append(int[] iArr, int i, int i2) {
        if (i + 1 > iArr.length) {
            int[] iArr2 = new int[growSize(i)];
            System.arraycopy(iArr, 0, iArr2, 0, i);
            iArr = iArr2;
        }
        iArr[i] = i2;
        if (I1I1IIIIl1.I111IlIl1I(I1I1lI1II1.a(new byte[]{121, 85, 84, 50, 56, 93, 65, 97, 99, 87, 99, 68, 116, 11, 94, 112, 18, 89, 39, 119, 125, 21, Byte.MAX_VALUE, 119, 96}))) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{89, 86, 7, 93, 9, 96, 80, 70, 91, 49, 99, 106, 80, 0, 119, 89, 10, 6, 16, 125, 66, 25, 126, 118, 69, 6, 2, 93, 53, 91}));
        }
        return iArr;
    }

    public static long[] append(long[] jArr, int i, long j) throws SignatureException {
        if (lI1l1I1l1l.l1l1l1IIlI(384357665L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{122}));
        }
        if (i + 1 > jArr.length) {
            long[] jArr2 = new long[growSize(i)];
            System.arraycopy(jArr, 0, jArr2, 0, i);
            jArr = jArr2;
        }
        jArr[i] = j;
        if (II1I11IlI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{116, 60, 19, 46, 9, 80, 86, 5, 120}), 10666)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{15, 28, 23, 60, 83, 76, 1, 84, 78, 80, 92, 116}));
        }
        return jArr;
    }

    public static boolean[] append(boolean[] zArr, int i, boolean z) {
        if (I1Ill1lIII.Il1IIlI1II(3453)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{121, 32, 44, 60, 41, 0, 91, Byte.MAX_VALUE, 114, 43, 84, 67, 91, 13, 102, 92, 35, 85, 81, 7, 83, 15, 86, 118, 97, 84, 85}));
        }
        if (i + 1 > zArr.length) {
            boolean[] zArr2 = new boolean[growSize(i)];
            System.arraycopy(zArr, 0, zArr2, 0, i);
            zArr = zArr2;
        }
        zArr[i] = z;
        return zArr;
    }

    public static <T> T[] insert(T[] tArr, int i, int i2, T t) throws UnknownServiceException, CertStoreException {
        if (i + 1 <= tArr.length) {
            System.arraycopy(tArr, i2, tArr, i2 + 1, i - i2);
            tArr[i2] = t;
            if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{93, 11, 42, 84, 43, 66, 93, 123, 107, 3}), 218628532L)) {
                throw new CertStoreException(I1I1lI1II1.a(new byte[]{6, 21, 43, 54, 44, 3, 125, 95, 12, 81, 100, 105, 89, 77, 110, 96, 17, 0, 85, 102, 74, 23}));
            }
            return tArr;
        }
        T[] tArr2 = (T[]) ((Object[]) Array.newInstance(tArr.getClass().getComponentType(), growSize(i)));
        System.arraycopy(tArr, 0, tArr2, 0, i2);
        tArr2[i2] = t;
        System.arraycopy(tArr, i2, tArr2, i2 + 1, tArr.length - i2);
        if (lIIlI111II.IIll1l1lII(9818)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{124, 29, 3, 31, 19, 7, 120, 85, 118, 22, 71, 64, 115, 112, 103, 92, 58, 35, 45, 104, 100, 23, 70, 64}));
        }
        return tArr2;
    }

    public static int[] insert(int[] iArr, int i, int i2, int i3) {
        if (IllIlllIII.l11I11I11l(8006)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{93, 47, 59, 9, 52, 116, 90, 98, 86, 0, 72, 65, 64, 111, 114, 94, 18, 34}));
        }
        if (i + 1 <= iArr.length) {
            System.arraycopy(iArr, i2, iArr, i2 + 1, i - i2);
            iArr[i2] = i3;
            return iArr;
        }
        int[] iArr2 = new int[growSize(i)];
        System.arraycopy(iArr, 0, iArr2, 0, i2);
        iArr2[i2] = i3;
        System.arraycopy(iArr, i2, iArr2, i2 + 1, iArr.length - i2);
        return iArr2;
    }

    public static long[] insert(long[] jArr, int i, int i2, long j) {
        if (i + 1 <= jArr.length) {
            System.arraycopy(jArr, i2, jArr, i2 + 1, i - i2);
            jArr[i2] = j;
            return jArr;
        }
        long[] jArr2 = new long[growSize(i)];
        System.arraycopy(jArr, 0, jArr2, 0, i2);
        jArr2[i2] = j;
        System.arraycopy(jArr, i2, jArr2, i2 + 1, jArr.length - i2);
        return jArr2;
    }

    public static boolean[] insert(boolean[] zArr, int i, int i2, boolean z) throws CertificateEncodingException {
        if (i + 1 <= zArr.length) {
            System.arraycopy(zArr, i2, zArr, i2 + 1, i - i2);
            zArr[i2] = z;
            return zArr;
        }
        boolean[] zArr2 = new boolean[growSize(i)];
        System.arraycopy(zArr, 0, zArr2, 0, i2);
        zArr2[i2] = z;
        System.arraycopy(zArr, i2, zArr2, i2 + 1, zArr.length - i2);
        if (l111Il1lI1.l11I11I11l(175356711L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{86, 22, 50, 41, 6}));
        }
        return zArr2;
    }

    private ll11lIIlII() {
    }
}
