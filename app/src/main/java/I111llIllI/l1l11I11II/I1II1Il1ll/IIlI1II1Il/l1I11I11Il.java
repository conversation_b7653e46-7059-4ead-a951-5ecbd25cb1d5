package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.ColorStateList;
import android.content.res.Configuration;
import android.content.res.Resources$Theme;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class l1I11I11Il {
    final Configuration mConfiguration;
    final int mThemeHash;
    final ColorStateList mValue;

    l1I11I11Il(ColorStateList colorStateList, Configuration configuration, Resources$Theme resources$Theme) {
        this.mValue = colorStateList;
        this.mConfiguration = configuration;
        this.mThemeHash = resources$Theme == null ? 0 : resources$Theme.hashCode();
    }
}
