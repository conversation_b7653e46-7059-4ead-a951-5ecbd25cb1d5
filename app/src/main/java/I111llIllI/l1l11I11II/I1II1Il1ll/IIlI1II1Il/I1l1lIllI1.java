package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.graphics.Color;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I1Ill1lIII;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.CharConversionException;
import java.io.StreamCorruptedException;
import java.net.BindException;
import java.net.MalformedURLException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIll1l1l11;
import lIlIllIlIl.llI111llII.Il1IIIlI1l.I11l1I11ll.IIll1l1lII;

/* loaded from: classes.dex */
final class I1l1lIllI1 {
    static final float[][] XYZ_TO_CAM16RGB = {new float[]{0.401288f, 0.650173f, -0.051461f}, new float[]{-0.250268f, 1.204414f, 0.045854f}, new float[]{-0.002079f, 0.048952f, 0.953127f}};
    static final float[][] CAM16RGB_TO_XYZ = {new float[]{1.8620678f, -1.0112547f, 0.14918678f}, new float[]{0.38752654f, 0.62144744f, -0.00897398f}, new float[]{-0.0158415f, -0.03412294f, 1.0499644f}};
    static final float[] WHITE_POINT_D65 = {95.047f, 100.0f, 108.883f};
    static final float[][] SRGB_TO_XYZ = {new float[]{0.41233894f, 0.35762063f, 0.18051042f}, new float[]{0.2126f, 0.7152f, 0.0722f}, new float[]{0.01932141f, 0.11916382f, 0.9503448f}};

    private I1l1lIllI1() {
    }

    static int intFromLStar(float f) throws MalformedURLException, CertPathValidatorException {
        if (I1Ill1lIII.l11I11I11l(181235701L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{102, 92, 83, 28, 46, 95, 3, 122, 114, 7, 117, 86, 112, 90, 93, 114, 53, 6, 47, 93, 85, 4, 85, 7, 86, 113, 66}));
        }
        if (f < 1.0f) {
            if (I1Ill1lIII.I1lIllll1l(1386)) {
                throw new MalformedURLException(I1I1lI1II1.a(new byte[]{85, 17, 59, 1, 44, 94, 110, 93, 87, 50, 103}));
            }
            return lIll1l1l11.MEASURED_STATE_MASK;
        }
        if (f > 99.0f) {
            return -1;
        }
        float f2 = (f + 16.0f) / 116.0f;
        float f3 = (f > 8.0f ? 1 : (f == 8.0f ? 0 : -1)) > 0 ? f2 * f2 * f2 : f / 903.2963f;
        float f4 = f2 * f2 * f2;
        boolean z = f4 > 0.008856452f;
        float f5 = z ? f4 : ((f2 * 116.0f) - 16.0f) / 903.2963f;
        if (!z) {
            f4 = ((f2 * 116.0f) - 16.0f) / 903.2963f;
        }
        float[] fArr = WHITE_POINT_D65;
        int iXYZToColor = IIll1l1lII.XYZToColor(f5 * fArr[0], f3 * fArr[1], f4 * fArr[2]);
        if (ll1l11I1II.Il1IIlI1II(I1I1lI1II1.a(new byte[]{110, 61, 32, 9, 46, 88, 89, 0, 108, 44, 0, 89, 115, 77, 99, 67, 13, 22, 37, 2}), 5013)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{100, 61, 37, 7, 45, 6, 111, 125, 109, 7, 7, 116, 91, 80, 121, 96, 8, 22, 82, 80, 116, 15, 82, 111, 88, 90}));
        }
        return iXYZToColor;
    }

    static float lerp(float f, float f2, float f3) throws InvalidAlgorithmParameterException {
        float f4 = f + ((f2 - f) * f3);
        if (II1lllllI1.I1II1111ll(398299453L)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{109, 43, 23, 82, 42, 6}));
        }
        return f4;
    }

    static float lStarFromInt(int i) {
        return lStarFromY(yFromInt(i));
    }

    static float lStarFromY(float f) {
        float f2 = f / 100.0f;
        return f2 <= 0.008856452f ? f2 * 903.2963f : (((float) Math.cbrt(f2)) * 116.0f) - 16.0f;
    }

    static float yFromInt(int i) throws StreamCorruptedException, CertificateParsingException {
        float fLinearized = linearized(Color.red(i));
        float fLinearized2 = linearized(Color.green(i));
        float fLinearized3 = linearized(Color.blue(i));
        float[] fArr = SRGB_TO_XYZ[1];
        return (fLinearized * fArr[0]) + (fLinearized2 * fArr[1]) + (fLinearized3 * fArr[2]);
    }

    static void xyzFromInt(int i, float[] fArr) {
        if (I111I11Ill.IIll1I11lI(I1I1lI1II1.a(new byte[]{101, 55, 12}))) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{97, 0, 13, 85, 83, 81, 4, 85, 111, 34, 118, 105, 67}));
        }
        float fLinearized = linearized(Color.red(i));
        float fLinearized2 = linearized(Color.green(i));
        float fLinearized3 = linearized(Color.blue(i));
        float[][] fArr2 = SRGB_TO_XYZ;
        float[] fArr3 = fArr2[0];
        fArr[0] = (fArr3[0] * fLinearized) + (fArr3[1] * fLinearized2) + (fArr3[2] * fLinearized3);
        float[] fArr4 = fArr2[1];
        fArr[1] = (fArr4[0] * fLinearized) + (fArr4[1] * fLinearized2) + (fArr4[2] * fLinearized3);
        float[] fArr5 = fArr2[2];
        fArr[2] = (fLinearized * fArr5[0]) + (fLinearized2 * fArr5[1]) + (fLinearized3 * fArr5[2]);
    }

    static float yFromLStar(float f) throws CharConversionException, BindException {
        if (lII1llllI1.I1II1111ll(174902826L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{70, 32, 3, 63, 36, 77, 83, 91, 111, 9, 68, 1, 94, 78, 113, 90, 51, 12, 0, 100, 73, 80, 91, 100, 121}));
        }
        if (f <= 8.0f) {
            return (f / 903.2963f) * 100.0f;
        }
        float fPow = ((float) Math.pow((f + 16.0d) / 116.0d, 3.0d)) * 100.0f;
        if (l1lI1I1l11.IlII1Illll(187282687L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{95, 47, 41, 7, 14, 69, 70, 101, 105, 30, 126, 124, 87, 96, 78, 67, 23, 57, 80, 97, 70, 20, 91, 124, 107, 97, 1, 35, 19, 117, 3}));
        }
        return fPow;
    }

    static float linearized(int i) throws StreamCorruptedException, CertificateParsingException {
        if (IIlI1ll1ll.I1lllI1llI(IronSourceConstants.RV_CHECK_READY_TRUE)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{65, 82, 44, 43, 8, 98, 70, 69, Byte.MAX_VALUE, 83, 83, 120, 102, 113, 112, 79, 1, 87, 35, Byte.MAX_VALUE, 102, 25, 75}));
        }
        float f = i / 255.0f;
        if (f <= 0.04045f) {
            float f2 = (f / 12.92f) * 100.0f;
            if (lII1llllI1.Il1IIlI1II(691148264L)) {
                throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{90, 14, 44, 49, 50, 88}));
            }
            return f2;
        }
        float fPow = ((float) Math.pow((f + 0.055f) / 1.055f, 2.4000000953674316d)) * 100.0f;
        if (lIlIII1I1l.IIll1I11lI(1262140006L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{110, 82, 11, 41, 56, 97, 96, 71}));
        }
        return fPow;
    }
}
