package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.graphics.Typeface;
import android.os.Handler;
import android.os.Looper;
import androidx.core.location.I111I11Ill;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.security.UnrecoverableKeyException;
import java.util.concurrent.RejectedExecutionException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class llI1l11lII {
    public abstract void onFontRetrievalFailed(int i);

    /* renamed from: onFontRetrieved, reason: merged with bridge method [inline-methods] */
    public abstract void lambda$callbackSuccessAsync$0$I111llIllI-l1l11I11II-I1II1Il1ll-IIlI1II1Il-llI1l11lII(Typeface typeface);

    public final void callbackSuccessAsync(final Typeface typeface, Handler handler) {
        getHandler(handler).post(new Runnable() { // from class: I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il.llI1l11lII$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$callbackSuccessAsync$0$I111llIllI-l1l11I11II-I1II1Il1ll-IIlI1II1Il-llI1l11lII(typeface);
            }
        });
    }

    public final void callbackFailAsync(final int i, Handler handler) throws UnrecoverableKeyException {
        getHandler(handler).post(new Runnable() { // from class: I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il.llI1l11lII$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                this.f$0.lambda$callbackFailAsync$1$I111llIllI-l1l11I11II-I1II1Il1ll-IIlI1II1Il-llI1l11lII(i);
            }
        });
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{86, 80, 3, 22, 7, 96, 14, 97, 91}), 4700)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{91, 47, 82, 38, 9, Byte.MAX_VALUE, 100, 92, 113, 17, 125, Byte.MAX_VALUE, 111, 80, 100, 82, 39, 48, 5, 83, 71, 54, 6, 123}));
        }
    }

    /* synthetic */ void lambda$callbackFailAsync$1$I111llIllI-l1l11I11II-I1II1Il1ll-IIlI1II1Il-llI1l11lII(int i) {
        if (IIll1llI1l.Il1IIlI1II(4146)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{6, 9, 39, 54, 1, 94, 78, 102, 80, 11, 88, 113, 82, 93}));
        }
        onFontRetrievalFailed(i);
    }

    public static Handler getHandler(Handler handler) {
        if (handler == null) {
            handler = new Handler(Looper.getMainLooper());
        }
        if (I111I11Ill.l1l1l1IIlI(277595021L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{77, 86, 26, 0, 17, 92, 14, 106, 1, 84, 74, 81, 90, 123, 108, 102, 81, 52, 27}));
        }
        return handler;
    }
}
