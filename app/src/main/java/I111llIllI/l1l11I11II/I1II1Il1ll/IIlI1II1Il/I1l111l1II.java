package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import I1IlIlll1l.I1lIIlll1l.lIl1llI11l.lIll1Ill11.lllIlIl1ll;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.content.res.TypedArray;
import android.graphics.LinearGradient;
import android.graphics.RadialGradient;
import android.graphics.Shader;
import android.graphics.Shader$TileMode;
import android.graphics.SweepGradient;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Xml;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I111I11Ill;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.IOException;
import java.net.BindException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertPathBuilderException;
import java.util.ArrayList;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
final class I1l111l1II {
    private static final int TILE_MODE_CLAMP = 0;
    private static final int TILE_MODE_MIRROR = 2;
    private static final int TILE_MODE_REPEAT = 1;

    private I1l111l1II() {
    }

    static Shader createFromXml(Resources resources, XmlPullParser xmlPullParser, Resources$Theme resources$Theme) throws XmlPullParserException, CertPathBuilderException, NoSuchAlgorithmException, IOException, BrokenBarrierException {
        int next;
        AttributeSet attributeSetAsAttributeSet = Xml.asAttributeSet(xmlPullParser);
        do {
            next = xmlPullParser.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next != 2) {
            throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{121, 11, 66, 22, 22, 84, 69, 68, 25, 16, 81, 87, 21, 95, 91, 64, 12, 5}));
        }
        Shader shaderCreateFromXmlInner = createFromXmlInner(resources, xmlPullParser, attributeSetAsAttributeSet, resources$Theme);
        if (I111I11Ill.lll1111l11(I1I1lI1II1.a(new byte[]{115, 51, 22, 50, 52, 87, 96, 67, 125, 40, 86, 4, 101, 13, 122, 120, 49, 52, 80, 0}), 5883)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{64, 60, 90, 19, 85, 0, 84, 99, 86, 18, 8, 97, 112, 83, 98, 99, 56, 47, 37, 103, 125}));
        }
        return shaderCreateFromXmlInner;
    }

    static Shader createFromXmlInner(Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, NoSuchAlgorithmException, BrokenBarrierException, IOException {
        String name = xmlPullParser.getName();
        if (!name.equals(I1I1lI1II1.a(new byte[]{80, 22, 3, 1, 11, 80, 89, 68}))) {
            throw new XmlPullParserException(xmlPullParser.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 11, 11, 20, 84, 91, 89, 93, 68, 87, 66, 84, 93, 93, 80, 12, 21, 66, 81, 95, 13, 92, 71, 19, 66, 86, 2, 68}) + name);
        }
        TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.GradientColor);
        float namedFloat = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{68, 16, 3, 23, 22, 109}), lllIlIl1ll.GradientColor_android_startX, 0.0f);
        float namedFloat2 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{68, 16, 3, 23, 22, 108}), lllIlIl1ll.GradientColor_android_startY, 0.0f);
        float namedFloat3 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{82, 10, 6, 61}), lllIlIl1ll.GradientColor_android_endX, 0.0f);
        float namedFloat4 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{82, 10, 6, 60}), lllIlIl1ll.GradientColor_android_endY, 0.0f);
        float namedFloat5 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{84, 1, 12, 17, 7, 71, 111}), lllIlIl1ll.GradientColor_android_centerX, 0.0f);
        float namedFloat6 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{84, 1, 12, 17, 7, 71, 110}), lllIlIl1ll.GradientColor_android_centerY, 0.0f);
        int namedInt = ll111Illll.getNamedInt(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{67, 29, 18, 0}), lllIlIl1ll.GradientColor_android_type, 0);
        int namedColor = ll111Illll.getNamedColor(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{68, 16, 3, 23, 22, 118, 88, 92, 86, 22}), lllIlIl1ll.GradientColor_android_startColor, 0);
        boolean zHasAttribute = ll111Illll.hasAttribute(xmlPullParser, I1I1lI1II1.a(new byte[]{84, 1, 12, 17, 7, 71, 116, 95, 85, 11, 66}));
        int namedColor2 = ll111Illll.getNamedColor(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{84, 1, 12, 17, 7, 71, 116, 95, 85, 11, 66}), lllIlIl1ll.GradientColor_android_centerColor, 0);
        int namedColor3 = ll111Illll.getNamedColor(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{82, 10, 6, 38, 13, 89, 88, 66}), lllIlIl1ll.GradientColor_android_endColor, 0);
        int namedInt2 = ll111Illll.getNamedInt(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{67, 13, 14, 0, 47, 90, 83, 85}), lllIlIl1ll.GradientColor_android_tileMode, 0);
        float namedFloat7 = ll111Illll.getNamedFloat(typedArrayObtainAttributes, xmlPullParser, I1I1lI1II1.a(new byte[]{80, 22, 3, 1, 11, 80, 89, 68, 107, 5, 84, 89, 64, 74}), lllIlIl1ll.GradientColor_android_gradientRadius, 0.0f);
        typedArrayObtainAttributes.recycle();
        lII11llIIl lii11lliilCheckColors = checkColors(inflateChildElements(resources, xmlPullParser, attributeSet, resources$Theme), namedColor, namedColor3, zHasAttribute, namedColor2);
        if (namedInt == 1) {
            if (namedFloat7 <= 0.0f) {
                throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{11, 3, 16, 4, 6, 92, 82, 94, 77, 90, 16, 68, 84, 94, 20, 71, 7, 16, 23, 91, 66, 4, 64, 21, 20, 81, 69, 4, 0, 91, 82, 13, 67, 54, 3, 1, 11, 64, 68, 23, 25, 5, 68, 68, 71, 80, 86, 64, 22, 4, 66, 69, 89, 21, 91, 21, 65, 87, 83, 12, 5, 94, 23, 23, 78, 20, 7}));
            }
            return new RadialGradient(namedFloat5, namedFloat6, namedFloat7, lii11lliilCheckColors.mColors, lii11lliilCheckColors.mOffsets, parseTileMode(namedInt2));
        }
        if (namedInt == 2) {
            SweepGradient sweepGradient = new SweepGradient(namedFloat5, namedFloat6, lii11lliilCheckColors.mColors, lii11lliilCheckColors.mOffsets);
            if (I1111IIl11.I1lIllll1l(240836036L)) {
                throw new ClassFormatError(I1I1lI1II1.a(new byte[]{121, 44, 55, 40, 83, 7, 66, 99, 10, 85, 4, 85, 66, 118, 6, 80, 82, 89, 11, 102, 87, 56, 99}));
            }
            return sweepGradient;
        }
        LinearGradient linearGradient = new LinearGradient(namedFloat, namedFloat2, namedFloat3, namedFloat4, lii11lliilCheckColors.mColors, lii11lliilCheckColors.mOffsets, parseTileMode(namedInt2));
        if (Il1I1lllIl.I1lIllll1l(210558351L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{86, 81, 43, 12, 80, 80, 100, 73, 9, 82, 124, 89, 82, 82, 120, 95, 6, 3, 23, 121, 6, 5, 96, 122, 75, 3, 121, 50, 81, 89, 77}));
        }
        return linearGradient;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x009c, code lost:
    
        if (r4.size() <= 0) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x009e, code lost:
    
        r8 = new I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il.lII11llIIl(r4, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00a9, code lost:
    
        if (android.accounts.utils.lI1l1I1l1l.Ill1lIIlIl(3658) != false) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x00ab, code lost:
    
        return r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00bc, code lost:
    
        throw new java.io.EOFException(l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1.a(new byte[]{116, 86, 3, 29, 19, 97, 14, 84, 9, 9, 101, 1, 95, 72}));
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00bd, code lost:
    
        return null;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static lII11llIIl inflateChildElements(Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IOException {
        int depth;
        int depth2 = xmlPullParser.getDepth() + 1;
        ArrayList arrayList = new ArrayList(20);
        ArrayList arrayList2 = new ArrayList(20);
        while (true) {
            int next = xmlPullParser.next();
            if (next == 1 || ((depth = xmlPullParser.getDepth()) < depth2 && next == 3)) {
                break;
            }
            if (next == 2 && depth <= depth2 && xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{94, 16, 7, 8}))) {
                TypedArray typedArrayObtainAttributes = ll111Illll.obtainAttributes(resources, resources$Theme, attributeSet, lllIlIl1ll.GradientColorItem);
                boolean zHasValue = typedArrayObtainAttributes.hasValue(lllIlIl1ll.GradientColorItem_android_color);
                boolean zHasValue2 = typedArrayObtainAttributes.hasValue(lllIlIl1ll.GradientColorItem_android_offset);
                if (!zHasValue || !zHasValue2) {
                    break;
                }
                int color = typedArrayObtainAttributes.getColor(lllIlIl1ll.GradientColorItem_android_color, 0);
                float f = typedArrayObtainAttributes.getFloat(lllIlIl1ll.GradientColorItem_android_offset, 0.0f);
                typedArrayObtainAttributes.recycle();
                arrayList2.add(Integer.valueOf(color));
                arrayList.add(Float.valueOf(f));
            }
        }
        throw new XmlPullParserException(xmlPullParser.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 94, 12, 22, 80, 90, 14, 25, 16, 81, 87, 21, 75, 81, 68, 23, 8, 16, 87, 67, 65, 82, 21, 20, 85, 88, 9, 11, 64, 16, 67, 86, 16, 22, 23, 11, 87, 66, 68, 92, 68, 81, 94, 81, 25, 85, 21, 69, 14, 4, 84, 67, 4, 71, 18, 19, 87, 67, 17, 22, 91, 85, 22, 67, 1, 67}));
    }

    private static lII11llIIl checkColors(lII11llIIl lii11lliil, int i, int i2, boolean z, int i3) {
        if (lii11lliil != null) {
            return lii11lliil;
        }
        if (z) {
            return new lII11llIIl(i, i3, i2);
        }
        return new lII11llIIl(i, i2);
    }

    private static Shader$TileMode parseTileMode(int i) {
        if (lIIlI111II.I1Ill1lIII(629712863L)) {
            Log.v(I1I1lI1II1.a(new byte[]{121, 54, 10, 36, 21, 109, 109, 93, 120, 0, 120, 115, 97, 112, 108, 111, 35, 59, 1, 98, 9, 42, 103, 12, 105, 67, 103, 31, 44, 6, 102}), I1I1lI1II1.a(new byte[]{121, 1, 17, 51, 85, 91, 86, 88, 86, 5, 121, 105, 93, 118, 103, 3, 87, 37, 87, 68, 4, 25}));
            return null;
        }
        if (i == 1) {
            Shader$TileMode shader$TileMode = Shader$TileMode.REPEAT;
            if (l1lll111II.Il1IIlI1II(856188542L)) {
                throw new SecurityException(I1I1lI1II1.a(new byte[]{69, 51, 13, 45, 85, 79, 6, 104, 106, 13, 69, 94, 121, 67, 103, 80, 39, 0, 54, 122, 82, 9, 70}));
            }
            return shader$TileMode;
        }
        if (i == 2) {
            Shader$TileMode shader$TileMode2 = Shader$TileMode.MIRROR;
            if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{2, 61, 51, 21, 41, 13, 91, 8, 115, 8, 86, 102, 96, 92, 88, 116, 5, 8, 41, 74, 85, 20, 68, 95, 65, 93, 80}), 10397)) {
                throw new UnknownError(I1I1lI1II1.a(new byte[]{120, 17, 53, 21, 83, 116, 5, 71, 10, 80, 98, 87, 4, 124}));
            }
            return shader$TileMode2;
        }
        return Shader$TileMode.CLAMP;
    }
}
