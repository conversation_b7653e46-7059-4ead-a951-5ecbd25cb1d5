package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.graphics.drawable.Drawable;
import androidx.constraintlayout.widget.Il1lII1l1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II1ll1I1lI {
    private II1ll1I1lI() {
    }

    static Drawable getDrawableForDensity(Resources resources, int i, int i2) throws Resources$NotFoundException {
        Drawable drawableForDensity = resources.getDrawableForDensity(i, i2);
        if (Il1lII1l1l.I1lllI1llI(161836669L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{84, 3, 1, 22, 59, 93, 14, 66, 67, 48, 104, 85, 4, 120, 69, 4}));
        }
        return drawableForDensity;
    }
}
