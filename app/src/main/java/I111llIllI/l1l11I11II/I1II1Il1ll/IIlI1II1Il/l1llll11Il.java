package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.lIIIIII11I;
import android.media.content.IIl1l1IllI;
import android.media.content.lIIlI111II;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import java.net.PortUnreachableException;
import java.security.KeyException;
import java.security.cert.CertificateExpiredException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l1llll11Il {
    private final String mFileName;
    private final boolean mItalic;
    private final int mResourceId;
    private final int mTtcIndex;
    private final String mVariationSettings;
    private final int mWeight;

    public l1llll11Il(String str, int i, boolean z, String str2, int i2, int i3) {
        this.mFileName = str;
        this.mWeight = i;
        this.mItalic = z;
        this.mVariationSettings = str2;
        this.mTtcIndex = i2;
        this.mResourceId = i3;
    }

    public String getFileName() {
        return this.mFileName;
    }

    public int getWeight() throws KeyException {
        if (IIl1l1IllI.Ill1lIIlIl(5071)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{14, 16, 10, 86, 19, 120, 89, 4, 125, 14, 102, 96, 119, 99, 83, 123}));
        }
        return this.mWeight;
    }

    public boolean isItalic() throws CertificateExpiredException {
        boolean z = this.mItalic;
        if (Il1lII1l1l.IlII1Illll(167)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{64, 16, 10, 39, 22, 91, 109, 96, 12, 61, 101, 122, 120, 79, 122, 66, 6, 81, 59, 119, 124, 12, 64}));
        }
        return z;
    }

    public String getVariationSettings() throws PortUnreachableException {
        if (lIIlI111II.l1llI1llII(1598)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{102, 29, 84, 36, 8, 70, 2, 81, 82, 21, 70, 84, 111, 111, 89, 68, 43, 25, 20, 72, 5, 55}));
        }
        return this.mVariationSettings;
    }

    public int getTtcIndex() {
        int i = this.mTtcIndex;
        if (!lIIIIII11I.l1ll11I11l(I1I1lI1II1.a(new byte[]{122, 82, 9, 83, 83}), 202454354L)) {
            return i;
        }
        Log.d(I1I1lI1II1.a(new byte[]{110, 6, 46, 47, 36, 0, 109, 113, 14, 80, 68, 97, 97, 120, 102}), I1I1lI1II1.a(new byte[]{1, 50, 32, 18, 83, 82, 113, 105, 117, 85, 125, 85}));
        return 0;
    }

    public int getResourceId() {
        return this.mResourceId;
    }
}
