package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lI1l1I1l1l;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import java.io.IOException;
import java.io.NotSerializableException;
import java.net.BindException;
import java.security.AccessControlException;
import java.security.KeyStoreException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateParsingException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIllIlIl.llI111llII.Il1IIIlI1l.I11l1I11ll.IIll1l1lII;

/* loaded from: classes.dex */
public class l1l1II1111 {
    private static final float CHROMA_SEARCH_ENDPOINT = 0.4f;
    private static final float DE_MAX = 1.0f;
    private static final float DL_MAX = 0.2f;
    private static final float LIGHTNESS_SEARCH_ENDPOINT = 0.01f;
    private final float mAstar;
    private final float mBstar;
    private final float mChroma;
    private final float mHue;
    private final float mJ;
    private final float mJstar;
    private final float mM;
    private final float mQ;
    private final float mS;

    float getHue() throws BrokenBarrierException {
        float f = this.mHue;
        if (I1IllIll1l.l1Il11I1Il(I1I1lI1II1.a(new byte[]{99, 22, 36, 22, 32, 86, 112, 104, 74, 14, 100, 69, 3, 9, 124, 101, 40, 18, 82, 6, 114, 85, 102, 81, 125, 70, 71, 40, 10, 123, 116, 58}), 1132752345L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{99, 47, 83, 19, 24, 87, 97, 5, 74, 14, 115, 92}));
        }
        return f;
    }

    float getChroma() {
        return this.mChroma;
    }

    float getJ() throws IllegalAccessException, CertificateParsingException {
        if (lIIlI111II.l11llI1lI1(433160475L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{95, 60, 15, 22, 19, 121, 71, 89, 125, 41, 120, 99, 96, 75, 70, 88, 4}));
        }
        float f = this.mJ;
        if (IIlIIlIII1.Ill1lIIlIl(6494)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{109, 87, 91, 44, 6, 81, 83, 68, 64, 48, 83, 66, 82, 82, 97, 79, 38}));
        }
        return f;
    }

    float getQ() throws NotSerializableException {
        float f = this.mQ;
        if (I1Ill1lIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{118, 50, 24, 54, 90, 82, 14, 3, 111, 35, 72, 115, 91, 97, 102, 112, 91, 54}), 10847)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{69, 41, 82, 34, 87, 115, 7, 7, 65, 43, 84, 91, 120, 116, 66, 116, 5, 49, 45, 70, 106, 18, 121, 119}));
        }
        return f;
    }

    float getM() {
        if (lII1llllI1.I11II1I1I1(I1I1lI1II1.a(new byte[]{123, 51, 12, 0, 10, 102, 65, 5, 74, 47, 9, 2, 121, 111, 99, 89, 16, 89, 24, 1, 95, 19, 118, 95, Byte.MAX_VALUE}))) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{0, 22, 80, 81, 55, 126}));
        }
        return this.mM;
    }

    float getS() {
        float f = this.mS;
        if (IIlII1IIIl.IllIlI1l1I(I1I1lI1II1.a(new byte[]{103, 43, 47, 63, 80, 119, 67, 81, 109, 60, 102, 0, 111, 113}), 162240178L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{89, 86, 8, 44, 23, 0, 111, 73, 81, 19, 84, 113, 66, 99, 119, 71, 51, 38, 3, 66, 65, 20}));
        }
        return f;
    }

    float getJStar() {
        if (lIlIII1I1l.II1111I11I(I1I1lI1II1.a(new byte[]{111, 19, 84, 85, 7, 96, 66, 74, 95, 14, 96, 118, 67, 10, 93, 113, 87, 41, 45, 89, 118, 46, 68, 88}), I1I1lI1II1.a(new byte[]{110, 22, 7, 47, 40, 5, 98, 121}))) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{91, 9, 11, 41, 17, 126, 124, 66, 109, 54, 90, 68, 103, 110, 97, 12, 40, 32, 43, 11, 105, 53, 99, 13, 94, 3, 3, 6}));
        }
        return this.mJstar;
    }

    float getAStar() throws CertificateException {
        if (l1lI1I1l11.IllIlI1l1I(I1I1lI1II1.a(new byte[]{14}), 340088828L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{103, 32, 90, 40, 47, 115, 102, 74, 0, 11, 69}));
        }
        float f = this.mAstar;
        if (lI1l1I1l1l.l11I11I11l(2213)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{122, 10}));
        }
        return f;
    }

    float getBStar() {
        if (I1Ill1lIII.I111IlIl1I(595962987L)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{85, 7, 19, 87, 82, 115, 98, 90, 91, 20, 117, 87, 92, Byte.MAX_VALUE, 92, 101, 83, 52, 19}));
        }
        return this.mBstar;
    }

    l1l1II1111(float f, float f2, float f3, float f4, float f5, float f6, float f7, float f8, float f9) {
        this.mHue = f;
        this.mChroma = f2;
        this.mJ = f3;
        this.mQ = f4;
        this.mM = f5;
        this.mS = f6;
        this.mJstar = f7;
        this.mAstar = f8;
        this.mBstar = f9;
    }

    public static int toColor(float f, float f2, float f3) throws BindException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{3, 49, 52, 34, 21, 121, 115, 90, 74, 38, 71, 102, 82, 11}), 568759756L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{120, 13, 55, 8, 33}));
        }
        return toColor(f, f2, f3, IlIlII11Il.DEFAULT);
    }

    static l1l1II1111 fromColor(int i) throws TimeoutException, KeyStoreException {
        if (IllIlllIII.l11I11I11l(4221)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{81, 34, 21, 31, 4, 111, 109, 114, 1, 50, 8, 120, 82, 11, 115, 5, 82, 2, 12, 11, 117, 82, 100}));
        }
        float[] fArr = new float[7];
        float[] fArr2 = new float[3];
        fromColorInViewingConditions(i, IlIlII11Il.DEFAULT, fArr, fArr2);
        l1l1II1111 l1l1ii1111 = new l1l1II1111(fArr2[0], fArr2[1], fArr[0], fArr[1], fArr[2], fArr[3], fArr[4], fArr[5], fArr[6]);
        if (Ill11ll111.Ill1lIIlIl(4243)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{78, 16, 84, 23, 16, 76, 15, 90, 84, 17, 72, 114, 115}));
        }
        return l1l1ii1111;
    }

    public static void getM3HCTfromColor(int i, float[] fArr) {
        fromColorInViewingConditions(i, IlIlII11Il.DEFAULT, null, fArr);
        fArr[2] = I1l1lIllI1.lStarFromInt(i);
    }

    static void fromColorInViewingConditions(int i, IlIlII11Il ilIlII11Il, float[] fArr, float[] fArr2) throws KeyStoreException {
        if (Ill11ll111.l11I11I11l(308011726L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{116}));
        }
        I1l1lIllI1.xyzFromInt(i, fArr2);
        float[][] fArr3 = I1l1lIllI1.XYZ_TO_CAM16RGB;
        float f = fArr2[0];
        float[] fArr4 = fArr3[0];
        float f2 = fArr4[0] * f;
        float f3 = fArr2[1];
        float f4 = f2 + (fArr4[1] * f3);
        float f5 = fArr2[2];
        float f6 = f4 + (fArr4[2] * f5);
        float[] fArr5 = fArr3[1];
        float f7 = (fArr5[0] * f) + (fArr5[1] * f3) + (fArr5[2] * f5);
        float[] fArr6 = fArr3[2];
        float f8 = (f * fArr6[0]) + (f3 * fArr6[1]) + (f5 * fArr6[2]);
        float f9 = ilIlII11Il.getRgbD()[0] * f6;
        float f10 = ilIlII11Il.getRgbD()[1] * f7;
        float f11 = ilIlII11Il.getRgbD()[2] * f8;
        float fPow = (float) Math.pow((ilIlII11Il.getFl() * Math.abs(f9)) / 100.0d, 0.42d);
        float fPow2 = (float) Math.pow((ilIlII11Il.getFl() * Math.abs(f10)) / 100.0d, 0.42d);
        float fPow3 = (float) Math.pow((ilIlII11Il.getFl() * Math.abs(f11)) / 100.0d, 0.42d);
        float fSignum = ((Math.signum(f9) * 400.0f) * fPow) / (fPow + 27.13f);
        float fSignum2 = ((Math.signum(f10) * 400.0f) * fPow2) / (fPow2 + 27.13f);
        float fSignum3 = ((Math.signum(f11) * 400.0f) * fPow3) / (fPow3 + 27.13f);
        double d = fSignum3;
        float f12 = ((float) (((fSignum * 11.0d) + (fSignum2 * (-12.0d))) + d)) / 11.0f;
        float f13 = ((float) ((fSignum + fSignum2) - (d * 2.0d))) / 9.0f;
        float f14 = fSignum2 * 20.0f;
        float f15 = (((fSignum * 20.0f) + f14) + (21.0f * fSignum3)) / 20.0f;
        float f16 = (((fSignum * 40.0f) + f14) + fSignum3) / 20.0f;
        float fAtan2 = (((float) Math.atan2(f13, f12)) * 180.0f) / 3.1415927f;
        if (fAtan2 < 0.0f) {
            fAtan2 += 360.0f;
        } else if (fAtan2 >= 360.0f) {
            fAtan2 -= 360.0f;
        }
        float f17 = (3.1415927f * fAtan2) / 180.0f;
        float fPow4 = ((float) Math.pow((f16 * ilIlII11Il.getNbb()) / ilIlII11Il.getAw(), ilIlII11Il.getC() * ilIlII11Il.getZ())) * 100.0f;
        float c = (4.0f / ilIlII11Il.getC()) * ((float) Math.sqrt(fPow4 / 100.0f)) * (ilIlII11Il.getAw() + 4.0f) * ilIlII11Il.getFlRoot();
        float fSqrt = ((float) Math.sqrt(fPow4 / 100.0d)) * ((float) Math.pow(1.64d - Math.pow(0.29d, ilIlII11Il.getN()), 0.73d)) * ((float) Math.pow((((((((float) (Math.cos((((((double) fAtan2) < 20.14d ? 360.0f + fAtan2 : fAtan2) * 3.141592653589793d) / 180.0d) + 2.0d) + 3.8d)) * 0.25f) * 3846.1538f) * ilIlII11Il.getNc()) * ilIlII11Il.getNcb()) * ((float) Math.sqrt((f12 * f12) + (f13 * f13)))) / (f15 + 0.305f), 0.9d));
        float flRoot = ilIlII11Il.getFlRoot() * fSqrt;
        float fSqrt2 = ((float) Math.sqrt((r5 * ilIlII11Il.getC()) / (ilIlII11Il.getAw() + 4.0f))) * 50.0f;
        float f18 = (1.7f * fPow4) / ((0.007f * fPow4) + 1.0f);
        float fLog = ((float) Math.log((0.0228f * flRoot) + 1.0f)) * 43.85965f;
        double d2 = f17;
        float fCos = ((float) Math.cos(d2)) * fLog;
        float fSin = fLog * ((float) Math.sin(d2));
        fArr2[0] = fAtan2;
        fArr2[1] = fSqrt;
        if (fArr != null) {
            fArr[0] = fPow4;
            fArr[1] = c;
            fArr[2] = flRoot;
            fArr[3] = fSqrt2;
            fArr[4] = f18;
            fArr[5] = fCos;
            fArr[6] = fSin;
        }
        if (IIIlIl1I1l.I1lllI1llI(I1I1lI1II1.a(new byte[]{124, 14, 39, 84, 87, 91, 90, 102, 115}), 5699)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{0, 81, 22, 53, 6, 77, 125, 126, 99, 12, 65, 121, 87, 108, 110, Byte.MAX_VALUE, 45, 22, 16, 84, 101, 2, Byte.MAX_VALUE, 126}));
        }
    }

    private static l1l1II1111 fromJch(float f, float f2, float f3) {
        return fromJchInFrame(f, f2, f3, IlIlII11Il.DEFAULT);
    }

    private static l1l1II1111 fromJchInFrame(float f, float f2, float f3, IlIlII11Il ilIlII11Il) {
        float c = (4.0f / ilIlII11Il.getC()) * ((float) Math.sqrt(f / 100.0d)) * (ilIlII11Il.getAw() + 4.0f) * ilIlII11Il.getFlRoot();
        float flRoot = f2 * ilIlII11Il.getFlRoot();
        float fSqrt = ((float) Math.sqrt(((f2 / ((float) Math.sqrt(r4))) * ilIlII11Il.getC()) / (ilIlII11Il.getAw() + 4.0f))) * 50.0f;
        float f4 = (1.7f * f) / ((0.007f * f) + 1.0f);
        float fLog = ((float) Math.log((flRoot * 0.0228d) + 1.0d)) * 43.85965f;
        double d = (3.1415927f * f3) / 180.0f;
        return new l1l1II1111(f3, f2, f, c, flRoot, fSqrt, f4, fLog * ((float) Math.cos(d)), fLog * ((float) Math.sin(d)));
    }

    float distance(l1l1II1111 l1l1ii1111) {
        float jStar = getJStar() - l1l1ii1111.getJStar();
        float aStar = getAStar() - l1l1ii1111.getAStar();
        float bStar = getBStar() - l1l1ii1111.getBStar();
        return (float) (Math.pow(Math.sqrt((jStar * jStar) + (aStar * aStar) + (bStar * bStar)), 0.63d) * 1.41d);
    }

    int viewedInSrgb() throws IOException {
        if (l1lll111II.l11I11I11l(8626)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{6, 3, 49, 11, 18, Byte.MAX_VALUE, 3, 99}));
        }
        int iViewed = viewed(IlIlII11Il.DEFAULT);
        if (Il1I1lllIl.I1lI11IIll(I1I1lI1II1.a(new byte[]{6, 28, 13, 16, 49, 126, 86, 71, 73, 54, 86, 85, 7, 88, 123, 79, 45, 53, 91, 106, 104, 82, 75, 91, 86, 78, 84}), 473175030L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 8, 51, 83, 58, 113, 64, 124, 97, 28, 6, 100, 93, 11, 86, 6, 50, 17, 19, 67, 2, 43}));
        }
        return iViewed;
    }

    int viewed(IlIlII11Il ilIlII11Il) {
        float fPow = (float) Math.pow(((((double) getChroma()) == FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE || ((double) getJ()) == FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE) ? 0.0f : getChroma() / ((float) Math.sqrt(getJ() / 100.0d))) / Math.pow(1.64d - Math.pow(0.29d, ilIlII11Il.getN()), 0.73d), 1.1111111111111112d);
        double hue = (getHue() * 3.1415927f) / 180.0f;
        float fCos = ((float) (Math.cos(2.0d + hue) + 3.8d)) * 0.25f;
        float aw = ilIlII11Il.getAw() * ((float) Math.pow(getJ() / 100.0d, (1.0d / ilIlII11Il.getC()) / ilIlII11Il.getZ()));
        float nc = fCos * 3846.1538f * ilIlII11Il.getNc() * ilIlII11Il.getNcb();
        float nbb = aw / ilIlII11Il.getNbb();
        float fSin = (float) Math.sin(hue);
        float fCos2 = (float) Math.cos(hue);
        float f = (((0.305f + nbb) * 23.0f) * fPow) / (((nc * 23.0f) + ((11.0f * fPow) * fCos2)) + ((fPow * 108.0f) * fSin));
        float f2 = fCos2 * f;
        float f3 = f * fSin;
        float f4 = nbb * 460.0f;
        float f5 = (((451.0f * f2) + f4) + (288.0f * f3)) / 1403.0f;
        float f6 = ((f4 - (891.0f * f2)) - (261.0f * f3)) / 1403.0f;
        float fSignum = Math.signum(f5) * (100.0f / ilIlII11Il.getFl()) * ((float) Math.pow((float) Math.max(FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE, (Math.abs(f5) * 27.13d) / (400.0d - Math.abs(f5))), 2.380952380952381d));
        float fSignum2 = Math.signum(f6) * (100.0f / ilIlII11Il.getFl()) * ((float) Math.pow((float) Math.max(FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE, (Math.abs(f6) * 27.13d) / (400.0d - Math.abs(f6))), 2.380952380952381d));
        float fSignum3 = Math.signum(((f4 - (f2 * 220.0f)) - (f3 * 6300.0f)) / 1403.0f) * (100.0f / ilIlII11Il.getFl()) * ((float) Math.pow((float) Math.max(FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE, (Math.abs(r8) * 27.13d) / (400.0d - Math.abs(r8))), 2.380952380952381d));
        float f7 = fSignum / ilIlII11Il.getRgbD()[0];
        float f8 = fSignum2 / ilIlII11Il.getRgbD()[1];
        float f9 = fSignum3 / ilIlII11Il.getRgbD()[2];
        float[][] fArr = I1l1lIllI1.CAM16RGB_TO_XYZ;
        float[] fArr2 = fArr[0];
        float f10 = (fArr2[0] * f7) + (fArr2[1] * f8) + (fArr2[2] * f9);
        float[] fArr3 = fArr[1];
        float f11 = (fArr3[0] * f7) + (fArr3[1] * f8) + (fArr3[2] * f9);
        float[] fArr4 = fArr[2];
        int iXYZToColor = IIll1l1lII.XYZToColor(f10, f11, (f7 * fArr4[0]) + (f8 * fArr4[1]) + (f9 * fArr4[2]));
        if (android.accounts.utils.lIIlI111II.I1111IIl11(474196731L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 0, 36, 83, 14, 98, 103, 81, 110, 37, 91, 0, 100, 13}));
        }
        return iXYZToColor;
    }

    static int toColor(float f, float f2, float f3, IlIlII11Il ilIlII11Il) throws IllegalAccessException, TimeoutException, IOException, KeyStoreException {
        if (f2 < 1.0d || Math.round(f3) <= FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE || Math.round(f3) >= 100.0d) {
            return I1l1lIllI1.intFromLStar(f3);
        }
        float fMin = f < 0.0f ? 0.0f : Math.min(360.0f, f);
        l1l1II1111 l1l1ii1111 = null;
        boolean z = true;
        float f4 = 0.0f;
        float f5 = f2;
        while (Math.abs(f4 - f2) >= CHROMA_SEARCH_ENDPOINT) {
            l1l1II1111 l1l1ii1111FindCamByJ = findCamByJ(fMin, f5, f3);
            if (!z) {
                if (l1l1ii1111FindCamByJ == null) {
                    f2 = f5;
                } else {
                    f4 = f5;
                    l1l1ii1111 = l1l1ii1111FindCamByJ;
                }
                f5 = ((f2 - f4) / 2.0f) + f4;
            } else {
                if (l1l1ii1111FindCamByJ != null) {
                    return l1l1ii1111FindCamByJ.viewed(ilIlII11Il);
                }
                f5 = ((f2 - f4) / 2.0f) + f4;
                z = false;
            }
        }
        if (l1l1ii1111 == null) {
            return I1l1lIllI1.intFromLStar(f3);
        }
        return l1l1ii1111.viewed(ilIlII11Il);
    }

    private static l1l1II1111 findCamByJ(float f, float f2, float f3) throws IllegalAccessException, TimeoutException, IOException, KeyStoreException {
        float f4 = 100.0f;
        float f5 = 1000.0f;
        float f6 = 0.0f;
        l1l1II1111 l1l1ii1111 = null;
        float f7 = 1000.0f;
        while (Math.abs(f6 - f4) > LIGHTNESS_SEARCH_ENDPOINT) {
            float f8 = ((f4 - f6) / 2.0f) + f6;
            int iViewedInSrgb = fromJch(f8, f2, f).viewedInSrgb();
            float fLStarFromInt = I1l1lIllI1.lStarFromInt(iViewedInSrgb);
            float fAbs = Math.abs(f3 - fLStarFromInt);
            if (fAbs < 0.2f) {
                l1l1II1111 l1l1ii1111FromColor = fromColor(iViewedInSrgb);
                float fDistance = l1l1ii1111FromColor.distance(fromJch(l1l1ii1111FromColor.getJ(), l1l1ii1111FromColor.getChroma(), f));
                if (fDistance <= 1.0f) {
                    l1l1ii1111 = l1l1ii1111FromColor;
                    f5 = fAbs;
                    f7 = fDistance;
                }
            }
            if (f5 == 0.0f && f7 == 0.0f) {
                break;
            }
            if (fLStarFromInt < f3) {
                f6 = f8;
            } else {
                f4 = f8;
            }
        }
        if (IIlI1Il1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{123, 55, 37, 31, 26, 96, 121, 91, 97, 21, 69, 120, 76, 104, 108, 70, 4, 55, 36, 99, 96}), 511469836L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{97, 84, 10, 44, 40, 79, 82, 64, 74, 14, 74, 94, 93, 82, 114, 101, 7}));
        }
        return l1l1ii1111;
    }
}
