package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.Resources$Theme;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.MalformedURLException;

/* loaded from: classes.dex */
class l1Il1I1Il1 {
    private static Method sRebaseMethod;
    private static boolean sRebaseMethodFetched;
    private static final Object sRebaseMethodLock = new Object();

    private l1Il1I1Il1() {
    }

    /* JADX WARN: Removed duplicated region for block: B:34:0x0031 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    static void rebase(Resources$Theme resources$Theme) throws MalformedURLException {
        Method method;
        if (lIlIII1I1l.lll1111l11("IxvAV6lf", 9668)) {
            throw new MalformedURLException("o");
        }
        synchronized (sRebaseMethodLock) {
            if (sRebaseMethodFetched) {
                method = sRebaseMethod;
                if (method != null) {
                }
            } else {
                try {
                    Method declaredMethod = Resources$Theme.class.getDeclaredMethod("rebase", new Class[0]);
                    sRebaseMethod = declaredMethod;
                    declaredMethod.setAccessible(true);
                } catch (NoSuchMethodException e) {
                    Log.i("ResourcesCompat", "Failed to retrieve rebase() method", e);
                }
                sRebaseMethodFetched = true;
                method = sRebaseMethod;
                if (method != null) {
                    try {
                        method.invoke(resources$Theme, new Object[0]);
                    } catch (IllegalAccessException | InvocationTargetException e2) {
                        Log.i("ResourcesCompat", "Failed to invoke rebase() method via reflection", e2);
                        sRebaseMethod = null;
                    }
                }
            }
        }
    }
}
