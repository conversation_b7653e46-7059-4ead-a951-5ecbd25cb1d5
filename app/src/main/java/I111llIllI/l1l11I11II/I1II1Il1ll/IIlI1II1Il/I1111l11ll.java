package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.I1lllI11II;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import android.graphics.drawable.Drawable;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1111l11ll {
    private I1111l11ll() {
    }

    static Drawable getDrawable(Resources resources, int i, Resources$Theme resources$Theme) throws Resources$NotFoundException, BrokenBarrierException {
        if (I1lllI11II.l1Il11I1Il(I1I1lI1II1.a(new byte[]{81, 47, 81, 60, 10, 123}), 679651407L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{67, 52, 35, 31, 52, 83, 114, 87, 83, 49, 105, 4, 82, 91, 94, 64, 56, 20, 26, 74, 74, 3, 69}));
        }
        Drawable drawable = resources.getDrawable(i, resources$Theme);
        if (lIIlI111II.I1111l111I(1725833878L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{80, 81, 55, 41, 80, 3, 5, 71, 72, 14, 100, 68, 13, 95, 95, 116, 26, 12, 53, 67, 9, 82, 100, 114, 124, 113, 124, 8}));
        }
        return drawable;
    }

    static Drawable getDrawableForDensity(Resources resources, int i, int i2, Resources$Theme resources$Theme) {
        return resources.getDrawableForDensity(i, i2, resources$Theme);
    }
}
