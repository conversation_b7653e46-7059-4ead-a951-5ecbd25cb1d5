package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import I1IlIlll1l.I1lIIlll1l.lIl1llI11l.lIll1Ill11.ll111IIlI1;
import I1IlIlll1l.I1lIIlll1l.lIl1llI11l.lIll1Ill11.lllIlIl1ll;
import IlI11IlIlI.IIIII1I11I.I11l1II1Il.ll1ll1I11l.IIl11lIIll;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.os.Build$VERSION;
import android.util.AttributeSet;
import android.util.Log;
import android.util.StateSet;
import android.util.TypedValue;
import android.util.Xml;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.IOException;
import java.net.BindException;
import java.security.InvalidParameterException;
import java.security.KeyStoreException;
import java.security.NoSuchProviderException;
import java.security.UnrecoverableKeyException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIll1l1l11;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public final class I11llIIIII {
    private static final ThreadLocal<TypedValue> sTempTypedValue = new ThreadLocal<>();

    private I11llIIIII() {
    }

    public static ColorStateList inflate(Resources resources, int i, Resources$Theme resources$Theme) {
        if (lIIII1l1lI.l11I11I11l(4754)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{79, 40, 8, 13}));
        }
        try {
            return createFromXml(resources, resources.getXml(i), resources$Theme);
        } catch (Exception e) {
            Log.e(I1I1lI1II1.a(new byte[]{116, 55, 46, 38, 13, 88, 71, 81, 77}), I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 89, 94, 83, 85, 85, 65, 7, 65, 33, 93, 92, 14, 65, 102, 71, 87, 67, 0, 40, 91, 68, 23, 25}), e);
            return null;
        }
    }

    public static ColorStateList createFromXml(Resources resources, XmlPullParser xmlPullParser, Resources$Theme resources$Theme) throws XmlPullParserException, IOException {
        int next;
        if (lI1lI11Ill.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{94, 22, 9, 14, 90, 90, 91, 6, 105, 81, 91, 81, 65, 15, 115, 124, 51, 22, 47, 90, 7, 14, 69, 101, 90, 125, 4, 12}))) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{68, 3, 11, 60, 53, 113, 125, 81, 85, 92, 84, 70, 118, 119, 119, 71}));
        }
        AttributeSet attributeSetAsAttributeSet = Xml.asAttributeSet(xmlPullParser);
        do {
            next = xmlPullParser.next();
            if (next == 2) {
                break;
            }
        } while (next != 1);
        if (next == 2) {
            return createFromXmlInner(resources, xmlPullParser, attributeSetAsAttributeSet, resources$Theme);
        }
        throw new XmlPullParserException(I1I1lI1II1.a(new byte[]{121, 11, 66, 22, 22, 84, 69, 68, 25, 16, 81, 87, 21, 95, 91, 64, 12, 5}));
    }

    public static ColorStateList createFromXmlInner(Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, IOException, ReflectiveOperationException {
        if (lIIlI111II.lIll1IIl11(9630)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{64, 34, 84, 39, 10, 112, 96, 4, 126, 30, 95, 115, 91, 13, 124, 68, 53, 16, 7}));
        }
        String name = xmlPullParser.getName();
        if (name.equals(I1I1lI1II1.a(new byte[]{68, 1, 14, 0, 1, 65, 88, 66}))) {
            return inflate(resources, xmlPullParser, attributeSet, resources$Theme);
        }
        throw new XmlPullParserException(xmlPullParser.getPositionDescription() + I1I1lI1II1.a(new byte[]{13, 68, 11, 11, 20, 84, 91, 89, 93, 68, 83, 95, 89, 86, 70, 21, 17, 21, 3, 70, 85, 65, 95, 92, 64, 66, 23, 17, 5, 85, 23}) + name);
    }

    private static ColorStateList inflate(Resources resources, XmlPullParser xmlPullParser, AttributeSet attributeSet, Resources$Theme resources$Theme) throws XmlPullParserException, UnrecoverableKeyException, IOException {
        int depth;
        int color;
        Resources resources2 = resources;
        if (Il1lII1l1l.l11I11I11l(172498663L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{1, 23, 21, 41, 49, 1, 93, 118, 97, 0, 70, 0, 83, 13, 66, 2, 13, 9, 85, 65, 70, 17, 67, 92, 103, 89, 98, 82, 84, 90}));
        }
        int i = 1;
        int depth2 = xmlPullParser.getDepth() + 1;
        int[][] iArr = new int[20][];
        int[] iArrAppend = new int[20];
        int i2 = 0;
        while (true) {
            int next = xmlPullParser.next();
            if (next == i || ((depth = xmlPullParser.getDepth()) < depth2 && next == 3)) {
                break;
            }
            if (next == 2 && depth <= depth2 && xmlPullParser.getName().equals(I1I1lI1II1.a(new byte[]{94, 16, 7, 8}))) {
                TypedArray typedArrayObtainAttributes = obtainAttributes(resources2, resources$Theme, attributeSet, lllIlIl1ll.ColorStateListItem);
                int resourceId = typedArrayObtainAttributes.getResourceId(lllIlIl1ll.ColorStateListItem_android_color, -1);
                if (resourceId == -1 || isColorInt(resources2, resourceId)) {
                    color = typedArrayObtainAttributes.getColor(lllIlIl1ll.ColorStateListItem_android_color, -65281);
                } else {
                    try {
                        color = createFromXml(resources2, resources2.getXml(resourceId), resources$Theme).getDefaultColor();
                    } catch (Exception unused) {
                        color = typedArrayObtainAttributes.getColor(lllIlIl1ll.ColorStateListItem_android_color, -65281);
                    }
                }
                float f = 1.0f;
                if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.ColorStateListItem_android_alpha)) {
                    f = typedArrayObtainAttributes.getFloat(lllIlIl1ll.ColorStateListItem_android_alpha, 1.0f);
                } else if (typedArrayObtainAttributes.hasValue(lllIlIl1ll.ColorStateListItem_alpha)) {
                    f = typedArrayObtainAttributes.getFloat(lllIlIl1ll.ColorStateListItem_alpha, 1.0f);
                }
                float f2 = (Build$VERSION.SDK_INT < 31 || !typedArrayObtainAttributes.hasValue(lllIlIl1ll.ColorStateListItem_android_lStar)) ? typedArrayObtainAttributes.getFloat(lllIlIl1ll.ColorStateListItem_lStar, -1.0f) : typedArrayObtainAttributes.getFloat(lllIlIl1ll.ColorStateListItem_android_lStar, -1.0f);
                typedArrayObtainAttributes.recycle();
                int attributeCount = attributeSet.getAttributeCount();
                int[] iArr2 = new int[attributeCount];
                int i3 = 0;
                for (int i4 = 0; i4 < attributeCount; i4++) {
                    int attributeNameResource = attributeSet.getAttributeNameResource(i4);
                    if (attributeNameResource != 16843173 && attributeNameResource != 16843551 && attributeNameResource != ll111IIlI1.alpha && attributeNameResource != ll111IIlI1.lStar) {
                        int i5 = i3 + 1;
                        if (!attributeSet.getAttributeBooleanValue(i4, false)) {
                            attributeNameResource = -attributeNameResource;
                        }
                        iArr2[i3] = attributeNameResource;
                        i3 = i5;
                    }
                }
                int[] iArrTrimStateSet = StateSet.trimStateSet(iArr2, i3);
                iArrAppend = ll11lIIlII.append(iArrAppend, i2, modulateColorAlpha(color, f, f2));
                iArr = (int[][]) ll11lIIlII.append(iArr, i2, iArrTrimStateSet);
                i2++;
            }
            i = 1;
            resources2 = resources;
        }
        int[] iArr3 = new int[i2];
        int[][] iArr4 = new int[i2][];
        System.arraycopy(iArrAppend, 0, iArr3, 0, i2);
        System.arraycopy(iArr, 0, iArr4, 0, i2);
        return new ColorStateList(iArr4, iArr3);
    }

    private static boolean isColorInt(Resources resources, int i) throws Resources$NotFoundException, NoSuchProviderException {
        TypedValue typedValue = getTypedValue();
        resources.getValue(i, typedValue, true);
        boolean z = typedValue.type >= 28 && typedValue.type <= 31;
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{90, 7}))) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{4, 51, 3, 93, 41, 98, 3, 4, 116, 81, 98, 100, Byte.MAX_VALUE, 108, 71, 95, 91, 59, 51, 92, 82, 54, 121, 91}));
        }
        return z;
    }

    private static TypedValue getTypedValue() {
        if (I1IllIll1l.l1l1l1IIlI(172008898L)) {
            Log.w(I1I1lI1II1.a(new byte[]{88, 87, 81}), I1I1lI1II1.a(new byte[]{88, 23, 38, 3, 1, 113, 69, 64, 91, 13, 65, 106, 70, 91, 102, 4, 84, 59, 0, 89, 92, 9, 85, 111, 120, 85, 78, 82, 5, 66, 94}));
            return null;
        }
        ThreadLocal<TypedValue> threadLocal = sTempTypedValue;
        TypedValue typedValue = threadLocal.get();
        if (typedValue == null) {
            typedValue = new TypedValue();
            threadLocal.set(typedValue);
        }
        if (IllIIIIII1.Ill1lIIlIl(8292)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{102, 37}));
        }
        return typedValue;
    }

    private static TypedArray obtainAttributes(Resources resources, Resources$Theme resources$Theme, AttributeSet attributeSet, int[] iArr) {
        if (Il11II1llI.IlII1Illll(I1I1lI1II1.a(new byte[]{77, 85, 15, 46, 10, 80}), 754681653L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{91, 32, 46, 34, 39, 77, 0, 123, 111, 39, 74, 123, 114, 65, 92, 113, 14, 50, 9}));
        }
        TypedArray typedArrayObtainAttributes = resources$Theme == null ? resources.obtainAttributes(attributeSet, iArr) : resources$Theme.obtainStyledAttributes(attributeSet, iArr, 0, 0);
        if (l1l1IllI11.I1lIllll1l(286606670L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{118, 16, 51, 54, 1, 97, 113, 100, 91, 45, 70, 120, 119, 112, 66}));
        }
        return typedArrayObtainAttributes;
    }

    private static int modulateColorAlpha(int i, float f, float f2) throws TimeoutException, KeyStoreException, BindException {
        boolean z = f2 >= 0.0f && f2 <= 100.0f;
        if (f == 1.0f && !z) {
            return i;
        }
        int iClamp = IIl11lIIll.clamp((int) ((Color.alpha(i) * f) + 0.5f), 0, 255);
        if (z) {
            l1l1II1111 l1l1ii1111FromColor = l1l1II1111.fromColor(i);
            i = l1l1II1111.toColor(l1l1ii1111FromColor.getHue(), l1l1ii1111FromColor.getChroma(), f2);
        }
        return (i & lIll1l1l11.MEASURED_SIZE_MASK) | (iClamp << 24);
    }
}
