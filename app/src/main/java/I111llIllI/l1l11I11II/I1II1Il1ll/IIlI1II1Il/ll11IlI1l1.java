package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.Resources$Theme;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class ll11IlI1l1 {
    private ll11IlI1l1() {
    }

    static void rebase(Resources$Theme resources$Theme) {
        resources$Theme.rebase();
        if (IIll1llI1l.Il1IIlI1II(8392)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{0, 32, 26, 0, 16}));
        }
    }
}
