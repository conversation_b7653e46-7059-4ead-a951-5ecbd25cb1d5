package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.media.content.IIl1l1IllI;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import java.io.FileNotFoundException;
import java.io.InvalidObjectException;
import java.security.SignatureException;
import java.security.cert.CRLException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class IlIlII11Il {
    static final IlIlII11Il DEFAULT = make(I1l1lIllI1.WHITE_POINT_D65, (float) ((I1l1lIllI1.yFromLStar(50.0f) * 63.66197723675813d) / 100.0d), 50.0f, 2.0f, false);
    private final float mAw;
    private final float mC;
    private final float mFl;
    private final float mFlRoot;
    private final float mN;
    private final float mNbb;
    private final float mNc;
    private final float mNcb;
    private final float[] mRgbD;
    private final float mZ;

    float getAw() throws SignatureException {
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{90, 83, 26, 11, 33, 118, 90, 68, 10, 5, 96, 86, 108, 15, 68, 124, 55, 9, 23, 83, 1, 51, 65}), 281554309L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{99, 51, 50, 92, 17, 125, 80, 2, 118, 9, 83, 120, 2, 115, 78, 3, 59, 14, 46, 4, 97}));
        }
        float f = this.mAw;
        if (l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{95, 30, 91, 80, 59, 13, 126}), 8530)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{114, 12, 18, 86, 38, 65, 79, 98, 105, 9, 66, 90, 66, 80, 80, 5, 26, 16, 14, 89, 66, 52, 122}));
        }
        return f;
    }

    float getN() throws CertificateNotYetValidException {
        if (lIIlI111II.I1I11l11l1(2506)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{95, 54, 1, 6, 36, 126, 98, 70}));
        }
        return this.mN;
    }

    float getNbb() throws FileNotFoundException {
        if (Il1I1lllIl.I1lIllll1l(199988490L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{115, 44, 33, 52, 11, 121, 109, 113, 107}));
        }
        float f = this.mNbb;
        if (android.support.v4.graphics.drawable.lIIlI111II.Il1lII1l1l(4516)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{77, 47, 80, 14, 87, 108, 120, 85, Byte.MAX_VALUE, 53, 101}));
        }
        return f;
    }

    float getNcb() {
        return this.mNcb;
    }

    float getC() {
        return this.mC;
    }

    float getNc() {
        return this.mNc;
    }

    float[] getRgbD() throws CRLException {
        float[] fArr = this.mRgbD;
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{14, 2, 20, 4}), 904685318L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{112, 6, 44, 80, 48, 84, 120, 91, 80, 47, 81, 70, 84, 92, 98, 0, 18, 87, 36, 103, 5, 38, 96, 108, 92}));
        }
        return fArr;
    }

    float getFl() throws CertPathBuilderException {
        float f = this.mFl;
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{102, 3, 87, 8, 84, 96, 114, 92, 122, 2, 9, 3}), 525231356L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{109, 45, 17, 13, 1, 5, 82, 106, 12, 2, 65, Byte.MAX_VALUE, 98, 67, 87, 102, 13, 0, 43, 0, 71, 37, 120, 125, 5}));
        }
        return f;
    }

    float getFlRoot() throws CertificateExpiredException {
        if (llIlII1IlI.lI11llll1I(I1I1lI1II1.a(new byte[]{90, 93, 37, 40, 58, 83}), I1I1lI1II1.a(new byte[]{110, 32, 20, 80, 0, 1, 5, 95, 114, 52, 122, 70, 67, 110, 85, 90, 10, 24, 45, 0, 104, 37, 67, 115, 1, 87, 86, 42, 19, 112, 70}))) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{68, 16, 87, 4, 44}));
        }
        float f = this.mFlRoot;
        if (II1lllllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{78, 39, 87, 19, 24, 111}), 273788201L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{122, 30, 37, 93, 47, 4, 89, 117, 67, 3, 67, 85, 88, 9, 103, 13, 7, 25, 41, 86, 118, 82, 75, 5, 70, 123, 1, 13, 13, 80, 67}));
        }
        return f;
    }

    float getZ() {
        return this.mZ;
    }

    private IlIlII11Il(float f, float f2, float f3, float f4, float f5, float f6, float[] fArr, float f7, float f8, float f9) {
        this.mN = f;
        this.mAw = f2;
        this.mNbb = f3;
        this.mNcb = f4;
        this.mC = f5;
        this.mNc = f6;
        this.mRgbD = fArr;
        this.mFl = f7;
        this.mFlRoot = f8;
        this.mZ = f9;
    }

    static IlIlII11Il make(float[] fArr, float f, float f2, float f3, boolean z) throws InvalidObjectException {
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.llI1llI1l1(232148373L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{94, 60, 15, 45, 90, 70, 98, 90, 123, 7, 98, 100, 116, 65, 110}));
        }
        float[][] fArr2 = I1l1lIllI1.XYZ_TO_CAM16RGB;
        float f4 = fArr[0];
        float[] fArr3 = fArr2[0];
        float f5 = fArr3[0] * f4;
        float f6 = fArr[1];
        float f7 = f5 + (fArr3[1] * f6);
        float f8 = fArr[2];
        float f9 = f7 + (fArr3[2] * f8);
        float[] fArr4 = fArr2[1];
        float f10 = (fArr4[0] * f4) + (fArr4[1] * f6) + (fArr4[2] * f8);
        float[] fArr5 = fArr2[2];
        float f11 = (f4 * fArr5[0]) + (f6 * fArr5[1]) + (f8 * fArr5[2]);
        float f12 = (f3 / 10.0f) + 0.8f;
        float fLerp = ((double) f12) >= 0.9d ? I1l1lIllI1.lerp(0.59f, 0.69f, (f12 - 0.9f) * 10.0f) : I1l1lIllI1.lerp(0.525f, 0.59f, (f12 - 0.8f) * 10.0f);
        float fExp = z ? 1.0f : (1.0f - (((float) Math.exp(((-f) - 42.0f) / 92.0f)) * 0.2777778f)) * f12;
        double d = fExp;
        if (d > 1.0d) {
            fExp = 1.0f;
        } else if (d < FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE) {
            fExp = 0.0f;
        }
        float[] fArr6 = {(((100.0f / f9) * fExp) + 1.0f) - fExp, (((100.0f / f10) * fExp) + 1.0f) - fExp, (((100.0f / f11) * fExp) + 1.0f) - fExp};
        float f13 = 1.0f / ((5.0f * f) + 1.0f);
        float f14 = f13 * f13 * f13 * f13;
        float f15 = 1.0f - f14;
        float fCbrt = (f14 * f) + (0.1f * f15 * f15 * ((float) Math.cbrt(f * 5.0d)));
        float fYFromLStar = I1l1lIllI1.yFromLStar(f2) / fArr[1];
        double d2 = fYFromLStar;
        float fSqrt = ((float) Math.sqrt(d2)) + 1.48f;
        float fPow = 0.725f / ((float) Math.pow(d2, 0.2d));
        float fPow2 = (float) Math.pow(((fArr6[2] * fCbrt) * f11) / 100.0d, 0.42d);
        float[] fArr7 = {(float) Math.pow(((fArr6[0] * fCbrt) * f9) / 100.0d, 0.42d), (float) Math.pow(((fArr6[1] * fCbrt) * f10) / 100.0d, 0.42d), fPow2};
        float f16 = fArr7[0];
        float f17 = fArr7[1];
        return new IlIlII11Il(fYFromLStar, ((((f16 * 400.0f) / (f16 + 27.13f)) * 2.0f) + ((f17 * 400.0f) / (f17 + 27.13f)) + (((400.0f * fPow2) / (fPow2 + 27.13f)) * 0.05f)) * fPow, fPow, fPow, fLerp, f12, fArr6, fCbrt, (float) Math.pow(fCbrt, 0.25d), fSqrt);
    }
}
