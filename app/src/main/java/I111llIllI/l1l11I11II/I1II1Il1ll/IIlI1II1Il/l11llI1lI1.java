package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\bÃ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\t\u0010\nJ!\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\u0007¢\u0006\u0004\b\u0007\u0010\b"}, d2 = {"LI111llIllI/l1l11I11II/I1II1Il1ll/IIlI1II1Il/l11llI1lI1;", "", "Landroid/content/res/TypedArray;", "p0", "", "p1", "Landroid/graphics/Typeface;", "getFont", "(Landroid/content/res/TypedArray;I)Landroid/graphics/Typeface;", "<init>", "()V"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
final class l11llI1lI1 {
    public static final l11llI1lI1 INSTANCE = new l11llI1lI1();

    private l11llI1lI1() {
    }

    public static final Typeface getFont(TypedArray p0, int p1) throws InstantiationException {
        if (Il1lII1l1l.Ill1lIIlIl(8420)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{102, 86, 37, 36, 21, 7, 77, 96, Byte.MAX_VALUE, 21, 90, 126, 124, 86}));
        }
        Typeface font = p0.getFont(p1);
        Intrinsics.a(font);
        if (!lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{78, 1, 33, 8, 41, 100, Byte.MAX_VALUE, 94, 12, 9, 101, 92, 81, 74, 5, 102, 85, 59, 14, 74, 120, 23, 88, 86, 1, 116, 124, 17, 16}), 1392154427L)) {
            return font;
        }
        Log.d(I1I1lI1II1.a(new byte[]{64, 40, 22, 0, 17, 124, 101, 3, 81, 85, 5, 95, 113, 115, 1, 7, 18, 82, 5, 88, 115, 2, 4}), I1I1lI1II1.a(new byte[]{2, 45, 19, 44, 16, 124, 88, 89, 14, 20, 67, 123, 13, 72, 71, 122, 9, 19, 49, 125, 96, 80, 118, 90, 96, 113, 124, 7, 8, 117, 97, 36}));
        return null;
    }
}
