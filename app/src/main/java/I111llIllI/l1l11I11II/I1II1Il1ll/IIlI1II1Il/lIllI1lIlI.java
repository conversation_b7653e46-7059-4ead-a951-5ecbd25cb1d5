package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.llllIllIII;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.media.content.Il1llIl111;
import android.os.Build$VERSION;
import android.os.Handler;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import android.util.SparseArray;
import android.util.TypedValue;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.IOException;
import java.io.SyncFailedException;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import java.security.KeyException;
import java.security.ProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateNotYetValidException;
import java.util.Iterator;
import java.util.WeakHashMap;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIllIlIl.llI111llII.Il1IIIlI1l.I11l1I11ll.llllII11Il;
import org.xmlpull.v1.XmlPullParserException;

/* loaded from: classes.dex */
public final class lIllI1lIlI {
    public static final int ID_NULL = 0;
    private static final String TAG = I1I1lI1II1.a(new byte[]{101, 1, 17, 10, 23, 71, 84, 85, 74, 39, 95, 93, 69, 88, 64});
    private static final ThreadLocal<TypedValue> sTempTypedValue = new ThreadLocal<>();
    private static final WeakHashMap<Il11IlIllI, SparseArray<l1I11I11Il>> sColorStateCaches = new WeakHashMap<>(0);
    private static final Object sColorStateCacheLock = new Object();

    public static void clearCachesForTheme(Resources$Theme resources$Theme) {
        if (IllIIIIII1.I111IlIl1I(4826)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{118, 11, 17, 55, 58, 114}));
        }
        synchronized (sColorStateCacheLock) {
            Iterator<Il11IlIllI> it = sColorStateCaches.keySet().iterator();
            while (it.hasNext()) {
                Il11IlIllI next = it.next();
                if (next != null && resources$Theme.equals(next.mTheme)) {
                    it.remove();
                }
            }
        }
    }

    public static Drawable getDrawable(Resources resources, int i, Resources$Theme resources$Theme) throws SyncFailedException, Resources$NotFoundException, BrokenBarrierException {
        Drawable drawable = I1111l11ll.getDrawable(resources, i, resources$Theme);
        if (lIIlI111II.lI1lIIll11(500113546L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{14, 92, 18, 48, 35, 93, 90, 86, 113, 10}));
        }
        return drawable;
    }

    public static Drawable getDrawableForDensity(Resources resources, int i, int i2, Resources$Theme resources$Theme) throws Resources$NotFoundException {
        Drawable drawableForDensity = I1111l11ll.getDrawableForDensity(resources, i, i2, resources$Theme);
        if (Il1llIl111.IlII1Illll(I1I1lI1II1.a(new byte[]{114, 50, 33, 61}))) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{5, 81, 47}));
        }
        return drawableForDensity;
    }

    public static int getColor(Resources resources, int i, Resources$Theme resources$Theme) throws Resources$NotFoundException {
        if (I1lllI11II.Il1IIlI1II(339)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{116, 39, 84, 16, 52, 93, 100, 97, 124, 33, 72, 115, 103, 86, 86, 80, 83, 17, 37}));
        }
        return Ill1llllll.getColor(resources, i, resources$Theme);
    }

    public static ColorStateList getColorStateList(Resources resources, int i, Resources$Theme resources$Theme) throws Resources$NotFoundException {
        Il11IlIllI il11IlIllI = new Il11IlIllI(resources, resources$Theme);
        ColorStateList cachedColorStateList = getCachedColorStateList(il11IlIllI, i);
        if (cachedColorStateList != null) {
            return cachedColorStateList;
        }
        ColorStateList colorStateListInflateColorStateList = inflateColorStateList(resources, i, resources$Theme);
        if (colorStateListInflateColorStateList != null) {
            addColorStateListToCache(il11IlIllI, i, colorStateListInflateColorStateList, resources$Theme);
            return colorStateListInflateColorStateList;
        }
        return Ill1llllll.getColorStateList(resources, i, resources$Theme);
    }

    private static ColorStateList inflateColorStateList(Resources resources, int i, Resources$Theme resources$Theme) {
        if (isColorInt(resources, i)) {
            return null;
        }
        try {
            return I11llIIIII.createFromXml(resources, resources.getXml(i), resources$Theme);
        } catch (Exception e) {
            Log.w(TAG, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 89, 94, 83, 85, 85, 65, 7, 65, 33, 93, 92, 14, 65, 102, 71, 87, 67, 0, 40, 91, 68, 23, 27, 68, 14, 0, 3, 67, 94, 94, 94, 68, 89, 68, 21, 77, 91, 21, 22, 9, 7, 18, 86, 19, 82, 88, 86, 65, 88, 23, 15}), e);
            return null;
        }
    }

    private static ColorStateList getCachedColorStateList(Il11IlIllI il11IlIllI, int i) {
        l1I11I11Il l1i11i11il;
        if (androidx.core.location.lIIlI111II.llI1llI1l1(259129629L)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{83, 86, 4, 33, 40, 101, 89, 83, 67, 40, 69, 124, 12, 97, 76, 92, 85, 11, 40, 126, 93, 50, 68}));
        }
        synchronized (sColorStateCacheLock) {
            SparseArray<l1I11I11Il> sparseArray = sColorStateCaches.get(il11IlIllI);
            if (sparseArray != null && sparseArray.size() > 0 && (l1i11i11il = sparseArray.get(i)) != null) {
                if (l1i11i11il.mConfiguration.equals(il11IlIllI.mResources.getConfiguration()) && ((il11IlIllI.mTheme == null && l1i11i11il.mThemeHash == 0) || (il11IlIllI.mTheme != null && l1i11i11il.mThemeHash == il11IlIllI.mTheme.hashCode()))) {
                    return l1i11i11il.mValue;
                }
                sparseArray.remove(i);
            }
            return null;
        }
    }

    private static void addColorStateListToCache(Il11IlIllI il11IlIllI, int i, ColorStateList colorStateList, Resources$Theme resources$Theme) {
        synchronized (sColorStateCacheLock) {
            WeakHashMap<Il11IlIllI, SparseArray<l1I11I11Il>> weakHashMap = sColorStateCaches;
            SparseArray<l1I11I11Il> sparseArray = weakHashMap.get(il11IlIllI);
            if (sparseArray == null) {
                sparseArray = new SparseArray<>();
                weakHashMap.put(il11IlIllI, sparseArray);
            }
            sparseArray.append(i, new l1I11I11Il(colorStateList, il11IlIllI.mResources.getConfiguration(), resources$Theme));
        }
    }

    private static boolean isColorInt(Resources resources, int i) throws Resources$NotFoundException {
        TypedValue typedValue = getTypedValue();
        resources.getValue(i, typedValue, true);
        return typedValue.type >= 28 && typedValue.type <= 31;
    }

    private static TypedValue getTypedValue() {
        ThreadLocal<TypedValue> threadLocal = sTempTypedValue;
        TypedValue typedValue = threadLocal.get();
        if (typedValue != null) {
            return typedValue;
        }
        TypedValue typedValue2 = new TypedValue();
        threadLocal.set(typedValue2);
        return typedValue2;
    }

    public static float getFloat(Resources resources, int i) throws Resources$NotFoundException, CertificateNotYetValidException, UnknownHostException, CertificateExpiredException {
        if (lI11IlI1lI.IlIIlIllI1(I1I1lI1II1.a(new byte[]{122, 9, 11, 33, 90, 77, 95}), I1I1lI1II1.a(new byte[]{71, 8, 86, 81, 55, 83, 70, 102, 85, 61, 74, 66, 118, 91, 78, 111, 16, 14, 49, 94, 84, 86, 96, 97, 101, 65}))) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{81, 29, 83, 14, 59, 6, 86, 0, 96, 28}));
        }
        if (Build$VERSION.SDK_INT >= 29) {
            float f = llI1l1111l.getFloat(resources, i);
            if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{71, 37, 8, 83, 86, 116, 89, 7, 85, 29, 118, 72, 70, 99, 112, 116, 87, 50, 36, 67, 125}), 1380)) {
                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{91, 11, 19, 0, 55, 65, 78, 106, 12, 40, 114, 70, 64, 109, 100, 120, 15, 9, 49, 116, 91, 45, 89, 89, 98, 124, 111, 22, 5, 103, 15, 6}));
            }
            return f;
        }
        TypedValue typedValue = getTypedValue();
        resources.getValue(i, typedValue, true);
        if (typedValue.type != 4) {
            throw new Resources$NotFoundException(I1I1lI1II1.a(new byte[]{101, 1, 17, 10, 23, 71, 84, 85, 25, 45, 116, 16, 22, 9, 76}) + Integer.toHexString(i) + I1I1lI1II1.a(new byte[]{23, 16, 27, 21, 7, 21, 20, 0, 65}) + Integer.toHexString(typedValue.type) + I1I1lI1II1.a(new byte[]{23, 13, 17, 69, 12, 90, 67, 16, 79, 5, 92, 89, 81}));
        }
        float f2 = typedValue.getFloat();
        if (Ill11ll111.IlIIlIllI1(I1I1lI1II1.a(new byte[]{102, 43, 51, 55, 47, 121, 92, 70, 64, 10, 95, 68, 96, 107, 98, 118, 43, 25, 32, 88, 64, 18, 66, 116, 97, 92, 100, 63, 50}), 167467247L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{68, 19, 43, 42, 22, 71, Byte.MAX_VALUE, 71, 14, 81, 6, 96, 108, 120, 109, 109, 83, 14, 1, 69, 74, 20, 106}));
        }
        return f2;
    }

    public static Typeface getFont(Context context, int i) throws Resources$NotFoundException {
        if (context.isRestricted()) {
            return null;
        }
        return loadFont(context, i, new TypedValue(), 0, null, null, false, false);
    }

    public static Typeface getCachedFont(Context context, int i) throws Resources$NotFoundException, UnrecoverableKeyException, UnknownServiceException {
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.llII1ll111(415834056L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{79, 33, 84, 18, 11, 95, 92, 118, 92, 53, 118, 69, 98, 111, 98, 100, 20, 38, 56, 87, 95, 54, 113}));
        }
        if (context.isRestricted()) {
            if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{85, 8, 49, 45, 43, 64, 71, 9, 112, 87, 102, 116, 77, 119, 113, 76, 56, 89, 87, 68, 126, 49, 100, 70, 68, 5, 113, 84, 11, 11}), 975512986L)) {
                throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{94, 84, 54, 21, 53, 92, 123, 95, 72, 38, 120, 69, 88, 99, 120, 65, 20, 80, 5, 11, 69, 34, 103, 100, 123, 100}));
            }
            return null;
        }
        Typeface typefaceLoadFont = loadFont(context, i, new TypedValue(), 0, null, null, false, true);
        if (androidx.core.location.lIIlI111II.IlIlII11Il(3388)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{70, 39, 50, 32, 8, 2, 2, 94, 120, 49, 3, 7, 76, 118, 12, 79, 9, 85, 22, 112, 119, 7, 123, 101, 2, 90, 4, 42}));
        }
        return typefaceLoadFont;
    }

    public static void getFont(Context context, int i, llI1l11lII lli1l11lii, Handler handler) throws KeyException, Resources$NotFoundException, UnrecoverableKeyException {
        if (l1lll111II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{93, 84, 3, 92, 20, 96, 84, 103, 112, 86, 85, 83, 121, 83, 119, 0, 20, 44, 46, 103, 106, 9, 100, 119, 88, 70, 121, 2, 47, 65, 88}))) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{70, 53, 13, 7, 19, 3, 122, 65, 111, 47, 5}));
        }
        llllIllIII.checkNotNull(lli1l11lii);
        if (context.isRestricted()) {
            lli1l11lii.callbackFailAsync(-4, handler);
            return;
        }
        loadFont(context, i, new TypedValue(), 0, lli1l11lii, handler, false, false);
        if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{92, 30, 59, 7, 50, 79, 3, Byte.MAX_VALUE, 111, 37, 115, 84, 93, 0, 95, 96, 85, 38, 16, 6, 3, 89, 99, 109, 102, 96, 120}), 7021)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{90, 42, 4, 60, 37, 68, 83, 81, 114, 0, 124, 93, 95}));
        }
    }

    public static Typeface getFont(Context context, int i, TypedValue typedValue, int i2, llI1l11lII lli1l11lii) throws MalformedURLException, Resources$NotFoundException, UnrecoverableKeyException {
        if (context.isRestricted()) {
            return null;
        }
        Typeface typefaceLoadFont = loadFont(context, i, typedValue, i2, lli1l11lii, null, true, false);
        if (Il1lII1l1l.I111IlIl1I(7853)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{123, 40}));
        }
        return typefaceLoadFont;
    }

    private static Typeface loadFont(Context context, int i, TypedValue typedValue, int i2, llI1l11lII lli1l11lii, Handler handler, boolean z, boolean z2) throws Resources$NotFoundException, UnrecoverableKeyException {
        Resources resources = context.getResources();
        resources.getValue(i, typedValue, true);
        Typeface typefaceLoadFont = loadFont(context, resources, typedValue, i, i2, lli1l11lii, handler, z, z2);
        if (typefaceLoadFont == null && lli1l11lii == null && !z2) {
            throw new Resources$NotFoundException(I1I1lI1II1.a(new byte[]{113, 11, 12, 17, 66, 71, 82, 67, 86, 17, 66, 83, 80, 25, 125, 113, 66, 66, 82, 74}) + Integer.toHexString(i) + I1I1lI1II1.a(new byte[]{23, 7, 13, 16, 14, 81, 23, 94, 86, 16, 16, 82, 80, 25, 70, 80, 22, 19, 11, 87, 70, 4, 87, 27}));
        }
        if (llIlII1IlI.l1ll11I11l(I1I1lI1II1.a(new byte[]{93, 12, 52, 18, 17, 95, 118, 114, 77, 20, 83, 93, 71, 73, 100, 82, 19, 9, 1, 2, 101, 89, 106, 88, 4}), 9207)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{126}));
        }
        return typefaceLoadFont;
    }

    /* JADX WARN: Removed duplicated region for block: B:47:0x01ba  */
    /* JADX WARN: Removed duplicated region for block: B:53:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static Typeface loadFont(Context context, Resources resources, TypedValue typedValue, int i, int i2, llI1l11lII lli1l11lii, Handler handler, boolean z, boolean z2) throws UnrecoverableKeyException {
        if (typedValue.string == null) {
            throw new Resources$NotFoundException(I1I1lI1II1.a(new byte[]{101, 1, 17, 10, 23, 71, 84, 85, 25, 70}) + resources.getResourceName(i) + I1I1lI1II1.a(new byte[]{21, 68, 74}) + Integer.toHexString(i) + I1I1lI1II1.a(new byte[]{30, 68, 11, 22, 66, 91, 88, 68, 25, 5, 16, 118, 90, 87, 64, 15, 66}) + typedValue);
        }
        String string = typedValue.string.toString();
        if (!string.startsWith(I1I1lI1II1.a(new byte[]{69, 1, 17, 74}))) {
            if (lli1l11lii != null) {
                lli1l11lii.callbackFailAsync(-3, handler);
            }
            return null;
        }
        Typeface typefaceFindFromCache = llllII11Il.findFromCache(resources, i, string, typedValue.assetCookie, i2);
        if (typefaceFindFromCache != null) {
            if (lli1l11lii != null) {
                lli1l11lii.callbackSuccessAsync(typefaceFindFromCache, handler);
            }
            return typefaceFindFromCache;
        }
        if (z2) {
            return null;
        }
        try {
            if (string.toLowerCase().endsWith(I1I1lI1II1.a(new byte[]{25, 28, 15, 9}))) {
                I1I1Il1I1I i1I1Il1I1I = l111l1llIl.parse(resources.getXml(i), resources);
                if (i1I1Il1I1I == null) {
                    Log.e(TAG, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 86, 89, 91, 93, 20, 83, 13, 15, 22, 31, 86, 0, 94, 92, 95, 79, 23, 17, 5, 85}));
                    if (lli1l11lii != null) {
                        lli1l11lii.callbackFailAsync(-3, handler);
                    }
                    if (lII1llllI1.IlIIl111lI(I1I1lI1II1.a(new byte[]{6, 28, 11}), 2844)) {
                        throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{79, 11, 58, 0, 5, 84, 114, 104, 9, 81, 122, 73, 3, 120, 98, 5, 10, 87, 10, 100, 114, 22, 102, 71, 116, Byte.MAX_VALUE, 14, 85, 37, 72, 64}));
                    }
                    return null;
                }
                Typeface typefaceCreateFromResourcesFamilyXml = llllII11Il.createFromResourcesFamilyXml(context, i1I1Il1I1I, resources, i, string, typedValue.assetCookie, i2, lli1l11lii, handler, z);
                if (lIlIII1I1l.IlIllIll1I(228346619L)) {
                    throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{98, 12, 33, 43, 45, 70}));
                }
                return typefaceCreateFromResourcesFamilyXml;
            }
            Typeface typefaceCreateFromResourcesFontFile = llllII11Il.createFromResourcesFontFile(context, resources, i, string, typedValue.assetCookie, i2);
            if (lli1l11lii != null) {
                if (typefaceCreateFromResourcesFontFile != null) {
                    lli1l11lii.callbackSuccessAsync(typefaceCreateFromResourcesFontFile, handler);
                } else {
                    lli1l11lii.callbackFailAsync(-3, handler);
                }
            }
            return typefaceCreateFromResourcesFontFile;
        } catch (IOException e) {
            Log.e(TAG, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 66, 85, 84, 93, 20, 77, 15, 13, 66, 64, 85, 18, 92, 64, 65, 85, 82, 69}) + string, e);
            if (lli1l11lii != null) {
                return null;
            }
            lli1l11lii.callbackFailAsync(-3, handler);
            return null;
        } catch (XmlPullParserException e2) {
            Log.e(TAG, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 64, 81, 71, 74, 81, 21, 26, 12, 14, 18, 66, 4, 64, 90, 70, 68, 84, 0, 68}) + string, e2);
            if (lli1l11lii != null) {
            }
        }
    }

    private lIllI1lIlI() {
    }
}
