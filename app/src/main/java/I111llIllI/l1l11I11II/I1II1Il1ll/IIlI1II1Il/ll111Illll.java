package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lI1l1I1l1l;
import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$Theme;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.media.content.IIl1l1IllI;
import android.media.content.Il1llIl111;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.AttributeSet;
import android.util.Log;
import android.util.TypedValue;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.IOException;
import java.io.InvalidClassException;
import java.io.InvalidObjectException;
import java.net.BindException;
import java.security.InvalidKeyException;
import java.security.KeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CertificateParsingException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.RejectedExecutionException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import org.xmlpull.v1.XmlPullParser;

/* loaded from: classes.dex */
public class ll111Illll {
    private static final String NAMESPACE = I1I1lI1II1.a(new byte[]{95, 16, 22, 21, 88, 26, 24, 67, 90, 12, 85, 93, 84, 74, 26, 84, 12, 5, 16, 93, 89, 5, 29, 86, 92, 91, 24, 4, 20, 89, 24, 17, 82, 23, 77, 4, 12, 81, 69, 95, 80, 0});

    public static boolean hasAttribute(XmlPullParser xmlPullParser, String str) {
        boolean z = xmlPullParser.getAttributeValue(NAMESPACE, str) != null;
        if (llIlII1IlI.I1lI11IIll(I1I1lI1II1.a(new byte[]{89, 19, 1, 80, 8, 13, 15, 72, 67, 49, 92, 121, 123, 78, 3, 67, 40, 39}), 338711950L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{69, 93, 24, 83, 21, 116, 65, 89, 76, 10}));
        }
        return z;
    }

    public static float getNamedFloat(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i, float f) throws NoSuchAlgorithmException, InvalidObjectException {
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{7, 60, 33, 8, 83, 7, 89, 68, 94}), 411259103L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{64, 3, 0, 14, 32, 77, 111, 64, 106, 41, 104, 69, 100, 9, 86, 77, 22, 4, 36, 115, 104, 46, 124, 6, 7, 88, 120, 40, 2}));
        }
        if (!hasAttribute(xmlPullParser, str)) {
            return f;
        }
        float f2 = typedArray.getFloat(i, f);
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{101, 40, 90, 92, 3, 12, 7, 86, 110, 61, 95}), 187662773L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{89, 0, 38}));
        }
        return f2;
    }

    public static boolean getNamedBoolean(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i, boolean z) throws InvalidKeyException {
        if (!hasAttribute(xmlPullParser, str)) {
            if (lI1lllIII1.Il1IIlI1II(1447)) {
                throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{101, 28, 11, 43, 80, 86}));
            }
            return z;
        }
        return typedArray.getBoolean(i, z);
    }

    public static int getNamedInt(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i, int i2) throws CharConversionException {
        if (!hasAttribute(xmlPullParser, str)) {
            return i2;
        }
        int i3 = typedArray.getInt(i, i2);
        if (llIlII1IlI.lI11llll1I(I1I1lI1II1.a(new byte[]{120, 52, 19, 42, 8, 2, 118, 6, 112, 9, 73, 95, 65, 65, 123, 93, 14, 35, 43, 104, Byte.MAX_VALUE, 11, 114, 70, Byte.MAX_VALUE}), 384610089L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{96, 5, 83, 84, 41, 95, 0, 67, 112, 48, 95, 114, 118, 11, 109, 111}));
        }
        return i3;
    }

    public static int getNamedColor(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i, int i2) throws BrokenBarrierException {
        if (IIlII1IIIl.I11II1I1I1(183699172L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{93, 12, 50, 29, 59}));
        }
        return !hasAttribute(xmlPullParser, str) ? i2 : typedArray.getColor(i, i2);
    }

    public static l1ll11l111 getNamedComplexColor(TypedArray typedArray, XmlPullParser xmlPullParser, Resources$Theme resources$Theme, String str, int i, int i2) throws CloneNotSupportedException {
        if (hasAttribute(xmlPullParser, str)) {
            TypedValue typedValue = new TypedValue();
            typedArray.getValue(i, typedValue);
            if (typedValue.type >= 28 && typedValue.type <= 31) {
                return l1ll11l111.from(typedValue.data);
            }
            l1ll11l111 l1ll11l111VarInflate = l1ll11l111.inflate(typedArray.getResources(), typedArray.getResourceId(i, 0), resources$Theme);
            if (l1ll11l111VarInflate != null) {
                if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{84, 12, 17, 40, 35, 94}), 484260994L)) {
                    throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{96, 60, 38, 29, 90, 92, 117, 87, 67, 45}));
                }
                return l1ll11l111VarInflate;
            }
        }
        return l1ll11l111.from(i2);
    }

    public static ColorStateList getNamedColorStateList(TypedArray typedArray, XmlPullParser xmlPullParser, Resources$Theme resources$Theme, String str, int i) throws IllegalAccessException, CertificateException {
        if (lIIlI111II.IIlI1Il1lI(1143814380L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{71, 1, 41, 2, 15, 109, 78, 87, 112, 81, 96, 64}));
        }
        if (!hasAttribute(xmlPullParser, str)) {
            if (lI1l1I1l1l.I1II1111ll(239098923L)) {
                throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{114, 35}));
            }
            return null;
        }
        TypedValue typedValue = new TypedValue();
        typedArray.getValue(i, typedValue);
        if (typedValue.type == 2) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 66, 85, 70, 86, 88, 67, 7, 65, 3, 70, 68, 19, 90, 87, 70, 66, 82, 69, 5, 70, 23, 10, 89, 0, 7, 29, 66}) + i + I1I1lI1II1.a(new byte[]{13, 68}) + typedValue);
        }
        if (typedValue.type >= 28 && typedValue.type <= 31) {
            return getNamedColorStateListFromInt(typedValue);
        }
        ColorStateList colorStateListInflate = I11llIIIII.inflate(typedArray.getResources(), typedArray.getResourceId(i, 0), resources$Theme);
        if (I1I1IIIIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{83, 9, 39, 3, 10, 90, 126, 4, 0, 2, 113, 74, 98, 76, 3, 1, 1, 24}), 3485)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{97, 93, 53, 13, 10, 103, 70, 72, 86, 54, 87, 95, 89, 96, 94, 7, 38, 81, 54, 90, 6, 9, 88, 0, 126, 80, 4}));
        }
        return colorStateListInflate;
    }

    private static ColorStateList getNamedColorStateListFromInt(TypedValue typedValue) {
        if (I1Ill1lIII.l1l1l1IIlI(7033)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{94, 20, 49, 36, 44, 94, 70, 92, 107, 5, 104, 101, 64, 104, 89, 69, 27, 43, 0, 80, 92, 15, 101, 118, 101, 6, 3, 49, 35, 85, 109}));
        }
        ColorStateList colorStateListValueOf = ColorStateList.valueOf(typedValue.data);
        if (IIlI1Il1lI.Il1IIlI1II(530464640L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{4, 13, 56, 50, 40, 109, 91, 2, 104, 55, 102, 85, 101, 1, 6, 77, 3, 10, 85, 74}));
        }
        return colorStateListValueOf;
    }

    public static int getNamedResourceId(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i, int i2) throws IOException {
        if (l1IIll1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{91}), 9232)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{126, 33, 19, 86, 53, 124}));
        }
        if (!hasAttribute(xmlPullParser, str)) {
            if (androidx.constraintlayout.widget.lIIlI111II.llII1ll111(1102195036L)) {
                throw new BindException(I1I1lI1II1.a(new byte[]{94, 32, 1, 52, 20, 100, 78, 121, 1, 29, 86, 101, 76, 124, 69, 13, 43, 20, 12, 92, 66, 21, 107, 12, 90, 1}));
            }
            return i2;
        }
        int resourceId = typedArray.getResourceId(i, i2);
        if (I1I1IIIIl1.l1l1l1IIlI(4073)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{116, 14, 8, 84, 47, 4, 123, 117, 97, 2, 4, 97, 12, 92, 7, 4, 24, 55, 59, 120, 117, 52, 2, 87, 124, 126, 124, 1}));
        }
        return resourceId;
    }

    public static String getNamedString(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i) throws CertificateParsingException {
        if (!hasAttribute(xmlPullParser, str)) {
            return null;
        }
        String string = typedArray.getString(i);
        if (l1l1I111I1.I111IlIl1I(207181472L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{118, 47, 50, 93, 19, 114, 68, 101, 111, 16, 69, 91, 95, 72, 83, 7, 39, 18, 37, 94, 1}));
        }
        return string;
    }

    public static TypedValue peekNamedValue(TypedArray typedArray, XmlPullParser xmlPullParser, String str, int i) throws CertificateNotYetValidException, NoSuchProviderException {
        if (Il1I1lllIl.IIlIl1Illl(I1I1lI1II1.a(new byte[]{66, 54}), I1I1lI1II1.a(new byte[]{1, 61, 0, 6, 35, 121, 67, 105}))) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{100, 47}));
        }
        if (!hasAttribute(xmlPullParser, str)) {
            return null;
        }
        TypedValue typedValuePeekValue = typedArray.peekValue(i);
        if (lI1lllIII1.I1lllI1llI(348937217L)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{82, 22, 4, 10, 44, 118, 65, 3, 82, 15, 96, 72, 95, 104, 108, 12, 82, 36, 5, 89, 9, 25, 69, 79, 93, 1, 121, 16}));
        }
        return typedValuePeekValue;
    }

    public static TypedArray obtainAttributes(Resources resources, Resources$Theme resources$Theme, AttributeSet attributeSet, int[] iArr) {
        if (resources$Theme == null) {
            return resources.obtainAttributes(attributeSet, iArr);
        }
        return resources$Theme.obtainStyledAttributes(attributeSet, iArr, 0, 0);
    }

    public static boolean getBoolean(TypedArray typedArray, int i, int i2, boolean z) throws KeyException {
        if (Il1llIl111.l1l1l1IIlI(298234406L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{100}));
        }
        boolean z2 = typedArray.getBoolean(i, typedArray.getBoolean(i2, z));
        if (!I1lllI11II.I111IlIl1I(641203973L)) {
            return z2;
        }
        Log.w(I1I1lI1II1.a(new byte[]{65, 13, 56}), I1I1lI1II1.a(new byte[]{7, 42, 38, 41}));
        return false;
    }

    public static Drawable getDrawable(TypedArray typedArray, int i, int i2) {
        Drawable drawable = typedArray.getDrawable(i);
        return drawable == null ? typedArray.getDrawable(i2) : drawable;
    }

    public static int getInt(TypedArray typedArray, int i, int i2, int i3) {
        if (l1lll111II.Il1IIlI1II(160799825L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{97, 54, 11, 43}));
        }
        int i4 = typedArray.getInt(i, typedArray.getInt(i2, i3));
        if (Il1IIllIll.llll111lI1(I1I1lI1II1.a(new byte[]{91, 33, 58, 45, 90, 93, 110, 116, 117, 81, 67, 5, 115, 112, 124, 103, 0, 34, 41, 0}), 479110686L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{80, 35, 22, 8, 80, 121, 99, 120, 84, 37, 92, 93, 93, 90, 99, 5, 8, 35, 3, 96, 89, 4, 98, 103, 81, Byte.MAX_VALUE, 77, 33}));
        }
        return i4;
    }

    public static int getResourceId(TypedArray typedArray, int i, int i2, int i3) throws EOFException {
        if (IIIlIll111.IlII1Illll(450833689L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{82, 21, 37, 9, 85, 77, 84, 65, 108, 53, 105, 103, 121, 72, 7, 125, 84, 84, 15, 100, 2, 41, 0, 126, 120, 89}));
        }
        return typedArray.getResourceId(i, typedArray.getResourceId(i2, i3));
    }

    public static String getString(TypedArray typedArray, int i, int i2) throws ClassNotFoundException {
        String string = typedArray.getString(i);
        if (string == null) {
            string = typedArray.getString(i2);
        }
        if (Ill11ll111.I111IlIl1I(175042232L)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{77, 19, 27, 40, 52, 69, 124, 71, 1, 0}));
        }
        return string;
    }

    public static CharSequence getText(TypedArray typedArray, int i, int i2) {
        CharSequence text = typedArray.getText(i);
        return text == null ? typedArray.getText(i2) : text;
    }

    public static CharSequence[] getTextArray(TypedArray typedArray, int i, int i2) throws CloneNotSupportedException {
        CharSequence[] textArray = typedArray.getTextArray(i);
        if (textArray == null) {
            textArray = typedArray.getTextArray(i2);
        }
        if (II1lllllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{84, 55, 90, 31, 80, 88, 79, 4, 120, 41, 87, 98, 123, 113, 98, 94, 18, 8, 90}), 398480128L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{71, 28, 87, 16, 84, 13, 120, 73, 76, 61, 105, 103, 82, 87, 99, 4, 9, 20}));
        }
        return textArray;
    }

    public static int getAttr(Context context, int i, int i2) throws InvalidClassException {
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(i, typedValue, true);
        if (typedValue.resourceId != 0) {
            return i;
        }
        if (IIIlIl1I1l.Ill1lIIlIl(8385)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{14, 45, 1, 41, 43, 80, 64, Byte.MAX_VALUE, 64, 84, 6, 83, 80, 88, 101, 71}));
        }
        return i2;
    }

    private ll111Illll() {
    }
}
