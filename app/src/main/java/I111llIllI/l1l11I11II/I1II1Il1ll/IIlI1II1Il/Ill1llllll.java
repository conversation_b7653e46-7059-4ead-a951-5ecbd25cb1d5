package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.lI1l1I1l1l;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.content.res.Resources$NotFoundException;
import android.content.res.Resources$Theme;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.CharConversionException;
import java.net.NoRouteToHostException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class Ill1llllll {
    private Ill1llllll() {
    }

    static ColorStateList getColorStateList(Resources resources, int i, Resources$Theme resources$Theme) throws Resources$NotFoundException, NoRouteToHostException {
        ColorStateList colorStateList = resources.getColorStateList(i, resources$Theme);
        if (l1lI1I1l11.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{117, 45, 49, 29, 47, 66}), 401026909L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{111, 29, 27, 40}));
        }
        return colorStateList;
    }

    static int getColor(Resources resources, int i, Resources$Theme resources$Theme) throws CharConversionException {
        if (lI1l1I1l1l.l11I11I11l(8699)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{66, 0, 84, 7, 41, 70, 117, Byte.MAX_VALUE, 93, 61}));
        }
        return resources.getColor(i, resources$Theme);
    }
}
