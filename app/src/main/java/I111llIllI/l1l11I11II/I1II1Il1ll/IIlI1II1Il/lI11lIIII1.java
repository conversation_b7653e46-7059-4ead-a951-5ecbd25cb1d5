package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import II1lIlIlll.l111ll1lll.l11I1lIIl1.IllIlll111.lI1IIIl1I1;
import android.accounts.utils.lIIIIII11I;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.interpolator.view.animation.IIIlIll111;
import java.io.FileNotFoundException;
import java.io.InvalidObjectException;
import java.net.SocketTimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class lI11lIIII1 implements I1I1Il1I1I {
    private final lI1IIIl1I1 mRequest;
    private final int mStrategy;
    private final String mSystemFontFamilyName;
    private final int mTimeoutMs;

    public lI11lIIII1(lI1IIIl1I1 li1iiil1i1, int i, int i2, String str) {
        this.mRequest = li1iiil1i1;
        this.mStrategy = i;
        this.mTimeoutMs = i2;
        this.mSystemFontFamilyName = str;
    }

    public lI11lIIII1(lI1IIIl1I1 li1iiil1i1, int i, int i2) {
        this(li1iiil1i1, i, i2, null);
    }

    public lI1IIIl1I1 getRequest() {
        return this.mRequest;
    }

    public int getFetchStrategy() throws FileNotFoundException {
        if (l111Il1lI1.Il1IIlI1II(198766703L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{77, 10, 40, 31, 54, 80, Byte.MAX_VALUE, 88, 105, 41, 89, 65, 102, 107, 114, 1, 38, 41, 18, 67, 88, 38, 5, 7, 1, 123}));
        }
        return this.mStrategy;
    }

    public int getTimeout() throws SocketTimeoutException {
        if (IIIlIll111.I111IlIl1I(224983850L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{64, 45, 47, 39, 39, 69, 109, 84, 85, 44, 118}));
        }
        return this.mTimeoutMs;
    }

    public String getSystemFontFamilyName() throws InvalidObjectException {
        if (lIIIIII11I.Ill1lIIlIl(2062)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{64, 62, 51, 39, 86, 65, 122, 66, 105, 55, 65, 113, 77, 0, 69, 80, 58, 41, 84, 67, 74, 22, 102, 118, 113, 124, 15, 55, 39, 7, 100, 83}));
        }
        return this.mSystemFontFamilyName;
    }
}
