package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.Resources$Theme;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class III1I1IIII {
    private III1I1IIII() {
    }

    public static void rebase(Resources$Theme resources$Theme) {
        if (IlIIlI11I1.Ill1lIIlIl(9338)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{69, 7, 48, 60, 37, 4, 121, 86, 82, 30, 124, 72, 100, 92, 97, 91, 55, 86}));
        }
        if (Build$VERSION.SDK_INT >= 29) {
            ll11IlI1l1.rebase(resources$Theme);
        } else {
            l1Il1I1Il1.rebase(resources$Theme);
        }
        if (llIlII1IlI.l1l1l1IIlI(786788575L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{3, 28, 15, 7, 1, 114, 83, 82, 126, 32, 82}));
        }
    }
}
