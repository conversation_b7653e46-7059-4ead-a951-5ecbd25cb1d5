package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lIIIIII11I;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.I1111IIl11;
import androidx.core.location.IllIlllIII;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.NotActiveException;
import java.io.SyncFailedException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.security.DigestException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import java.security.NoSuchProviderException;
import java.util.concurrent.CancellationException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\r\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u001b\u0010\u0004\u001a\u00020\u0003*\u00020\u00002\u0006\u0010\u0002\u001a\u00020\u0001H\u0002¢\u0006\u0004\b\u0004\u0010\u0005\u001a\u001b\u0010\u0007\u001a\u00020\u0006*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001¢\u0006\u0004\b\u0007\u0010\b\u001a\u001d\u0010\t\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\t\u0010\n\u001a\u001d\u0010\f\u001a\u00020\u000b*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\f\u0010\r\u001a\u001b\u0010\u000f\u001a\u00020\u000e*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001¢\u0006\u0004\b\u000f\u0010\u0010\u001a\u001d\u0010\u0011\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u0011\u0010\n\u001a\u001d\u0010\u0012\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u0012\u0010\n\u001a\u001d\u0010\u0014\u001a\u00020\u0013*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u0014\u0010\u0015\u001a\u001b\u0010\u0016\u001a\u00020\u000e*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001¢\u0006\u0004\b\u0016\u0010\u0010\u001a\u001d\u0010\u0018\u001a\u00020\u0017*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u0018\u0010\u0019\u001a\u001b\u0010\u001a\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001¢\u0006\u0004\b\u001a\u0010\n\u001a\u001b\u0010\u001b\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001¢\u0006\u0004\b\u001b\u0010\n\u001a\u001d\u0010\u001c\u001a\u00020\u0001*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u001c\u0010\n\u001a\u001d\u0010\u001e\u001a\u00020\u001d*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\u001e\u0010\u001f\u001a#\u0010\"\u001a\b\u0012\u0004\u0012\u00020!0 *\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b\"\u0010#\u001a\u001d\u0010$\u001a\u00020!*\u00020\u00002\b\b\u0001\u0010\u0002\u001a\u00020\u0001H\u0007¢\u0006\u0004\b$\u0010%\u001a.\u0010(\u001a\u00028\u0000\"\u0004\b\u0000\u0010&*\u00020\u00002\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0000\u0012\u0004\u0012\u00028\u00000'H\u0086\b¢\u0006\u0004\b(\u0010)"}, d2 = {"Landroid/content/res/TypedArray;", "", "p0", "", "checkAttribute", "(Landroid/content/res/TypedArray;I)V", "", "getBooleanOrThrow", "(Landroid/content/res/TypedArray;I)Z", "getColorOrThrow", "(Landroid/content/res/TypedArray;I)I", "Landroid/content/res/ColorStateList;", "getColorStateListOrThrow", "(Landroid/content/res/TypedArray;I)Landroid/content/res/ColorStateList;", "", "getDimensionOrThrow", "(Landroid/content/res/TypedArray;I)F", "getDimensionPixelOffsetOrThrow", "getDimensionPixelSizeOrThrow", "Landroid/graphics/drawable/Drawable;", "getDrawableOrThrow", "(Landroid/content/res/TypedArray;I)Landroid/graphics/drawable/Drawable;", "getFloatOrThrow", "Landroid/graphics/Typeface;", "getFontOrThrow", "(Landroid/content/res/TypedArray;I)Landroid/graphics/Typeface;", "getIntOrThrow", "getIntegerOrThrow", "getResourceIdOrThrow", "", "getStringOrThrow", "(Landroid/content/res/TypedArray;I)Ljava/lang/String;", "", "", "getTextArrayOrThrow", "(Landroid/content/res/TypedArray;I)[Ljava/lang/CharSequence;", "getTextOrThrow", "(Landroid/content/res/TypedArray;I)Ljava/lang/CharSequence;", "R", "Lkotlin/Function1;", "use", "(Landroid/content/res/TypedArray;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public final class lll1IIII11 {
    private static final void checkAttribute(TypedArray typedArray, int i) throws NotActiveException {
        if (I1lllI11II.l1Il11I1Il(I1I1lI1II1.a(new byte[]{118, 53, 90, 40, 83, 66, 117, 118, 12, 45, 4, 82, 90, 87, 96, 89, 37, 55, 41, 83, 0, 15, 3, 81, 91, 98, 124, 55, 18, 6}), 161762166L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{118, 53, 15, 35, 21, 103, 99, 74, 107, 29, 1, 123, 0, Byte.MAX_VALUE, 110, 12, 21, 80, 51, 118, 125}));
        }
        if (!typedArray.hasValue(i)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 16, 22, 23, 11, 87, 66, 68, 92, 68, 94, 95, 65, 25, 80, 80, 4, 8, 12, 87, 84, 65, 90, 91, 19, 69, 82, 17, 74}));
        }
        if (lII1llllI1.llII1lIIlI(213968930L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{97, 20, 20, 43, 14, 100, 82, 104, 110, 11, 74, 5, 103, 84, 121, 102, 46, 32, 22, 10, 72, 44, 114, 100, 122, 111, 124, 4, 11, 113, 124, 2}));
        }
    }

    public static final boolean getBooleanOrThrow(TypedArray typedArray, int i) throws SyncFailedException, NotActiveException {
        if (lI11IlI1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{65, 0, 43, 52, 86, 122, 70, 105, 105, 61}), 1079839512L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{6, 46, 6, 93}));
        }
        checkAttribute(typedArray, i);
        return typedArray.getBoolean(i, false);
    }

    public static final int getColorOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        checkAttribute(typedArray, i);
        return typedArray.getColor(i, 0);
    }

    public static final ColorStateList getColorStateListOrThrow(TypedArray typedArray, int i) throws DigestException, NotActiveException {
        if (lIlIII1I1l.II1111I11I(I1I1lI1II1.a(new byte[]{116, 16, 38, 15, 9, 71, 110, 89, 105, 49, 126, 94, 112, 96, 86, 91, 14, 38, 1, 5, 9, 16, 86, 121, 122, 67}), I1I1lI1II1.a(new byte[]{116, 81, 1, 3, 4, 97}))) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{81, 29, 85, 43, 14, 13, 126, 126, 111}));
        }
        checkAttribute(typedArray, i);
        ColorStateList colorStateList = typedArray.getColorStateList(i);
        if (colorStateList != null) {
            return colorStateList;
        }
        throw new IllegalStateException(I1I1lI1II1.a(new byte[]{118, 16, 22, 23, 11, 87, 66, 68, 92, 68, 70, 81, 89, 76, 81, 21, 21, 0, 17, 18, 94, 14, 71, 21, 82, 22, 84, 10, 8, 93, 69, 67, 88, 22, 66, 6, 13, 89, 88, 66, 25, 23, 68, 81, 65, 92, 20, 89, 11, 18, 22, 28}).toString());
    }

    public static final float getDimensionOrThrow(TypedArray typedArray, int i) throws SocketTimeoutException, NotActiveException {
        if (l1l1IllI11.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{64, 48, 82, 42, 17, 66, 14, 90, 80, 22, 84, 102, 114, 123, 87, 118, 44, 34, 36, 124, 93, 25, 73, 80, 89, 0, 68, 44, 0, Byte.MAX_VALUE, 99, 37}), 163630333L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{79, 17, 21, 46, 15, 77, 71, 116, 13, 53, 125, 3, 79, 91, 92, 101, 9, 16}));
        }
        checkAttribute(typedArray, i);
        return typedArray.getDimension(i, 0.0f);
    }

    public static final int getDimensionPixelOffsetOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        checkAttribute(typedArray, i);
        int dimensionPixelOffset = typedArray.getDimensionPixelOffset(i, 0);
        if (IIll1llI1l.Ill1lIIlIl(961)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{66}));
        }
        return dimensionPixelOffset;
    }

    public static final int getDimensionPixelSizeOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        checkAttribute(typedArray, i);
        int dimensionPixelSize = typedArray.getDimensionPixelSize(i, 0);
        if (IIIlIll111.I1lllI1llI(181142271L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{103, 28, 91, 45, 59, 69, 0, 121, 77, 23, 94, 101, 111, 75, 64, 67, 33, 24, 0, 70, 106, 80}));
        }
        return dimensionPixelSize;
    }

    public static final Drawable getDrawableOrThrow(TypedArray typedArray, int i) throws NotActiveException, NoSuchProviderException {
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{116, 29, 15, 61, 23, 115}))) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{113, 50, 39, 0, 36, 3, 78, 81, 67, 10, 106, 64, 93, 115, 91, 99, 5, 81, 44, 90, 116, 17}));
        }
        checkAttribute(typedArray, i);
        Drawable drawable = typedArray.getDrawable(i);
        Intrinsics.a(drawable);
        if (IIl1l1IllI.Ill1lIIlIl(9722)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{71}));
        }
        return drawable;
    }

    public static final float getFloatOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        checkAttribute(typedArray, i);
        return typedArray.getFloat(i, 0.0f);
    }

    public static final Typeface getFontOrThrow(TypedArray typedArray, int i) throws InstantiationException, NotActiveException, KeyManagementException {
        if (IIlI1ll1ll.llll111lI1(I1I1lI1II1.a(new byte[]{86, 40, 37, 14, 13, 111, 65, 119, 86, 82, 71, 103, 69, 14, 112, 115, 91, 41}))) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{111, 83, 48, 46, 27, 124, 101, 91, 105, 46, 92, 116, 88, Byte.MAX_VALUE, 6, 76}));
        }
        checkAttribute(typedArray, i);
        Typeface font = l11llI1lI1.getFont(typedArray, i);
        if (lIIIIII11I.I111IlIl1I(437039524L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{64, 80, 44, 38, 8, 1, 64, Byte.MAX_VALUE, 76, 19, 83, 9, 102, 126, 71, 112}));
        }
        return font;
    }

    public static final int getIntOrThrow(TypedArray typedArray, int i) throws IllegalAccessException, NotActiveException, KeyManagementException {
        if (lIlIII1I1l.I1lIllll1l(196060583L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{92, 35, 14, 40, 15, 2, 71, 100, 64, 30}));
        }
        checkAttribute(typedArray, i);
        int i2 = typedArray.getInt(i, 0);
        if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{116, 29, 90, 43, 27, 66, 2, 96, 111, 32, 119, 95}), 10730)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{98}));
        }
        return i2;
    }

    public static final int getIntegerOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        if (lIIlI111II.I1IIl11lll(223240011L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{125, 45, 59, 7, 1}));
        }
        checkAttribute(typedArray, i);
        return typedArray.getInteger(i, 0);
    }

    public static final int getResourceIdOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{112, 86, 9, 52, 91, 118, 68, 102, 110}), 2659)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{118, 86, 56, 50, 38, 67, 112, 68, 12, 10, 0, 86, 77, 65, 94, 6, 10, 13, 37, 65, 87, 42, 85, 120, 67, 15}));
        }
        checkAttribute(typedArray, i);
        int resourceId = typedArray.getResourceId(i, 0);
        if (llIlI11III.I1lllI1llI(120)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{1, 93, 14, 40, 40, 109, 102}));
        }
        return resourceId;
    }

    public static final String getStringOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        checkAttribute(typedArray, i);
        String string = typedArray.getString(i);
        if (string == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{118, 16, 22, 23, 11, 87, 66, 68, 92, 68, 70, 81, 89, 76, 81, 21, 1, 14, 23, 94, 84, 65, 93, 90, 71, 22, 85, 0, 68, 81, 88, 6, 69, 7, 7, 1, 66, 65, 88, 16, 106, 16, 66, 89, 91, 94, 26}).toString());
        }
        if (l1l1IllI11.IIll1I11lI(845069209L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{65, 13, 58}));
        }
        return string;
    }

    public static final CharSequence getTextOrThrow(TypedArray typedArray, int i) throws NotActiveException, UnknownHostException {
        checkAttribute(typedArray, i);
        CharSequence text = typedArray.getText(i);
        if (text == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{118, 16, 22, 23, 11, 87, 66, 68, 92, 68, 70, 81, 89, 76, 81, 21, 1, 14, 23, 94, 84, 65, 93, 90, 71, 22, 85, 0, 68, 81, 88, 6, 69, 7, 7, 1, 66, 65, 88, 16, 122, 12, 81, 66, 102, 92, 69, 64, 7, 15, 1, 87, 30}).toString());
        }
        if (Il1I1lllIl.III111l111(I1I1lI1II1.a(new byte[]{90, 15, 85, 33, 45, 13, 92, 66, 0, 82, 72, 99, 124, 67, 0, 67, 44, 10, 83, 94, 86, 40, 121, 111, 105}), 182515986L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{111}));
        }
        return text;
    }

    public static final CharSequence[] getTextArrayOrThrow(TypedArray typedArray, int i) throws NotActiveException {
        if (I1111IIl11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{101, 44, 33, 19, 27, 66, 109, 95, 10, 40, 85, 106, Byte.MAX_VALUE, 87, 65, 86, 85, 81, 26}), 248852533L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{88, 3, 55, 51, 12, 88, 81, 85, 112, 86, 115, 71, 108, 88, 67, 81, 4, 4, 13, 7, 95}));
        }
        checkAttribute(typedArray, i);
        return typedArray.getTextArray(i);
    }

    public static final <R> R use(TypedArray typedArray, Function1<? super TypedArray, ? extends R> function1) throws IllegalAccessException {
        R rInvoke = function1.invoke(typedArray);
        typedArray.recycle();
        if (lII1llllI1.l11I11I11l(263205825L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{1}));
        }
        return rInvoke;
    }
}
