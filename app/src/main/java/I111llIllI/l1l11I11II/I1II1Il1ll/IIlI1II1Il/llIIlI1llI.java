package I111llIllI.l1l11I11II.I1II1Il1ll.IIlI1II1Il;

import android.content.res.Resources;
import androidx.constraintlayout.widget.l111Il1lI1;
import java.net.UnknownHostException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class llIIlI1llI {
    private llIIlI1llI() {
    }

    public static int getDensityDpi(Resources resources) throws UnknownHostException {
        int i = resources.getConfiguration().densityDpi;
        if (l111Il1lI1.I1II1111ll(681232605L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 8, 81, 41, 59, 123, 124, 121, 13, 21, 125, 98, 98, 85, 122, 126, 39, 5, 33, 96, 96, 15, 92, 102, 85, 111, 118, 83, 8, 93, 95, 18}));
        }
        return i;
    }
}
