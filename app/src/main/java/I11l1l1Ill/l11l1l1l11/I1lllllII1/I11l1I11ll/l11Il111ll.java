package I11l1l1Ill.l11l1l1l11.I1lllllII1.I11l1I11ll;

import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.Il1l11I11I;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.net.NoRouteToHostException;
import java.util.HashSet;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.IlIlIIll11;
import l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.IllIIll11l;
import l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.l1lllIll1I;
import l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.llIIII1IlI;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class l11Il111ll implements IlIlIIll11 {
    private JSONObject a;
    private final l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.llIllIlll1 b;

    public l11Il111ll(l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.llIllIlll1 llillilll1) {
        this.b = llillilll1;
    }

    @Override // l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.IlIlIIll11
    public JSONObject a() {
        if (IIlIIlIII1.l11I11I11l(4824)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{126, 38, 10, 80, 52, 102, 112, 64, 75, 93, 97, 87, 12, 111, 117, 83, 91, 27, 15, 72, 64, 13, 80, 86, 90}));
        }
        JSONObject jSONObject = this.a;
        if (IIlII1IIIl.l11I11I11l(2378)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{4, 9, 3, 15, 21, 2}));
        }
        return jSONObject;
    }

    @Override // l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.IlIlIIll11
    public void a(JSONObject jSONObject) throws NoRouteToHostException {
        if (lIIlI111II.IIll1l1lII(5928)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{94, 23, 18, 14, 16, 66, 66, 85, 112, 61, 95, 85, 68}));
        }
        this.a = jSONObject;
        if (Il1l11I11I.IlII1Illll(243001228L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{112, 40, 15, 82, 39, 118, 95}));
        }
    }

    public void a(JSONObject jSONObject, HashSet<String> hashSet, long j) {
        this.b.b(new IllIIll11l(this, hashSet, jSONObject, j));
    }

    public void b() {
        this.b.b(new l1lllIll1I(this));
    }

    public void b(JSONObject jSONObject, HashSet<String> hashSet, long j) {
        this.b.b(new llIIII1IlI(this, hashSet, jSONObject, j));
    }
}
