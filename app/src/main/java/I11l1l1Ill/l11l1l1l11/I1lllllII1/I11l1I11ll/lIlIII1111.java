package I11l1l1Ill.l11l1l1l11.I1lllllII1.I11l1I11ll;

import I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.IllIIll11l;
import android.accounts.utils.lIIIIII11I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import android.view.View;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.EOFException;
import java.io.InterruptedIOException;
import java.io.InvalidObjectException;
import java.io.NotSerializableException;
import java.net.UnknownServiceException;
import java.security.KeyException;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.WeakHashMap;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.l1ll11l111;

/* loaded from: classes.dex */
public class lIlIII1111 {
    private final HashMap<View, String> a = new HashMap<>();
    private final HashMap<View, Il1Il1l1Il> b = new HashMap<>();
    private final HashMap<String, View> c = new HashMap<>();
    private final HashSet<View> d = new HashSet<>();
    private final HashSet<String> e = new HashSet<>();
    private final HashSet<String> f = new HashSet<>();
    private final HashMap<String, String> g = new HashMap<>();
    private final Map<View, Boolean> h = new WeakHashMap();
    private boolean i;

    private String a(View view) throws CertificateEncodingException {
        if (!view.isAttachedToWindow()) {
            return I1I1lI1II1.a(new byte[]{89, 11, 22, 36, 22, 65, 86, 83, 81, 1, 84});
        }
        if (b(view).booleanValue()) {
            return I1I1lI1II1.a(new byte[]{89, 11, 53, 12, 12, 81, 88, 71, Byte.MAX_VALUE, 11, 83, 69, 70});
        }
        HashSet hashSet = new HashSet();
        while (view != null) {
            String strA = l1ll11l111.a(view);
            if (strA != null) {
                return strA;
            }
            hashSet.add(view);
            Object parent = view.getParent();
            view = parent instanceof View ? (View) parent : null;
        }
        this.d.addAll(hashSet);
        return null;
    }

    private void a(IllIIll11l illIIll11l, l1Il111I11.Il1IlIllIl.I1lIlI1ll1.llIl1IlIll.lIlIII1111 liliii1111) {
        View view = illIIll11l.c().get();
        if (view == null) {
            return;
        }
        Il1Il1l1Il il1Il1l1Il = this.b.get(view);
        if (il1Il1l1Il != null) {
            il1Il1l1Il.a(liliii1111.getAdSessionId());
        } else {
            this.b.put(view, new Il1Il1l1Il(illIIll11l, liliii1111.getAdSessionId()));
        }
        if (Il11II1llI.l11I11I11l(6159)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{116, 38, 11, 85, 47, 83, 81, 104, 113, 13, 82, 98, 87, 119, 13, 65, 13, 57, 9}));
        }
    }

    private void a(l1Il111I11.Il1IlIllIl.I1lIlI1ll1.llIl1IlIll.lIlIII1111 liliii1111) throws InstantiationException {
        Iterator<IllIIll11l> it = liliii1111.d().iterator();
        while (it.hasNext()) {
            a(it.next(), liliii1111);
        }
        if (l1lI1I1l11.Ill1lIIlIl(8470)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{7, 80, 51, 10, 46, 124, 92, 84, 124, 50, 2, 81, 69, 119, 3, 108, 26, 37, 23, 69, 2, 41, 5, 69, 87, 123, 93, 8}));
        }
    }

    private Boolean b(View view) {
        if (lI11IlI1lI.III111l111(I1I1lI1II1.a(new byte[]{71, 5, 11, 51, 54, 88, 68, 67, 84, 3, 3, 82, 111, 87, 115, 115, 39, 39}), I1I1lI1II1.a(new byte[]{116, 80, 11, 32, 56, 83, 77, 102, 91, 81, 87, 4, 3, 125, 117, 101, 6, 87, 6, 5, 120, 0}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 34, 87, 9, 42, 87, 80, 103, 79, 39, 99, 124, 69, 79, 126}));
        }
        if (view.hasWindowFocus()) {
            this.h.remove(view);
            Boolean bool = Boolean.FALSE;
            if (lIIIIII11I.l111l1I1Il(I1I1lI1II1.a(new byte[]{65, 41, 83, 85, 85, 94, 86, 100, 13, 16, 9, 124, 93, 72, 6, 87, 1, 45, 47, 67, 69, 16, 80, 6, 1, 81, 123, 22, 2, 64, 15}), I1I1lI1II1.a(new byte[]{71, 29, 26, 43, 86, 118, 114, 122, 73, 51, 102, 103, 80, 113, 13, 87, 17, 23, 3, 0, 103, 11, 123, 126, 125, 110, 85, 54}))) {
                throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{90, 52, 16, 0, 6, 94, 7, 84, 120, 38, 83, 65, 94, 97, 103, 124, 91, 27, 54, 1, 106, 7, 75, 81, 6, 79, 68, 38, 6}));
            }
            return bool;
        }
        if (this.h.containsKey(view)) {
            return this.h.get(view);
        }
        Map<View, Boolean> map = this.h;
        Boolean bool2 = Boolean.FALSE;
        map.put(view, bool2);
        return bool2;
    }

    public View a(String str) throws UnknownServiceException {
        View view = this.c.get(str);
        if (lIIlI111II.I1lll11llI(7830)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{3, 20, 49, 51, 54, 109, 97, 126, 120, 17, 65, 106, 111}));
        }
        return view;
    }

    public void a() throws NoSuchProviderException {
        if (lII1llllI1.IlIllIll1I(183949207L)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{95, 10, 19, 81, 47, 1, 109, 72, 75, 6, 106, 8, 77, 78, 110}));
        }
        this.a.clear();
        this.b.clear();
        this.c.clear();
        this.d.clear();
        this.e.clear();
        this.f.clear();
        this.g.clear();
        this.i = false;
    }

    public String b(String str) throws InterruptedIOException {
        if (android.support.v4.graphics.drawable.l11Il111ll.Ill1lIIlIl(345506171L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{80, 42, 11, 10, 37, 64, 65, 1, 0, 14, 1, 86, 93, 84, 102, 124, 3, 84, 13, 89, 5, 4, 123, 3}));
        }
        String str2 = this.g.get(str);
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{97, 6, 54, 6, 19, 7, 100, 4, 64, 8, 87, 83, 100, 10, 92, 122}), 174191838L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{92, 86, 32, 39, 53, 7, 68, 87, 109, 43, 2, 89, 119, 73, 78, 120, 54, 21, 48, 72, 95, 49, 122, 79, 101, 119, 120, 82}));
        }
        return str2;
    }

    public HashSet<String> b() {
        return this.f;
    }

    public Il1Il1l1Il c(View view) throws CertificateException, EOFException {
        if (lIlIII1I1l.III11111Il(814888165L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{97, 39, 22, 81, 49, 4, 83, 96, 113, 18, 117, 8, 82, 95, 102, 113, 55, 11, 27, 121}));
        }
        Il1Il1l1Il il1Il1l1Il = this.b.get(view);
        if (il1Il1l1Il != null) {
            this.b.remove(view);
        }
        if (IIlI1Il1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{80, 84, 6, 52, 80, 12, 66, 124, 119, 22, 0, 113, 5, 92, 7, 121, 39, 56, 0}), 172940140L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{66, 13, 24, 61, 8, 83}));
        }
        return il1Il1l1Il;
    }

    public HashSet<String> c() {
        return this.e;
    }

    public String d(View view) throws InvalidObjectException, NotSerializableException, CertificateEncodingException {
        if (Il1I1lllIl.l1l1Il1I11(I1I1lI1II1.a(new byte[]{117, 10, 7, 40, 58, 101, 1, 123, 10, 5, 1, 126, 112, 117}), 221550931L)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{78, 48, 87, 21, 59, 89, 91, 92, 75}));
        }
        if (this.a.size() == 0) {
            if (IIlII1IIIl.III111l111(I1I1lI1II1.a(new byte[]{3, 92, 33, 43, 83, 64, 118, 116, 126, 6, 106}), 168981329L)) {
                throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{82, 41, 37, 4, 81, 84, 6, 88, 124, 92, 82, 101, 4, 72, 82}));
            }
            return null;
        }
        String str = this.a.get(view);
        if (str != null) {
            this.a.remove(view);
        }
        if (IIlIIlIII1.I1lllI1llI(247799560L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{68, 39, 15, 50, 9, 125, 97, 0, 82, 21, 9, 96, 65, 76, 88, 126, 43, 85, 85, 119, 74, 55, 11, 115, 73, 122, 111, 41}));
        }
        return str;
    }

    public void d() {
        this.i = true;
    }

    public llIllIlll1 e(View view) throws InterruptedException, CertPathValidatorException {
        if (I1IllIll1l.I1II1111ll(203580403L)) {
            Log.e(I1I1lI1II1.a(new byte[]{123, 46, 9, 61, 0, 121, 121, 2, 97, 47, 73, 6, 97, 104, 70, 7, 52, 47, 33, 83, 88, 35, 94, 114, 3, 70, 2, 46}), I1I1lI1II1.a(new byte[]{2, 22, 53, 50, 55, 89, 85, 66, 116, 21, 96, 126, 122, 105, 103, 91, 1, 38, 46, 118, 81, 82, 91, 108, 97, 121}));
            return null;
        }
        if (this.d.contains(view)) {
            llIllIlll1 llillilll1 = llIllIlll1.a;
            if (I11II1l1lI.ll1I1lII11(I1I1lI1II1.a(new byte[]{80, 7, 84, 11, 6, 109, 83, 81, 79, 82, 106, 74, 91, 1, 3, 87, 33, 53, 46, 91, 69, 83, 5, 7, 93}), 190140571L)) {
                throw new InterruptedException(I1I1lI1II1.a(new byte[]{103, 44, 85, 81, 4, 1, 95, 67, 126, 11}));
            }
            return llillilll1;
        }
        llIllIlll1 llillilll12 = this.i ? llIllIlll1.b : llIllIlll1.c;
        if (Il1I1lllIl.I11II1I1I1(I1I1lI1II1.a(new byte[]{116, 23, 23, 82}))) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{103, 51, 19, 81}));
        }
        return llillilll12;
    }

    public void e() throws InstantiationException, CertificateException, NoSuchProviderException {
        I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.llIllIlll1 llillilll1C = I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.llIllIlll1.c();
        if (llillilll1C != null) {
            for (l1Il111I11.Il1IlIllIl.I1lIlI1ll1.llIl1IlIll.lIlIII1111 liliii1111 : llillilll1C.a()) {
                View viewC = liliii1111.c();
                if (liliii1111.f()) {
                    String adSessionId = liliii1111.getAdSessionId();
                    if (viewC != null) {
                        String strA = a(viewC);
                        if (strA == null) {
                            this.e.add(adSessionId);
                            this.a.put(viewC, adSessionId);
                            a(liliii1111);
                        } else if (strA != I1I1lI1II1.a(new byte[]{89, 11, 53, 12, 12, 81, 88, 71, Byte.MAX_VALUE, 11, 83, 69, 70})) {
                            this.f.add(adSessionId);
                            this.c.put(adSessionId, viewC);
                            this.g.put(adSessionId, strA);
                        }
                    } else {
                        this.f.add(adSessionId);
                        this.g.put(adSessionId, I1I1lI1II1.a(new byte[]{89, 11, 35, 1, 52, 92, 82, 71}));
                    }
                }
            }
        }
    }

    public boolean f(View view) throws KeyException {
        if (!this.h.containsKey(view)) {
            if (II1lllllI1.IlIIl111lI(I1I1lI1II1.a(new byte[]{88, 93, 3, 40, 10, 2, 83, 122, 118, 52}), 337540611L)) {
                throw new KeyException(I1I1lI1II1.a(new byte[]{103, 30, 18, 29, 3, 89, 68, 81}));
            }
            return true;
        }
        this.h.put(view, Boolean.TRUE);
        if (androidx.interpolator.view.animation.lIIlI111II.ll1I111ll1(9317)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{89, 0, 23, 40, 80, 113, 4, 96, 76, 54, 2, 118, 81, 99, 122, 100, 16, 0, 3, 113, 70, 87, 105, 97, 91, 85, 99, 36, 52, 85, 83}));
        }
        return false;
    }
}
