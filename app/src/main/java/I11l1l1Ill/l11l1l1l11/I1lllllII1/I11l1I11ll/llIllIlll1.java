package I11l1l1Ill.l11l1l1l11.I1lllllII1.I11l1I11ll;

import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.FileNotFoundException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public enum llIllIlll1 {
    a,
    b,
    c;

    /* renamed from: values, reason: to resolve conflict with enum method */
    public static llIllIlll1[] valuesCustom() throws FileNotFoundException {
        if (IIlII1IIIl.I1lI11IIll(I1I1lI1II1.a(new byte[]{99}), 1287105409L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{100, 46, 14, 54}));
        }
        return (llIllIlll1[]) values().clone();
    }
}
