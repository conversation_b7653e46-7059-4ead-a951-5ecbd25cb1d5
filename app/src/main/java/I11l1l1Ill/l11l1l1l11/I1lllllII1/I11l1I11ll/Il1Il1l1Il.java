package I11l1l1Ill.l11l1l1l11.I1lllllII1.I11l1I11ll;

import I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.IllIIll11l;
import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.III1Il1II1;
import java.security.cert.CertificateNotYetValidException;
import java.util.ArrayList;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class Il1Il1l1Il {
    private final IllIIll11l a;
    private final ArrayList<String> b = new ArrayList<>();

    public Il1Il1l1Il(IllIIll11l illIIll11l, String str) {
        this.a = illIIll11l;
        a(str);
    }

    public IllIIll11l a() {
        IllIIll11l illIIll11l = this.a;
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{68}), 960054034L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{97, 52, 47, 13, 45, Byte.MAX_VALUE, 80, 0}));
        }
        return illIIll11l;
    }

    public void a(String str) {
        this.b.add(str);
    }

    public ArrayList<String> b() throws CertificateNotYetValidException {
        ArrayList<String> arrayList = this.b;
        if (lIIlI111II.Il11lIlI1I(2161)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{114, 43, 3, 7, 22, 97}));
        }
        return arrayList;
    }
}
