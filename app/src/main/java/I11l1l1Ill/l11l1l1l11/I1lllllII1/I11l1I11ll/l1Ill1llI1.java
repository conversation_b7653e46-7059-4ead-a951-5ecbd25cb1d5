package I11l1l1Ill.l11l1l1l11.I1lllllII1.I11l1I11ll;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIlI111II;
import android.media.content.II1I11IlI1;
import android.media.content.IIl1l1IllI;
import android.os.Handler;
import android.os.Looper;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import android.view.View;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.EOFException;
import java.io.InterruptedIOException;
import java.io.NotSerializableException;
import java.io.ObjectStreamException;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.security.KeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CRLException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.l1ll11l111;
import lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIIII1IlI;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class l1Ill1llI1 implements II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.Il1Il1l1Il {
    private static l1Ill1llI1 i = new l1Ill1llI1();
    private static Handler j = new Handler(Looper.getMainLooper());
    private static Handler k = null;
    private static final Runnable l = new IIl11IIIlI();
    private static final Runnable m = new II1IIlIIIl();
    private int b;
    private long h;
    private List<IIl111lI1I> a = new ArrayList();
    private boolean c = false;
    private final List<I11Ill11I1.llIl1IlIll.llI1IIIllI.ll1IllI11l.lIlIII1111> d = new ArrayList();
    private lIlIII1111 f = new lIlIII1111();
    private II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.l11Il111ll e = new II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.l11Il111ll();
    private l11Il111ll g = new l11Il111ll(new l1ll11l1I1.l11II1IIlI.lI1IlIlIlI.l1ll1lI1l1.llIllIlll1());

    l1Ill1llI1() {
    }

    static /* synthetic */ l11Il111ll a(l1Ill1llI1 l1ill1lli1) {
        if (llIl1lII1I.l111l1I1Il(I1I1lI1II1.a(new byte[]{7, 20, 20, 29, 36, 96, 70, 124, 97, 55, 120, 98, 7, 95, 80, 86, 56}), 187943825L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{3, 17, 26, 43, 21, 69, 90, 113, 99, 51, 81, 7, 2, 64, 98, 1, 87, 80, 38}));
        }
        l11Il111ll l11il111ll = l1ill1lli1.g;
        if (I1lllI11II.I1lI11IIll(I1I1lI1II1.a(new byte[]{89, 12, 3, 20, 81, 88, 93, 6, 120, 62, 92, 115, 116, 64, 123, 67, 47, 36}), 271839530L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{65}));
        }
        return l11il111ll;
    }

    private void a(long j2) throws NotSerializableException {
        if (Il11II1llI.l11I11I11l(9568)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{69, 46, 51, 6, 40, 68, 118, 66, 97, 29, 98, 116, 76, 81, 90, 103}));
        }
        if (this.a.size() > 0) {
            for (IIl111lI1I iIl111lI1I : this.a) {
                iIl111lI1I.onTreeProcessed(this.b, TimeUnit.NANOSECONDS.toMillis(j2));
                if (iIl111lI1I instanceof II111lllll) {
                    ((II111lllll) iIl111lI1I).onTreeProcessedNano(this.b, j2);
                }
            }
        }
    }

    private void a(View view, II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.lIlIII1111 liliii1111, JSONObject jSONObject, llIllIlll1 llillilll1, boolean z) throws CertificateParsingException {
        if (II1I11IlI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{112, 50, 11, 53}), 281960982L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{123, 50, 52, 81, 85, 95, 95, 69, 84, 15, 87, 99, 3, 8, 88, 121, 33, 8}));
        }
        liliii1111.a(view, jSONObject, this, llillilll1 == llIllIlll1.a, z);
    }

    private void a(String str, View view, JSONObject jSONObject) throws JSONException, KeyException, NoSuchFieldException, NoSuchAlgorithmException, CRLException, InterruptedIOException, CertificateExpiredException {
        if (lIIlI111II.I11II1111l(192305044L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{123, 47, 13, 15, 12, 92, 114, 91, 92, 52, 74, 74, 90, Byte.MAX_VALUE, 109, 65, 50, 57}));
        }
        II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.lIlIII1111 liliii1111B = this.e.b();
        String strB = this.f.b(str);
        if (strB != null) {
            JSONObject jSONObjectA = liliii1111B.a(view);
            lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObjectA, str);
            lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.b(jSONObjectA, strB);
            lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObject, jSONObjectA);
        }
        if (IllllI11Il.IlIIlIllI1(I1I1lI1II1.a(new byte[]{84, 19, 43, 38, 41, 121, 92, 90, 64, 38, 113, 93, 0, 11, 89, 93, 58, 81, 80, 115, 1, 89, 96, 119, 102, 123, 101, 7}), 219119341L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{123, 38, 11, 83, 86, 86, 64}));
        }
    }

    private boolean a(View view, JSONObject jSONObject) throws JSONException, NoSuchAlgorithmException, CertificateException, EOFException, UTFDataFormatException {
        Il1Il1l1Il il1Il1l1IlC = this.f.c(view);
        if (il1Il1l1IlC != null) {
            lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObject, il1Il1l1IlC);
            return true;
        }
        if (IIIlIl1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{2, 12, 27, 61, 1, 76, 93, 119, 87, 52, 67, 87, 0, 14, 117, 68, 8, 41, 12, 75, 83, 27, 122, 87}), 10517)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{0, 10, 55, 80, 40, 88, 0, 72, 13, 5, 126, 66, 71, 84, 126, 5}));
        }
        return false;
    }

    static /* synthetic */ Runnable b() {
        Runnable runnable = l;
        if (!I1Ill1lIII.l11I11I11l(367091283L)) {
            return runnable;
        }
        Log.e(I1I1lI1II1.a(new byte[]{6, 52, 4, 54, 42, 80, 116, 97, 96, 81, 73, 96, 81, 111, 71, 90, 55, 6, 11, 121, 119, 46, 95}), I1I1lI1II1.a(new byte[]{122, 13, 24, 61, 84, 65, 67, 68, 11, 87, 116, 105, 100, 88}));
        return null;
    }

    private boolean b(View view, JSONObject jSONObject) throws ObjectStreamException, JSONException, MalformedURLException, NoSuchAlgorithmException, CRLException, CertificateEncodingException {
        String strD = this.f.d(view);
        if (strD == null) {
            return false;
        }
        lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObject, strD);
        lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObject, Boolean.valueOf(this.f.f(view)));
        this.f.d();
        if (l1l1I111I1.IlII1Illll(206451102L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{125, 53, 39, 9, 6, 65, 121, 84, 14}));
        }
        return true;
    }

    static /* synthetic */ Runnable c() {
        Runnable runnable = m;
        if (lI1l1I1l1l.l1l1l1IIlI(253593949L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{110}));
        }
        return runnable;
    }

    private void d() throws ClassNotFoundException, NotSerializableException, UnsupportedEncodingException {
        if (IIIlIll111.Ill1lIIlIl(1097)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{6, 19, 20, 13, 21, 71, 86}));
        }
        a(llIIII1IlI.b() - this.h);
        if (l111Il1lI1.IIll1I11lI(I1I1lI1II1.a(new byte[]{97, 29, 40, 1, 1, 84, 98, 6, 116, 51, 0, 70, 113, Byte.MAX_VALUE, 122, 65, 45, 44, 54, 97, 124, 11, 126, 76, 80, 120}))) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{85, 52, 11, 6, 1, 113, 93, 8, 99, 49, 65, 113, 91, 110, 92, 100, 39, 24, 35, 4, 122, 42, 91, 76}));
        }
    }

    private void e() {
        this.b = 0;
        this.d.clear();
        this.c = false;
        Iterator<l1Il111I11.Il1IlIllIl.I1lIlI1ll1.llIl1IlIll.lIlIII1111> it = I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.llIllIlll1.c().a().iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            } else if (it.next().e()) {
                this.c = true;
                break;
            }
        }
        this.h = llIIII1IlI.b();
    }

    public static l1Ill1llI1 getInstance() {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.Il1lII1l1l(205870504L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{117, 84, 85, 93, 51, 2, 101, 106, 110, 37, 90, 82, 122, 91, 2, 120, 5, 39, 26, 85, 8, 55}));
        }
        return i;
    }

    private void i() throws SyncFailedException, CertificateExpiredException {
        if (lII1llllI1.Ill1lIIlIl(8212)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{89, 86, 46, 23, 41, 121, 98, 1, 125, 28, 64, 86, 98, 94, 90, 84, 85, 44, 84, 88, 84, 80, 68, 12, 81, 83}));
        }
        if (k == null) {
            Handler handler = new Handler(Looper.getMainLooper());
            k = handler;
            handler.post(l);
            k.postDelayed(m, 200L);
        }
        if (llIlII1IlI.IlII1Illll(458241753L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{84, 49, 86, 0, 41}));
        }
    }

    private void k() {
        Handler handler = k;
        if (handler != null) {
            handler.removeCallbacks(m);
            k = null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void l() throws KeyException, BrokenBarrierException, CertificateException, NotSerializableException, InterruptedIOException, JSONException, NoSuchFieldException, NoSuchAlgorithmException, InstantiationException, ClassNotFoundException, CRLException, NoSuchProviderException, UnsupportedEncodingException {
        if (IIl1l1IllI.I1lllI1llI(9757)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{94, 80, 36, 82, 21, 83, 0, 3, 13, 8, 64, 85, 100, 13, 95, 68, 56, 11, 24, 120, 3, 17, 82, 92}));
        }
        e();
        f();
        d();
        if (llIl1lII1I.Il1IIlI1II(1411163762L)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{111, 93, 82, 38, 26, 99, 81, 73, 97, 52, 118, 119, 108, 124, 87, 84, 58, 83, 50}));
        }
    }

    @Override // II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.Il1Il1l1Il
    public void a(View view, II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.lIlIII1111 liliii1111, JSONObject jSONObject, boolean z) throws JSONException, InterruptedException, NoSuchAlgorithmException, UnrecoverableKeyException, CertificateParsingException, CertPathValidatorException {
        if (!l1ll11l111.d(view)) {
            if (IIll1llI1l.Ill1lIIlIl(1756)) {
                throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{69, 34, 82, 85, 14, 112, 71, 5, 96, 1, 8, 72, 82, 105, 64, 83, 45, 2, 14, 11, 92, 49, 7, 88, 91, 0, 5, 0, 42, 6, 100, 12}));
            }
            return;
        }
        llIllIlll1 llillilll1E = this.f.e(view);
        if (llillilll1E == llIllIlll1.c) {
            return;
        }
        JSONObject jSONObjectA = liliii1111.a(view);
        lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.a(jSONObject, jSONObjectA);
        if (!b(view, jSONObjectA)) {
            boolean z2 = z || a(view, jSONObjectA);
            if (this.c && llillilll1E == llIllIlll1.b && !z2) {
                this.d.add(new I11Ill11I1.llIl1IlIll.llI1IIIllI.ll1IllI11l.lIlIII1111(view));
            }
            a(view, liliii1111, jSONObjectA, llillilll1E, z2);
        }
        this.b++;
        if (IIlII1IIIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{71, 61, 8, 82, 59, 79, 69, 114, 67, 13, 70, 64, 71, 73, 110, 2, 44, 18, 48, 1, 66}), I1I1lI1II1.a(new byte[]{110, 83, 15, 61, 10, Byte.MAX_VALUE, 67, 94, 105, 82, 87, 70, 99, Byte.MAX_VALUE}))) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{88, 7, 47, 48, 19, 86, 95, 126, 94, 47, 90, 101, 88, 0, 78, 81, 83, 10, 7}));
        }
    }

    public void addTimeLogger(IIl111lI1I iIl111lI1I) throws CertificateExpiredException {
        if (!this.a.contains(iIl111lI1I)) {
            this.a.add(iIl111lI1I);
        }
        if (II1lllllI1.llll111lI1(I1I1lI1II1.a(new byte[]{113, 43, 45, 36, 22, 69, 110, 95, 94, 15, 103, 93, 70, 9, 109, 0}), 194648668L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{102, 9, 32, 40, 6, 64, 114, 104}));
        }
    }

    void f() throws KeyException, BrokenBarrierException, CertificateException, InterruptedIOException, JSONException, NoSuchFieldException, NoSuchAlgorithmException, InstantiationException, CRLException, NoSuchProviderException, UnsupportedEncodingException {
        if (I1IllIll1l.IllIlI1l1I(I1I1lI1II1.a(new byte[]{121, 30, 1, 16, 16, 109, 67, 82, 11, 32, 70, 119, 77, 82, 114, 86, 58, 81, 23, 3, 116, 85, 105, 123, 4, 83, 123, 85, 42}), I1I1lI1II1.a(new byte[]{79, 6, 1, 9, 40, 99, 90}))) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{85, 21, 13, 92, 58, 2, 99, 98, 13, 7, 68, 68}));
        }
        this.f.e();
        long jB = llIIII1IlI.b();
        II1lIlIlll.I11ll1IlII.l1I1l11Il1.lIII1I11ll.lIlIII1111 liliii1111A = this.e.a();
        if (this.f.b().size() > 0) {
            Iterator<String> it = this.f.b().iterator();
            while (it.hasNext()) {
                String next = it.next();
                JSONObject jSONObjectA = liliii1111A.a(null);
                a(next, this.f.a(next), jSONObjectA);
                lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.b(jSONObjectA);
                HashSet<String> hashSet = new HashSet<>();
                hashSet.add(next);
                this.g.a(jSONObjectA, hashSet, jB);
            }
        }
        if (this.f.c().size() > 0) {
            JSONObject jSONObjectA2 = liliii1111A.a(null);
            a(null, liliii1111A, jSONObjectA2, llIllIlll1.a, false);
            lllII11ll1.lI1l1IlI1l.Il1IIIlI1l.I1lIlI1ll1.llIllIlll1.b(jSONObjectA2);
            this.g.b(jSONObjectA2, this.f.c(), jB);
            if (this.c) {
                Iterator<l1Il111I11.Il1IlIllIl.I1lIlI1ll1.llIl1IlIll.lIlIII1111> it2 = I1lI11lIll.I1lIl1lIII.l11IIllI1I.lIll1lllII.llIllIlll1.c().a().iterator();
                while (it2.hasNext()) {
                    it2.next().a(this.d);
                }
            }
        } else {
            this.g.b();
        }
        this.f.a();
    }

    public void g() {
        k();
    }

    public void h() {
        i();
        if (IIlII1IIIl.III111l111(I1I1lI1II1.a(new byte[]{97, 11, 35, 8, 10, 90, 102, 3, 75, 48, 114, 92, 108, 0, 7, 101, 37, 0, 85, 92, 121, 53, 71, 3, 74, 3, 112, 4, 54, 91}), 161613364L)) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{68, 46, 82, 13, 24, 96, 78, 90, 112, 61, 65, 74, 125, 123, 113, 119, 47, 4, 53}));
        }
    }

    public void j() {
        g();
        this.a.clear();
        j.post(new ll11IIllIl(this));
    }

    public void removeTimeLogger(IIl111lI1I iIl111lI1I) {
        if (this.a.contains(iIl111lI1I)) {
            this.a.remove(iIl111lI1I);
        }
        if (lIIllIlIl1.Il1IIlI1II(969590995L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{15, 46, 22, 93, 6, 82, 117, 69, Byte.MAX_VALUE, 52, 64, 96, 64, 86, 122, 115, 85, 7, 32, 11, 100, 3}));
        }
    }
}
