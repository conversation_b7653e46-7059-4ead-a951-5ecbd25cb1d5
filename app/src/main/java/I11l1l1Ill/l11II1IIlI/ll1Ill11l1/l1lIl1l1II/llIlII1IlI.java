package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;

@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0002"}, d2 = {"<anonymous>", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class llIlII1IlI extends s implements Function0<IIll1IIlII> {
    public static final llIlII1IlI INSTANCE = new llIlII1IlI();

    llIlII1IlI() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final IIll1IIlII invoke() {
        return IIl1II1lII.INSTANCE.getDescriptor();
    }
}
