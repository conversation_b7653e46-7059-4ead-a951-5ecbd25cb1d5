package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import II1Ill1I1I.lI1l11lIll.l11l1l1l11.IIl1I11lIl;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.IIl1l1IllI;
import android.util.Log;
import androidx.core.location.I1111IIl11;
import androidx.core.location.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.InvalidClassException;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateExpiredException;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.al;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\bÀ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0012B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IIl1II1lII;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/l1lll11lIl;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/l1lll11lIl;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/l1lll11lIl;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V", "llllllllI1"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class IIl1II1lII implements I11IIl1I1I<l1lll11lIl> {
    public static final IIl1II1lII INSTANCE = new IIl1II1lII();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = llllllllI1.INSTANCE;

    private IIl1II1lII() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) throws ReflectiveOperationException, CertificateExpiredException {
        l1lll11lIl l1lll11lilA = a(i1Illl11II);
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{120, 3, 22, 82, 58, 2, 114, 92, 75, 39, 88, 114, 119, 67, 81, 71, 91, 7, 56, 94, 94, 86, 88, 102, 107, 4, 102, 32, 44, 115}), 393218192L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 37, 48, 3, 1, 94, 91, 4, 73, 23, Byte.MAX_VALUE, 101, 92, 74, 120}));
        }
        return l1lll11lilA;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    public /* synthetic */ void serialize(Ill1llllll ill1llllll, Object obj) throws IllegalAccessException, NoSuchAlgorithmException, InvalidAlgorithmParameterException {
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{124, 61, 1, 3, 19, 98, 85, 96, 114, 21, 83, 113, 77, 77, 119, 113, 43, 2, 19, 7, 93, 52, 93, 116, 69}), 299760942L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{86, 62, 53, 87, 22, 124, 109, 92, 81, 21, 124, 86, 98, 90, 90, 5, 82, 22, 59, 11, 89, 80, 10, 124}));
        }
        a(ill1llllll, (l1lll11lIl) obj);
        if (lIIlI111II.IIlI1Il1lI(170622403L)) {
            Log.e(I1I1lI1II1.a(new byte[]{77, 47, 91, 38, 54, 103, 113, 96, 1, 9, 81, 114, 94, 64, 71, 12, 50}), I1I1lI1II1.a(new byte[]{111, 81, 16, 0, 38, 89, 92}));
        }
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public IIll1IIlII getDescriptor() throws InvalidClassException {
        IIll1IIlII iIll1IIlII = a;
        if (IIll1llI1l.Il1IIlI1II(7986)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{117, 40, 40, 52, 59}));
        }
        return iIll1IIlII;
    }

    public void a(Ill1llllll p0, l1lll11lIl p1) throws IllegalAccessException, InvalidAlgorithmParameterException {
        if (I1111IIl11.IIll1I11lI(I1I1lI1II1.a(new byte[]{5, 87, 20, 60, 4}))) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{71, 2, 54, 6, 49, 115, 5, 69, 117, 93, 118, 103, Byte.MAX_VALUE, 86, 112, 4, 91, 14, 23, 64, 93, 12, 103, 119, 73, 113, 120}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        IIl1I11lIl.c(IIl1I11lIl.a(al.INSTANCE), IlIlIlI11I.INSTANCE).serialize(p0, p1);
        if (lI1l1I1l1l.IlII1Illll(I1I1lI1II1.a(new byte[]{86, 47, 5, 15, 16, 83, 3, 97, 84, 9, 113, Byte.MAX_VALUE, 121, 115, 5, 102, 52, 87, 49, 123, 123, 25, 119, 5, 117}), 1717398715L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{6, 9, 55, 20, 11, 7, 2, 84, 13, 38, 90, 72, 77, 10, 87, 13, 80, 23, 82, 102, 113, 45, 101, 86, 3, 67, 109, 17, 39}));
        }
    }

    public l1lll11lIl a(I1Illl11II p0) throws CertificateExpiredException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        II1lIlI1ll.b(p0);
        return new l1lll11lIl((Map) IIl1I11lIl.c(IIl1I11lIl.a(al.INSTANCE), IlIlIlI11I.INSTANCE).deserialize(p0));
    }
}
