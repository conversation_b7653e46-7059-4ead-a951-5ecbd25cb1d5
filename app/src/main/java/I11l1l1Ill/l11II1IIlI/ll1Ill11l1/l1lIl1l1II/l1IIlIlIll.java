package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import androidx.core.location.I111I11Ill;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.EOFException;
import java.io.FileNotFoundException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0002"}, d2 = {"<anonymous>", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class l1IIlIlIll extends s implements Function0<IIll1IIlII> {
    public static final l1IIlIlIll INSTANCE = new l1IIlIlIll();

    l1IIlIlIll() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final IIll1IIlII invoke() throws FileNotFoundException {
        IIll1IIlII a = Il1III1Il1.INSTANCE.getA();
        if (IllIIIIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{70, 28, 53, 15, 16, 4, 15, 126, 120, 52, 95, 7, 89, 108, 76, 79, 90, 47, 81, 115, 106, 44, 66, 77, 86}), 160594048L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{89, 17}));
        }
        return a;
    }

    @Override // kotlin.jvm.functions.Function0
    public /* synthetic */ IIll1IIlII invoke() throws EOFException {
        if (I111I11Ill.lll1111l11(I1I1lI1II1.a(new byte[]{122, 9, 90, 43, 7, 82, 112, 90, 74, 54, 97, 95}), 10065)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{68, 12, 3, 13, 20, 1, 126, 82, 0, 16, 68, 97, 123, 10, 85, 88}));
        }
        return invoke();
    }
}
