package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.l111I111Il;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.lllI1l11lI;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IIII1l1lll;
import android.accounts.utils.I1lllI11II;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateExpiredException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bÀ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llI1lIlIlI;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1lI1I1lll;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1lI1I1lll;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1lI1I1lll;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class llI1lIlIlI implements I11IIl1I1I<I1lI1I1lll> {
    public static final llI1lIlIlI INSTANCE = new llI1lIlIlI();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = l111I111Il.a(I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 124, 8, 88, 124, 83, 40, 83, 94, 92, 13}), lllI1l11lI.INSTANCE, new IIll1IIlII[0], null, 8, null);

    private llI1lIlIlI() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) throws NoSuchMethodException, CertificateExpiredException {
        if (IIlI1ll1ll.IIll1I11lI(I1I1lI1II1.a(new byte[]{121, 48, 49, 40, 36, 81, 118, 89, 107, 2, 114, 102, 68}))) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{0, 2, 38, 82, 84, 7, 103, 5, 14, 28, 89, 121, 81, 78, 119, 67, 4, 19, 12, 99, 89, 89, 97, 79, 119}));
        }
        I1lI1I1lll i1lI1I1lllA = a(i1Illl11II);
        if (lIIlI111II.l111I1ll1l(2942)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{91, 93, 51, 49, 13, 97, 122, 103, 113, 23, 100, 4, 0, 120, 65, 101, 86, 47, 44, 85, 113, 35, 125, 123, 101, 6, 71, 42, 61, 116, 1}));
        }
        return i1lI1I1lllA;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    public /* synthetic */ void serialize(Ill1llllll ill1llllll, Object obj) throws UnknownHostException, InvalidAlgorithmParameterException {
        a(ill1llllll, (I1lI1I1lll) obj);
        if (I1lllI11II.l1Il11I1Il(I1I1lI1II1.a(new byte[]{66, 7, 3, 54, 13, 83, 6, 98, 79, 14, 81, 123, 123, 14, 85, 89, 26, 87, 54, 102, 1, 51, 97, 76}), 194713386L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{114, 12, 55, 6, 51, 102, 97, 98, 9, 11, 67, 4, 99, 126, Byte.MAX_VALUE, 122, 26, 41, 42, 71, 98, 83, 117, 125, 96, 2, 99, 87, 92, 5, 116, 52}));
        }
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public IIll1IIlII getDescriptor() {
        if (android.accounts.utils.lIIlI111II.I1I11l11l1(4495)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{121, 5, 46, 14, 90, 83, 98, 7, 75, 32, 96, 101, 123, 113, 64, 114, 9, 39, 39}));
        }
        return a;
    }

    public void a(Ill1llllll p0, I1lI1I1lll p1) throws InvalidAlgorithmParameterException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        p0.a();
    }

    public I1lI1I1lll a(I1Illl11II p0) throws CertificateExpiredException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        II1lIlI1ll.b(p0);
        if (p0.b()) {
            throw new IIII1l1lll(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65, 82, 84, 25, 67, 94, 69, 89, 85, 19, 21, 14, 8, 22, 87, 66, 0, 95}));
        }
        p0.c();
        return I1lI1I1lll.INSTANCE;
    }
}
