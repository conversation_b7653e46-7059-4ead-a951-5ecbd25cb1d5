package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.util.Log;
import java.security.cert.CertificateExpiredException;
import java.util.LinkedHashMap;
import java.util.Map;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class lI1IIIIll1 {
    private final Map<String, lII11llIIl> a = new LinkedHashMap();

    public final lII11llIIl a(String str, lII11llIIl lii11lliil) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        lII11llIIl lii11lliilPut = this.a.put(str, lii11lliil);
        if (!lIIlI111II.lIll1IIl11(264752695L)) {
            return lii11lliilPut;
        }
        Log.d(I1I1lI1II1.a(new byte[]{79, 13, 11}), I1I1lI1II1.a(new byte[]{3, 49, 27, 39, 51, 114, 79, 66, 72, 37, 71, 70, 81, 13, 7, 64, 40, 82, 37, 81, 124, 17}));
        return null;
    }

    public final l1lll11lIl a() {
        if (I111lIl11I.I1lllI1llI(263877874L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{84, 92, 42, 17, 40, 91, 85, 103, 112, 7, 92, 2, 81, 88, 4, 121, 35, 87, 27, 106, 64, 84, 114, 115, 69, 64, 88, 84, 12, 7, 123}));
        }
        return new l1lll11lIl(this.a);
    }
}
