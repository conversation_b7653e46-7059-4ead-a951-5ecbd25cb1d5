package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.ll1ll1I1I1;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u0000 \u00032\u00020\u0001:\u0001\u0003B\u0007\b\u0004¢\u0006\u0002\u0010\u0002\u0082\u0001\u0003\u0004\u0005\u0006¨\u0006\u0007"}, d2 = {"Lkotlinx/serialization/json/JsonElement;", "", "()V", "Companion", "Lkotlinx/serialization/json/JsonArray;", "Lkotlinx/serialization/json/JsonObject;", "Lkotlinx/serialization/json/JsonPrimitive;", "kotlinx-serialization-json"}, k = 1, mv = {1, 7, 1}, xi = 48)
@ll1ll1I1I1(a = IlIlIlI11I.class)
/* loaded from: classes.dex */
public abstract class lII11llIIl {
    public static final lll1lll1l1 b = new lll1lll1l1(null);

    public /* synthetic */ lII11llIIl(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    private lII11llIIl() {
    }
}
