package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.I1111l111I;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.util.concurrent.RejectedExecutionException;
import kotlin.Metadata;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\f\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\n¢\u0006\u0002\b\u0003"}, d2 = {"<anonymous>", "", "Lkotlinx/serialization/descriptors/ClassSerialDescriptorBuilder;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class lII111lllI extends s implements Function1<I1111l111I, Unit> {
    public static final lII111lllI INSTANCE = new lII111lllI();

    lII111lllI() {
        super(1);
    }

    @Override // kotlin.jvm.functions.Function1
    public /* bridge */ /* synthetic */ Unit invoke(I1111l111I i1111l111I) {
        invoke(i1111l111I);
        return Unit.INSTANCE;
    }

    public final void invoke(I1111l111I i1111l111I) {
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{84, 28, 1, 87, 43, 114, 124, 5, 104, 10, 70, 90, 123, 81, 87, 82, 4, 6, 32, 95, 6, 85, 102, 64, 86, 6, 117, 17, 62, 80, 94}), 1005666219L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{98, 48, 48, 21, 24, 97, 81, 91, 67, 2, 104, 93, 70, 81, 113, 65, 59, 47, 54, 123, 0, 13}));
        }
        Intrinsics.checkNotNullParameter(i1111l111I, I1I1lI1II1.a(new byte[]{19, 16, 10, 12, 17, 17, 85, 69, 80, 8, 84, 99, 80, 75, 93, 84, 14, 37, 7, 65, 83, 19, 90, 69, 71, 89, 69}));
        I1111l111I.a$default(i1111l111I, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 50, 71, 94, 93, 80, 16, 89, 70, 80}), II1lIlI1ll.b((Function0<? extends IIll1IIlII>) II1lII1l1I.INSTANCE), null, false, 12, null);
        I1111l111I.a$default(i1111l111I, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 44, 64, 91, 92}), II1lIlI1ll.b((Function0<? extends IIll1IIlII>) llll1lI1II.INSTANCE), null, false, 12, null);
        I1111l111I.a$default(i1111l111I, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 46, 92, 67, 85, 75, 5, 92}), II1lIlI1ll.b((Function0<? extends IIll1IIlII>) l1IIlIlIll.INSTANCE), null, false, 12, null);
        I1111l111I.a$default(i1111l111I, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 45, 87, 93, 85, 90, 16}), II1lIlI1ll.b((Function0<? extends IIll1IIlII>) llIlII1IlI.INSTANCE), null, false, 12, null);
        I1111l111I.a$default(i1111l111I, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 35, 71, 69, 81, 64}), II1lIlI1ll.b((Function0<? extends IIll1IIlII>) lI1llII1I1.INSTANCE), null, false, 12, null);
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.I11II1I1I1(394720037L)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{114, 60, 80, 23, 58, 86, 78, 8, 84, 8, 114, 114, 101, 74, 4, 80, 91, 24, 44, 88}));
        }
    }
}
