package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.l111I111Il;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.l1lI1lll1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import android.media.content.IIl1l1IllI;
import android.media.content.Il1llIl111;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.NotActiveException;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyException;
import java.security.UnrecoverableEntryException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bÀ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IlIlIlI11I;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class IlIlIlI11I implements I11IIl1I1I<lII11llIIl> {
    public static final IlIlIlI11I INSTANCE = new IlIlIlI11I();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = l111I111Il.a(I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 89, 112, 125, 4, 83, 13, 14, 123, 121, 13}), l1lI1lll1I.INSTANCE, new IIll1IIlII[0], lII111lllI.INSTANCE);

    private IlIlIlI11I() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) throws KeyException {
        if (l11Il1lI11.IlIllIll1I(I1I1lI1II1.a(new byte[]{116, 32}), IronSourceConstants.IS_COLLECT_TOKENS_FAILED)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{113, 93}));
        }
        return a(i1Illl11II);
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    public /* synthetic */ void serialize(Ill1llllll ill1llllll, Object obj) throws NotActiveException, InvalidAlgorithmParameterException, UnrecoverableEntryException {
        a(ill1llllll, (lII11llIIl) obj);
        if (IIl1l1IllI.I1lllI1llI(4690)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{113, 0, 41, 9, 32, 119, 93, 126, 9, 21}));
        }
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public IIll1IIlII getDescriptor() {
        return a;
    }

    public void a(Ill1llllll p0, lII11llIIl p1) throws InvalidAlgorithmParameterException, UnrecoverableEntryException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        if (p1 instanceof llIII111Il) {
            p0.a(I11I11II1I.INSTANCE, p1);
        } else if (p1 instanceof l1lll11lIl) {
            p0.a(IIl1II1lII.INSTANCE, p1);
        } else if (p1 instanceof IIl1Il11Il) {
            p0.a(l1l1I1llII.INSTANCE, p1);
        }
        if (Il1llIl111.l11I11I11l(I1I1lI1II1.a(new byte[]{83, 60, 9, 81, 46, 111, Byte.MAX_VALUE, 71, 67, 84, 87, 99, 100, 84, 71, 71, 50, 57, 80, 83, 84, 27, 124, 96, 93, 3}), 7086)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{98, 37, 80, 85, 48, Byte.MAX_VALUE, 112, 65, 99, 18, 94, 84, 99, 67, 65, 3, 10, 37, 48, 11, 89, 15, 126, 6, 70, 108, 117, 22}));
        }
    }

    public lII11llIIl a(I1Illl11II p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        return II1lIlI1ll.a(p0).o();
    }
}
