package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.IIlIl1IIl1;
import I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.I1lI111111;
import android.accounts.utils.Ill11ll111;
import java.security.cert.CRLException;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class I1lll11IlI extends I1ll11lI1l {
    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public I1lll11IlI(Il1llllI1I il1llllI1I, Ill1llllll ill1llllll) throws CRLException {
        super(il1llllI1I, ill1llllll, null);
        Intrinsics.checkNotNullParameter(il1llllI1I, I1I1lI1II1.a(new byte[]{84, 11, 12, 3, 11, 82, 66, 66, 88, 16, 89, 95, 91}));
        Intrinsics.checkNotNullParameter(ill1llllll, I1I1lI1II1.a(new byte[]{90, 11, 6, 16, 14, 80}));
        d();
    }

    private final void d() throws CRLException {
        if (Intrinsics.a(getC(), IIlIl1IIl1.a())) {
            if (Ill11ll111.l11I11I11l(180881178L)) {
                throw new CRLException(I1I1lI1II1.a(new byte[]{100, 62, 59, 20, 46, 96, 83}));
            }
        } else {
            getC().a(new I1lI111111(b().i(), b().getC()));
        }
    }
}
