package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import android.accounts.utils.lIIIIII11I;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.net.SocketTimeoutException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0002"}, d2 = {"<anonymous>", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class II1lII1l1I extends s implements Function0<IIll1IIlII> {
    public static final II1lII1l1I INSTANCE = new II1lII1l1I();

    II1lII1l1I() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final IIll1IIlII invoke() throws SocketTimeoutException {
        if (lIIIIII11I.l1l1l1IIlI(351872398L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{112, 46, 46, 38, 6, 90, 123, 0, 116, 18, 104, 94, 87, 88, 95, 98, 43, 25}));
        }
        IIll1IIlII a = I11I11II1I.INSTANCE.getA();
        if (l11Il1lI11.IlII1Illll(898)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{123, 33, 49, 87, 22, 103, Byte.MAX_VALUE, 105, 97, 45, 114, 121, 70, 15, 121, 116, 7, 43, 21, 94, 85, 19, 66}));
        }
        return a;
    }

    @Override // kotlin.jvm.functions.Function0
    public /* synthetic */ IIll1IIlII invoke() throws SocketTimeoutException {
        IIll1IIlII iIll1IIlIIInvoke = invoke();
        if (IIll1llI1l.Ill1lIIlIl(6855)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 15, 16, 81, 9, 70, 83, 81, 116, 41, 1, 8, 1, 92, 69, 86, 14, 9, 13, 64, 120, 5, 74, 102}));
        }
        return iIll1IIlIIInvoke;
    }
}
