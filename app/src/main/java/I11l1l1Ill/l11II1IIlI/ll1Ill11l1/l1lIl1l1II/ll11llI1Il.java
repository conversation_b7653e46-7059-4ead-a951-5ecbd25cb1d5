package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IlIIl111lI;
import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import java.io.UTFDataFormatException;
import java.net.PortUnreachableException;
import java.security.SignatureException;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class ll11llI1Il extends llIII111Il {
    private final boolean a;
    private final String d;

    public boolean a() throws SignatureException {
        if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{125, 39, 33, 10, 86, 3, 2, 6, 85, 52, 102, 81, 13, 126, 87, 0, 6, 19, 84, 66, 103}), 399385970L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{96, 61, 16, 41, 36, 69, 125, 83, 73, 32, Byte.MAX_VALUE, 84, 69, 91, 94, 96, 22, 19, 82, Byte.MAX_VALUE, 103, 32, 116, 89, 95, 99, 94}));
        }
        boolean z = this.a;
        if (lIIlI111II.I1I11l11l1(1048)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{118, 23, 33, 6, 15, 111, 94, 83, 119, 7, 88, 113, 6}));
        }
        return z;
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public ll11llI1Il(Object obj, boolean z) {
        super(null);
        Intrinsics.checkNotNullParameter(obj, I1I1lI1II1.a(new byte[]{85, 11, 6, 28}));
        this.a = z;
        this.d = obj.toString();
    }

    @Override // I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II.llIII111Il
    public String b() {
        return this.d;
    }

    @Override // I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II.llIII111Il
    public String toString() {
        if (!a()) {
            return b();
        }
        StringBuilder sb = new StringBuilder();
        IlIIl111lI.a(sb, b());
        String string = sb.toString();
        Intrinsics.checkNotNullExpressionValue(string, I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 117, 69, 80, 8, 84, 85, 71, 17, 29, 27, 3, 17, 18, 94, 73, 73, 81, 64, 90, 90, 83, 0, 22, 115, 84, 23, 94, 11, 12, 76, 76, 65, 88, 99, 77, 22, 89, 94, 82, 17, 29}));
        return string;
    }

    public boolean equals(Object obj) throws PortUnreachableException {
        if (this == obj) {
            return true;
        }
        if (obj == null || !Intrinsics.a(ai.b(getClass()), ai.b(obj.getClass()))) {
            return false;
        }
        ll11llI1Il ll11lli1il = (ll11llI1Il) obj;
        if (a() != ll11lli1il.a() || !Intrinsics.a((Object) b(), (Object) ll11lli1il.b())) {
            return false;
        }
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{120, 10, 24, 36}), 219475188L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{110, 80, 39, 2, 44, 114}));
        }
        return true;
    }

    public int hashCode() throws UTFDataFormatException {
        int iHashCode = (Boolean.hashCode(a()) * 31) + b().hashCode();
        if (androidx.constraintlayout.widget.lIIlI111II.Il1lII1l1l(8830)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{77, 92, 48, 1, 42, 90, 110, 66, 125, 3, 68, 69, 123, 90, 88, 118, 36, 50, 3, 70, 91, 6, 122, 67, 103, 85, 110, 61, 9, 88, 120}));
        }
        return iHashCode;
    }
}
