package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII$DefaultImpls;
import android.media.content.II1I11IlI1;
import android.util.Log;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.lang.annotation.Annotation;
import java.util.List;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;
import kotlin.l;
import kotlin.m;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class II1ll1I1lI implements IIll1IIlII {
    private final l a;

    II1ll1I1lI(Function0<? extends IIll1IIlII> function0) {
        this.a = m.a(function0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a() throws CloneNotSupportedException {
        List<Annotation> listC = IIll1IIlII$DefaultImpls.c(this);
        if (lIIlI111II.l11l1IIl1I(4947)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{98, 80, 37, 51, 23, 66, 121, 70, 118, 21, 115, 68, 79, 73, 91, 102, 37, 34, 44, 98, 0}));
        }
        return listC;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean c() {
        return IIll1IIlII$DefaultImpls.b(this);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d() {
        if (!IIlI1ll1ll.IlIllIll1I(167212228L)) {
            return IIll1IIlII$DefaultImpls.a(this);
        }
        Log.v(I1I1lI1II1.a(new byte[]{116, 41, 51, 6, 47, 123, 110, 86, 125, 16, 86, 116, 111, 95, 96, 94, 19, 86}), I1I1lI1II1.a(new byte[]{121, 39, 82, 4, 16, 13, 120, 104, 96, 86, 70, 2, 125, 80, 82, 13, 8, 38, 90, 99, 104, 14, 114, 116, 2, Byte.MAX_VALUE, 116, 4}));
        return false;
    }

    private final IIll1IIlII g() {
        if (!androidx.interpolator.view.animation.lIIlI111II.Il1IIIIlll(3525)) {
            return (IIll1IIlII) this.a.getValue();
        }
        Log.e(I1I1lI1II1.a(new byte[]{120, 9, 46, 81, 44, 80, 110, 125, 0, 83, 97, 116, 115, 11, 0, 77, 13, 38, 24, 84}), I1I1lI1II1.a(new byte[]{117, 21, 18, 92, 42, 96, 86, 119, 94, 49, 95, 74, 64}));
        return null;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: f */
    public String getD() {
        if (IIlIIlIII1.I1lllI1llI(167915405L)) {
            Log.i(I1I1lI1II1.a(new byte[]{79, 50, 40, 40, 80, Byte.MAX_VALUE, 118, 94, Byte.MAX_VALUE, 40}), I1I1lI1II1.a(new byte[]{90, 85, 91, 29, 7, 82, 109, 118, 115, 54, 81, 4, 89, 1, 83, 111, 42, 27, 38, 69, 119, 36, 123, 120, 69, 126, 112, 35, 85, 98, 109}));
            return null;
        }
        String d = g().getD();
        if (lII1llllI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{124, 41, 36, 54, 56, 101, 126, 126, 108, 2, 97, 7, 116, 109, 99, 6, 18, 18, 91, 93, 85, 19, 0, 82, 4, 90, 111, 47}), 5853)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{0, 9, 37, 60, 56, 65, 88, 84, Byte.MAX_VALUE, 81, 86, 91, 4, 67, 67}));
        }
        return d;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: e */
    public I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.lI1llII1I1 getH() {
        I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.lI1llII1I1 h = g().getH();
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{112, 20, 49, 1, 42, 114, 88, 68, 74, 7, 81, 88, 66, 86, 126, 112, 41, 35, 17, 69, 99, 81, 0, 95, 121, 100, 93, 15, 0, 85, 124, 40}), 5078)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{118, 39, 85, 60, 9, 94, 88, 91, 80, 40, 6, 82, 5, 126}));
        }
        return h;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int b() {
        int iB = g().b();
        if (II1I11IlI1.IlIllIll1I(234346923L)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{89, 34, 84, 87}));
        }
        return iB;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public String c(int i) throws CloneNotSupportedException {
        if (androidx.constraintlayout.widget.IIlI1Il1lI.I11II1I1I1(I1I1lI1II1.a(new byte[]{116, 12, 3, 36, 11, 122, 70, 81, 126, 17, 0, 84, 96, 109, 122, 77}))) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{89, 55, 50, 4, 26, 122}));
        }
        return g().c(i);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int a(String str) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{89, 5, 15, 0}));
        return g().a(str);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a(int i) {
        return g().a(i);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public IIll1IIlII b(int i) {
        return g().b(i);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d(int i) {
        return g().d(i);
    }
}
