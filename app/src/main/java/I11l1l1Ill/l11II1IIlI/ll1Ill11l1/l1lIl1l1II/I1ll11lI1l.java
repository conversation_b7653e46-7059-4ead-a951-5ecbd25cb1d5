package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il1I1lI1ll;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.I11l111l1l;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.II1IIlIIIl;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IlIlIlllI1;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IllllI11Il;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.lIIlI11IlI;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.llllIlII1I;
import android.accounts.utils.lIIIIII11I;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.llIl1lII1I;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.measurement.api.AppMeasurementSdk$ConditionalUserProperty;
import java.io.EOFException;
import java.io.NotActiveException;
import java.net.UnknownHostException;
import java.security.cert.CertificateParsingException;
import java.util.concurrent.CancellationException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u0000 $2\u00020\u0001:\u0001$B\u0017\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005¢\u0006\u0002\u0010\u0006J'\u0010\u0011\u001a\u0002H\u0012\"\u0004\b\u0000\u0010\u00122\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u0002H\u00120\u00142\u0006\u0010\u0015\u001a\u00020\u0016¢\u0006\u0002\u0010\u0017J'\u0010\u0018\u001a\u0002H\u0012\"\u0004\b\u0000\u0010\u00122\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u0002H\u00120\u00142\u0006\u0010\u0019\u001a\u00020\u001a¢\u0006\u0002\u0010\u001bJ'\u0010\u001c\u001a\u00020\u0016\"\u0004\b\u0000\u0010\u00122\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u0002H\u00120\u001e2\u0006\u0010\u001f\u001a\u0002H\u0012¢\u0006\u0002\u0010 J'\u0010!\u001a\u00020\u001a\"\u0004\b\u0000\u0010\u00122\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u0002H\u00120\u001e2\u0006\u0010\u001f\u001a\u0002H\u0012¢\u0006\u0002\u0010\"J\u000e\u0010#\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u001aR\u001c\u0010\u0007\u001a\u00020\b8\u0000X\u0081\u0004¢\u0006\u000e\n\u0000\u0012\u0004\b\t\u0010\n\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0002\u001a\u00020\u0003¢\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0014\u0010\u0004\u001a\u00020\u0005X\u0096\u0004¢\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u0082\u0001\u0002%&¨\u0006'"}, d2 = {"Lkotlinx/serialization/json/Json;", "Lkotlinx/serialization/StringFormat;", "configuration", "Lkotlinx/serialization/json/JsonConfiguration;", "serializersModule", "Lkotlinx/serialization/modules/SerializersModule;", "(Lkotlinx/serialization/json/JsonConfiguration;Lkotlinx/serialization/modules/SerializersModule;)V", "_schemaCache", "Lkotlinx/serialization/json/internal/DescriptorSchemaCache;", "get_schemaCache$kotlinx_serialization_json$annotations", "()V", "get_schemaCache$kotlinx_serialization_json", "()Lkotlinx/serialization/json/internal/DescriptorSchemaCache;", "getConfiguration", "()Lkotlinx/serialization/json/JsonConfiguration;", "getSerializersModule", "()Lkotlinx/serialization/modules/SerializersModule;", "decodeFromJsonElement", RequestConfiguration.MAX_AD_CONTENT_RATING_T, "deserializer", "Lkotlinx/serialization/DeserializationStrategy;", "element", "Lkotlinx/serialization/json/JsonElement;", "(Lkotlinx/serialization/DeserializationStrategy;Lkotlinx/serialization/json/JsonElement;)Ljava/lang/Object;", "decodeFromString", "string", "", "(Lkotlinx/serialization/DeserializationStrategy;Ljava/lang/String;)Ljava/lang/Object;", "encodeToJsonElement", "serializer", "Lkotlinx/serialization/SerializationStrategy;", AppMeasurementSdk$ConditionalUserProperty.VALUE, "(Lkotlinx/serialization/SerializationStrategy;Ljava/lang/Object;)Lkotlinx/serialization/json/JsonElement;", "encodeToString", "(Lkotlinx/serialization/SerializationStrategy;Ljava/lang/Object;)Ljava/lang/String;", "parseToJsonElement", "Default", "Lkotlinx/serialization/json/Json$Default;", "Lkotlinx/serialization/json/JsonImpl;", "kotlinx-serialization-json"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public abstract class I1ll11lI1l implements Il1I1lI1ll {
    public static final llll1I1lll a = new llll1I1lll(null);
    private final Il1llllI1I b;
    private final Ill1llllll c;
    private final IllllI11Il d;

    public /* synthetic */ I1ll11lI1l(Il1llllI1I il1llllI1I, Ill1llllll ill1llllll, DefaultConstructorMarker defaultConstructorMarker) {
        this(il1llllI1I, ill1llllll);
    }

    private I1ll11lI1l(Il1llllI1I il1llllI1I, Ill1llllll ill1llllll) {
        this.b = il1llllI1I;
        this.c = ill1llllll;
        this.d = new IllllI11Il();
    }

    public final Il1llllI1I b() throws EOFException {
        if (lIIIIII11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{4, 62, 18, 8, 84, 98, 111}), 205846383L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{81, 8, 83, 17, 53, 108, 14, 95, 73, 87, 69, 94, 82, 112, 96, 125}));
        }
        Il1llllI1I il1llllI1I = this.b;
        if (androidx.interpolator.view.animation.llIlII1IlI.Il1IIlI1II(6374)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{117, 44, 4, 41, 17, 80, 77, 120, 85, 43, Byte.MAX_VALUE}));
        }
        return il1llllI1I;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.l1l1lI1lI1
    /* renamed from: a, reason: from getter */
    public Ill1llllll getC() {
        return this.c;
    }

    public final IllllI11Il c() throws CertificateParsingException, UnknownHostException {
        if (llIl1lII1I.I111IlIl1I(685217974L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{82, 29, 56, 1, 32, 103, 93, 64, 8, 1, 66, 6, 12, 12, 2, 86, 83, 4, 17, 97, 104, 13, 85, 111, 105, 15, 86, 87, 49, 118}));
        }
        IllllI11Il illllI11Il = this.d;
        if (Il1l11I11I.lll1111l11(I1I1lI1II1.a(new byte[]{79, 9, 44, 3, 85, 89, 115, 8, 97, 29, 114, 97, 6, 99, 85, 99, 36, 34, 11, 117, 87}), 279716428L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{121, 3, 12, 83, 40, 90, 81, 2, 107, 10, 88, 5, 115, 110, 125, 118, 53, 18, 84, 71, 83, 42, 70, 86, 69, 108, 97, 83, 80, 3}));
        }
        return illllI11Il;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il1I1lI1ll
    public final <T> String a(II11llll1I<? super T> iI11llll1I, T t) {
        if (l11Il1lI11.I111IlIl1I(I1I1lI1II1.a(new byte[]{78, 11, 20, 28, 32, 77, 4, 113, 115, 8, 121, 0, 95, 77, 90, 96, 90, 16, 17, 66, 102, 21, 74, 66, 82, 110}), 7823)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{95, 40, 33, 9}));
        }
        Intrinsics.checkNotNullParameter(iI11llll1I, I1I1lI1II1.a(new byte[]{68, 1, 16, 12, 3, 89, 94, 74, 92, 22}));
        I11l111l1l i11l111l1l = new I11l111l1l();
        try {
            llllIlII1I.a(this, i11l111l1l, iI11llll1I, t);
            return i11l111l1l.toString();
        } finally {
            i11l111l1l.a();
        }
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il1I1lI1ll
    public final <T> T a(Il11I111ll<T> il11I111ll, String str) throws CertificateParsingException {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{123, 49, 6, 34, 56, 121, 118, 118, 105, 42, 95, 65}), 7776)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{115, 93, 24, 52, 39, 67, 113, 7, 90, 1, 83, 95, 101, 11, 124, 12, 3}));
        }
        Intrinsics.checkNotNullParameter(il11I111ll, I1I1lI1II1.a(new byte[]{83, 1, 17, 0, 16, 92, 86, 92, 80, 30, 85, 66}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{68, 16, 16, 12, 12, 82}));
        lIIlI11IlI liili11ili = new lIIlI11IlI(str);
        T t = (T) new IlIlIlllI1(this, II1IIlIIIl.OBJ, liili11ili, il11I111ll.getDescriptor(), null).a(il11I111ll);
        liili11ili.f();
        return t;
    }

    public final <T> T a(Il11I111ll<T> il11I111ll, lII11llIIl lii11lliil) throws NotActiveException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{91, 47, 32, 55, 32, 115, 5, 114, 82, 5, 87, 103, 114, 115, 88, 116, 59, 16, 59, 3, 0}), 256182398L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{123, 46, 47, 2, 54, 116, Byte.MAX_VALUE, 86, 13, 34, 98, 94}));
        }
        Intrinsics.checkNotNullParameter(il11I111ll, I1I1lI1II1.a(new byte[]{83, 1, 17, 0, 16, 92, 86, 92, 80, 30, 85, 66}));
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        return (T) Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.l1l1I1llII.a(this, lii11lliil, il11I111ll);
    }
}
