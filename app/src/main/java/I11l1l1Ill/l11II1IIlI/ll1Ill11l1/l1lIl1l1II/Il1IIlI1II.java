package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.lI1l11lIIl;
import android.support.v4.graphics.drawable.Il1IIllIll;
import androidx.core.location.I1111IIl11;
import com.google.android.gms.ads.RequestConfiguration;
import java.io.CharConversionException;
import java.io.StreamCorruptedException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\b&\u0018\u0000*\b\b\u0000\u0010\u0002*\u00020\u00012\b\u0012\u0004\u0012\u00028\u00000\u0003B\u0015\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00028\u00000\u0003¢\u0006\u0004\b\u0017\u0010\u0018J\u0017\u0010\u0006\u001a\u00028\u00002\u0006\u0010\u0005\u001a\u00020\u0004H\u0007¢\u0006\u0004\b\u0006\u0010\u0007J\u001d\u0010\u000b\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\b2\u0006\u0010\t\u001a\u00028\u0000¢\u0006\u0004\b\u000b\u0010\fJ\u0017\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\rH\u0015¢\u0006\u0004\b\u000e\u0010\u000fJ\u0017\u0010\u0010\u001a\u00020\r2\u0006\u0010\u0005\u001a\u00020\rH\u0015¢\u0006\u0004\b\u0010\u0010\u000fR\u0014\u0010\u0014\u001a\u00020\u00118WX\u0096\u0004¢\u0006\u0006\u001a\u0004\b\u0012\u0010\u0013R\u001a\u0010\u0015\u001a\b\u0012\u0004\u0012\u00028\u00000\u00038\u0002X\u0083\u0004¢\u0006\u0006\n\u0004\b\u0015\u0010\u0016"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/Il1IIlI1II;", "", RequestConfiguration.MAX_AD_CONTENT_RATING_T, "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "deserialize", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)Ljava/lang/Object;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "serialize", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;Ljava/lang/Object;)V", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;", "transformDeserialize", "(LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lII11llIIl;", "transformSerialize", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "descriptor", "tSerializer", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "<init>", "(LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;)V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public abstract class Il1IIlI1II<T> implements I11IIl1I1I<T> {
    private final I11IIl1I1I<T> tSerializer;

    public Il1IIlI1II(I11IIl1I1I<T> i11IIl1I1I) {
        Intrinsics.checkNotNullParameter(i11IIl1I1I, I1I1lI1II1.a(new byte[]{67, 55, 7, 23, 11, 84, 91, 89, 67, 1, 66}));
        this.tSerializer = i11IIl1I1I;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    /* renamed from: getDescriptor */
    public IIll1IIlII getA() {
        return this.tSerializer.getA();
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    public final void serialize(Ill1llllll p0, T p1) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1111ll1l iI1111ll1lA = II1lIlI1ll.a(p0);
        iI1111ll1lA.a(transformSerialize(lI1l11lIIl.a(iI1111ll1lA.getB(), p1, this.tSerializer)));
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public final T deserialize(I1Illl11II p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        I1IlI111l1 i1IlI111l1A = II1lIlI1ll.a(p0);
        return (T) i1IlI111l1A.a().a((Il11I111ll) this.tSerializer, transformDeserialize(i1IlI111l1A.o()));
    }

    protected lII11llIIl transformDeserialize(lII11llIIl p0) throws CharConversionException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        if (androidx.interpolator.view.animation.llIlII1IlI.IIll1I11lI(I1I1lI1II1.a(new byte[]{126, 13, 47, 42, 22, 116, 89, 87, Byte.MAX_VALUE, 81, 8}))) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{77, 46, 43, 21, 50, 103, 96, 87, 96, 13, 73, 113, 13, 109, 96, 88, 38, 11, 82, 119, 88, 2, 92, 103, 94, 112, 103}));
        }
        return p0;
    }

    protected lII11llIIl transformSerialize(lII11llIIl p0) throws StreamCorruptedException {
        if (Il1IIllIll.I1lllI1llI(205942900L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{82, 86, 80, 4, 15, 71, 83, 6, 122, 81, 90, 105, 64, 80, 0, 69, 16, 0, 53, 93, 115, 23, 80, 3, 3, 102, 110, 81, 16, 102, 7}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        if (I1111IIl11.IllIlI1l1I(I1I1lI1II1.a(new byte[]{100, 34, 33, 39, 10, 99, 88, 113, 86, 54, 1, 119, 111, 88, Byte.MAX_VALUE, 94, 19, 82, 17, 68}), I1I1lI1II1.a(new byte[]{70, 18, 36, 92, 54, 121, 1, 81, 74, 33, 124}))) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{114, 0, 84, 46, 21, 84}));
        }
        return p0;
    }
}
