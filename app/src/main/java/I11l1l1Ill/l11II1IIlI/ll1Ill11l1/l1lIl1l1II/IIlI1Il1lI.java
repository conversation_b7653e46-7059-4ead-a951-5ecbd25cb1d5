package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import android.support.v4.graphics.drawable.III1Il1II1;
import androidx.core.location.l1l1I111I1;
import java.net.SocketTimeoutException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class IIlI1Il1lI extends s implements Function0<I11IIl1I1I<Object>> {
    public static final IIlI1Il1lI INSTANCE = new IIlI1Il1lI();

    IIlI1Il1lI() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final I11IIl1I1I<Object> invoke() {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{7, 23, 13, 1, 15, 80, 97, 121, 15, 83, 68, 82, 67, 83, Byte.MAX_VALUE, 108, 12, 0, 9, 93, 74, 8, 89, 111, 7, 125, 84}), 262145461L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{96, 3, 33, 52, 32, 82, 100, 103, 87, 84, 9, 121, 114, 1, 101, 66, 32, 57, 23}));
        }
        return llI1lIlIlI.INSTANCE;
    }

    @Override // kotlin.jvm.functions.Function0
    public /* synthetic */ I11IIl1I1I<Object> invoke() throws SocketTimeoutException {
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{120, 50, 59, 13, 16, 88, 89, 4, 12, 42, 113, 4, 67, 86, 108, 92, 55, 19}))) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{89, 53, 80}));
        }
        return invoke();
    }
}
