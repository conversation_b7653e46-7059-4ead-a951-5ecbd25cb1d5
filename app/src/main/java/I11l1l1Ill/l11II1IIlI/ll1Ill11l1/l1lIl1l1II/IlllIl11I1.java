package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll;
import android.accounts.utils.Ill11ll111;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.IllIlllIII;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.InvalidObjectException;
import java.io.ObjectStreamException;
import java.security.cert.CertificateParsingException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0011\b\u0000\u0012\u0006\u0010\u001a\u001a\u00020\u0019¢\u0006\u0004\b\u001b\u0010\u001cJ\u000f\u0010\u0003\u001a\u00020\u0002H\u0001¢\u0006\u0004\b\u0003\u0010\u0004R\u0012\u0010\b\u001a\u00020\u0005X\u0086\u0002¢\u0006\u0006\n\u0004\b\u0006\u0010\u0007R\u001a\u0010\u0003\u001a\u00020\u0005@\u0007X\u0086\n¢\u0006\f\n\u0004\b\t\u0010\u0007\"\u0004\b\n\u0010\u000bR\u0012\u0010\u000f\u001a\u00020\fX\u0087\u0002¢\u0006\u0006\n\u0004\b\r\u0010\u000eR\u0012\u0010\n\u001a\u00020\u0005X\u0087\u0002¢\u0006\u0006\n\u0004\b\n\u0010\u0007R\u001a\u0010\u0011\u001a\u00020\u0005@\u0007X\u0087\n¢\u0006\f\n\u0004\b\u0010\u0010\u0007\"\u0004\b\b\u0010\u000bR\u001a\u0010\r\u001a\u00020\u0005@\u0007X\u0086\n¢\u0006\f\n\u0004\b\u0012\u0010\u0007\"\u0004\b\u0003\u0010\u000bR\u001a\u0010\u0006\u001a\u00020\u0005@\u0007X\u0086\n¢\u0006\f\n\u0004\b\u0013\u0010\u0007\"\u0004\b\u000f\u0010\u000bR\u0012\u0010\u0014\u001a\u00020\u0005X\u0086\u0002¢\u0006\u0006\n\u0004\b\b\u0010\u0007R\u0012\u0010\u0010\u001a\u00020\u0005X\u0086\u0002¢\u0006\u0006\n\u0004\b\u0003\u0010\u0007R\u0012\u0010\u0012\u001a\u00020\fX\u0087\u0002¢\u0006\u0006\n\u0004\b\u000f\u0010\u000eR\u001a\u0010\u0013\u001a\u00020\u00158\u0007X\u0087\u0006¢\u0006\f\n\u0004\b\u0016\u0010\u0017\u001a\u0004\b\b\u0010\u0018R\u0012\u0010\t\u001a\u00020\u0005X\u0087\u0002¢\u0006\u0006\n\u0004\b\u0014\u0010\u0007R\u0012\u0010\u0016\u001a\u00020\u0005X\u0087\u0002¢\u0006\u0006\n\u0004\b\u0011\u0010\u0007"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IlllIl11I1;", "", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/Il1llllI1I;", "b", "()LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/Il1llllI1I;", "", "g", "Z", "a", "l", "d", "(Z)V", "", "f", "Ljava/lang/String;", "c", "i", "e", "j", "k", "h", "LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/Ill1llllll;", "m", "LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/Ill1llllll;", "()LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/Ill1llllll;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1ll11lI1l;", "p0", "<init>", "(LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1ll11lI1l;)V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class IlllIl11I1 {

    /* renamed from: a, reason: from kotlin metadata */
    public boolean h;

    /* renamed from: b, reason: from kotlin metadata */
    public boolean i;

    /* renamed from: c, reason: from kotlin metadata */
    public String j;
    public boolean d;

    /* renamed from: e, reason: from kotlin metadata */
    public boolean m;

    /* renamed from: f, reason: from kotlin metadata */
    public String c;

    /* renamed from: g, reason: from kotlin metadata */
    public boolean a;

    /* renamed from: h, reason: from kotlin metadata */
    public boolean l;

    /* renamed from: i, reason: from kotlin metadata */
    private boolean e;

    /* renamed from: j, reason: from kotlin metadata */
    private boolean f;

    /* renamed from: k, reason: from kotlin metadata */
    private boolean g;

    /* renamed from: l, reason: from kotlin metadata */
    private boolean b;

    /* renamed from: m, reason: from kotlin metadata */
    private Ill1llllll k;

    public IlllIl11I1(I1ll11lI1l i1ll11lI1l) {
        Intrinsics.checkNotNullParameter(i1ll11lI1l, I1I1lI1II1.a(new byte[]{93, 23, 13, 11}));
        this.e = i1ll11lI1l.b().a();
        this.f = i1ll11lI1l.b().getF();
        this.g = i1ll11lI1l.b().b();
        this.h = i1ll11lI1l.b().c();
        this.b = i1ll11lI1l.b().getB();
        this.i = i1ll11lI1l.b().getI();
        this.j = i1ll11lI1l.b().g();
        this.d = i1ll11lI1l.b().h();
        this.m = i1ll11lI1l.b().i();
        this.c = i1ll11lI1l.b().getC();
        this.a = i1ll11lI1l.b().getA();
        this.l = i1ll11lI1l.b().l();
        this.k = i1ll11lI1l.getC();
    }

    public final void a(boolean z) {
        this.e = z;
    }

    public final void b(boolean z) throws InvalidObjectException {
        this.f = z;
        if (IllIlllIII.l11I11I11l(8331)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{95, 86, 40, 31, 81, 77, 6, 120, 117, 87, 73, 68, 126, 107, 87, 124, 20, 82, 35, 85}));
        }
    }

    public final void c(boolean z) throws CertificateParsingException {
        this.g = z;
        if (l111Il1lI1.l1l1Il1I11(I1I1lI1II1.a(new byte[]{113, 86, 83, 21, 35, 66, 71, 117, 87, 49, 97, 6, 109, 117, 110, 119, 84, 37, 49, 11, 91, 48}), I1I1lI1II1.a(new byte[]{123, 39, 46, 28, 3, 103, 123, 73, 86, 92, 93, 124, 111, 108}))) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{120, 28, 24, 54, 22, 87, 85, Byte.MAX_VALUE, 79, 29}));
        }
    }

    public final void d(boolean z) throws EOFException {
        if (I1Ill1lIII.IlIllIll1I(9853)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{86, 86, 4, 43, 87, 64, 99, 99, 107, 40, 118, 94}));
        }
        this.b = z;
        if (Il11II1llI.l11I11I11l(842)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{84, 50, 84, 61}));
        }
    }

    public final Ill1llllll a() throws ObjectStreamException {
        Ill1llllll ill1llllll = this.k;
        if (Ill11ll111.I1lI11IIll(I1I1lI1II1.a(new byte[]{95, 2, 59, 85, 4, 4, 70, 65, 108, 5, 4, 70, 120, Byte.MAX_VALUE, 117, 109, 38, 49, 11, 112, 113, 32, 91, 116, 105, 112, 97, 51, 10, 11, 90, 46}), 1614796769L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{5, 44, 59, 86, 82, 87, 124, 101, 99, 29, 5, 6, 96, 114, 13, 97, 39, 43}));
        }
        return ill1llllll;
    }

    public final Il1llllI1I b() throws CharConversionException {
        if (this.m && !Intrinsics.a((Object) this.c, (Object) I1I1lI1II1.a(new byte[]{67, 29, 18, 0}))) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{116, 8, 3, 22, 17, 21, 83, 89, 74, 7, 66, 89, 88, 80, 90, 84, 22, 14, 16, 18, 67, 9, 92, 64, 95, 82, 23, 11, 11, 70, 23, 1, 82, 68, 17, 21, 7, 86, 94, 86, 80, 1, 84, 16, 66, 81, 81, 91, 66, 0, 16, 64, 81, 24, 19, 69, 92, 90, 78, 8, 11, 64, 71, 11, 94, 23, 15, 69, 11, 70, 23, 67, 73, 1, 83, 89, 83, 80, 81, 81}).toString());
        }
        if (!this.i) {
            if (!Intrinsics.a((Object) this.j, (Object) "    ")) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{126, 10, 6, 0, 12, 65, 23, 67, 81, 11, 69, 92, 81, 25, 90, 90, 22, 65, 0, 87, 16, 18, 67, 80, 80, 95, 81, 12, 1, 86, 23, 20, 95, 1, 12, 69, 6, 80, 81, 81, 76, 8, 68, 16, 69, 75, 93, 91, 22, 8, 12, 85, 16, 12, 92, 81, 86, 22, 94, 22, 68, 71, 68, 6, 83}).toString());
            }
        } else if (!Intrinsics.a((Object) this.j, (Object) "    ")) {
            String str = this.j;
            boolean z = false;
            int i = 0;
            while (true) {
                boolean z2 = true;
                if (i >= str.length()) {
                    z = true;
                    break;
                }
                char cCharAt = str.charAt(i);
                if (cCharAt != ' ' && cCharAt != '\t' && cCharAt != '\r' && cCharAt != '\n') {
                    z2 = false;
                }
                if (!z2) {
                    break;
                }
                i++;
            }
            if (!z) {
                throw new IllegalArgumentException((I1I1lI1II1.a(new byte[]{120, 10, 14, 28, 66, 66, 95, 89, 77, 1, 67, 64, 84, 90, 81, 25, 66, 21, 3, 80, 28, 65, 93, 80, 68, 90, 94, 11, 1, 18, 86, 13, 83, 68, 1, 4, 16, 71, 94, 81, 94, 1, 16, 66, 80, 77, 65, 71, 12, 65, 3, 64, 85, 65, 82, 89, 95, 89, 64, 0, 0, 18, 86, 16, 23, 20, 16, 0, 22, 65, 78, 16, 73, 22, 89, 94, 65, 25, 71, 76, 15, 3, 13, 94, 67, 79, 19, 125, 82, 82, 23}) + this.j).toString());
            }
        }
        Il1llllI1I il1llllI1I = new Il1llllI1I(this.e, this.g, this.h, this.b, this.i, this.f, this.j, this.d, this.m, this.c, this.a, this.l);
        if (lIIlI111II.I1111IIl11(3949)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{4, 22, 90, 93, 27, 90, 115, 95}));
        }
        return il1llllI1I;
    }
}
