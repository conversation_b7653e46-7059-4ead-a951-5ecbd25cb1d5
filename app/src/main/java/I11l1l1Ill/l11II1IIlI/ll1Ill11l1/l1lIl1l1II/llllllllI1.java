package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import II1Ill1I1I.lI1l11lIll.l11l1l1l11.IIl1I11lIl;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.lIIII1l1lI;
import java.io.ObjectStreamException;
import java.lang.annotation.Annotation;
import java.net.PortUnreachableException;
import java.util.List;
import java.util.concurrent.TimeoutException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.al;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
@Metadata(d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u001b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\bÂ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u001a\u0010\u001bJ\u001e\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\u0006\u0010\u0007J\u0018\u0010\b\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\b\u0010\tJ\u0018\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\nH\u0096\u0001¢\u0006\u0004\b\u0006\u0010\u000bJ\u0018\u0010\f\u001a\u00020\n2\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\f\u0010\rJ\u0018\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0003\u001a\u00020\u0002H\u0096\u0001¢\u0006\u0004\b\u000f\u0010\u0010R\u001a\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0011R\u0014\u0010\b\u001a\u00020\u00028\u0017X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\b\u0010\u0012R\u0014\u0010\f\u001a\u00020\u000e8WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\f\u0010\u0013R\u0014\u0010\u000f\u001a\u00020\u000e8WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u000f\u0010\u0013R\u0014\u0010\u0015\u001a\u00020\u00148\u0017X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0015\u0010\u0016R\u001a\u0010\u0018\u001a\u00020\n8\u0017X\u0097D¢\u0006\f\n\u0004\b\f\u0010\u0017\u001a\u0004\b\u0018\u0010\u0019"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llllllllI1;", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "", "p0", "", "", "a", "(I)Ljava/util/List;", "b", "(I)LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "", "(Ljava/lang/String;)I", "c", "(I)Ljava/lang/String;", "", "d", "(I)Z", "()Ljava/util/List;", "()I", "()Z", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/lI1llII1I1;", "e", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/lI1llII1I1;", "Ljava/lang/String;", "f", "()Ljava/lang/String;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class llllllllI1 implements IIll1IIlII {
    public static final llllllllI1 INSTANCE = new llllllllI1();

    /* renamed from: c, reason: from kotlin metadata */
    private static final String f = I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 89, 8, 88, 89, 14, 80, 83, 94, 121, 13});
    private final /* synthetic */ IIll1IIlII b = IIl1I11lIl.c(IIl1I11lIl.a(al.INSTANCE), IlIlIlI11I.INSTANCE).getA();

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int a(String p0) throws ObjectStreamException {
        if (lI1l1I1l1l.Il1IIlI1II(I1I1lI1II1.a(new byte[]{1, 18, 12, 45, 4, 98, 117, 98, 108, 18, 103, 0, 95, 72, 85, 121, 20, 15, 49, 74, Byte.MAX_VALUE, 12, 1, 71, 98, 67}), 268680055L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{102, 38, 84, 42, 0, 69, 88, 122, 96, 38, 113, 118, 80, 79}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{89, 5, 15, 0}));
        int iA = this.b.a(p0);
        if (!androidx.constraintlayout.widget.IIlI1Il1lI.I11II1I1I1(I1I1lI1II1.a(new byte[]{90, 60, 35, 36}))) {
            return iA;
        }
        Log.v(I1I1lI1II1.a(new byte[]{67, 32, 13, 51, 18, 120, 84, 115, 125, 50, 7, 115, 111}), I1I1lI1II1.a(new byte[]{94, 0, 55, 44, 23, 120, 78, 7, 78, 85, 6, 69, 94, 79, 7}));
        return 0;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a() throws TimeoutException {
        if (lIIII1l1lI.l11I11I11l(3319)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{88, 32, 85, 28, 51, 116, 94, 68, 81, 29, 94, 98, 90, 109, 76, 77, 50, 89, 5, 101, 70, 45, 103, 76, 1, 64, 68, 13}));
        }
        List<Annotation> listA = this.b.a();
        if (l1l1I111I1.I1lllI1llI(I1I1lI1II1.a(new byte[]{70, 41, 13, 53, 44, 98, 5, 92, 9, 47, 64, 104, 87}))) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{93, 87, 58, 46, 85, 92, 77}));
        }
        return listA;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a(int p0) {
        return this.b.a(p0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int b() {
        return this.b.b();
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public IIll1IIlII b(int p0) {
        return this.b.b(p0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public String c(int p0) throws InstantiationException {
        if (IIIlIll111.IlII1Illll(174071028L)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{116, 38, 59}));
        }
        return this.b.c(p0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean c() throws PortUnreachableException {
        if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{110, 37, 1, 15, 19, 99, 98}), 1392154426L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{120, 30, 47, 42, 40, 6, 81, 101, 72, 82, 4, 7, 108, 84, 112, 125, 33, 45, 91, 6, 0, 35, 5, 2, 126, 4, 111, 39, 21, 64, 125}));
        }
        return this.b.c();
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d() {
        boolean zD = this.b.d();
        if (I1Ill1lIII.l11I11I11l(354857379L)) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{89, 45, 16, 51, 21, 101, 78, 117, 65, 80, 67, 69, 98, 105, 86}));
        }
        return zD;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d(int p0) {
        return this.b.d(p0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: e */
    public I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.lI1llII1I1 getH() {
        if (!IIIlIll111.Ill1lIIlIl(9971)) {
            return this.b.getH();
        }
        Log.w(I1I1lI1II1.a(new byte[]{69, 93, 83, 44, 13, 89, 71, 93, 67, 29}), I1I1lI1II1.a(new byte[]{64, 0, 0, 55, 52, 12, 15, 119, 108, 41, 65, 82, 98, 92, 91, 83, 48, 55, 7, 72, 6, 16, 125, 100, 89, 84, 69, 3, 92}));
        return null;
    }

    private llllllllI1() {
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: f */
    public String getD() throws ObjectStreamException {
        String str = f;
        if (IlIIlI11I1.l11I11I11l(4344)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{94, 34, 46, 83, 82, 118, 98, 87, 111, 45, 86, 96, 114, 13, 3, 79, 58, 43, 87, 64, 73, 48, 92, 123}));
        }
        return str;
    }
}
