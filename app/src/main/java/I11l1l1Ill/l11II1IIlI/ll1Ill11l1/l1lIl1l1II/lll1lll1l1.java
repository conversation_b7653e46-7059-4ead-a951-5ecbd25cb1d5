package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/lll1lll1l1;", "", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class lll1lll1l1 {
    public /* synthetic */ lll1lll1l1(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    private lll1lll1l1() {
    }
}
