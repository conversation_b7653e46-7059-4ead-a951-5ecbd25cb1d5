package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IlIIl111lI;
import android.support.v4.graphics.drawable.I111lIl11I;
import androidx.constraintlayout.widget.Il1lII1l1l;
import java.security.cert.CertificateExpiredException;
import java.util.Map$Entry;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0016\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010&\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u00012\u0012\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003H\n¢\u0006\u0002\b\u0006"}, d2 = {"<anonymous>", "", "<name for destructuring parameter 0>", "", "", "Lkotlinx/serialization/json/JsonElement;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class I1l1Il1II1 extends s implements Function1<Map$Entry<? extends String, ? extends lII11llIIl>, CharSequence> {
    public static final I1l1Il1II1 INSTANCE = new I1l1Il1II1();

    I1l1Il1II1() {
        super(1);
    }

    @Override // kotlin.jvm.functions.Function1
    public /* synthetic */ CharSequence invoke(Map$Entry<? extends String, ? extends lII11llIIl> map$Entry) throws CertificateExpiredException {
        if (I111lIl11I.IlIllIll1I(935018033L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{7, 46, 56, 15, 84, 96, 113, 103, 107, 87, 66, 105, 125}));
        }
        CharSequence charSequenceInvoke = invoke((Map$Entry<String, ? extends lII11llIIl>) map$Entry);
        if (Il1lII1l1l.l11I11I11l(413096375L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{3, 44}));
        }
        return charSequenceInvoke;
    }

    public final CharSequence invoke(Map$Entry<String, ? extends lII11llIIl> map$Entry) {
        Intrinsics.checkNotNullParameter(map$Entry, I1I1lI1II1.a(new byte[]{11, 10, 3, 8, 7, 21, 81, 95, 75, 68, 84, 85, 70, 77, 70, 64, 1, 21, 23, 64, 89, 15, 84, 21, 67, 87, 69, 4, 9, 87, 67, 6, 69, 68, 82, 91}));
        String key = map$Entry.getKey();
        lII11llIIl value = map$Entry.getValue();
        StringBuilder sb = new StringBuilder();
        IlIIl111lI.a(sb, key);
        sb.append(':');
        sb.append(value);
        String string = sb.toString();
        Intrinsics.checkNotNullExpressionValue(string, I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 117, 69, 80, 8, 84, 85, 71, 17, 29, 27, 3, 17, 18, 94, 73, 73, 81, 64, 90, 90, 83, 0, 22, 115, 84, 23, 94, 11, 12, 76, 76, 65, 88, 99, 77, 22, 89, 94, 82, 17, 29}));
        return string;
    }
}
