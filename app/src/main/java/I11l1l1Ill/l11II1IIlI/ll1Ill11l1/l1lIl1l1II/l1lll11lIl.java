package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.ll1ll1I1I1;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.google.android.gms.measurement.api.AppMeasurementSdk$ConditionalUserProperty;
import java.io.InterruptedIOException;
import java.security.AccessControlException;
import java.security.KeyException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CRLException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateExpiredException;
import java.util.Collection;
import java.util.Map;
import java.util.Map$Entry;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import kotlin.Metadata;
import kotlin.collections.q;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.a.a;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\u0010&\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u001e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0000\n\u0002\b\u0006\b\u0007\u0018\u0000 !2\u00020\u00012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0002:\u0001!B\u0019\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0002¢\u0006\u0002\u0010\u0005J\u0011\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0003H\u0096\u0001J\u0011\u0010\u0018\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u0001H\u0096\u0001J\u0013\u0010\u001a\u001a\u00020\u00162\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0096\u0002J\u0013\u0010\u001d\u001a\u0004\u0018\u00010\u00012\u0006\u0010\u0017\u001a\u00020\u0003H\u0096\u0003J\b\u0010\u001e\u001a\u00020\u000eH\u0016J\t\u0010\u001f\u001a\u00020\u0016H\u0096\u0001J\b\u0010 \u001a\u00020\u0003H\u0016R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0002X\u0082\u0004¢\u0006\u0002\n\u0000R$\u0010\u0006\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\b0\u0007X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\t\u0010\nR\u0018\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0007X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\f\u0010\nR\u0012\u0010\r\u001a\u00020\u000eX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u000f\u0010\u0010R\u0018\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0012X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0013\u0010\u0014¨\u0006\""}, d2 = {"Lkotlinx/serialization/json/JsonObject;", "Lkotlinx/serialization/json/JsonElement;", "", "", "content", "(Ljava/util/Map;)V", "entries", "", "", "getEntries", "()Ljava/util/Set;", "keys", "getKeys", "size", "", "getSize", "()I", "values", "", "getValues", "()Ljava/util/Collection;", "containsKey", "", "key", "containsValue", AppMeasurementSdk$ConditionalUserProperty.VALUE, "equals", "other", "", "get", "hashCode", "isEmpty", "toString", "Companion", "kotlinx-serialization-json"}, k = 1, mv = {1, 7, 1}, xi = 48)
@ll1ll1I1I1(a = IIl1II1lII.class)
/* loaded from: classes.dex */
public final class l1lll11lIl extends lII11llIIl implements Map<String, lII11llIIl>, a {
    public static final llIl1IlllI a = new llIl1IlllI(null);
    private final Map<String, lII11llIIl> c;

    @Override // java.util.Map
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public lII11llIIl remove(Object obj) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    public Set<Map$Entry<String, lII11llIIl>> a() {
        return this.c.entrySet();
    }

    public boolean a(lII11llIIl lii11lliil) {
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        return this.c.containsValue(lii11lliil);
    }

    public boolean a(String str) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        boolean zContainsKey = this.c.containsKey(str);
        if (!Il1lII1l1l.I1lllI1llI(288394432L)) {
            return zContainsKey;
        }
        Log.i(I1I1lI1II1.a(new byte[]{79, 54, 47, 45, 9, 76, 109, 101, 104, 50}), I1I1lI1II1.a(new byte[]{79, 80, 23, 13, 17, 118, 65, 1, 106, 21, 65, 99, 90, 106, 112, 103, 18, 18, 36, 74, 8, 17, 97, 87, 85, 108, 14, 22}));
        return false;
    }

    public lII11llIIl b(String str) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        return this.c.get(str);
    }

    public Set<String> b() throws CRLException {
        if (lI1lI11Ill.IlIllIll1I(I1I1lI1II1.a(new byte[]{83, 45, 16, 7, 49, 109, 78, 68, 119, 38, 4}), 421723208L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{7, 0, 23, 47, 35}));
        }
        return this.c.keySet();
    }

    public int c() {
        int size = this.c.size();
        if (Il1l11I11I.l1l1Il1I11(I1I1lI1II1.a(new byte[]{89, 7, 40, 55, 5, Byte.MAX_VALUE, 7, 118, 84, 21, 66, 118, 124, 79, 81, 103, 11, 32, 37}), 167323703L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{122, 7, 53, 10, 33}));
        }
        return size;
    }

    @Override // java.util.Map
    public void clear() {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl compute(String str, BiFunction<? super String, ? super lII11llIIl, ? extends lII11llIIl> biFunction) {
        if (I111lIl11I.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{102, 60, 23, 42, 81, 68, 5, 69, 116, 49, 6, 94, 69, 80, 12, 81, 14, 46}), 631070002L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{14, 14, 83, 13, 6, 96, 92, 64, 113, 30, 99, 100, 121, 113, 90, 119, 47, 36}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl computeIfAbsent(String str, Function<? super String, ? extends lII11llIIl> function) throws CertStoreException {
        if (IlIIlI11I1.I1lllI1llI(163007875L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{70, 92, 0, 32, 40, 64, 15, 70, 114, 49, 67, 74, 76, 1, 87, 125, 53, 32, 52, 117, 85, 7, 121, 111, 120, 91, 120}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl computeIfPresent(String str, BiFunction<? super String, ? super lII11llIIl, ? extends lII11llIIl> biFunction) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    public Collection<lII11llIIl> d() throws NoSuchFieldException {
        if (lIIlI111II.Il1lII1l1l(3258)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{89, 53, 53, 23, 32, 81, 96, 87, 107}));
        }
        return this.c.values();
    }

    @Override // java.util.Map
    public boolean isEmpty() {
        return this.c.isEmpty();
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl merge(String str, lII11llIIl lii11lliil, BiFunction<? super lII11llIIl, ? super lII11llIIl, ? extends lII11llIIl> biFunction) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl put(String str, lII11llIIl lii11lliil) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public void putAll(Map<? extends String, ? extends lII11llIIl> map) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl putIfAbsent(String str, lII11llIIl lii11lliil) throws KeyException {
        if (lIIlI111II.I1111IIl11(961)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{14, 32}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public boolean remove(Object obj, Object obj2) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ lII11llIIl replace(String str, lII11llIIl lii11lliil) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public /* synthetic */ boolean replace(String str, lII11llIIl lii11lliil, lII11llIIl lii11lliil2) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.Map
    public void replaceAll(BiFunction<? super String, ? super lII11llIIl, ? extends lII11llIIl> biFunction) throws InterruptedException {
        if (!androidx.constraintlayout.widget.IIlI1Il1lI.I111IlIl1I(277858435L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
        }
        throw new InterruptedException(I1I1lI1II1.a(new byte[]{94, 84, 32, 23, 4, 68, 2, 0, 13, 19, 121, 91, 2, 86, 126, 97, 83, 21, 36, 81, 91}));
    }

    @Override // java.util.Map
    public final boolean containsKey(Object obj) throws NoSuchAlgorithmException, InterruptedIOException {
        if (!(obj instanceof String)) {
            if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{100, 43, 82, 92, 43, 126, 118, 125}), 275160357L)) {
                throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{15, 40, 36, 29, 16, 94, 109, 114, 77, 18, 68, 98, 111, 116, 125, 4, 17, 83, 56, 67, 94, 36, 117, 116, 126, 88}));
            }
            return false;
        }
        boolean zA = a((String) obj);
        if (Il1lII1l1l.I111IlIl1I(2749)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{71}));
        }
        return zA;
    }

    @Override // java.util.Map
    public final boolean containsValue(Object obj) {
        if (obj instanceof lII11llIIl) {
            return a((lII11llIIl) obj);
        }
        return false;
    }

    @Override // java.util.Map
    public final Set<Map$Entry<String, lII11llIIl>> entrySet() {
        return a();
    }

    @Override // java.util.Map
    public final /* synthetic */ lII11llIIl get(Object obj) {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{101, 7}), 201872789L)) {
            Log.d(I1I1lI1II1.a(new byte[]{70, 5, 14, 87, 15, 2, 15, 68, 115, 45, 116}), I1I1lI1II1.a(new byte[]{80}));
            return null;
        }
        if (obj instanceof String) {
            return b((String) obj);
        }
        if (IIlII1IIIl.Il11lIlI1I(I1I1lI1II1.a(new byte[]{116, 13, 20, 6, 10, 95, 97, 104, 97, 0, 69, 4, 84, 99, 78, 91, 5, 32, 1, 106, 69, 35, 121, 95, 2, 5, 98, 52, 55, 85, 0}), I1I1lI1II1.a(new byte[]{7, 42, 59, 29, 1, 7, 112, 126, 93, 44, 70, 73, 101}))) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{4, 8, 22, 42, 91, 83, 79, 67, 119, 39, 9, 67, 123, 109, 109, 99, 42, 39, 1, 115, 1, 12, 69, 126, 88, 67, 109, 44, 84, 112, 1, 23}));
        }
        return null;
    }

    @Override // java.util.Map
    public final Set<String> keySet() throws CRLException {
        if (Il1llIl111.Il1IIlI1II(I1I1lI1II1.a(new byte[]{91, 81, 16, 40, 85}), 555277776L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{88, 23, 52, 18, 87, 70, 118, 66}));
        }
        return b();
    }

    @Override // java.util.Map
    public final int size() {
        if (androidx.core.location.lIIlI111II.l111lI11I1(393)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{93, 85, 59, 87, 40, 13, 80, 72, 117, 82, 84, 116, 80, 83, 112, 84, 23, 59, 50, 72, 9, 80, 125}));
        }
        return c();
    }

    @Override // java.util.Map
    public final Collection<lII11llIIl> values() throws NoSuchFieldException {
        Collection<lII11llIIl> collectionD = d();
        if (IllllI11Il.I1II1111ll(1177546170L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{123, 6, 42, 18, 3, 108, 84, 66, 116, 46, 70, 117, 84, 14, 2, 112, 18, 19, 83, 121, 5, 27, 81, 67, 73, 1, 86, 6, 87, 85, 86}));
        }
        return collectionD;
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public l1lll11lIl(Map<String, ? extends lII11llIIl> map) {
        super(null);
        Intrinsics.checkNotNullParameter(map, I1I1lI1II1.a(new byte[]{84, 11, 12, 17, 7, 91, 67}));
        this.c = map;
    }

    @Override // java.util.Map
    public boolean equals(Object other) throws InterruptedIOException, CertificateExpiredException {
        if (androidx.constraintlayout.widget.lIIlI111II.I1lll11llI(3329)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{14, 50, 18, 7, 56, 119, 123, 94, 116, 9, 6, 95, 113, 114, 67, 119, 8, 8}));
        }
        boolean zA = Intrinsics.a(this.c, other);
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.IIll1l1lII(203250680L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{78, 41}));
        }
        return zA;
    }

    @Override // java.util.Map
    public int hashCode() {
        return this.c.hashCode();
    }

    public String toString() throws ClassNotFoundException {
        String strA = q.a(this.c.entrySet(), I1I1lI1II1.a(new byte[]{27}), I1I1lI1II1.a(new byte[]{76}), I1I1lI1II1.a(new byte[]{74}), 0, null, I1l1Il1II1.INSTANCE, 24, null);
        if (l1IIll1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{84, 48, 0, 85, 58, 112, 118, 66, 75, 45, 105, 100, 113, 11, 5, 112, 87, 54, 41, 118, 71, 13}), 3880)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{109, 33, 81, 12, 91, 71, 71, 125, Byte.MAX_VALUE, 92, 102, 85, 81, 107, 69, 70, 85, 24, 42, 122}));
        }
        return strA;
    }
}
