package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import android.accounts.utils.I1lllI11II;
import androidx.core.location.Il1l11I11I;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.net.UnknownHostException;
import java.security.AccessControlException;
import java.security.InvalidKeyException;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1lll111Il {
    public static final lII11llIIl a(lI1IIIIll1 li1iiiill1, String str, Boolean bool) {
        if (llIlI11III.l11I11I11l(4770)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{64, 14, 13, 0, 8, 88, 94, 118, 74, 45, 93, 6, 67, 109, 12, 70, 32, 56, 90, 126, 74, 15, 112, 68, 93, Byte.MAX_VALUE}));
        }
        Intrinsics.checkNotNullParameter(li1iiiill1, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        return li1iiiill1.a(str, l111l1llIl.a(bool));
    }

    public static final lII11llIIl a(lI1IIIIll1 li1iiiill1, String str, Number number) throws InvalidKeyException {
        if (Il1l11I11I.I111IlIl1I(529984580L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{0}));
        }
        Intrinsics.checkNotNullParameter(li1iiiill1, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        return li1iiiill1.a(str, l111l1llIl.a(number));
    }

    public static final lII11llIIl a(lI1IIIIll1 li1iiiill1, String str, String str2) {
        if (I1lllI11II.I1lllI1llI(297476953L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{80, 10, 7, 83}));
        }
        Intrinsics.checkNotNullParameter(li1iiiill1, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{92, 1, 27}));
        return li1iiiill1.a(str, l111l1llIl.a(str2));
    }
}
