package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.ll1ll1I1I1;
import kotlin.Metadata;
import kotlin.l;
import kotlin.m;
import kotlin.p;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\bÇ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\b\u0010\tR\u001a\u0010\u0007\u001a\u00020\u00028\u0017X\u0097D¢\u0006\f\n\u0004\b\u0003\u0010\u0004\u001a\u0004\b\u0005\u0010\u0006"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1lI1I1lll;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llIII111Il;", "", "d", "Ljava/lang/String;", "b", "()Ljava/lang/String;", "a", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
@ll1ll1I1I1(a = llI1lIlIlI.class)
/* loaded from: classes.dex */
public final class I1lI1I1lll extends llIII111Il {
    public static final I1lI1I1lll INSTANCE = new I1lI1I1lll();

    /* renamed from: d, reason: from kotlin metadata */
    private static final String a = I1I1lI1II1.a(new byte[]{89, 17, 14, 9});
    private static final /* synthetic */ l<I11IIl1I1I<Object>> e = m.a(p.PUBLICATION, IIlI1Il1lI.INSTANCE);

    private I1lI1I1lll() {
        super(null);
    }

    @Override // I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II.llIII111Il
    public String b() {
        return a;
    }
}
