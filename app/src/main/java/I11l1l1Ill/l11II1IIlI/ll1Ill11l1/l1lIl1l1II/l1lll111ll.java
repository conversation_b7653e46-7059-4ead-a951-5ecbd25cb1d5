package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.UnsupportedEncodingException;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l1lll111ll {
    private static final String a = I1I1lI1II1.a(new byte[]{67, 29, 18, 0});

    public static /* synthetic */ I1ll11lI1l a(I1ll11lI1l i1ll11lI1l, Function1 function1, int i, Object obj) {
        if ((i & 1) != 0) {
            i1ll11lI1l = I1ll11lI1l.a;
        }
        return a(i1ll11lI1l, function1);
    }

    public static final I1ll11lI1l a(I1ll11lI1l i1ll11lI1l, Function1<? super IlllIl11I1, Unit> function1) throws UnsupportedEncodingException {
        Intrinsics.checkNotNullParameter(i1ll11lI1l, I1I1lI1II1.a(new byte[]{81, 22, 13, 8}));
        Intrinsics.checkNotNullParameter(function1, I1I1lI1II1.a(new byte[]{85, 17, 11, 9, 6, 80, 69, 113, 90, 16, 89, 95, 91}));
        IlllIl11I1 illlIl11I1 = new IlllIl11I1(i1ll11lI1l);
        function1.invoke(illlIl11I1);
        I1lll11IlI i1lll11IlI = new I1lll11IlI(illlIl11I1.b(), illlIl11I1.a());
        if (II1I11IlI1.l11I11I11l(I1I1lI1II1.a(new byte[]{2, 35, 42, 31, 21, 103, 109}))) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{114, 40, 45, 13, 17, 120, 70, 126, 95, 48, 71, 3}));
        }
        return i1lll11IlI;
    }
}
