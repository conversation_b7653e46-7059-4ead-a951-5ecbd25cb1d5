package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.IIlIl1IIl1;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llll1I1lll;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I1ll11lI1l;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class llll1I1lll extends I1ll11lI1l {
    public /* synthetic */ llll1I1lll(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    private llll1I1lll() {
        super(new Il1llllI1I(false, false, false, false, false, false, null, false, false, null, false, false, 4095, null), IIlIl1IIl1.a(), null);
    }
}
