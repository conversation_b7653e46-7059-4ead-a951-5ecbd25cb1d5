package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.I1lIlI1Ill;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.l111I111Il;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.I1IIIIllI1;
import android.media.content.IIl1l1IllI;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.StreamCorruptedException;
import java.security.InvalidAlgorithmParameterException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bÀ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/I11I11II1I;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llIII111Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llIII111Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/llIII111Il;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class I11I11II1I implements I11IIl1I1I<llIII111Il> {
    public static final I11I11II1I INSTANCE = new I11I11II1I();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = l111I111Il.a(I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 89, 85, 125, 124, 43, 80, 83, 3, 121, 13}), I1lIlI1Ill.INSTANCE, new IIll1IIlII[0], null, 8, null);

    private I11I11II1I() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) {
        if (IIl1l1IllI.I1lllI1llI(1427)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{102, 51, 50, 45}));
        }
        return a(i1Illl11II);
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public IIll1IIlII getDescriptor() {
        return a;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void serialize(Ill1llllll p0, llIII111Il p1) throws InvalidAlgorithmParameterException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        if (p1 instanceof I1lI1I1lll) {
            p0.a(llI1lIlIlI.INSTANCE, I1lI1I1lll.INSTANCE);
        } else {
            p0.a(Il1III1Il1.INSTANCE, (ll11llI1Il) p1);
        }
    }

    public llIII111Il a(I1Illl11II p0) throws StreamCorruptedException {
        if (l1lI1I1l11.l11I11I11l(163508504L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{78, 50, 7, 23, 27, 123, 70}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        lII11llIIl lii11lliilO = II1lIlI1ll.a(p0).o();
        if (!(lii11lliilO instanceof llIII111Il)) {
            throw I1IIIIllI1.a(-1, I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 122, 102, 118, 122, 21, 7, 13, 7, 95, 85, 15, 71, 25, 19, 83, 79, 21, 1, 81, 67, 6, 83, 68, 40, 22, 13, 91, 103, 66, 80, 9, 89, 68, 92, 79, 81, 25, 66, 9, 3, 86, 16}) + ai.b(lii11lliilO.getClass()), lii11lliilO.toString());
        }
        llIII111Il lliii111il = (llIII111Il) lii11lliilO;
        if (l1l1IllI11.IlIllIll1I(357820546L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{5, 16, 90, 49, 20, 120, 65, 70, 90, 93, 1, 118, 86, 110, 97, Byte.MAX_VALUE, 87, 51, 51, 66, 69, 35, 116, 93, 11, Byte.MAX_VALUE}));
        }
        return lliii111il;
    }
}
