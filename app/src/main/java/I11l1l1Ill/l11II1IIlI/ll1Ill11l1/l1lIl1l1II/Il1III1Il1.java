package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.I1lIlI1Ill;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.l111I111Il;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import II1Ill1I1I.lI1l11lIll.l11l1l1l11.IIl1I11lIl;
import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.I1IIIIllI1;
import android.accounts.utils.I1lllI11II;
import android.media.content.lIIlI111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.InterruptedIOException;
import java.security.AccessControlException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateExpiredException;
import kotlin.Metadata;
import kotlin.ad;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import kotlin.text.x;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bÂ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/Il1III1Il1;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/ll11llI1Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/ll11llI1Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/ll11llI1Il;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class Il1III1Il1 implements I11IIl1I1I<ll11llI1Il> {
    public static final Il1III1Il1 INSTANCE = new Il1III1Il1();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = l111I111Il.a(I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 89, 85, 5, 4, 14, 13, 43, 3, 121, 13}), I1lIlI1Ill.INSTANCE);

    private Il1III1Il1() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) throws InterruptedIOException {
        if (I1lllI11II.IlIIl111lI(I1I1lI1II1.a(new byte[]{70, 87, 14, 33, 11, 125, 125, 122, 97, 92}), 246859475L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{99}));
        }
        return a(i1Illl11II);
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    public /* synthetic */ void serialize(Ill1llllll ill1llllll, Object obj) throws CertificateExpiredException, InvalidAlgorithmParameterException {
        if (I1lllI11II.IlII1Illll(171706302L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{126, 41, 84, 61, 0, 76, 95, Byte.MAX_VALUE, 1, 28, 68, 101, 79, 97, 95, 103, 7, 50, 85, 11, 74, 32, 11, 70, 10, 122, 123, 61, 7}));
        }
        a(ill1llllll, (ll11llI1Il) obj);
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    /* renamed from: getDescriptor */
    public IIll1IIlII getA() {
        return a;
    }

    public void a(Ill1llllll p0, ll11llI1Il p1) throws CertificateExpiredException, InvalidAlgorithmParameterException {
        if (lIIlI111II.lI1lIIll11(178582932L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{7, 54, 41, 4, 17, 6, 65, 101, 80, 61, 118, 93, 3, 65, 103, 100, 50}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        if (p1.a()) {
            p0.a(p1.b());
            return;
        }
        ll11llI1Il ll11lli1il = p1;
        Long lC = l111l1llIl.c(ll11lli1il);
        if (lC != null) {
            p0.a(lC.longValue());
            if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{114, 0, 85, 17, 32, 126, 123, 83, 76, 3, 85, 97, 91, 112, 122, 91, 54, 43}), 239816937L)) {
                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{118, 12, 87, 55, 91, 1, 122, 65, 118, 43, 87, 71, 67, 87, 86, 91, 18, 45, 81, 7, 124, 8, 88, 5, 3, 120, 123, 39, 11, 65, 94}));
            }
            return;
        }
        ad adVarH = x.h(p1.b());
        if (adVarH != null) {
            p0.c(IIl1I11lIl.a(ad.a).getA()).a(adVarH.getA());
            return;
        }
        Double dE = l111l1llIl.e(ll11lli1il);
        if (dE != null) {
            p0.a(dE.doubleValue());
            return;
        }
        Boolean boolG = l111l1llIl.g(ll11lli1il);
        if (boolG != null) {
            p0.a(boolG.booleanValue());
            return;
        }
        p0.a(p1.b());
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{96, 81, 87, 23, 52, 121, 103, 67, 114, 12, 66, 100, 71, 93, 113, 125, 35, 25, 8}), 302444290L)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{113, 51, 45, 34, 18, 79, 15, 65, 88, 45, 66, 113, 91}));
        }
    }

    public ll11llI1Il a(I1Illl11II p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        lII11llIIl lii11lliilO = II1lIlI1ll.a(p0).o();
        if (!(lii11lliilO instanceof ll11llI1Il)) {
            throw I1IIIIllI1.a(-1, I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 122, 102, 118, 122, 21, 7, 13, 7, 95, 85, 15, 71, 25, 19, 83, 79, 21, 1, 81, 67, 6, 83, 68, 40, 22, 13, 91, 123, 89, 77, 1, 66, 81, 89, 21, 20, 93, 3, 5, 66}) + ai.b(lii11lliilO.getClass()), lii11lliilO.toString());
        }
        return (ll11llI1Il) lii11lliilO;
    }
}
