package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.IIl1l1IllI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.io.UnsupportedEncodingException;
import java.security.UnrecoverableEntryException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0002"}, d2 = {"<anonymous>", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class lI1llII1I1 extends s implements Function0<IIll1IIlII> {
    public static final lI1llII1I1 INSTANCE = new lI1llII1I1();

    lI1llII1I1() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final IIll1IIlII invoke() throws UnsupportedEncodingException, UnrecoverableEntryException {
        if (IIl1l1IllI.Ill1lIIlIl(3396)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{118, 16, 26, 39, 44, 116}));
        }
        IIll1IIlII descriptor = l1l1I1llII.INSTANCE.getDescriptor();
        if (lI1l1I1l1l.Ill1lIIlIl(4429)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{78, 52, 53, 93, 24, 2, 101, 94, 97, 87, 87, 123, 68, 72, 82, 86, 52, 84, 14}));
        }
        return descriptor;
    }

    @Override // kotlin.jvm.functions.Function0
    public /* synthetic */ IIll1IIlII invoke() throws UnsupportedEncodingException, UnrecoverableEntryException {
        if (l111Il1lI1.I1lI11IIll(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 80, 44, 51, 35, 88, 88, 81, 72, 48, 104}), 708426970L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{95, 32, 43, 38, 45, 66, 121, 119, 75, 16, 91, 87, 84, 123, 2, 108, 11, 5, 85, 87, 105, 81, 84, 88, 85, 6, 115, 8}));
        }
        IIll1IIlII iIll1IIlIIInvoke = invoke();
        if (l1l1IllI11.l1l1Il1I11(I1I1lI1II1.a(new byte[]{124, 43, 14}), 309858710L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{90, 55, 47, 42, 20, 86, 103, Byte.MAX_VALUE, 120, 28, 83}));
        }
        return iIll1IIlIIInvoke;
    }
}
