package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import II1Ill1I1I.lI1l11lIll.l11l1l1l11.IIl1I11lIl;
import android.support.v4.graphics.drawable.III1Il1II1;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateExpiredException;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\bÀ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u0012B\t\b\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0004\u001a\u00020\u0003H\u0017¢\u0006\u0004\b\u0005\u0010\u0006J\u001f\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0005\u0010\nR\u001a\u0010\u0005\u001a\u00020\u000b8\u0017X\u0097\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/l1l1I1llII;", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IIl1Il11Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;", "p0", "a", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/I1Illl11II;)LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IIl1Il11Il;", "LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;", "p1", "", "(LI1I1l1lI1l/l1IlI11I1l/I111IlIl1l/llIIll1IlI/Ill1llllll;LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/IIl1Il11Il;)V", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "b", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "getDescriptor", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "<init>", "()V", "III1llIlIl"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class l1l1I1llII implements I11IIl1I1I<IIl1Il11Il> {
    public static final l1l1I1llII INSTANCE = new l1l1I1llII();

    /* renamed from: b, reason: from kotlin metadata */
    private static final IIll1IIlII a = III1llIlIl.INSTANCE;

    private l1l1I1llII() {
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public /* synthetic */ Object deserialize(I1Illl11II i1Illl11II) {
        if (l1lll111II.l11I11I11l(8555)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{102, 42, 47, 39, 47, 83, 98, 72, 90, 46, 92, 99, 79, 105, 97, 84, 5, 43, 35, 96}));
        }
        return a(i1Illl11II);
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I, I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll
    public IIll1IIlII getDescriptor() throws UnsupportedEncodingException {
        IIll1IIlII iIll1IIlII = a;
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 55, 16, 83, 1, 4, 100, 84, 86, 92, 4, 4, 102, 117, 101, 102}), 164735010L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{98, 39, 15, 23, 22, 112, 125, 92, 97, 55, 96, 73, 79, 115, 115, 6, 38, 10, 18, 95, 92, 32, 124, 108, 3, 82, 102, 9, 43, 91, 86}));
        }
        return iIll1IIlII;
    }

    @Override // I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void serialize(Ill1llllll p0, IIl1Il11Il p1) throws InvalidAlgorithmParameterException {
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.l1l1l1IIlI(239294318L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{89, 12}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 69}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        II1lIlI1ll.c(p0);
        IIl1I11lIl.b(IlIlIlI11I.INSTANCE).serialize(p0, p1);
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{99}), 459199917L)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{98, 39, 83, 38, 43, 80}));
        }
    }

    public IIl1Il11Il a(I1Illl11II p0) throws CertificateExpiredException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{83, 1, 1, 10, 6, 80, 69}));
        II1lIlI1ll.b(p0);
        return new IIl1Il11Il((List) IIl1I11lIl.b(IlIlIlI11I.INSTANCE).deserialize(p0));
    }
}
