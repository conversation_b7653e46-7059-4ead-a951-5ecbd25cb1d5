package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.ll1ll1I1I1;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.II1I11IlI1;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.FileNotFoundException;
import java.io.InterruptedIOException;
import java.io.NotActiveException;
import java.io.StreamCorruptedException;
import java.io.SyncFailedException;
import java.net.BindException;
import java.net.SocketTimeoutException;
import java.net.UnknownServiceException;
import java.security.GeneralSecurityException;
import java.security.cert.CertPathBuilderException;
import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.RejectedExecutionException;
import java.util.function.UnaryOperator;
import kotlin.Metadata;
import kotlin.collections.q;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.a.a;
import kotlin.jvm.internal.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u001e\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0010(\n\u0002\b\u0002\n\u0002\u0010*\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u0000 !2\u00020\u00012\b\u0012\u0004\u0012\u00020\u00010\u0002:\u0001!B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00010\u0002¢\u0006\u0002\u0010\u0004J\u0011\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0001H\u0096\u0003J\u0017\u0010\f\u001a\u00020\n2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0096\u0001J\u0013\u0010\u000f\u001a\u00020\n2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0096\u0002J\u0011\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u0006H\u0096\u0003J\b\u0010\u0014\u001a\u00020\u0006H\u0016J\u0011\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0001H\u0096\u0001J\t\u0010\u0016\u001a\u00020\nH\u0096\u0001J\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u0018H\u0096\u0003J\u0011\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\u0001H\u0096\u0001J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u001bH\u0096\u0001J\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u001b2\u0006\u0010\u0013\u001a\u00020\u0006H\u0096\u0001J\u001f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00022\u0006\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001e\u001a\u00020\u0006H\u0096\u0001J\b\u0010\u001f\u001a\u00020 H\u0016R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00010\u0002X\u0082\u0004¢\u0006\u0002\n\u0000R\u0012\u0010\u0005\u001a\u00020\u0006X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0007\u0010\b¨\u0006\""}, d2 = {"Lkotlinx/serialization/json/JsonArray;", "Lkotlinx/serialization/json/JsonElement;", "", "content", "(Ljava/util/List;)V", "size", "", "getSize", "()I", "contains", "", "element", "containsAll", "elements", "", "equals", "other", "", "get", "index", "hashCode", "indexOf", "isEmpty", "iterator", "", "lastIndexOf", "listIterator", "", "subList", "fromIndex", "toIndex", "toString", "", "Companion", "kotlinx-serialization-json"}, k = 1, mv = {1, 7, 1}, xi = 48)
@ll1ll1I1I1(a = l1l1I1llII.class)
/* loaded from: classes.dex */
public final class IIl1Il11Il extends lII11llIIl implements List<lII11llIIl>, a {
    public static final I11lI11III a = new I11lI11III(null);
    private final List<lII11llIIl> c;

    public int a() {
        return this.c.size();
    }

    public lII11llIIl a(int i) throws BindException {
        lII11llIIl lii11lliil = this.c.get(i);
        if (lI11IlI1lI.IlIIl111lI(I1I1lI1II1.a(new byte[]{116, 12, 44, 54, 5, 111, 95}), 10295)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{1, 34, 59, 61, 23, 80, 103, 69, 123, 52, 120, 123, 76, 109, 115, 82}));
        }
        return lii11lliil;
    }

    public boolean a(lII11llIIl lii11lliil) {
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        return this.c.contains(lii11lliil);
    }

    @Override // java.util.List
    public /* synthetic */ void add(int i, lII11llIIl lii11lliil) throws StreamCorruptedException {
        if (!IIlII1IIIl.II1111I11I(I1I1lI1II1.a(new byte[]{64, 6, 42, 42, 19, 68, 113, 81, 117, 0, 114, 81, 87, 15, 77}), 384525910L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
        }
        throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{89, 20, 5, 63, 36, 84, 65, 72, 78, 3, 91, 117, 65, 120, 82, 122, 42, 80, 24, 92, 84, 54, 88, 121, 94, 120, 71}));
    }

    @Override // java.util.List, java.util.Collection
    public /* synthetic */ boolean add(Object obj) {
        if (lIIlI111II.IIlI1II1ll(162270147L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{69, 3, 38, 9, 52, 83, 2, 124, 74}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List
    public boolean addAll(int i, Collection<? extends lII11llIIl> collection) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List, java.util.Collection
    public boolean addAll(Collection<? extends lII11llIIl> collection) throws InterruptedIOException {
        if (I1lllI11II.I1lIllll1l(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 23, 26, 48, 21, 120, 123, 90, 0, 23, 65, 65, 95, 106}))) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{97, 50, 53, 80, 51, 95, 110, 114, 73, 48, 92, 87, 80, 84, 80}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    public int b(lII11llIIl lii11lliil) {
        if (Ill11ll111.I1lI11IIll(I1I1lI1II1.a(new byte[]{97, 16, 0, 33, 20, 123, 68, 100, 126, 45, 96, 71, 86, 110, 0, 66, 12}), 209154629L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{0, 30, 4, 6, 16, 122, 94, 67, 116, 40, 82, 65, 115, 87, 122, 84, 81, 38, 56, 74, 2, 51}));
        }
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        return this.c.indexOf(lii11lliil);
    }

    public int c(lII11llIIl lii11lliil) throws SocketTimeoutException {
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67}));
        int iLastIndexOf = this.c.lastIndexOf(lii11lliil);
        if (IllllI11Il.IIlIl1Illl(I1I1lI1II1.a(new byte[]{67, 30, 12, 9, 27, 108, 2, 74, 93, 28, 105, 86, 88, 73, 115, 93, 85, 12, 8, 121, 4, 20, 3}), I1I1lI1II1.a(new byte[]{15, 82, 0, 63, 11, 79, 89}))) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{113, 6, 86, 34, 85, 0, 125, 122, 110, 33, 124, 124, 115, 81, 68, 86, 26, 3, 47, 106, 70, 50, 96, 108, 116, 90, 2, 55, 85, 6, 89}));
        }
        return iLastIndexOf;
    }

    @Override // java.util.List, java.util.Collection
    public void clear() throws InterruptedException {
        if (!I111lIl11I.l1ll11I11l(I1I1lI1II1.a(new byte[]{125, 3, 39, 6, 37, 79}))) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
        }
        throw new InterruptedException(I1I1lI1II1.a(new byte[]{70, 51, 4, 0, 39, 86, 92, 8, 67, 5, 125, 113, 126, 114, 83, 5, 55, 32, 91, 2, 1, 82, 80, 90, 101, 4, 114}));
    }

    @Override // java.util.List, java.util.Collection
    public boolean containsAll(Collection<? extends Object> elements) {
        Intrinsics.checkNotNullParameter(elements, I1I1lI1II1.a(new byte[]{82, 8, 7, 8, 7, 91, 67, 67}));
        return this.c.containsAll(elements);
    }

    @Override // java.util.List, java.util.Collection
    public boolean isEmpty() {
        return this.c.isEmpty();
    }

    @Override // java.util.List, java.util.Collection, java.lang.Iterable
    public Iterator<lII11llIIl> iterator() {
        Iterator<lII11llIIl> it = this.c.iterator();
        if (lI11IlI1lI.lI11llll1I(I1I1lI1II1.a(new byte[]{66, 37, 27}), I1I1lI1II1.a(new byte[]{64, 7, 10, 83, 41, 121, 86, 84, 77, 54, 97, 86, 86, 97, 7, 90, 51, 7, 13, Byte.MAX_VALUE, 97, 21, 80, 86, 70, 6, 78, 81, 47, 115}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 1, 4, 41, 19, 126, 113, 97, 124, 18, 122, 116, 96, 14, 103, 2, 11, 51, 7, 96, 7, 35, 98, 109, 98, 91, 81, 4, 84, 98}));
        }
        return it;
    }

    @Override // java.util.List
    public ListIterator<lII11llIIl> listIterator() {
        return this.c.listIterator();
    }

    @Override // java.util.List
    public ListIterator<lII11llIIl> listIterator(int index) throws StreamCorruptedException {
        if (II1I11IlI1.ll1I1lII11(I1I1lI1II1.a(new byte[]{65, 19, 36, 43, 83, 67, 67, 7, 82, 35, 66, 123, 121, 110, 2}))) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{92, 23, 36, 29, 32, 82}));
        }
        return this.c.listIterator(index);
    }

    @Override // java.util.List
    public /* synthetic */ lII11llIIl remove(int i) throws BrokenBarrierException {
        if (Il11II1llI.I111IlIl1I(9936)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{114, 82, 54, 9, 52, 114, 94, 100, 96, 20, 122, 2, 124, 83, 68, 90, 6, 86, 58, 119, 69, 48, 96}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List, java.util.Collection
    public boolean remove(Object obj) throws IllegalAccessException {
        if (IllIlllIII.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{93, 43, 19, 43, 8, 95, 4}), 4604)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{77, 84, 27, 16, 7, 69, 83, 89, 67, 54, 98, 85, 113, 105, 108, 13, 46, 83, 82, 97, 6, 6, 95, 93, 96, 76}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List, java.util.Collection
    public boolean removeAll(Collection<? extends Object> collection) throws CertPathBuilderException {
        if (I1IllIll1l.IlIllIll1I(I1I1lI1II1.a(new byte[]{90, 12, 82, 45, 14, 2, 109, 92, 104, 20, 126, 119, 79, 85, 93, 4, 42}))) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{126, 48, 12, 35, 47, 77, 94, 97, Byte.MAX_VALUE, 93, 1, 124}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List
    public void replaceAll(UnaryOperator<lII11llIIl> unaryOperator) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List, java.util.Collection
    public boolean retainAll(Collection<? extends Object> collection) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List
    public /* synthetic */ lII11llIIl set(int i, lII11llIIl lii11lliil) {
        if (androidx.interpolator.view.animation.llIlII1IlI.Ill1lIIlIl(8647)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{91, 30, 16, 93, 46}));
        }
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List
    public void sort(Comparator<? super lII11llIIl> comparator) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{120, 20, 7, 23, 3, 65, 94, 95, 87, 68, 89, 67, 21, 87, 91, 65, 66, 18, 23, 66, 64, 14, 65, 65, 86, 82, 23, 3, 11, 64, 23, 17, 82, 5, 6, 72, 13, 91, 91, 73, 25, 7, 95, 92, 89, 92, 87, 65, 11, 14, 12}));
    }

    @Override // java.util.List
    public List<lII11llIIl> subList(int fromIndex, int toIndex) throws GeneralSecurityException, NotActiveException {
        if (I111I11Ill.l1l1Il1I11(I1I1lI1II1.a(new byte[]{90, 85, 14, 44, 26, 86, Byte.MAX_VALUE, 3, 109, 54, 0, 69, 123, 80, 97, 119, 4, 6, 26, 95, 118, 2, 125, 116}), 623898752L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{70, 87, 10, 34}));
        }
        List<lII11llIIl> listSubList = this.c.subList(fromIndex, toIndex);
        if (ll1l11I1II.I1lllI1llI(243001229L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{66, 2, 48, 53, 13, 65, 97, 90, 13, 29, 87, 86, 125, 88, 100, Byte.MAX_VALUE, 14, 50, 43, 64, 9, 24, 75, 83, 126, 99, 98, 86}));
        }
        return listSubList;
    }

    @Override // java.util.List, java.util.Collection
    public Object[] toArray() throws SyncFailedException {
        if (l1IIll1I1l.Il1IIlI1II(5307)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{94, 13, 59, 18, 41, 5, 126, 69, 78, 2, 95, 7, 3, 93, 82, 89}));
        }
        return j.a(this);
    }

    @Override // java.util.List, java.util.Collection
    public <T> T[] toArray(T[] tArr) throws ReflectiveOperationException {
        Intrinsics.checkNotNullParameter(tArr, I1I1lI1II1.a(new byte[]{86, 22, 16, 4, 27}));
        T[] tArr2 = (T[]) j.a(this, tArr);
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.I11II1I1I1(912674745L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{111, 40, 17, 82, 49, 82, 66, 7, 64}));
        }
        return tArr2;
    }

    @Override // java.util.List, java.util.Collection
    public final boolean contains(Object obj) throws SocketTimeoutException, NotActiveException {
        if (androidx.constraintlayout.widget.lIIlI111II.lI1lIIll11(582333074L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{68, 52, 59, 42, 47, 81, 5, 94, 104, 18, 1, 71, 95, 110, 98, 101, 6, 37, 56, 1, 71, 42, 75, 94, 95, 81, 81, 28, 60, 64, 14}));
        }
        if (obj instanceof lII11llIIl) {
            return a((lII11llIIl) obj);
        }
        if (I11II1l1lI.IlIllIll1I(463806464L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{125, 32}));
        }
        return false;
    }

    @Override // java.util.List
    public /* synthetic */ lII11llIIl get(int i) throws InterruptedIOException {
        if (IIlII1IIIl.l1l1l1IIlI(445461177L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{66, 51, 0, 53, 27, 82, 93, 100, 104, 85}));
        }
        return a(i);
    }

    @Override // java.util.List
    public final int indexOf(Object obj) {
        if (obj instanceof lII11llIIl) {
            return b((lII11llIIl) obj);
        }
        return -1;
    }

    @Override // java.util.List
    public final int lastIndexOf(Object obj) throws SocketTimeoutException, BrokenBarrierException, FileNotFoundException {
        if (l111Il1lI1.I1II1111ll(245034712L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 22, 80, 49, 84, 115, 101, 89, 79, 50, 126, Byte.MAX_VALUE, 98, 79, 90, 111, 45, 34, 18, 124, 86}));
        }
        if (!(obj instanceof lII11llIIl)) {
            if (lI1l1I1l1l.I1lllI1llI(172838060L)) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{6, 18, 14, 8, 16, 94, 89, 117, 113, 34, 96}));
            }
            return -1;
        }
        int iC = c((lII11llIIl) obj);
        if (android.accounts.utils.lIIlI111II.IIll1l1lII(399023140L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{96, 44}));
        }
        return iC;
    }

    @Override // java.util.List, java.util.Collection
    public final int size() {
        return a();
    }

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public IIl1Il11Il(List<? extends lII11llIIl> list) {
        super(null);
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{84, 11, 12, 17, 7, 91, 67}));
        this.c = list;
    }

    @Override // java.util.List, java.util.Collection
    public boolean equals(Object other) throws BindException, InterruptedIOException {
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.IIl1lIII11(8385)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{124, 19, 44, 87, 32, 103, 103, 85, 108, 53, 116, 83, 76, 112, 113, 82, 50, 24, 80, 120, 126, 10, 88, 68, 3, 68, 65}));
        }
        boolean zA = Intrinsics.a(this.c, other);
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.I1lI11IIll(I1I1lI1II1.a(new byte[]{122}), 185522585L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{118, 18, 8, 60, 9, 12, 3, 88, 64, 7, 124, 98, 98, 77, 76, 96, 52, 5, 42, 103, 3, 56, 0, 119, 90, 85, 96, 40, 32, 115}));
        }
        return zA;
    }

    @Override // java.util.List, java.util.Collection
    public int hashCode() throws UnknownServiceException {
        if (IIlII1IIIl.I1II1111ll(258443495L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{109, 6, 17, 49, 84, 121, 4, 122, 78, 39, 100, 118}));
        }
        return this.c.hashCode();
    }

    public String toString() {
        if (android.accounts.utils.lIIlI111II.I1111l111I(512663812L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{120, 16, 81, 51, 37, 101, 2, 64, 99, 12, 81, 2}));
        }
        String strA = q.a(this.c, I1I1lI1II1.a(new byte[]{27}), I1I1lI1II1.a(new byte[]{108}), I1I1lI1II1.a(new byte[]{106}), 0, null, null, 56, null);
        if (I111I11Ill.Ill1lIIlIl(6196)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{91, 61, 41, 12, 54, 98, 112, 98}));
        }
        return strA;
    }
}
