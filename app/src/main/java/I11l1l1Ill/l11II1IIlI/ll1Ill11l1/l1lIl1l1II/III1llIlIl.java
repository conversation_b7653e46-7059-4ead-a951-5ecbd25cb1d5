package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import II1Ill1I1I.lI1l11lIll.l11l1l1l11.IIl1I11lIl;
import android.accounts.utils.lIIIIII11I;
import android.accounts.utils.lIIlI111II;
import android.media.content.II1I11IlI1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.core.location.I1111IIl11;
import androidx.core.location.Il1l11I11I;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.StreamCorruptedException;
import java.lang.annotation.Annotation;
import java.net.MalformedURLException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableEntryException;
import java.util.List;
import java.util.concurrent.TimeoutException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
@Metadata(d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u001b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\bÂ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u001a\u0010\u001bJ\u001e\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\u0006\u0010\u0007J\u0018\u0010\b\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\b\u0010\tJ\u0018\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\nH\u0096\u0001¢\u0006\u0004\b\u0006\u0010\u000bJ\u0018\u0010\f\u001a\u00020\n2\u0006\u0010\u0003\u001a\u00020\u0002H\u0097\u0001¢\u0006\u0004\b\f\u0010\rJ\u0018\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0003\u001a\u00020\u0002H\u0096\u0001¢\u0006\u0004\b\u000f\u0010\u0010R\u001a\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00048WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0011R\u0014\u0010\b\u001a\u00020\u00028\u0017X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\b\u0010\u0012R\u0014\u0010\f\u001a\u00020\u000e8WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\f\u0010\u0013R\u0014\u0010\u000f\u001a\u00020\u000e8WX\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u000f\u0010\u0013R\u0014\u0010\u0015\u001a\u00020\u00148\u0017X\u0096\u0005¢\u0006\u0006\u001a\u0004\b\u0015\u0010\u0016R\u001a\u0010\u0018\u001a\u00020\n8\u0017X\u0097D¢\u0006\f\n\u0004\b\f\u0010\u0017\u001a\u0004\b\u0018\u0010\u0019"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/III1llIlIl;", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "", "p0", "", "", "a", "(I)Ljava/util/List;", "b", "(I)LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/IIll1IIlII;", "", "(Ljava/lang/String;)I", "c", "(I)Ljava/lang/String;", "", "d", "(I)Z", "()Ljava/util/List;", "()I", "()Z", "LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/lI1llII1I1;", "e", "()LI1IlIlll1l/llll111lll/I1lllllII1/IIl11IllI1/lI1llII1I1;", "Ljava/lang/String;", "f", "()Ljava/lang/String;", "<init>", "()V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class III1llIlIl implements IIll1IIlII {
    public static final III1llIlIl INSTANCE = new III1llIlIl();

    /* renamed from: c, reason: from kotlin metadata */
    private static final String f = I1I1lI1II1.a(new byte[]{126, 85, 83, 9, 83, 89, 6, 121, 85, 8, 30, 92, 4, 8, 125, 124, 83, 40, 43, 94, 121, 79, 95, 89, 2, Byte.MAX_VALUE, 91, 9, 85, 3, 91, 82, 25, 8, 83, 9, 43, 89, 6, 92, 8, 45, 121, 30, 124, 112, 88, 4, 43, 13, 83, 3, 121, 13});
    private final /* synthetic */ IIll1IIlII b = IIl1I11lIl.b(IlIlIlI11I.INSTANCE).getA();

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int a(String p0) {
        if (IllllI11Il.l1l1Il1I11(I1I1lI1II1.a(new byte[]{0, 48, 82, 61, 32, 112, 86, 90, 82, 83, 96, 92, 118, 126, 80, 12, 37, 48, 22, 100, 69, 48, 71}), 308011726L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{115, 81}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{89, 5, 15, 0}));
        int iA = this.b.a(p0);
        if (lIIIIII11I.I1lI11IIll(I1I1lI1II1.a(new byte[]{110, 14, 33, 17, 56, Byte.MAX_VALUE, 81, 124, 72, 14, 70, 98, 91, 105, 13, 68, 38, 59, 39, 95, 6, 20, 124, 94, 94, Byte.MAX_VALUE}), 244217248L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{113, 93, 40, 10, 13, 94, 92, 105, 116, 80, 115, 103, 70, 123, 108, 125, 47, 38, 37, 120, 102, 47, 75, 119, 113}));
        }
        return iA;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a() throws TimeoutException {
        List<Annotation> listA = this.b.a();
        if (lIIIIII11I.ll1I1lII11(I1I1lI1II1.a(new byte[]{93, 8, 55, 45}), 215359020L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{2, 55, 12, 3, 83, 66, 96, 93, 80, 83, 124, 94, 3, 93, 125, 1, 40, 6, 36, Byte.MAX_VALUE, 84}));
        }
        return listA;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public List<Annotation> a(int p0) {
        List<Annotation> listA = this.b.a(p0);
        if (lIIlI111II.I1I11l11l1(5559)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{110, 7, 11, 11}));
        }
        return listA;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public int b() throws MalformedURLException {
        int iB = this.b.b();
        if (I1111IIl11.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 48, 84, 44, 0, 99, 15, 122, 82, 47, 97, 3}), 633573345L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{77, 6, 50, 10, 35, 69, 71, 89, 9, 52, 101, 114, 69, 84, 101}));
        }
        return iB;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public IIll1IIlII b(int p0) throws UnrecoverableEntryException {
        if (II1I11IlI1.I111IlIl1I(165917356L)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{15, 37, 16, 83, 23, 87, 79, 89, 78, 15, 87, 64, 4, 75, 120, 2, 14, 15, 26, 97, 9, 2, 112, 114, 91, 126, 114}));
        }
        IIll1IIlII iIll1IIlIIB = this.b.b(p0);
        if (android.media.content.lIIlI111II.Il1IIIIlll(5976)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{85, 22, 15, 12, 82, 126, 15}));
        }
        return iIll1IIlIIB;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public String c(int p0) throws NoSuchMethodException {
        String strC = this.b.c(p0);
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{84, 39, 54, 16, 0, 65, 70, Byte.MAX_VALUE, 119, 23, 117, 126, 76, 90, 5, 70, 23, 50, 40, 100, 126}), 237258458L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{94, 87, 12, 14, 19, 118}));
        }
        return strC;
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean c() {
        return this.b.c();
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d() {
        return this.b.d();
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    public boolean d(int p0) {
        return this.b.d(p0);
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: e */
    public I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.lI1llII1I1 getH() {
        if (androidx.recyclerview.widget.content.adapter.llIlII1IlI.IllIlI1l1I(I1I1lI1II1.a(new byte[]{5, 47, 52, 54, 46, 98, 103, 1, 0, 38, 113, 120, 13, 97, 101, 123, 5, 86, 21, 122, 100, 6, 2, 92, 1, 76, 116, 36, 37, Byte.MAX_VALUE, 3, 41}), 207990870L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{89, 2, 85, 28}));
        }
        return this.b.getH();
    }

    private III1llIlIl() {
    }

    @Override // I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII
    /* renamed from: f */
    public String getD() throws StreamCorruptedException, NoSuchAlgorithmException {
        if (IlIIlI11I1.IlII1Illll(252246789L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{118, 15, 6, 36}));
        }
        String str = f;
        if (Il1l11I11I.IlII1Illll(281103821L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{80, 40, 86, 49, 54, 67, 91, 88, 12, 10}));
        }
        return str;
    }
}
