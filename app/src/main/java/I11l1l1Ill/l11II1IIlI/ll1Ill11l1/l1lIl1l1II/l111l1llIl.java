package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IlIIl111lI;
import android.accounts.utils.Ill11ll111;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.Log;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I1Ill1lIII;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.FileNotFoundException;
import java.io.ObjectStreamException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateExpiredException;
import kotlin.KotlinNothingValueException;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import kotlin.text.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l111l1llIl {
    public static final llIII111Il a(Boolean bool) throws UnrecoverableEntryException {
        if (bool == null) {
            I1lI1I1lll i1lI1I1lll = I1lI1I1lll.INSTANCE;
            if (I1Ill1lIII.l11I11I11l(423451165L)) {
                throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{0, 38, 21, 12, 81, 84, 118, 6, 79, 18, 122, 86, 115, 96, 1, 90, 42}));
            }
            return i1lI1I1lll;
        }
        return new ll11llI1Il(bool, false);
    }

    public static final llIII111Il a(Number number) {
        if (l1lll111II.I1lllI1llI(20)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{79, 45, 87, 29, 23, 64, 84, 114, 113, 43, 114, 6, 87, 76, 64, 79, 52, 52, 90, 101, 88, 40, 125, 115, 74, 122, 79, 80, 6}));
        }
        if (number == null) {
            return I1lI1I1lll.INSTANCE;
        }
        ll11llI1Il ll11lli1il = new ll11llI1Il(number, false);
        if (Ill11ll111.I111IlIl1I(210634082L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{3, 49, 17, 7, 87, 87, 114, 82, 113, 39, 74, 126}));
        }
        return ll11lli1il;
    }

    public static final llIII111Il a(String str) {
        if (IIll1llI1l.Il1IIlI1II(6997)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{0, 15, 15, 1, 9, 114, 4, 66, 125, 22, 7, 87, 1, 126, 126, 64, 40, 27, 23, 122, 100, 35, 82, 3}));
        }
        if (str != null) {
            return new ll11llI1Il(str, true);
        }
        I1lI1I1lll i1lI1I1lll = I1lI1I1lll.INSTANCE;
        if (I111I11Ill.IIll1I11lI(I1I1lI1II1.a(new byte[]{86, 43, 3, 7, 6, 114, 0, 91, 72, 55, 68, 113, 83, 88, 70, 67}))) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{94, 10, 18, 50, 56, 83, 115, 119, 73, 38, 2, 124, 4, 107, 96}));
        }
        return i1lI1I1lll;
    }

    public static final llIII111Il a(lII11llIIl lii11lliil) throws ObjectStreamException {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{116, 20, 15, 49, 8, 86, 70, 67, 123, 6, 95, 125, 111, 91, 96, 125}), 6472)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{90, 93, 33, 92, 52, 124, 65, 116, 126, 33, 113, 117, 102, 65, 93, 114, 15, 10, 49, 107, 91, 10, 0, 5, 91, 76, 92, 21, 3, 11}));
        }
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        llIII111Il lliii111il = lii11lliil instanceof llIII111Il ? (llIII111Il) lii11lliil : null;
        if (lliii111il != null) {
            return lliii111il;
        }
        a(lii11lliil, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 50, 71, 94, 93, 80, 16, 89, 70, 80}));
        throw new KotlinNothingValueException();
    }

    public static final l1lll11lIl b(lII11llIIl lii11lliil) {
        Intrinsics.checkNotNullParameter(lii11lliil, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        l1lll11lIl l1lll11lil = lii11lliil instanceof l1lll11lIl ? (l1lll11lIl) lii11lliil : null;
        if (l1lll11lil != null) {
            return l1lll11lil;
        }
        a(lii11lliil, I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 45, 87, 93, 85, 90, 16}));
        throw new KotlinNothingValueException();
    }

    public static final int a(llIII111Il lliii111il) throws NumberFormatException {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        int i = Integer.parseInt(lliii111il.b());
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{77, 43, 81, 12, 80, 115, 120, 104, 11}), 257458805L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{102, 40, 82}));
        }
        return i;
    }

    public static final long b(llIII111Il lliii111il) throws NumberFormatException {
        if (IIIlIll111.Ill1lIIlIl(6596)) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{14, 44, 80, 29, 9, 124, 100, 102, 107, 62, 8, 87, 2, 114, 124, 13}));
        }
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        long j = Long.parseLong(lliii111il.b());
        if (Il11II1llI.l11I11I11l(5677)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{115, 80, 58, 2, 55, 7, 120, 125, 85, 45, 8, 71, 2, 120, 69, 68, 6, 8, 85, 69, 74, 87, Byte.MAX_VALUE, 92, 69, 78, 99, 81, 41, 5}));
        }
        return j;
    }

    public static final Long c(llIII111Il lliii111il) {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Long lD = j.d(lliii111il.b());
        if (lIIlI111II.Il11IIIlI1(8891)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{95, 16, 27, 23, 53, 90, 103, 82, 115, 87, 90, 93, 125, 108, 121, 76, 87, 83, 84, 1, 117, 8}));
        }
        return lD;
    }

    public static final double d(llIII111Il lliii111il) {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        return Double.parseDouble(lliii111il.b());
    }

    public static final Double e(llIII111Il lliii111il) {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Double dB = j.b(lliii111il.b());
        if (IllllI11Il.IIll1I11lI(335350045L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{69, 0, 59, 29, 44, 114, 1}));
        }
        return dB;
    }

    public static final float f(llIII111Il lliii111il) throws CertificateExpiredException {
        if (llIlI11III.I111IlIl1I(I1I1lI1II1.a(new byte[]{86, 23, 90, 84, 1, 111, 79, 88, 109, 83, 4, 66, 84, 96, 80, 82, 83, 35, 52, 121, 103, 13, 112, 94, 121}), 660985284L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{96, 80, 54, 55, 0, 5, 97, 91, 97, 7, 122, 123, 66, 9, 71, 64, 14, 32, 80, 98, 125}));
        }
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        return Float.parseFloat(lliii111il.b());
    }

    public static final Boolean g(llIII111Il lliii111il) {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Boolean boolA = IlIIl111lI.a(lliii111il.b());
        if (!IllllI11Il.l1ll11I11l(I1I1lI1II1.a(new byte[]{4, 23, 23, 61, 83, 71, 99, 113, 119, 48, 88, 70, 12, 112, 1, 120, 39, 40, 83, 97, 65, 50, 0}))) {
            return boolA;
        }
        Log.i(I1I1lI1II1.a(new byte[]{101, 29, 37, 84, 44, 69, 64, 103}), I1I1lI1II1.a(new byte[]{68, 3, 81, 63, 10, 79, 120, 4, 91, 18, 99, 65, 120, 91, 87}));
        return null;
    }

    public static final String h(llIII111Il lliii111il) {
        Intrinsics.checkNotNullParameter(lliii111il, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        String strB = lliii111il instanceof I1lI1I1lll ? null : lliii111il.b();
        if (IIlI1ll1ll.IlIllIll1I(928102950L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{94, 60}));
        }
        return strB;
    }

    private static final Void a(lII11llIIl lii11lliil, String str) {
        if (IllIIIIII1.I111IlIl1I(455)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{115, 93, 91, 7, 42, 124, 92, 126, 118, 55, 94, 88, 80, 124, 114, 80, 12, 84, 81, 91, 83, 20, 101, 67, 10, 82, 4, 92, 92}));
        }
        throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{114, 8, 7, 8, 7, 91, 67, 16}) + ai.b(lii11lliil.getClass()) + I1I1lI1II1.a(new byte[]{23, 13, 17, 69, 12, 90, 67, 16, 88, 68}) + str);
    }
}
