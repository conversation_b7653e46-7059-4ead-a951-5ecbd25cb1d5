package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import android.accounts.utils.I1lllI11II;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.util.Log;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.InvalidClassException;
import java.io.ObjectStreamException;
import java.net.BindException;
import java.net.UnknownHostException;
import java.util.concurrent.RejectedExecutionException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001d\u0018\u00002\u00020\u0001B\u0081\u0001\b\u0000\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0002\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0005\u0012\b\b\u0002\u0010 \u001a\u00020\u0005¢\u0006\u0004\b!\u0010\"J\u000f\u0010\u0003\u001a\u00020\u0002H\u0017¢\u0006\u0004\b\u0003\u0010\u0004R\u0017\u0010\t\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u0006\u0010\u0007\u001a\u0004\b\u0006\u0010\bR\u0017\u0010\u000b\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\n\u0010\u0007\u001a\u0004\b\n\u0010\bR\u001a\u0010\u000e\u001a\u00020\u00028\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\f\u0010\u0004R\u001a\u0010\n\u001a\u00020\u00058\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u000f\u0010\u0007\u001a\u0004\b\u000f\u0010\bR\u0017\u0010\u0010\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\t\u0010\u0007\u001a\u0004\b\t\u0010\bR\u0017\u0010\u0011\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u0011\u0010\u0007\u001a\u0004\b\u0011\u0010\bR\u0017\u0010\u0012\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u000b\u0010\u0007\u001a\u0004\b\u000b\u0010\bR\u0017\u0010\u000f\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u000e\u0010\u0007\u001a\u0004\b\u000e\u0010\bR\u0017\u0010\u0013\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u0010\u0010\u0007\u001a\u0004\b\u0010\u0010\bR\u001a\u0010\f\u001a\u00020\u00028\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u0012\u0010\r\u001a\u0004\b\u0012\u0010\u0004R\u001a\u0010\u0006\u001a\u00020\u00058\u0007X\u0087\u0004¢\u0006\f\n\u0004\b\u0014\u0010\u0007\u001a\u0004\b\u0014\u0010\bR\u0017\u0010\u0014\u001a\u00020\u00058\u0007¢\u0006\f\n\u0004\b\u0013\u0010\u0007\u001a\u0004\b\u0013\u0010\b"}, d2 = {"LI11l1l1Ill/l11II1IIlI/ll1Ill11l1/l1lIl1l1II/Il1llllI1I;", "", "", "toString", "()Ljava/lang/String;", "", "k", "Z", "()Z", "a", "d", "b", "j", "Ljava/lang/String;", "c", "h", "e", "f", "g", "i", "l", "p0", "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9", "p10", "p11", "<init>", "(ZZZZZZLjava/lang/String;ZZLjava/lang/String;ZZ)V"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class Il1llllI1I {

    /* renamed from: a, reason: from kotlin metadata */
    private final boolean e;

    /* renamed from: b, reason: from kotlin metadata */
    private final boolean g;

    /* renamed from: c, reason: from kotlin metadata */
    private final boolean h;

    /* renamed from: d, reason: from kotlin metadata */
    private final boolean b;

    /* renamed from: e, reason: from kotlin metadata */
    private final boolean i;
    private final boolean f;

    /* renamed from: g, reason: from kotlin metadata */
    private final String j;

    /* renamed from: h, reason: from kotlin metadata */
    private final boolean d;

    /* renamed from: i, reason: from kotlin metadata */
    private final boolean l;

    /* renamed from: j, reason: from kotlin metadata */
    private final String c;

    /* renamed from: k, reason: from kotlin metadata */
    private final boolean a;

    /* renamed from: l, reason: from kotlin metadata */
    private final boolean k;

    public Il1llllI1I() {
        this(false, false, false, false, false, false, null, false, false, null, false, false, 4095, null);
    }

    public Il1llllI1I(boolean z, boolean z2, boolean z3, boolean z4, boolean z5, boolean z6, String str, boolean z7, boolean z8, String str2, boolean z9, boolean z10) {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{71, 22, 7, 17, 22, 76, 103, 66, 80, 10, 68, 121, 91, 93, 81, 91, 22}));
        Intrinsics.checkNotNullParameter(str2, I1I1lI1II1.a(new byte[]{84, 8, 3, 22, 17, 113, 94, 67, 90, 22, 89, 93, 92, 87, 85, 65, 13, 19}));
        this.e = z;
        this.g = z2;
        this.h = z3;
        this.b = z4;
        this.i = z5;
        this.f = z6;
        this.j = str;
        this.d = z7;
        this.l = z8;
        this.c = str2;
        this.a = z9;
        this.k = z10;
    }

    public final boolean a() {
        if (!lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{118, 14, 47, 49, 24, 101, 123}), 793540598L)) {
            return this.e;
        }
        Log.i(I1I1lI1II1.a(new byte[]{4}), I1I1lI1II1.a(new byte[]{14, 50, 37, 93, 58, 119, 101, 82, 76, 39}));
        return false;
    }

    public final boolean b() throws ObjectStreamException {
        boolean z = this.g;
        if (Il1I1lllIl.lll1111l11(I1I1lI1II1.a(new byte[]{92, 86, 80, 12, 38, 116, 85, 100, 96, 80, 71, 81, 3, 105, 85, 101, 22, 13}), 2808)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{125, 28, 13, 14, 37, 66, 121, 123, 13, 19, 5, 105, 65, 75, 70, 98, 49, 13, 45, 117, 3, 32, 5, 97, 94, 124, 118}));
        }
        return z;
    }

    public final boolean c() {
        if (IIll1llI1l.Il1IIlI1II(6269)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{7, 33, 90, 8, 32, 108, 103, 64, 90, 30, 100, 68, 69, 119, 123, 92, 43, 7, 20, 115, 104, 85, 70, 120, 86, 120, 5}));
        }
        return this.h;
    }

    /* renamed from: d, reason: from getter */
    public final boolean getB() {
        return this.b;
    }

    /* renamed from: e, reason: from getter */
    public final boolean getI() {
        return this.i;
    }

    /* renamed from: f, reason: from getter */
    public final boolean getF() {
        return this.f;
    }

    public final String g() {
        String str = this.j;
        if (I1lllI11II.I1II1111ll(341742634L)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{118, 47, 42, 14, 21, 7, 85, 90, 120}));
        }
        return str;
    }

    public final boolean h() throws BindException {
        boolean z = this.d;
        if (IllllI11lI.I1lllI1llI(6769)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{65, 6, 5, 44, 39}));
        }
        return z;
    }

    public final boolean i() throws InvalidClassException {
        if (I1lllI11II.l11I11I11l(3584)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{94, 29, 33, 10, 33, 111, 98, 86, 8, 37, 118, 94, 96, 8}));
        }
        boolean z = this.l;
        if (ll1l11I1II.Il1IIlI1II(I1I1lI1II1.a(new byte[]{94, 55, 54, 46, 51, 4, 69, 66, 124, 18, 71, 7, 116, 106, 12, 84, 91, 86}), 6374)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{14, 60}));
        }
        return z;
    }

    public /* synthetic */ Il1llllI1I(boolean z, boolean z2, boolean z3, boolean z4, boolean z5, boolean z6, String str, boolean z7, boolean z8, String str2, boolean z9, boolean z10, int i, DefaultConstructorMarker defaultConstructorMarker) {
        this((i & 1) != 0 ? false : z, (i & 2) != 0 ? false : z2, (i & 4) != 0 ? false : z3, (i & 8) != 0 ? false : z4, (i & 16) != 0 ? false : z5, (i & 32) != 0 ? true : z6, (i & 64) != 0 ? "    " : str, (i & 128) != 0 ? false : z7, (i & 256) != 0 ? false : z8, (i & 512) != 0 ? I1I1lI1II1.a(new byte[]{67, 29, 18, 0}) : str2, (i & 1024) == 0 ? z9 : false, (i & 2048) == 0 ? z10 : true);
    }

    /* renamed from: j, reason: from getter */
    public final String getC() {
        return this.c;
    }

    /* renamed from: k, reason: from getter */
    public final boolean getA() {
        return this.a;
    }

    public final boolean l() throws UnknownHostException {
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.IlII1Illll(268474768L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{112, 47, 37, 63, 43, 70, 110, 4, 11, 6, 70, 8, 92, 15, 115, 90, 51, 32, 1}));
        }
        return this.k;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(I1I1lI1II1.a(new byte[]{125, 23, 13, 11, 33, 90, 89, 86, 80, 3, 69, 66, 84, 77, 93, 90, 12, 73, 7, 92, 83, 14, 87, 80, 119, 83, 81, 4, 17, 94, 67, 16, 10})).append(this.e).append(I1I1lI1II1.a(new byte[]{27, 68, 11, 2, 12, 90, 69, 85, 108, 10, 91, 94, 90, 78, 90, 126, 7, 24, 17, 15})).append(this.g).append(I1I1lI1II1.a(new byte[]{27, 68, 11, 22, 46, 80, 89, 89, 92, 10, 68, 13})).append(this.h).append(I1I1lI1II1.a(new byte[]{27, 68, 3, 9, 14, 90, 64, 99, 77, 22, 69, 83, 65, 76, 70, 80, 6, 44, 3, 66, 123, 4, 74, 70, 14})).append(this.b).append(I1I1lI1II1.a(new byte[]{27, 68, 18, 23, 7, 65, 67, 73, 105, 22, 89, 94, 65, 4})).append(this.i).append(I1I1lI1II1.a(new byte[]{27, 68, 7, 29, 18, 89, 94, 83, 80, 16, 126, 69, 89, 85, 71, 8})).append(this.f).append(I1I1lI1II1.a(new byte[]{27, 68, 18, 23, 7, 65, 67, 73, 105, 22, 89, 94, 65, 112, 90, 81, 7, 15, 22, 15, 23})).append(this.j).append(I1I1lI1II1.a(new byte[]{16, 72, 66, 6, 13, 80, 69, 83, 92, 45, 94, 64, 64, 77, 98, 84, 14, 20, 7, 65, 13})).append(this.d).append(I1I1lI1II1.a(new byte[]{27, 68, 23, 22, 7, 116, 69, 66, 88, 29, 96, 95, 89, 64, 89, 90, 16, 17, 10, 91, 67, 12, 14})).append(this.l).append(I1I1lI1II1.a(new byte[]{27, 68, 1, 9, 3, 70, 68, 116, 80, 23, 83, 66, 92, 84, 93, 91, 3, 21, 13, 64, 13, 70})).append(this.c).append(I1I1lI1II1.a(new byte[]{16, 72, 66, 4, 14, 89, 88, 71, 106, 20, 85, 83, 92, 88, 88, 115, 14, 14, 3, 70, 89, 15, 84, 101, 92, 95, 89, 17, 50, 83, 91, 22, 82, 23, 95})).append(this.a).append(')');
        return sb.toString();
    }
}
