package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.IOException;
import java.net.BindException;
import kotlin.Metadata;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n¢\u0006\u0002\b\u0002"}, d2 = {"<anonymous>", "Lkotlinx/serialization/descriptors/SerialDescriptor;", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class llll1lI1II extends s implements Function0<IIll1IIlII> {
    public static final llll1lI1II INSTANCE = new llll1lI1II();

    llll1lI1II() {
        super(0);
    }

    @Override // kotlin.jvm.functions.Function0
    public final IIll1IIlII invoke() {
        return llI1lIlIlI.INSTANCE.getDescriptor();
    }

    @Override // kotlin.jvm.functions.Function0
    public /* synthetic */ IIll1IIlII invoke() throws IOException {
        if (I1I1IIIIl1.l1l1l1IIlI(5037)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{102, 18, 50, 86, 24, 70, 122, 90, 112, 50, 67, 0, 81, 12, 117, 126, 56, 20, 14, 3, 6, 9, 85, 121, 103, 83, 66, 82}));
        }
        IIll1IIlII iIll1IIlIIInvoke = invoke();
        if (llIlI11III.I1lIllll1l(I1I1lI1II1.a(new byte[]{123, 92, 8, 49, 86, 67, 70, 97, 114, 53, 85, 119, 82, 75, 6, 98, 37, 35, 38, 10, 88, 7, 95, 101, 64, 5, 5, 0}), 502401740L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{97, 0, 24, 82, 39, 7, 122, 123, 74, 52, 126, 73, 109, 109, 92, 67, 4, 19}));
        }
        return iIll1IIlIIInvoke;
    }
}
