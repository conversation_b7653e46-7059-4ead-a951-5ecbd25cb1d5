package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.I1Illl11II;
import I1I1l1lI1l.l1IlI11I1l.I111IlIl1l.llIIll1IlI.Ill1llllll;
import I1IlIlll1l.llll111lll.I1lllllII1.IIl11IllI1.IIll1IIlII;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lI1l1I1l1l;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.core.location.I1111IIl11;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.io.FileNotFoundException;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateExpiredException;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class II1lIlI1ll {
    public static final /* synthetic */ void b(I1Illl11II i1Illl11II) throws CertificateExpiredException {
        if (I1IllIll1l.I111IlIl1I(425296268L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{82}));
        }
        c(i1Illl11II);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void c(Ill1llllll ill1llllll) throws InvalidAlgorithmParameterException {
        if (lIIlI111II.lIl11IlI1l(4710)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{109, 38, 37, 46, 22, 115, 7, 65, Byte.MAX_VALUE, 22, 1, 93, 64, 99, 68, 94, 41, 47, 59, 104, 70, 4, 64}));
        }
        a(ill1llllll);
    }

    private static final void c(I1Illl11II i1Illl11II) {
        a(i1Illl11II);
    }

    public static final I1IlI111l1 a(I1Illl11II i1Illl11II) {
        if (I1lllI11II.l1ll11I11l(I1I1lI1II1.a(new byte[]{103, 39, 35, 29, 14, 112, 110, 84, 109, 93, 116, 91, 7, 91}), 368091113L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{96, 93, 18, 36, 55, 119, 4, 85, 73, 62, 2, 0, 94, 122, 97, 122, 13, 81, 27, 66, 2}));
        }
        Intrinsics.checkNotNullParameter(i1Illl11II, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        I1IlI111l1 i1IlI111l1 = i1Illl11II instanceof I1IlI111l1 ? (I1IlI111l1) i1Illl11II : null;
        if (i1IlI111l1 != null) {
            return i1IlI111l1;
        }
        throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 82, 66, 80, 5, 92, 89, 79, 92, 70, 21, 1, 0, 12, 18, 82, 4, 19, 64, 64, 83, 83, 69, 11, 92, 91, 26, 23, 19, 11, 17, 10, 21, 125, 67, 86, 10, 16, 86, 90, 75, 89, 84, 22, 79, 39, 74, 64, 4, 80, 65, 86, 82, 23, 33, 1, 81, 88, 7, 82, 22, 66, 17, 13, 21, 85, 85, 25, 46, 67, 95, 91, 125, 81, 86, 13, 5, 7, 64, 28, 65, 84, 90, 71, 22}) + ai.b(i1Illl11II.getClass()));
    }

    public static final II1111ll1l a(Ill1llllll ill1llllll) {
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{93, 29, 26, 93, 42, 113, 79, 8, 85, 16, 83, 105, 111, 124, 123, 0, 7, 11}), 228941736L)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{99, 33, 0, 31, 27, 97, 126, 65, 79, 48, 8, 70, 91, 123, 77, 6, 56, 83, 6, 99, 69, 56, 116, 12, 95, 65, 14, 53, 34, 102}));
        }
        Intrinsics.checkNotNullParameter(ill1llllll, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        II1111ll1l iI1111ll1l = ill1llllll instanceof II1111ll1l ? (II1111ll1l) ill1llllll : null;
        if (iI1111ll1l != null) {
            return iI1111ll1l;
        }
        throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 82, 66, 80, 5, 92, 89, 79, 92, 70, 21, 1, 0, 12, 18, 82, 4, 19, 64, 64, 83, 83, 69, 11, 92, 91, 26, 23, 19, 11, 17, 10, 21, 125, 67, 86, 10, 16, 86, 90, 75, 89, 84, 22, 79, 39, 74, 64, 4, 80, 65, 86, 82, 23, 32, 10, 81, 88, 7, 82, 22, 66, 17, 13, 21, 85, 85, 25, 46, 67, 95, 91, 124, 90, 86, 13, 5, 7, 64, 28, 65, 84, 90, 71, 22}) + ai.b(ill1llllll.getClass()));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final IIll1IIlII b(Function0<? extends IIll1IIlII> function0) throws UnknownHostException, FileNotFoundException {
        if (lI1l1I1l1l.Il1IIlI1II(I1I1lI1II1.a(new byte[]{78, 34, 82, 18, 15, 1, 102, 116, 12, 18, 92, 1, 2, 126, 0, 86, 58, 47, 83}), 229240460L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{65, 60, 23, 23, 54, 68, 6, Byte.MAX_VALUE, 82, 82, 102, 73, 124, 8, 119, 114, 17, 49, 37, 104, 1, 0}));
        }
        II1ll1I1lI iI1ll1I1lI = new II1ll1I1lI(function0);
        if (I1111IIl11.I1II1111ll(200627941L)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{94, 2, 83, 53, 87, 116, 70}));
        }
        return iI1ll1I1lI;
    }
}
