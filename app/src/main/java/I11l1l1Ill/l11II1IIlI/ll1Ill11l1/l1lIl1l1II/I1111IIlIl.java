package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import Il1l111IIl.I1lIIlll1l.lIll1ll1Il.IIII1I1111.IllllI11Il;
import androidx.core.location.Il1l11I11I;
import java.io.ObjectStreamException;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1111IIlIl {
    public static final IllllI11Il a(I1ll11lI1l i1ll11lI1l) throws ObjectStreamException {
        if (Il1l11I11I.l11I11I11l(480158208L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{112, 47, 26, 18, 18, 100, 64, 0, 104, 86, 104, 96, 125, 90, 64, 0, 38, 46, 54, 1, 88, 89, 80, 102, 87, 124, 79, 8, 55}));
        }
        Intrinsics.checkNotNullParameter(i1ll11lI1l, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        return i1ll11lI1l.c();
    }
}
