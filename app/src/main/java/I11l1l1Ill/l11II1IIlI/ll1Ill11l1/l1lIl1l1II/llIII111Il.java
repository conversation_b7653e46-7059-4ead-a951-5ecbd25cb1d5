package I11l1l1Ill.l11II1IIlI.ll1Ill11l1.l1lIl1l1II;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.ll1ll1I1I1;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b7\u0018\u0000 \u000b2\u00020\u0001:\u0001\u000bB\u0007\b\u0004¢\u0006\u0002\u0010\u0002J\b\u0010\n\u001a\u00020\u0004H\u0016R\u0012\u0010\u0003\u001a\u00020\u0004X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006R\u0012\u0010\u0007\u001a\u00020\bX¦\u0004¢\u0006\u0006\u001a\u0004\b\u0007\u0010\t\u0082\u0001\u0002\f\r¨\u0006\u000e"}, d2 = {"Lkotlinx/serialization/json/JsonPrimitive;", "Lkotlinx/serialization/json/JsonElement;", "()V", "content", "", "getContent", "()Ljava/lang/String;", "isString", "", "()Z", "toString", "Companion", "Lkotlinx/serialization/json/JsonLiteral;", "Lkotlinx/serialization/json/JsonNull;", "kotlinx-serialization-json"}, k = 1, mv = {1, 7, 1}, xi = 48)
@ll1ll1I1I1(a = I11I11II1I.class)
/* loaded from: classes.dex */
public abstract class llIII111Il extends lII11llIIl {
    public static final llll1I1III c = new llll1I1III(null);

    public /* synthetic */ llIII111Il(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    public abstract String b();

    private llIII111Il() {
        super(null);
    }

    public String toString() {
        return b();
    }
}
