package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.core.location.llIl1lII1I;
import com.ebook.bible.db.entity.DailyPlanCommonEntity;
import java.security.KeyException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class Il11IlIllI implements Callable<List<DailyPlanCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ II1I111llI_Impl b;

    Il11IlIllI(II1I111llI_Impl iI1I111llI_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = iI1I111llI_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ List<DailyPlanCommonEntity> call() throws Exception {
        List<DailyPlanCommonEntity> listA = a();
        if (android.support.v4.graphics.drawable.lIIllIlIl1.Il1IIlI1II(497705066L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{66}));
        }
        return listA;
    }

    public List<DailyPlanCommonEntity> a() throws Exception {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        int i17;
        int i18;
        if (llIl1lII1I.l1ll11I11l(I1I1lI1II1.a(new byte[]{65, 6, 0, 48, 50, 64, 113, 74, 87, 10, 64, 96, 3, 88, 67, 94, 21}), 227577575L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{82, 53, 42, 81, 45, 76, 118, 68, 96, 83, 119}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(this.b.a, this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 89, 84}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 68, 89, 65, 85, 81}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 89, 81}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 68, 92, 77, 88, 80}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 91, 86, 93, 92}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 90, 87, 102, 17, 66, 92}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 7, 95, 69, 91, 77}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 10, 69, 93, 87, 92, 70}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 23, 68, 81, 65, 76, 71}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 71, 66, 86, 3, 66, 85, 70, 74}));
            int columnIndex16 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
            int columnIndex17 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
            int columnIndex18 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndex19 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
            int i19 = columnIndex18;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                DailyPlanCommonEntity dailyPlanCommonEntity = new DailyPlanCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i20 = -1;
                if (columnIndex != -1) {
                    i = columnIndex8;
                    dailyPlanCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i20 = -1;
                } else {
                    i = columnIndex8;
                }
                if (columnIndex2 != i20) {
                    dailyPlanCommonEntity.setFirstTagId(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != i20) {
                    dailyPlanCommonEntity.setFirstTagTitle(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                    i20 = -1;
                }
                if (columnIndex4 != i20) {
                    dailyPlanCommonEntity.setSecondTagId(cursorQuery.getInt(columnIndex4));
                }
                if (columnIndex5 != i20) {
                    dailyPlanCommonEntity.setSecondTagTitle(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                    i20 = -1;
                }
                if (columnIndex6 != i20) {
                    dailyPlanCommonEntity.setPlanId(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != i20) {
                    dailyPlanCommonEntity.setPlanName(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i3 = i;
                    i2 = -1;
                } else {
                    i2 = i20;
                    i3 = i;
                }
                if (i3 != i2) {
                    dailyPlanCommonEntity.setPlanImgUrl(cursorQuery.isNull(i3) ? null : cursorQuery.getString(i3));
                    i2 = -1;
                }
                if (columnIndex9 != i2) {
                    dailyPlanCommonEntity.setPlanDayId(cursorQuery.getInt(columnIndex9));
                    i2 = -1;
                }
                if (columnIndex10 != i2) {
                    dailyPlanCommonEntity.setPlanDayCount(cursorQuery.getInt(columnIndex10));
                    i2 = -1;
                }
                if (columnIndex11 != i2) {
                    dailyPlanCommonEntity.setPlanDayNumber(cursorQuery.getInt(columnIndex11));
                    i2 = -1;
                }
                if (columnIndex12 != i2) {
                    dailyPlanCommonEntity.setPlanDayStatus(cursorQuery.getInt(columnIndex12));
                    i2 = -1;
                }
                if (columnIndex13 != i2) {
                    i4 = columnIndex;
                    dailyPlanCommonEntity.setDateCompleted(cursorQuery.getLong(columnIndex13));
                    i6 = columnIndex14;
                    i5 = -1;
                } else {
                    i4 = columnIndex;
                    i5 = i2;
                    i6 = columnIndex14;
                }
                if (i6 != i5) {
                    dailyPlanCommonEntity.setAudioLink(cursorQuery.isNull(i6) ? null : cursorQuery.getString(i6));
                    i9 = columnIndex15;
                    i7 = columnIndex4;
                    i8 = -1;
                } else {
                    int i21 = columnIndex15;
                    i7 = columnIndex4;
                    i8 = i5;
                    i9 = i21;
                }
                if (i9 != i8) {
                    dailyPlanCommonEntity.setAudioProgress(cursorQuery.isNull(i9) ? null : cursorQuery.getString(i9));
                    i10 = columnIndex3;
                    i12 = columnIndex16;
                    i11 = -1;
                } else {
                    i10 = columnIndex3;
                    i11 = i8;
                    i12 = columnIndex16;
                }
                if (i12 != i11) {
                    dailyPlanCommonEntity.setVerseContent(cursorQuery.isNull(i12) ? null : cursorQuery.getString(i12));
                    columnIndex16 = i12;
                    i14 = columnIndex17;
                    i13 = -1;
                } else {
                    columnIndex16 = i12;
                    i13 = i11;
                    i14 = columnIndex17;
                }
                if (i14 != i13) {
                    dailyPlanCommonEntity.setReference(cursorQuery.isNull(i14) ? null : cursorQuery.getString(i14));
                    columnIndex17 = i14;
                    i16 = i19;
                    i15 = -1;
                } else {
                    columnIndex17 = i14;
                    i15 = i13;
                    i16 = i19;
                }
                if (i16 != i15) {
                    dailyPlanCommonEntity.setInspiration(cursorQuery.isNull(i16) ? null : cursorQuery.getString(i16));
                    i19 = i16;
                    i18 = columnIndex19;
                    i17 = -1;
                } else {
                    i19 = i16;
                    i17 = i15;
                    i18 = columnIndex19;
                }
                if (i18 != i17) {
                    dailyPlanCommonEntity.setDate(cursorQuery.isNull(i18) ? null : cursorQuery.getString(i18));
                }
                arrayList2.add(dailyPlanCommonEntity);
                columnIndex19 = i18;
                columnIndex14 = i6;
                columnIndex3 = i10;
                columnIndex8 = i3;
                arrayList = arrayList2;
                columnIndex4 = i7;
                columnIndex15 = i9;
                columnIndex = i4;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
