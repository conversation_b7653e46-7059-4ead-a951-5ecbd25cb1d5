package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import com.ebook.bible.db.entity.ChapterAudioEsEntity;
import java.io.FileNotFoundException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I1II1lIl1I extends lll1l11l1I<ChapterAudioEsEntity> {
    final /* synthetic */ l1I1l1ll1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I1II1lIl1I(l1I1l1ll1I_Impl l1i1l1ll1i_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = l1i1l1ll1i_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, ChapterAudioEsEntity chapterAudioEsEntity) throws FileNotFoundException {
        a(i1lI1I1lll, chapterAudioEsEntity);
        if (l1lll111II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 8, 1, 46, 50, 115, 90, 125, 8, 28, 89, 93, 99, 94, 93, 90, 14, 6, 50, 98, 99, 89, 97, 3, 71, 87, 100}))) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{66, 86, 7, 39, 33, 125, 82}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws BrokenBarrierException {
        if (lI1lllIII1.IlII1Illll(6397)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{114, 13, 85, 55, 21, 70, 95, 89, 11, 19, 83, 84, 111, 72, Byte.MAX_VALUE, 120, 33}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 80, 94, 86, 21, 16, 87, 69, 60, 86, 17, 6, 12, 13, 106, 82, 67, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 0, 93, 95, 10, 108, 91, 82, 91, 82, 5, 72, 82, 84, 11, 86, 20, 22, 0, 16, 106, 89, 69, 84, 6, 85, 66, 85, 21, 84, 86, 10, 0, 18, 70, 85, 19, 108, 84, 70, 82, 94, 10, 59, 71, 69, 15, 87, 72, 2, 4, 23, 81, 94, 95, 102, 13, 67, 111, 81, 86, 67, 91, 14, 14, 3, 86, 80, 77, 83, 84, 70, 82, 94, 10, 59, 86, 88, 20, 89, 8, 13, 4, 6, 106, 71, 81, 77, 12, 80, 28, 85, 80, 80, 85, 75, 65, 52, 115, 124, 52, 118, 102, 19, 30, 8, 73, 91, 30, 8, 79, 8, 72, 93, 73, 12, 64, 91, 92, 80, 2, 24, 15, 25, 25, 4, 28, 75});
        if (android.support.v4.graphics.drawable.lIIllIlIl1.Il1IIlI1II(480946860L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{99, 7, 50, 87, 45, 67}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, ChapterAudioEsEntity chapterAudioEsEntity) throws FileNotFoundException {
        if (chapterAudioEsEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, chapterAudioEsEntity.getBookName());
        }
        i1lI1I1lll.bindLong(2, chapterAudioEsEntity.getChapterNumber());
        if (chapterAudioEsEntity.getChapterAudioUrl() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, chapterAudioEsEntity.getChapterAudioUrl());
        }
        i1lI1I1lll.bindLong(4, chapterAudioEsEntity.getAudioIsDownload());
        if (chapterAudioEsEntity.getAudioDownloadPath() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, chapterAudioEsEntity.getAudioDownloadPath());
        }
        i1lI1I1lll.bindLong(6, chapterAudioEsEntity.getId());
        if (lIIlI111II.I1I11l11l1(4737)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{123, 15, 8, 16, 90, 115, 121, 119, 112, 0, 96, 113, 119, 94, 87, 80, 24, 50, 50, 90, 113, 24, 10, 109, 116, 79}));
        }
    }
}
