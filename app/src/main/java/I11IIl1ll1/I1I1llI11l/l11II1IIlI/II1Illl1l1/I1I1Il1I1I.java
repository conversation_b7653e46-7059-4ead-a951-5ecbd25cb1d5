package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import com.ebook.bible.db.entity.DevotionCommonEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class I1I1Il1I1I implements Callable<List<DevotionCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ IIII1IIl1I_Impl b;

    I1I1Il1I1I(IIII1IIl1I_Impl iIII1IIl1I_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = iIII1IIl1I_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public List<DevotionCommonEntity> call() throws Exception {
        Cursor cursorQuery = Il1IIlI1lI.query(this.b.a, this.a, false, null);
        try {
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                arrayList.add(IIII1IIl1I_Impl.a(this.b, cursorQuery));
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
