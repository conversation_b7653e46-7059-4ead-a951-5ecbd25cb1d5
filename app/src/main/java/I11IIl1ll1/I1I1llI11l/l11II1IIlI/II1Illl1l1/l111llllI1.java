package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.core.location.IIlIIlIII1;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.l1lllll11l;
import java.net.PortUnreachableException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class l111llllI1 extends lll1l11l1I<l1lllll11l> {
    final /* synthetic */ lIlIIlIII1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    l111llllI1(lIlIIlIII1_Impl liliiliii1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liliiliii1_impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws PortUnreachableException {
        if (IIlIIlIII1.Ill1lIIlIl(7335)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{1, 21, 53, 12, 81, 81, 79, 118, 122, 93, 2, 67, 1, 97, 113, 12, 22, 15, 3, 80, 93, 44, 123, 126, 112, 87, 86, 54, 38, 115}));
        }
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 81, 89, 88, 14, 59, 81, 95, 12, 88, 23, 7, 58, 10, 92, 68, 68, 86, 22, 73, 80, 21, 17, 84, 87, 13, 14, 9, 109, 94, 20, 94, 87, 86, 68, 87, 73, 4, 80, 88, 12, 92, 59, 12, 4, 15, 80, 87, 28, 89, 7, 88, 81, 69, 77, 81, 71, 61, 15, 23, 95, 82, 4, 65, 85, 31, 86, 65, 0, 22, 65, 82, 60, 89, 17, 15, 7, 7, 71, 87, 28, 89, 13, 84, 80, 28, 25, 98, 116, 46, 52, 39, 97, 16, 73, 12, 25, 12, 26, 8, 73, 91, 30, 89, 22, 91, 8, 11, 3, 74, 10, 27, 16, 9, 77, 25});
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, l1lllll11l l1lllll11lVar) {
        i1lI1I1lll.bindLong(1, l1lllll11lVar.getBookNumber());
        if (l1lllll11lVar.getBookName() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, l1lllll11lVar.getBookName());
        }
        i1lI1I1lll.bindLong(3, l1lllll11lVar.getChapterNumber());
        i1lI1I1lll.bindLong(4, l1lllll11lVar.getVerseNumber());
        i1lI1I1lll.bindLong(5, l1lllll11lVar.getId());
    }
}
