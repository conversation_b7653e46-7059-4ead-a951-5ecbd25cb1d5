package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.accounts.utils.IIIlIl1I1l;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.util.Log;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.WorshipSongsEnEntity;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class lll1I1I11l extends lll1l11l1I<WorshipSongsEnEntity> {
    final /* synthetic */ IlII1llI1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lll1I1I11l(IlII1llI1I_Impl ilII1llI1I_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = ilII1llI1I_Impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws CertificateEncodingException {
        if (Il1IIllIll.llll111lI1(I1I1lI1II1.a(new byte[]{5, 7, 86, 20, 1, 65, 110, 115, 14, 3}), 281193803L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 17, 4, 80, 59, 5, 121, 116, 85, 2, 101, 91, 79, 118, 96}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 68, 89, 69, 22, 12, 91, 71, 60, 68, 11, 12, 2, 17, 106, 82, 94, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 4, 91, 66, 18, 71, 106, 71, 87, 80, 58, 10, 83, 90, 6, 87, 72, 2, 8, 7, 81, 94, 81, 102, 10, 81, 93, 80, 89, 24, 85, 17, 14, 12, 85, 111, 21, 90, 65, 95, 83, 87, 73, 4, 65, 88, 13, 80, 59, 17, 16, 0, 65, 94, 68, 85, 1, 80, 28, 85, 74, 91, 91, 5, 62, 14, 91, 94, 10, 83, 25, 83, 69, 88, 11, 3, 109, 71, 17, 88, 3, 16, 0, 17, 70, 87, 28, 89, 23, 95, 94, 82, 102, 82, 84, 20, 14, 16, 91, 68, 4, 83, 25, 83, 95, 83, 5, 77, 18, 97, 34, 123, 49, 39, 54, 66, 29, 8, 28, 6, 72, 15, 28, 10, 21, 11, 25, 93, 77, 93, 30, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 30});
        if (!IIIlIl1I1l.Ill1lIIlIl(910)) {
            return strA;
        }
        Log.v(I1I1lI1II1.a(new byte[]{109, 62, 80, 32, 13, 124, 115, 101, 124, 49, 116, 88, 7, 11, 80, 69, 51, 22, 36, 10, 94, 22}), I1I1lI1II1.a(new byte[]{113, 62, 80, 53, 37, 120, 84, 7, 80, 15, 99, 8, 81, 92, 68, 12, 26, 22, 81, 88, 70, 16}));
        return null;
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, WorshipSongsEnEntity worshipSongsEnEntity) {
        if (worshipSongsEnEntity.getFirstTagName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, worshipSongsEnEntity.getFirstTagName());
        }
        if (worshipSongsEnEntity.getMediaName() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, worshipSongsEnEntity.getMediaName());
        }
        if (worshipSongsEnEntity.getSongTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, worshipSongsEnEntity.getSongTitle());
        }
        if (worshipSongsEnEntity.getSongSubtitle() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, worshipSongsEnEntity.getSongSubtitle());
        }
        if (worshipSongsEnEntity.getSongLink() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, worshipSongsEnEntity.getSongLink());
        }
        if (worshipSongsEnEntity.getSongProgress() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, worshipSongsEnEntity.getSongProgress());
        }
        i1lI1I1lll.bindLong(7, worshipSongsEnEntity.getSongFavorite());
        i1lI1I1lll.bindLong(8, worshipSongsEnEntity.getId());
    }
}
