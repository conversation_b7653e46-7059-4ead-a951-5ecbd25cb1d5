package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.net.PortUnreachableException;
import java.net.UnknownServiceException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class IIIl1lIlll implements Callable<Integer> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    IIIl1lIlll(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ Integer call() throws Exception {
        if (llIlII1IlI.IlIIl111lI(I1I1lI1II1.a(new byte[]{99, 14, 15, 9, 84, 100, 95, 104}))) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{121, 87, 14, 31, 18, 112, Byte.MAX_VALUE, 7, 106, 14, 83, 116, 126, 8, 91, 120, 55, 5, 1, 71, 74}));
        }
        return a();
    }

    public Integer a() throws Exception {
        if (lIIlI111II.Il1IlllI1I(2938)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{98, 83, 80, 3, 15, 6, 83, 91, 118, 13, 122}));
        }
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            cursorQuery.close();
            if (androidx.versionedparcelable.custom.entities.lIIlI111II.II1lIllIll(3155)) {
                throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{115, 1, 38, 85, 16, 116, 88, 93, 125, 23, 90, 7, 64, 113, 89, 77, 40, 48, 51, 1, 123, 51, 4, 1, 87, 64}));
            }
            return numValueOf;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }
}
