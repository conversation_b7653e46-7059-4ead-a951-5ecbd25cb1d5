package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.ChapterAudioEnEntity;
import java.security.KeyException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I1lll111Il extends lll1l11l1I<ChapterAudioEnEntity> {
    final /* synthetic */ l1I1l1ll1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I1lll111Il(l1I1l1ll1I_Impl l1i1l1ll1i_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = l1i1l1ll1i_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, ChapterAudioEnEntity chapterAudioEnEntity) throws KeyException, CertificateException {
        if (lI1lllIII1.Il1IIlI1II(9924)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{7, 47, 80, 38, 0, 83, 14, 74, 81, 7, 117, 7, 126, 13, 68, 113, 20, 36, 52}));
        }
        a(i1lI1I1lll, chapterAudioEnEntity);
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 80, 94, 86, 21, 16, 87, 69, 60, 86, 17, 6, 12, 13, 106, 67, 81, 91, 8, 85, 80, 21, 17, 84, 87, 13, 14, 9, 109, 94, 0, 94, 80, 83, 26, 87, 6, 12, 83, 71, 23, 82, 22, 61, 11, 23, 88, 85, 85, 75, 4, 28, 80, 86, 81, 85, 69, 22, 4, 16, 109, 81, 20, 87, 92, 92, 105, 66, 23, 8, 82, 27, 3, 86, 17, 6, 12, 13, 106, 94, 67, 102, 0, 95, 71, 91, 85, 91, 84, 6, 1, 78, 82, 81, 20, 87, 92, 92, 105, 83, 10, 19, 92, 91, 12, 86, 0, 61, 21, 3, 65, 95, 80, 21, 4, 89, 84, 85, 16, 20, 99, 35, 45, 55, 119, 99, 65, 27, 10, 31, 9, 27, 90, 72, 13, 27, 92, 27, 10, 23, 9, 14, 92, 81, 24, 6, 72, 16, 0, 28, 16});
    }

    public void a(I1lI1I1lll i1lI1I1lll, ChapterAudioEnEntity chapterAudioEnEntity) throws CertificateException {
        if (chapterAudioEnEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, chapterAudioEnEntity.getBookName());
        }
        i1lI1I1lll.bindLong(2, chapterAudioEnEntity.getChapterNumber());
        if (chapterAudioEnEntity.getChapterAudioUrl() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, chapterAudioEnEntity.getChapterAudioUrl());
        }
        i1lI1I1lll.bindLong(4, chapterAudioEnEntity.getAudioIsDownload());
        if (chapterAudioEnEntity.getAudioDownloadPath() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, chapterAudioEnEntity.getAudioDownloadPath());
        }
        i1lI1I1lll.bindLong(6, chapterAudioEnEntity.getId());
        if (I1IllIll1l.I111IlIl1I(316102713L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{122, 48, 50, 39, 38, 96, 95, 101, Byte.MAX_VALUE}));
        }
    }
}
