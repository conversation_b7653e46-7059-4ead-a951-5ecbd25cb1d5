package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.support.v4.graphics.drawable.Il1IIllIll;
import androidx.core.location.lI1lI11Ill;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.room.CoroutinesRoom;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.ebook.bible.db.entity.PrayerCommonEntity;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.InvalidClassException;
import java.io.NotSerializableException;
import java.io.StreamCorruptedException;
import java.net.BindException;
import java.security.InvalidAlgorithmParameterException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateParsingException;
import java.util.Collections;
import java.util.List;
import kotlin.coroutines.d;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
public final class IllllI11lI_Impl implements IllllI11lI {
    private final RoomDatabase a;
    private final lll1l11l1I<PrayerCommonEntity> b;

    static /* synthetic */ RoomDatabase a(IllllI11lI_Impl illllI11lI_Impl) throws CertificateParsingException {
        RoomDatabase roomDatabase = illllI11lI_Impl.a;
        if (II1I11IlI1.I1lllI1llI(6107)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{123, 48, 81, 2, 22, 66, 126, 99, 88, 41, 4, 105, 108, 123, 112, 92, 83, 16, 9, 88, 96, 20, 6, 67, 86, 83, 96, 44}));
        }
        return roomDatabase;
    }

    static /* synthetic */ lll1l11l1I b(IllllI11lI_Impl illllI11lI_Impl) throws StreamCorruptedException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 51, 58, 10, 23, 71, 81, 69, 122, 81, 74, 73, 108, 72, 96, 90, 0, 86, 41, 96, 114, 8, 87, 108, 99, 7, 77, 4, 16, 124}), 345234600L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{120, 1, 8, 80, 80, 88, 96, 70, 118, 19, 95, 64, 93, 117, 100, 119, 85, 14, 40, 6}));
        }
        return illllI11lI_Impl.b;
    }

    public IllllI11lI_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new I111lIl11I(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IllllI11lI
    public Object a(PrayerCommonEntity prayerCommonEntity, d<? super Long> dVar) {
        return CoroutinesRoom.execute(this.a, true, new IIlII1l1Il(this, prayerCommonEntity), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IllllI11lI
    public Object a(IlIlIIll11 ilIlIIll11, d<? super List<PrayerCommonEntity>> dVar) throws BindException, EOFException {
        if (android.media.content.II1I11IlI1.I11II1I1I1(I1I1lI1II1.a(new byte[]{0, 80, 43, 20, 35, 3, 67, 96, 116, 18, Byte.MAX_VALUE, 106, 101, 75, 65, 115, 59, 5, 56, 87, 93, 15, 90, 122, 126, 67, 125, 31, 49}))) {
            throw new BindException(I1I1lI1II1.a(new byte[]{95, 81, 17, 4, 49, 2, 78, 125, 82, 22, 66, 88, Byte.MAX_VALUE, 72, 126, 3, 9, 24, 21, 99, 0, 54, 117}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l1IIll1I1l(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IllllI11lI
    public int a(IlIlIIll11 ilIlIIll11) throws InvalidClassException, NotSerializableException, EOFException, CertificateEncodingException {
        if (lI1lI11Ill.l11I11I11l(I1I1lI1II1.a(new byte[]{67, 29, 6, 49, 26, 5, 115, 90, 65, 41, 0, Byte.MAX_VALUE, 87, 85, 101, 6, 32, 37, 14, 99, 93, 12, 103, 101, 98, 102, 98, 0, 49}))) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{84, 46, 6, 48, 84, 113, 121, 91, 10, 87, 85, 126, 0, 1, 69, Byte.MAX_VALUE, 32, 10}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int i = cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
            cursorQuery.close();
            if (IIlI1ll1ll.I11II1I1I1(I1I1lI1II1.a(new byte[]{121, 10, 45}))) {
                throw new EOFException(I1I1lI1II1.a(new byte[]{91, 42, 56, 3, 40, 100, 66, 82, 123, 1, 7, 99, 116, 112, 113, 102}));
            }
            return i;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IllllI11lI
    public int b(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    public static List<Class<?>> a() {
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (Il1IIllIll.l1Il11I1Il(I1I1lI1II1.a(new byte[]{98, 51, 47, 55, 45, 64, 98, 116, 104, 12, 116, 105, 108, 15, 126, 94, 56, 89, 45, 94, 74, 35, 113, 111, 70, 123, 68}), I1I1lI1II1.a(new byte[]{117, 9, 80, 15, 16, 70, 2, 125, 72, 85, 81, 113, 126, 109, 87, 98, 14, 40, 91, 119, 0, 47, 85}))) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{7, 21, 55, 41, 84, 65, 98, 69, 13}));
        }
        return listEmptyList;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public PrayerCommonEntity a(Cursor cursor) throws CharConversionException, UnrecoverableKeyException, InvalidAlgorithmParameterException {
        if (Il1IIllIll.I1lllI1llI(235509233L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{102, 22, 40, 48, 84, 126, 86, 92, 112, 17, 114, 94, 67, 113, 77, 91, 33, 23}));
        }
        int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{94, 0}));
        int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{71, 22, 3, 28, 7, 71, 104, 68, 80, 9, 85}));
        int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
        int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{78, 1, 3, 23}));
        int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7}));
        int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
        int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
        int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{71, 22, 3, 28, 7, 71}));
        int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{88, 22, 11, 2, 11, 91, 86, 92, 102, 8, 89, 94, 94}));
        int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
        int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{91, 5, 12, 2, 23, 84, 80, 85}));
        PrayerCommonEntity prayerCommonEntity = new PrayerCommonEntity();
        if (columnIndex != -1) {
            prayerCommonEntity.setId(cursor.getLong(columnIndex));
        }
        if (columnIndex2 != -1) {
            prayerCommonEntity.setPrayerTime(cursor.getInt(columnIndex2));
        }
        if (columnIndex3 != -1) {
            prayerCommonEntity.setDate(cursor.isNull(columnIndex3) ? null : cursor.getString(columnIndex3));
        }
        if (columnIndex4 != -1) {
            prayerCommonEntity.setYear(cursor.isNull(columnIndex4) ? null : cursor.getString(columnIndex4));
        }
        if (columnIndex5 != -1) {
            prayerCommonEntity.setVerse(cursor.isNull(columnIndex5) ? null : cursor.getString(columnIndex5));
        }
        if (columnIndex6 != -1) {
            prayerCommonEntity.setReference(cursor.isNull(columnIndex6) ? null : cursor.getString(columnIndex6));
        }
        if (columnIndex7 != -1) {
            prayerCommonEntity.setInspiration(cursor.isNull(columnIndex7) ? null : cursor.getString(columnIndex7));
        }
        if (columnIndex8 != -1) {
            prayerCommonEntity.setPrayer(cursor.isNull(columnIndex8) ? null : cursor.getString(columnIndex8));
        }
        if (columnIndex9 != -1) {
            prayerCommonEntity.setOriginalLink(cursor.isNull(columnIndex9) ? null : cursor.getString(columnIndex9));
        }
        if (columnIndex10 != -1) {
            prayerCommonEntity.setAudioLink(cursor.isNull(columnIndex10) ? null : cursor.getString(columnIndex10));
        }
        if (columnIndex11 != -1) {
            prayerCommonEntity.setLanguage(cursor.isNull(columnIndex11) ? null : cursor.getString(columnIndex11));
        }
        return prayerCommonEntity;
    }
}
