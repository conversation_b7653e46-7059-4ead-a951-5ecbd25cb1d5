package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.core.location.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.room.CoroutinesRoom;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import com.ebook.bible.db.entity.WorshipSongsEnEntity;
import com.ebook.bible.db.entity.WorshipSongsEsEntity;
import com.ebook.bible.db.entity.WorshipSongsPtEntity;
import java.io.EOFException;
import java.io.NotSerializableException;
import java.io.StreamCorruptedException;
import java.net.SocketTimeoutException;
import java.security.KeyStoreException;
import java.security.cert.CertificateEncodingException;
import java.util.Collections;
import java.util.List;
import kotlin.coroutines.d;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
public final class IlII1llI1I_Impl implements IlII1llI1I {
    private final RoomDatabase a;
    private final lll1l11l1I<WorshipSongsEnEntity> b;
    private final lll1l11l1I<WorshipSongsEsEntity> c;
    private final lll1l11l1I<WorshipSongsPtEntity> d;

    public IlII1llI1I_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new lll1I1I11l(this, roomDatabase);
        this.c = new IIllIIll1I(this, roomDatabase);
        this.d = new I1llIl1lIl(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public long a(WorshipSongsEnEntity worshipSongsEnEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (lIIlI111II.I1IlI11II1(983)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{89, 45, 23, 51, 50, 126, 86, 117, 79, 42, 8, 7}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(worshipSongsEnEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public long a(WorshipSongsEsEntity worshipSongsEsEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (lII1llllI1.I1II1111ll(755655433L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{78, 8, 91, 84, 45, 12, 100, 125, 1, 87, 123, 88, 126}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.c.insertAndReturnId(worshipSongsEsEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public long a(WorshipSongsPtEntity worshipSongsPtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.d.insertAndReturnId(worshipSongsPtEntity);
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            if (l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{121}), 1067)) {
                throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{102, 80, 50, 31, 15, 113}));
            }
            return jInsertAndReturnId;
        } catch (Throwable th) {
            this.a.endTransaction();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public Integer a(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        if (l11Il1lI11.I1II1111ll(4752)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{65, 93, 10, 28, 10, 68, 93, 7, 109, 60, 122, 102, 2, 79, Byte.MAX_VALUE, Byte.MAX_VALUE, 32, 13, 43, 119}));
        }
        this.a.assertNotSuspendingTransaction();
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            return numValueOf;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public Integer b(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            return numValueOf;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public Object a(IlIlIIll11 ilIlIIll11, d<? super List<WorshipSongsCommonEntity>> dVar) throws StreamCorruptedException, EOFException {
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new I111I1IlII(this, ilIlIIll11), dVar);
        if (II1lllllI1.l1l1Il1I11(I1I1lI1II1.a(new byte[]{103, 6, 90, 42, 19, 98, 101, 69, 124, 61, 89, 70, 68, 74, 82, 88, 42, 14, 44, 2, 0, 20, 86, 80}), I1I1lI1II1.a(new byte[]{0, 54, 59, 4, 91, 91, 110, 99, 107, 7, 101, 100, 100, 107, 86, 65, 10, 57, 10, 96}))) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{7, 20, 40, 40}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public Object b(IlIlIIll11 ilIlIIll11, d<? super WorshipSongsCommonEntity> dVar) throws EOFException {
        if (lIlIII1I1l.IllIlI1l1I(I1I1lI1II1.a(new byte[]{102, 2, 38, 4, 22, 124, 122, 106, Byte.MAX_VALUE, 8, 95, 85, 124, 73, 118, 4, 51, 39, 41, Byte.MAX_VALUE, 100, 82, 86, 113, 87, 91}), 826387056L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{84, 39, 0, 84, 11, 99, 94, 83, 77, 60, 85, 8, 115, 124, 88, 126, 56, 42, 32, 6, 117, 27, 71, 120}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new lll1llll1I(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public WorshipSongsCommonEntity c(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        WorshipSongsCommonEntity worshipSongsCommonEntity;
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 94, 81, 88, 92}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 89, 81, 84, 1}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 65, 94, 68, 85, 1}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 70, 66, 82, 77, 13, 68, 92, 80}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 89, 94, 94, 82}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 69, 69, 95, 94, 22, 85, 67, 70}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 83, 86, 70, 86, 22, 89, 68, 80}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            if (cursorQuery.moveToFirst()) {
                WorshipSongsCommonEntity worshipSongsCommonEntity2 = new WorshipSongsCommonEntity();
                if (columnIndex != -1) {
                    worshipSongsCommonEntity2.setFirstTagName(cursorQuery.isNull(columnIndex) ? null : cursorQuery.getString(columnIndex));
                }
                if (columnIndex2 != -1) {
                    worshipSongsCommonEntity2.setMediaName(cursorQuery.isNull(columnIndex2) ? null : cursorQuery.getString(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    worshipSongsCommonEntity2.setSongTitle(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    worshipSongsCommonEntity2.setSongSubtitle(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    worshipSongsCommonEntity2.setSongLink(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    worshipSongsCommonEntity2.setSongProgress(cursorQuery.isNull(columnIndex6) ? null : cursorQuery.getString(columnIndex6));
                }
                if (columnIndex7 != -1) {
                    worshipSongsCommonEntity2.setSongFavorite(cursorQuery.getInt(columnIndex7));
                }
                if (columnIndex8 != -1) {
                    worshipSongsCommonEntity2.setId(cursorQuery.getLong(columnIndex8));
                }
                worshipSongsCommonEntity = worshipSongsCommonEntity2;
            } else {
                worshipSongsCommonEntity = null;
            }
            return worshipSongsCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IlII1llI1I
    public int d(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    public static List<Class<?>> a() {
        return Collections.emptyList();
    }
}
