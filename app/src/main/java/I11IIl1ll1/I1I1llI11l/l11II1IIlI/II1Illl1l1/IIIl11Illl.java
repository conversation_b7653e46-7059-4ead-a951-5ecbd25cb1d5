package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lllIII11lI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIIl11Illl extends lllIII11lI {
    final /* synthetic */ IIlllll1Il_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIIl11Illl(IIlllll1Il_Impl iIlllll1Il_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iIlllll1Il_Impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        I1I1lI1II1.a(new byte[]{98, 52, 38, 36, 54, 112, 23, 70, 92, 22, 67, 85, 106, 80, 90, 70, 18, 8, 16, 83, 68, 8, 92, 91, 108, 66, 86, 7, 8, 87, 23, 48, 114, 48, 66, 12, 12, 70, 71, 89, 75, 5, 68, 89, 90, 87, 20, 8, 66, 94, 66, 101, 120, 36, 97, 112, 19, 70, 91, 4, 10, 109, 83, 2, 78, 59, 11, 1, 66, 8, 23, 15});
        return I1I1lI1II1.a(new byte[]{98, 52, 38, 36, 54, 112, 23, 70, 92, 22, 67, 85, 106, 80, 90, 70, 18, 8, 16, 83, 68, 8, 92, 91, 108, 66, 86, 7, 8, 87, 23, 48, 114, 48, 66, 12, 12, 70, 71, 89, 75, 5, 68, 89, 90, 87, 20, 8, 66, 94, 66, 101, 120, 36, 97, 112, 19, 70, 91, 4, 10, 109, 83, 2, 78, 59, 11, 1, 66, 8, 23, 15});
    }
}
