package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.media.content.IIl1l1IllI;
import android.util.Log;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.core.location.I1111IIl11;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.IllIlllIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.room.CoroutinesRoom;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import com.ebook.bible.db.entity.BookChapterNumberEntity;
import com.ebook.bible.db.entity.BookVerseCommonEntity;
import com.ebook.bible.db.entity.VerseActionsCommonEntity;
import com.ebook.bible.db.entity.VerseActionsEnEntity;
import com.ebook.bible.db.entity.VerseActionsEsEntity;
import com.ebook.bible.db.entity.VerseActionsPtEntity;
import com.ebook.bible.db.entity.VerseAnnotationCommonEntity;
import com.ebook.bible.db.entity.VerseAnnotationEnEntity;
import com.ebook.bible.db.entity.VerseAnnotationEsEntity;
import com.ebook.bible.db.entity.VerseAnnotationPtEntity;
import com.ebook.bible.db.entity.VerseEnEntity;
import com.ebook.bible.db.entity.VerseEsEntity;
import com.ebook.bible.db.entity.VersePtEntity;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.IOException;
import java.io.NotSerializableException;
import java.io.StreamCorruptedException;
import java.io.UTFDataFormatException;
import java.net.NoRouteToHostException;
import java.net.SocketTimeoutException;
import java.net.UnknownServiceException;
import java.security.KeyException;
import java.security.KeyStoreException;
import java.security.ProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import kotlin.coroutines.d;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIlllI1;

/* loaded from: classes.dex */
public final class lIIllIlIl1_Impl implements lIIllIlIl1 {
    private final RoomDatabase a;
    private final lll1l11l1I<VerseEnEntity> b;
    private final lll1l11l1I<VerseEsEntity> c;
    private final lll1l11l1I<VersePtEntity> d;
    private final lll1l11l1I<VerseActionsEnEntity> e;
    private final lll1l11l1I<VerseActionsEsEntity> f;
    private final lll1l11l1I<VerseActionsPtEntity> g;
    private final lll1l11l1I<VerseAnnotationEnEntity> h;
    private final lll1l11l1I<VerseAnnotationEsEntity> i;
    private final lll1l11l1I<VerseAnnotationPtEntity> j;

    static /* synthetic */ RoomDatabase a(lIIllIlIl1_Impl liillilil1_impl) {
        if (android.support.v4.graphics.drawable.IllllI11Il.I11II1I1I1(602916569L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{64, 34, 6, 6, 1, 7, 82, 115, 11, 41, 103, 70, 69, 0, 80, 66, 59, 55}));
        }
        RoomDatabase roomDatabase = liillilil1_impl.a;
        if (llIl1lII1I.I1lllI1llI(372461073L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{68, 34, 17, 61, 82, 116, 89, 4, 11, 87, 68, 2, 6, 14, 7, 79, 6, 16, 86, 116, 68, 83, 71, 119, 120, 116, 15, 63, 3}));
        }
        return roomDatabase;
    }

    public lIIllIlIl1_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new I1Ill1lIII(this, roomDatabase);
        this.c = new IIll1l1lII(this, roomDatabase);
        this.d = new IllIIlI11I(this, roomDatabase);
        this.e = new lIIIIlIIl1(this, roomDatabase);
        this.f = new ll1ll1lIl1(this, roomDatabase);
        this.g = new II111lllll(this, roomDatabase);
        this.h = new Il1I1111l1(this, roomDatabase);
        this.i = new lIllI1lIlI(this, roomDatabase);
        this.j = new I1I11l11l1(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseEnEntity verseEnEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException, UnknownServiceException {
        if (lIIlI111II.Il1IIIIlll(2836)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{121, 7, 82, 51, 35, 89, 71, 69, 97, 35, 90, 65, 66, 112, 112, 123, 5, 87, 26, 115}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(verseEnEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseEsEntity verseEsEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.c.insertAndReturnId(verseEsEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VersePtEntity versePtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.d.insertAndReturnId(versePtEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseActionsEnEntity verseActionsEnEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (lIIlI111II.Il1IIIIlll(2576)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{84, 50, 90, 83, 16}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.e.insertAndReturnId(verseActionsEnEntity);
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            if (!lIlIII1I1l.lll1111l11(I1I1lI1II1.a(new byte[]{86, 51}), 5289)) {
                return jInsertAndReturnId;
            }
            Log.d(I1I1lI1II1.a(new byte[]{124, 33, 9, 32, 12, 80, 69, 71, 118, 62}), I1I1lI1II1.a(new byte[]{0, 19, 83, 17, 37, 89, 99, 8, 116, 46, 103, 86, 95, 73, 76, 97, 15, 13}));
            return 0L;
        } catch (Throwable th) {
            this.a.endTransaction();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseActionsEsEntity verseActionsEsEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (android.support.v4.graphics.drawable.IllllI11Il.I1lllI1llI(172025740L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{95, 92, 47, 17, 22, 100, 120, 84}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.f.insertAndReturnId(verseActionsEsEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseActionsPtEntity verseActionsPtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.g.insertAndReturnId(verseActionsPtEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseAnnotationEnEntity verseAnnotationEnEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.h.insertAndReturnId(verseAnnotationEnEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseAnnotationEsEntity verseAnnotationEsEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.i.insertAndReturnId(verseAnnotationEsEntity);
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            if (!androidx.core.location.I1Ill1lIII.Ill1lIIlIl(422331462L)) {
                return jInsertAndReturnId;
            }
            Log.d(I1I1lI1II1.a(new byte[]{70, 32, 53, 49, 21, Byte.MAX_VALUE, 2, 84, 79, 5, 9, 120, 70, 112, 120, 119, 15, 22, 53, 67, 104, 34}), I1I1lI1II1.a(new byte[]{3, 14, 37, 47, 33, 113, 86, 103, 93, 17, 100, 102, 99, 122, 65, 113, 22, 88, 23, 104, 5, 46, 85, 87, 124, 82, 99}));
            return 0L;
        } catch (Throwable th) {
            this.a.endTransaction();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public long a(VerseAnnotationPtEntity verseAnnotationPtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (llIlI11III.I1lIllll1l(I1I1lI1II1.a(new byte[]{97, 8, 85, 55, 20, 12, 68, 101, 15, 35, 92, 96, 100, 120, 109, 94, 90}), 261598999L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{83, 40, 9, 11, 16, 91, 116, 123, 73, 81, 90, 67, 115, 126, 123, 67, 50, 85, 86, 100, 66, 20, 119, 98, 70, 92, 121, 46, 6, 98, 121, 11}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.j.insertAndReturnId(verseAnnotationPtEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public List<BookVerseCommonEntity> a(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 23, 61, 11, 7, 66, 104, 68, 92, 23, 68, 81, 88, 92, 90, 65}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 87, 16, 85, 94, 65}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 85, 11, 66}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 85, 95, 86, 15, 93, 81, 71, 82}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 95, 77, 1}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 88, 68, 92, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 93, 94, 87, 81, 8, 89, 87, 93, 77, 107, 88, 13, 5, 11, 84, 73, 62, 71, 92, 94, 83}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 2, 13, 92, 68, 4, 93, 65}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 19, 7, 84, 85, 19, 86, 91, 80, 83}));
            int i7 = columnIndex14;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                BookVerseCommonEntity bookVerseCommonEntity = new BookVerseCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i8 = -1;
                if (columnIndex != -1) {
                    i = columnIndex10;
                    i2 = columnIndex9;
                    bookVerseCommonEntity.setId(cursorQuery.getLong(columnIndex));
                } else {
                    i = columnIndex10;
                    i2 = columnIndex9;
                }
                if (columnIndex2 != -1) {
                    bookVerseCommonEntity.setNewTestament(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    bookVerseCommonEntity.setBookNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    bookVerseCommonEntity.setBookName(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    bookVerseCommonEntity.setChapterNumber(cursorQuery.getInt(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    bookVerseCommonEntity.setVerseNumber(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != -1) {
                    bookVerseCommonEntity.setVerseContent(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                }
                if (columnIndex8 != -1) {
                    bookVerseCommonEntity.setVerseColor(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                }
                int i9 = i2;
                if (i9 != -1) {
                    bookVerseCommonEntity.setVerseBookMark(cursorQuery.isNull(i9) ? null : cursorQuery.getString(i9));
                }
                int i10 = i;
                if (i10 != -1) {
                    bookVerseCommonEntity.setVerseNote(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    i8 = -1;
                }
                if (columnIndex11 != i8) {
                    i3 = columnIndex8;
                    i4 = columnIndex2;
                    bookVerseCommonEntity.setBookMarkModifyTime(cursorQuery.getLong(columnIndex11));
                } else {
                    i3 = columnIndex8;
                    i4 = columnIndex2;
                }
                if (columnIndex12 != i8) {
                    bookVerseCommonEntity.setNoteModifyTime(cursorQuery.getLong(columnIndex12));
                }
                if (columnIndex13 != i8) {
                    bookVerseCommonEntity.setHighlightModifyTime(cursorQuery.getLong(columnIndex13));
                }
                int i11 = i7;
                if (i11 != i8) {
                    bookVerseCommonEntity.setVerseAnnotationContent(cursorQuery.isNull(i11) ? null : cursorQuery.getString(i11));
                    i6 = columnIndex15;
                    i5 = -1;
                } else {
                    i5 = i8;
                    i6 = columnIndex15;
                }
                if (i6 != i5) {
                    bookVerseCommonEntity.setVerseAnnotationReference(cursorQuery.isNull(i6) ? null : cursorQuery.getString(i6));
                }
                arrayList2.add(bookVerseCommonEntity);
                columnIndex15 = i6;
                i7 = i11;
                arrayList = arrayList2;
                columnIndex2 = i4;
                columnIndex8 = i3;
                columnIndex9 = i9;
                columnIndex10 = i10;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object a(IlIlIIll11 ilIlIIll11, d<? super BookVerseCommonEntity> dVar) throws NoSuchMethodException, EOFException {
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{120, 54, 47, 53, 40, 86, 83, 70, 82, 22, 87, 67, 68, 76, 66, 118, 84, 40, 50, 65, 96, 37, 0, 94, 82, 80}), 1219221986L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{93, 35, 47, 55, 81, 70, 114, 117, 124, 53, 87, 95, 3, 82, 98, 94, 9, 48, 3, 67, 90, 53, 105, 92, 7, 100, 86, 45, 44}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l1lI1lll1I(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object b(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws IOException {
        if (llIlI11III.l1l1l1IIlI(194756555L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{118, 44, 19}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l1l11llIl1(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object c(IlIlIIll11 ilIlIIll11, d<? super List<Integer>> dVar) throws StreamCorruptedException, EOFException {
        if (IIlI1ll1ll.IlIIlIllI1(I1I1lI1II1.a(new byte[]{125, 16, 90, 83, 48, 109, 98, 126}), I1I1lI1II1.a(new byte[]{94, 17, 6, 31, 32, 71, 83, 0, 117, 40, 119, 97, 71, 15, 99}))) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{71, 10, 24, 50, 55, 3, 103, 71, 80, 22, 124, 98, 123, 1, 108, 88, 38, 85, 86, 88, 6, 34, 100, 116, 120, 96, 94}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new lllI111lll(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object d(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws KeyException, EOFException {
        if (Il1l11I11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{64, 5, 56, 55, 36, 125, 115, 121, 97, 3, 100, 120, 65, 96, 4, 103, 83, 10, 22}), 287356703L)) {
            Log.d(I1I1lI1II1.a(new byte[]{100, 42, 7, 42, 38, 92, 121, 84, 117, 84, 90, 121, 122, 74, 90, 12, 27, 88, 56, 64, 81, 36, 71, 70, 92, 92, 123, 17}), I1I1lI1II1.a(new byte[]{79, 62, 18, 40, 54, 120, 82, 103, 8, 60, 9, 86, 81, 11, 95, 64, 84, 50, 17, 86, 95, 13, 1, 125, 65, 117, 115, 1, 28}));
            return null;
        }
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new IIII1l1lll(this, ilIlIIll11), dVar);
        if (androidx.constraintlayout.widget.l1IIll1I1l.IlII1Illll(I1I1lI1II1.a(new byte[]{121, 46, 80, 93, 91, 84, 116, 6, 74, 9, 86, 122, 113, 104, 97, 125, 83, 13, 32, 67, 68, 49, 86, 4}), 4051)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{0, 14, 14, 47, 35, 90, 7, 88, 114, 18, 92, 122, 91, 85, 6, 86, 45, 20, 21, 75, 114, 89, 1, 99, 82, 123, 81, 61, 37, 102}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object e(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws EOFException {
        if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{4, 11, 82, 32, 22, 80, 70, 104, 83, 61, 8, 105, 88, 79, 103, 119, 41, 37, 38, 122, 95, 51, 74, 112, 10, 92, 125, 11, 40, 81}), 6429)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{4, 33, 27, 63, 59}));
        }
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new IIll1II1l1(this, ilIlIIll11), dVar);
        if (ll1l11I1II.l11I11I11l(I1I1lI1II1.a(new byte[]{123, 20, 41, 1, 4, 93, 121, 81}))) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{124, 40, 51, 3, 20, 97, 82, 121, 84, 30, 0, 93, 113, 83, 2, 92, 47, 24, 32, 64, 100, 17, 84, 84, 92, 101}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object f(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws EOFException {
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new III1l1I11I(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object g(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws EOFException {
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new Il1lI1IllI(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public int b(IlIlIIll11 ilIlIIll11) throws InterruptedException, NotSerializableException, CertPathValidatorException, CertificateEncodingException {
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{88, 41, 14, 49, 11, 5, 126, 83, 99, 15, 4, 74, 2, 106, 124, 70}))) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{126, 82, 17, 83, 83, 3, 15, 99, 13, 33, 66, 1, 103}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int i = cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
            cursorQuery.close();
            if (l1l1IllI11.IlII1Illll(526806261L)) {
                throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{115, 51, 86, 55, 32, 102, 90, 114, 97, 43}));
            }
            return i;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public int c(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public VerseActionsCommonEntity d(IlIlIIll11 ilIlIIll11) throws UnrecoverableKeyException, NotSerializableException, CertificateEncodingException {
        VerseActionsCommonEntity verseActionsCommonEntity;
        int i;
        int i2;
        int i3;
        int i4;
        if (android.support.v4.graphics.drawable.I111lIl11I.I1lIllll1l(393042255L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{78, 43, 80}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 23, 61, 11, 7, 66, 104, 68, 92, 23, 68, 81, 88, 92, 90, 65}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 87, 16, 85, 94, 65}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 85, 11, 66}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 85, 95, 86, 15, 93, 81, 71, 82}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 95, 77, 1}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 88, 68, 92, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 93, 94, 87, 81, 8, 89, 87, 93, 77, 107, 88, 13, 5, 11, 84, 73, 62, 71, 92, 94, 83}));
            if (cursorQuery.moveToFirst()) {
                VerseActionsCommonEntity verseActionsCommonEntity2 = new VerseActionsCommonEntity();
                int i5 = -1;
                if (columnIndex != -1) {
                    i = columnIndex10;
                    verseActionsCommonEntity2.setId(cursorQuery.getLong(columnIndex));
                    i5 = -1;
                } else {
                    i = columnIndex10;
                }
                if (columnIndex2 != i5) {
                    verseActionsCommonEntity2.setNewTestament(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != i5) {
                    verseActionsCommonEntity2.setBookNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != i5) {
                    verseActionsCommonEntity2.setBookName(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                    i5 = -1;
                }
                if (columnIndex5 != i5) {
                    verseActionsCommonEntity2.setChapterNumber(cursorQuery.getInt(columnIndex5));
                }
                if (columnIndex6 != i5) {
                    verseActionsCommonEntity2.setVerseNumber(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != i5) {
                    verseActionsCommonEntity2.setVerseContent(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i5 = -1;
                }
                if (columnIndex8 != i5) {
                    verseActionsCommonEntity2.setVerseColor(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i5 = -1;
                }
                if (columnIndex9 != i5) {
                    verseActionsCommonEntity2.setVerseBookMark(cursorQuery.isNull(columnIndex9) ? null : cursorQuery.getString(columnIndex9));
                    i3 = i;
                    i2 = -1;
                } else {
                    i2 = i5;
                    i3 = i;
                }
                if (i3 != i2) {
                    verseActionsCommonEntity2.setVerseNote(cursorQuery.isNull(i3) ? null : cursorQuery.getString(i3));
                    i4 = -1;
                } else {
                    i4 = i2;
                }
                if (columnIndex11 != i4) {
                    verseActionsCommonEntity2.setBookMarkModifyTime(cursorQuery.getLong(columnIndex11));
                }
                if (columnIndex12 != i4) {
                    verseActionsCommonEntity2.setNoteModifyTime(cursorQuery.getLong(columnIndex12));
                }
                if (columnIndex13 != i4) {
                    verseActionsCommonEntity2.setHighlightModifyTime(cursorQuery.getLong(columnIndex13));
                }
                verseActionsCommonEntity = verseActionsCommonEntity2;
            } else {
                verseActionsCommonEntity = null;
            }
            return verseActionsCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public VerseAnnotationCommonEntity e(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        VerseAnnotationCommonEntity verseAnnotationCommonEntity;
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 2, 13, 92, 68, 4, 93, 65}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 19, 7, 84, 85, 19, 86, 91, 80, 83}));
            if (cursorQuery.moveToFirst()) {
                VerseAnnotationCommonEntity verseAnnotationCommonEntity2 = new VerseAnnotationCommonEntity();
                if (columnIndex != -1) {
                    verseAnnotationCommonEntity2.setId(cursorQuery.getLong(columnIndex));
                }
                if (columnIndex2 != -1) {
                    verseAnnotationCommonEntity2.setBookName(cursorQuery.isNull(columnIndex2) ? null : cursorQuery.getString(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    verseAnnotationCommonEntity2.setChapterNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    verseAnnotationCommonEntity2.setVerseNumber(cursorQuery.getInt(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    verseAnnotationCommonEntity2.setVerseAnnotationContent(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    verseAnnotationCommonEntity2.setVerseAnnotationReference(cursorQuery.isNull(columnIndex6) ? null : cursorQuery.getString(columnIndex6));
                }
                verseAnnotationCommonEntity = verseAnnotationCommonEntity2;
            } else {
                verseAnnotationCommonEntity = null;
            }
            return verseAnnotationCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public BookChapterNumberEntity a(IlIlIlllI1 ilIlIlllI1) throws NotSerializableException, NoRouteToHostException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        BookChapterNumberEntity bookChapterNumberEntity = null;
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIlllI1, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            if (cursorQuery.moveToFirst()) {
                bookChapterNumberEntity = new BookChapterNumberEntity();
                if (columnIndex != -1) {
                    bookChapterNumberEntity.setBookNumber(cursorQuery.getInt(columnIndex));
                }
                if (columnIndex2 != -1) {
                    bookChapterNumberEntity.setChapterNumber(cursorQuery.getInt(columnIndex2));
                }
            }
            cursorQuery.close();
            if (II1lllllI1.l1l1l1IIlI(961104421L)) {
                throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{103, 46, 15, 53, 26, 3, 100, 99, 95}));
            }
            return bookChapterNumberEntity;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public BookChapterNumberEntity b(IlIlIlllI1 ilIlIlllI1) throws NotSerializableException, UTFDataFormatException, CertificateEncodingException {
        if (I1111IIl11.IllIlI1l1I(I1I1lI1II1.a(new byte[]{1, 17, 32, 51, 85, 65, 102, 93, 0, 48, 101, 83, 121, 116, 66, 92, 22, 21}), I1I1lI1II1.a(new byte[]{2, 7, 54, 81, 85, 79, Byte.MAX_VALUE, 121, 65, 8, 113, 123, 79, 126}))) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 20, 43, 40, 23, 102, 92, 95}));
        }
        this.a.assertNotSuspendingTransaction();
        BookChapterNumberEntity bookChapterNumberEntity = null;
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIlllI1, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            if (cursorQuery.moveToFirst()) {
                bookChapterNumberEntity = new BookChapterNumberEntity();
                if (columnIndex != -1) {
                    bookChapterNumberEntity.setBookNumber(cursorQuery.getInt(columnIndex));
                }
                if (columnIndex2 != -1) {
                    bookChapterNumberEntity.setChapterNumber(cursorQuery.getInt(columnIndex2));
                }
            }
            return bookChapterNumberEntity;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object h(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws CharConversionException, InterruptedException, EOFException {
        if (androidx.constraintlayout.widget.l1IIll1I1l.l11I11I11l(I1I1lI1II1.a(new byte[]{101, 6, 37, 17, 58, 81, Byte.MAX_VALUE, 117, 123, 13, 113, 1, 102, 73, 70, 115, 81, 47, 44, 10, 114, 13, 89, 122, 5, 69}))) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{78, 6, 80, 22, 37, 89, 69, 117, 92, 50, 122, 104, 71, 84, 13, 97, 37, 34, 20, 65, 98, 86, 121, 69, 86, 119, 81, 20, 9, 87}));
        }
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new lI1llII1I1(this, ilIlIIll11), dVar);
        if (android.accounts.utils.lIIlI111II.llIIIl11I1(164058164L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{77, 43, 47, 29, 80, 70, 112, 117, 84, 60, 84, 123}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object i(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar) throws EOFException {
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new I1llIII1Il(this, ilIlIIll11), dVar);
        if (androidx.core.location.lIIlI111II.l1l11llIl1(182194222L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{114, 30, 0, 49, 44, 108, 90, 122, 99, 49, 113, 115, 97, 92, 65, 124}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object j(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws EOFException {
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new llIlllI111(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object k(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws CertPathValidatorException, EOFException {
        if (I1IllIll1l.IlII1Illll(179001414L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{67, 7, 51, 13, 27, 88, 114, Byte.MAX_VALUE, 122, 0, 67, 8}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new IIIl1lIlll(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIIllIlIl1
    public Object l(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws EOFException {
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l11ll111lI(this, ilIlIIll11), dVar);
    }

    public static List<Class<?>> a() {
        return Collections.emptyList();
    }
}
