package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.support.v4.graphics.drawable.III1Il1II1;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import com.ebook.bible.db.entity.DevotionCommonEntity;
import java.security.InvalidParameterException;
import java.security.cert.CertPathBuilderException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I11lII1Il1 implements Callable<Long> {
    final /* synthetic */ DevotionCommonEntity a;
    final /* synthetic */ IIII1IIl1I_Impl b;

    I11lII1Il1(IIII1IIl1I_Impl iIII1IIl1I_Impl, DevotionCommonEntity devotionCommonEntity) {
        this.b = iIII1IIl1I_Impl;
        this.a = devotionCommonEntity;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ Long call() throws Exception {
        Long lA = a();
        if (l1l1IllI11.Ill1lIIlIl(5650)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{100, 45, 26, 0, 21, 81, 117, 91, 72, 21, 98, Byte.MAX_VALUE, 80, 112, 92, 112, 20, 5, 44, 99, 94, 48, 2, 102, 118, 68, 67, 23, 83}));
        }
        return lA;
    }

    public Long a() throws Exception {
        if (l1l1IllI11.I1lI11IIll(I1I1lI1II1.a(new byte[]{71, 3, 91, 87, 21, 0, 121, 83, 87, 0, 6, 9, 70, 106, 100, 92, 33, 12, 3, 7, 100, 36}), 162841679L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{115, 34, 91, 61, 46, 7}));
        }
        this.b.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.b.insertAndReturnId(this.a);
            this.b.a.setTransactionSuccessful();
            Long lValueOf = Long.valueOf(jInsertAndReturnId);
            this.b.a.endTransaction();
            if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{7}), 235099542L)) {
                throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{1, 39, 91, 45, 44, 102, 114, 81, 0, 55, 104, 99, 84, 91, 82, 113, 20, 36, 39, 102, 99}));
            }
            return lValueOf;
        } catch (Throwable th) {
            this.b.a.endTransaction();
            throw th;
        }
    }
}
