package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.lI1lI11Ill;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.VersePtEntity;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class IllIIlI11I extends lll1l11l1I<VersePtEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IllIIlI11I(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VersePtEntity versePtEntity) {
        a(i1lI1I1lll, versePtEntity);
        if (androidx.interpolator.view.animation.IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{91, 61, 16, 32, 8, 77, 110, 103, 87, 55, 105, 83, 81, 88, 103}), lIIlI111II.III11111Il)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{111, 22}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws CertificateException {
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 71, 23, 104, 16, 3, 7, 14, 80, 87, 16, 17, 4, 89, 84, 85, 21, 84, 92, 17, 62, 12, 87, 71, 62, 71, 80, 64, 66, 86, 8, 1, 92, 67, 3, 27, 4, 0, 10, 13, 94, 104, 94, 76, 9, 82, 85, 71, 89, 24, 85, 0, 14, 13, 89, 111, 15, 82, 88, 86, 86, 27, 5, 7, 90, 86, 19, 67, 1, 16, 58, 12, 64, 90, 82, 92, 22, 80, 28, 85, 79, 81, 71, 17, 4, 61, 92, 69, 12, 81, 80, 65, 86, 27, 5, 18, 87, 69, 16, 82, 59, 1, 10, 12, 65, 82, 94, 77, 4, 25, 16, 99, 120, 120, 96, 39, 50, 66, 26, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 28});
        if (lI1lI11Ill.I1II1111ll(6522)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{120, 44, 86, 82, 91, 4, 100, 84, 0, 5, 64, 92, 76, 77, 3, 6, 26, 47, 55, 5, 86, 13, 112, Byte.MAX_VALUE, 116, 103, 79, 63, 46}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VersePtEntity versePtEntity) {
        i1lI1I1lll.bindLong(1, versePtEntity.getId());
        i1lI1I1lll.bindLong(2, versePtEntity.getNewTestament());
        i1lI1I1lll.bindLong(3, versePtEntity.getBookNumber());
        if (versePtEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, versePtEntity.getBookName());
        }
        i1lI1I1lll.bindLong(5, versePtEntity.getChapterNumber());
        i1lI1I1lll.bindLong(6, versePtEntity.getVerseNumber());
        if (versePtEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, versePtEntity.getVerseContent());
        }
    }
}
