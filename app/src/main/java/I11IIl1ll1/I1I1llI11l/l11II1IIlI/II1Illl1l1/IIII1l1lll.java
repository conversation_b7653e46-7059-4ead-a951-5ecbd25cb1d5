package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.core.location.Il1l11I11I;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.UTFDataFormatException;
import java.security.ProviderException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class IIII1l1lll implements Callable<Integer> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    IIII1l1lll(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ Integer call() throws Exception {
        if (Il11II1llI.l11I11I11l(4843)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{124, 30, 33, 42, 0, 94, 79, 90, 112, 35, 105, 89, 111, 120, 83, 7, 27, 46, 9}));
        }
        Integer numA = a();
        if (IlIIlI11I1.IlII1Illll(224609931L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{81, 54, 51, 34, 45}));
        }
        return numA;
    }

    public Integer a() throws Exception {
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            cursorQuery.close();
            if (Il1l11I11I.lll1111l11(I1I1lI1II1.a(new byte[]{125, 87, 85, 61, 43, 113, 99, 82, 115, 85, 95, 91, 84, 83}), 222364116L)) {
                throw new InterruptedException(I1I1lI1II1.a(new byte[]{0, 22, 43, 7, 81, 102, 118, 84, 115, 52, 81, 92, 94, 99, 91, 84, 54, 80, 20, 5}));
            }
            return numValueOf;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }
}
