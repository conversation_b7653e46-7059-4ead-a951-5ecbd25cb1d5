package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import com.ebook.bible.db.entity.BookVerseCommonEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class IIll1II1l1 implements Callable<List<BookVerseCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    IIll1II1l1(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public List<BookVerseCommonEntity> call() throws Exception {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 23, 61, 11, 7, 66, 104, 68, 92, 23, 68, 81, 88, 92, 90, 65}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 87, 16, 85, 94, 65}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 85, 11, 66}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 85, 95, 86, 15, 93, 81, 71, 82}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 95, 77, 1}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 88, 68, 92, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 93, 94, 87, 81, 8, 89, 87, 93, 77, 107, 88, 13, 5, 11, 84, 73, 62, 71, 92, 94, 83}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 2, 13, 92, 68, 4, 93, 65}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 19, 7, 84, 85, 19, 86, 91, 80, 83}));
            int i7 = columnIndex14;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                BookVerseCommonEntity bookVerseCommonEntity = new BookVerseCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i8 = -1;
                if (columnIndex != -1) {
                    i = columnIndex10;
                    i2 = columnIndex9;
                    bookVerseCommonEntity.setId(cursorQuery.getLong(columnIndex));
                } else {
                    i = columnIndex10;
                    i2 = columnIndex9;
                }
                if (columnIndex2 != -1) {
                    bookVerseCommonEntity.setNewTestament(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    bookVerseCommonEntity.setBookNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    bookVerseCommonEntity.setBookName(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    bookVerseCommonEntity.setChapterNumber(cursorQuery.getInt(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    bookVerseCommonEntity.setVerseNumber(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != -1) {
                    bookVerseCommonEntity.setVerseContent(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                }
                if (columnIndex8 != -1) {
                    bookVerseCommonEntity.setVerseColor(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                }
                int i9 = i2;
                if (i9 != -1) {
                    bookVerseCommonEntity.setVerseBookMark(cursorQuery.isNull(i9) ? null : cursorQuery.getString(i9));
                }
                int i10 = i;
                if (i10 != -1) {
                    bookVerseCommonEntity.setVerseNote(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    i8 = -1;
                }
                if (columnIndex11 != i8) {
                    i3 = columnIndex8;
                    i4 = columnIndex2;
                    bookVerseCommonEntity.setBookMarkModifyTime(cursorQuery.getLong(columnIndex11));
                } else {
                    i3 = columnIndex8;
                    i4 = columnIndex2;
                }
                if (columnIndex12 != i8) {
                    bookVerseCommonEntity.setNoteModifyTime(cursorQuery.getLong(columnIndex12));
                }
                if (columnIndex13 != i8) {
                    bookVerseCommonEntity.setHighlightModifyTime(cursorQuery.getLong(columnIndex13));
                }
                int i11 = i7;
                if (i11 != i8) {
                    bookVerseCommonEntity.setVerseAnnotationContent(cursorQuery.isNull(i11) ? null : cursorQuery.getString(i11));
                    i6 = columnIndex15;
                    i5 = -1;
                } else {
                    i5 = i8;
                    i6 = columnIndex15;
                }
                if (i6 != i5) {
                    bookVerseCommonEntity.setVerseAnnotationReference(cursorQuery.isNull(i6) ? null : cursorQuery.getString(i6));
                }
                arrayList2.add(bookVerseCommonEntity);
                columnIndex15 = i6;
                i7 = i11;
                arrayList = arrayList2;
                columnIndex2 = i4;
                columnIndex8 = i3;
                columnIndex9 = i9;
                columnIndex10 = i10;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
