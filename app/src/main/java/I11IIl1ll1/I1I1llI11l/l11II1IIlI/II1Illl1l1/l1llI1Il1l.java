package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.core.location.lIIlI111II;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class l1llI1Il1l implements Callable<Integer> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ II1I111llI_Impl b;

    l1llI1Il1l(II1I111llI_Impl iI1I111llI_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = iI1I111llI_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ Integer call() throws Exception {
        if (lIIlI111II.l111I1ll1l(4044)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{64, 23, 1, 22, 55, 94}));
        }
        return a();
    }

    public Integer a() throws Exception {
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(this.b.a, this.a, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            return numValueOf;
        } finally {
            cursorQuery.close();
        }
    }
}
