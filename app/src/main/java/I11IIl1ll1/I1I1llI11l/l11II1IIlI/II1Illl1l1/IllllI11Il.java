package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lllIII11lI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IllllI11Il extends lllIII11lI {
    final /* synthetic */ lIlIIlIII1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IllllI11Il(lIlIIlIII1_Impl liliiliii1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liliiliii1_impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        I1I1lI1II1.a(new byte[]{115, 33, 46, 32, 54, 112, 23, 118, 107, 43, 125, 16, 87, 86, 91, 94, 61, 2, 10, 93, 95, 18, 86, 106, 91, 95, 68, 17, 11, 64, 78});
        return I1I1lI1II1.a(new byte[]{115, 33, 46, 32, 54, 112, 23, 118, 107, 43, 125, 16, 87, 86, 91, 94, 61, 2, 10, 93, 95, 18, 86, 106, 91, 95, 68, 17, 11, 64, 78});
    }
}
