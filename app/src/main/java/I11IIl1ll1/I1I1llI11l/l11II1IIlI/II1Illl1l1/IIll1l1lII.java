package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.interpolator.view.animation.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.ebook.bible.db.entity.VerseEsEntity;
import java.io.ObjectStreamException;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class IIll1l1lII extends lll1l11l1I<VerseEsEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIll1l1lII(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VerseEsEntity verseEsEntity) throws ObjectStreamException {
        a(i1lI1I1lll, verseEsEntity);
        if (llIlII1IlI.Il1IIlI1II(1873)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{5, 20, 58, 81, 0, 114, Byte.MAX_VALUE, 126}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws IllegalAccessException, UnrecoverableEntryException {
        if (lIIlI111II.llIIIl11I1(770)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{114, 92}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 82, 16, 104, 16, 3, 7, 14, 80, 87, 16, 17, 4, 89, 84, 85, 21, 84, 92, 17, 62, 12, 87, 71, 62, 71, 80, 64, 66, 86, 8, 1, 92, 67, 3, 27, 4, 0, 10, 13, 94, 104, 94, 76, 9, 82, 85, 71, 89, 24, 85, 0, 14, 13, 89, 111, 15, 82, 88, 86, 86, 27, 5, 7, 90, 86, 19, 67, 1, 16, 58, 12, 64, 90, 82, 92, 22, 80, 28, 85, 79, 81, 71, 17, 4, 61, 92, 69, 12, 81, 80, 65, 86, 27, 5, 18, 87, 69, 16, 82, 59, 1, 10, 12, 65, 82, 94, 77, 4, 25, 16, 99, 120, 120, 96, 39, 50, 66, 26, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 28});
        if (androidx.constraintlayout.widget.l1IIll1I1l.I1lllI1llI(I1I1lI1II1.a(new byte[]{120, 41, 90, 18, 87, 13, 115, 120, 64, 44, 123, 119, 95, 107, 67, 83, 15, 43, 47, 96, 4, 10, 84, 81, 3, 6, 82, 45, 49, 64, 95}), 1879)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{89, 51, 55, 16, 86, 125, 115, 98, 83, 49, 117, 66}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VerseEsEntity verseEsEntity) throws ObjectStreamException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{0, 85, 27, 54, 58, 7, 93, 106, 83, 61, 94, 95, 13, 83, 66, 108, 82, 82, 49, 124, 85, 8, 93, 77, 103, 3, 67, 17, 20, 122}), 364956145L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{78, 48, 42, 36, 38, 91, 116, 115, 1, 49, 96, 104, 6, 83, 93, 88, 3, 35, 5, 96, 90, 8, 119, 80, 116, 65, 80, 86, 83, 72, 123, 46}));
        }
        i1lI1I1lll.bindLong(1, verseEsEntity.getId());
        i1lI1I1lll.bindLong(2, verseEsEntity.getNewTestament());
        i1lI1I1lll.bindLong(3, verseEsEntity.getBookNumber());
        if (verseEsEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, verseEsEntity.getBookName());
        }
        i1lI1I1lll.bindLong(5, verseEsEntity.getChapterNumber());
        i1lI1I1lll.bindLong(6, verseEsEntity.getVerseNumber());
        if (verseEsEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, verseEsEntity.getVerseContent());
        }
    }
}
