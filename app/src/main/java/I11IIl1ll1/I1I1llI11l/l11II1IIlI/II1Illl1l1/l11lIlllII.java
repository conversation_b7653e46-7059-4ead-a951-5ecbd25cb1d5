package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.accounts.utils.IIIlIl1I1l;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.DailyPlanPtEntity;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class l11lIlllII extends lll1l11l1I<DailyPlanPtEntity> {
    final /* synthetic */ II1I111llI_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    l11lIlllII(II1I111llI_Impl iI1I111llI_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iI1I111llI_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, DailyPlanPtEntity dailyPlanPtEntity) {
        a(i1lI1I1lll, dailyPlanPtEntity);
        if (IIIlIl1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{89, 17, 13, 35, 15, 79, 109, 5, 110, 9, 82, 99, 122, 1, 100, 126, 82, 4}), 7655)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{93, 9, 4, 36, 44, 70, 71, 94, 104, 54}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 87, 87, 94, 9, 29, 109, 71, 15, 86, 10, 61, 21, 22, 106, 67, 81, 91, 8, 85, 80, 21, 17, 84, 92, 6, 1, 78, 82, 86, 8, 65, 70, 71, 105, 67, 4, 3, 109, 94, 7, 87, 72, 2, 3, 11, 71, 68, 68, 102, 16, 81, 87, 106, 77, 93, 65, 14, 4, 2, 30, 80, 18, 86, 86, 92, 88, 83, 58, 16, 83, 80, 60, 94, 0, 2, 73, 2, 70, 82, 83, 86, 10, 84, 111, 65, 88, 83, 106, 22, 8, 22, 94, 85, 1, 31, 85, 67, 90, 86, 11, 59, 91, 83, 3, 27, 4, 18, 9, 3, 91, 104, 94, 88, 9, 85, 80, 25, 89, 68, 89, 3, 15, 61, 91, 93, 6, 108, 64, 65, 90, 87, 73, 4, 66, 91, 2, 89, 59, 6, 4, 27, 106, 94, 84, 89, 72, 80, 64, 89, 88, 90, 106, 6, 0, 27, 109, 83, 14, 70, 91, 71, 86, 27, 5, 20, 94, 86, 13, 104, 0, 3, 28, 61, 91, 66, 93, 91, 1, 66, 80, 25, 89, 68, 89, 3, 15, 61, 86, 81, 24, 108, 70, 71, 87, 67, 16, 23, 82, 27, 3, 83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93, 84, 25, 2, 0, 23, 86, 89, 14, 108, 89, 90, 88, 92, 5, 72, 82, 86, 22, 83, 13, 13, 58, 18, 71, 88, 87, 75, 1, 67, 67, 85, 21, 84, 68, 23, 14, 22, 87, 80, 77, 83, 71, 86, 80, 82, 23, 1, 92, 84, 6, 87, 72, 2, 12, 12, 70, 71, 89, 75, 5, 68, 89, 90, 87, 84, 25, 2, 5, 3, 70, 85, 1, 26, 21, 101, 119, 123, 48, 33, 97, 23, 75, 89, 17, 14, 9, 11, 83, 31, 15, 21, 68, 0, 25, 25, 6, 24, 10, 78, 94, 78, 13, 28, 94, 31, 10, 31, 9, 27, 90, 72, 13, 27, 92, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 25, 6, 24, 10, 75});
    }

    public void a(I1lI1I1lll i1lI1I1lll, DailyPlanPtEntity dailyPlanPtEntity) {
        i1lI1I1lll.bindLong(1, dailyPlanPtEntity.getId());
        i1lI1I1lll.bindLong(2, dailyPlanPtEntity.getFirstTagId());
        if (dailyPlanPtEntity.getFirstTagTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, dailyPlanPtEntity.getFirstTagTitle());
        }
        i1lI1I1lll.bindLong(4, dailyPlanPtEntity.getSecondTagId());
        if (dailyPlanPtEntity.getSecondTagTitle() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, dailyPlanPtEntity.getSecondTagTitle());
        }
        i1lI1I1lll.bindLong(6, dailyPlanPtEntity.getPlanId());
        if (dailyPlanPtEntity.getPlanName() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, dailyPlanPtEntity.getPlanName());
        }
        if (dailyPlanPtEntity.getPlanImgUrl() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, dailyPlanPtEntity.getPlanImgUrl());
        }
        i1lI1I1lll.bindLong(9, dailyPlanPtEntity.getPlanDayId());
        i1lI1I1lll.bindLong(10, dailyPlanPtEntity.getPlanDayCount());
        i1lI1I1lll.bindLong(11, dailyPlanPtEntity.getPlanDayNumber());
        i1lI1I1lll.bindLong(12, dailyPlanPtEntity.getPlanDayStatus());
        i1lI1I1lll.bindLong(13, dailyPlanPtEntity.getDateCompleted());
        if (dailyPlanPtEntity.getAudioLink() == null) {
            i1lI1I1lll.bindNull(14);
        } else {
            i1lI1I1lll.bindString(14, dailyPlanPtEntity.getAudioLink());
        }
        if (dailyPlanPtEntity.getAudioProgress() == null) {
            i1lI1I1lll.bindNull(15);
        } else {
            i1lI1I1lll.bindString(15, dailyPlanPtEntity.getAudioProgress());
        }
        if (dailyPlanPtEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(16);
        } else {
            i1lI1I1lll.bindString(16, dailyPlanPtEntity.getVerseContent());
        }
        if (dailyPlanPtEntity.getReference() == null) {
            i1lI1I1lll.bindNull(17);
        } else {
            i1lI1I1lll.bindString(17, dailyPlanPtEntity.getReference());
        }
        if (dailyPlanPtEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(18);
        } else {
            i1lI1I1lll.bindString(18, dailyPlanPtEntity.getInspiration());
        }
        if (dailyPlanPtEntity.getDate() == null) {
            i1lI1I1lll.bindNull(19);
        } else {
            i1lI1I1lll.bindString(19, dailyPlanPtEntity.getDate());
        }
    }
}
