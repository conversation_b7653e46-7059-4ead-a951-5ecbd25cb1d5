package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import com.ebook.bible.db.entity.DailyPlanEnEntity;
import java.security.UnrecoverableKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class II11lI1111 extends lll1l11l1I<DailyPlanEnEntity> {
    final /* synthetic */ II1I111llI_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    II11lI1111(II1I111llI_Impl iI1I111llI_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iI1I111llI_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, DailyPlanEnEntity dailyPlanEnEntity) {
        if (llIlI11III.IlIllIll1I(I1I1lI1II1.a(new byte[]{84, 5, 58, 8, 17, 95, 98, 122, 81, 61, 94, 88, 86, 12, 7, 101, 82, 57, 38, 112}), 166152722L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{122, 84, 48, 33, 6, 95, 113, 96, 75, 52, 6, 101, 89, 109, 78, 1, 51, 86, 17}));
        }
        a(i1lI1I1lll, dailyPlanEnEntity);
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws UnrecoverableKeyException {
        if (androidx.core.location.I1Ill1lIII.l11I11I11l(253009631L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{78, 46, 5, 60, 48, 115, 64, 103, 15, 21, 96, 89, 114, 9, 99, 101, 17, 17, 9, 113}));
        }
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 87, 87, 94, 9, 29, 109, 71, 15, 86, 10, 61, 0, 12, 106, 67, 81, 91, 8, 85, 80, 21, 17, 84, 92, 6, 1, 78, 82, 86, 8, 65, 70, 71, 105, 67, 4, 3, 109, 94, 7, 87, 72, 2, 3, 11, 71, 68, 68, 102, 16, 81, 87, 106, 77, 93, 65, 14, 4, 2, 30, 80, 18, 86, 86, 92, 88, 83, 58, 16, 83, 80, 60, 94, 0, 2, 73, 2, 70, 82, 83, 86, 10, 84, 111, 65, 88, 83, 106, 22, 8, 22, 94, 85, 1, 31, 85, 67, 90, 86, 11, 59, 91, 83, 3, 27, 4, 18, 9, 3, 91, 104, 94, 88, 9, 85, 80, 25, 89, 68, 89, 3, 15, 61, 91, 93, 6, 108, 64, 65, 90, 87, 73, 4, 66, 91, 2, 89, 59, 6, 4, 27, 106, 94, 84, 89, 72, 80, 64, 89, 88, 90, 106, 6, 0, 27, 109, 83, 14, 70, 91, 71, 86, 27, 5, 20, 94, 86, 13, 104, 0, 3, 28, 61, 91, 66, 93, 91, 1, 66, 80, 25, 89, 68, 89, 3, 15, 61, 86, 81, 24, 108, 70, 71, 87, 67, 16, 23, 82, 27, 3, 83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93, 84, 25, 2, 0, 23, 86, 89, 14, 108, 89, 90, 88, 92, 5, 72, 82, 86, 22, 83, 13, 13, 58, 18, 71, 88, 87, 75, 1, 67, 67, 85, 21, 84, 68, 23, 14, 22, 87, 80, 77, 83, 71, 86, 80, 82, 23, 1, 92, 84, 6, 87, 72, 2, 12, 12, 70, 71, 89, 75, 5, 68, 89, 90, 87, 84, 25, 2, 5, 3, 70, 85, 1, 26, 21, 101, 119, 123, 48, 33, 97, 23, 75, 89, 17, 14, 9, 11, 83, 31, 15, 21, 68, 0, 25, 25, 6, 24, 10, 78, 94, 78, 13, 28, 94, 31, 10, 31, 9, 27, 90, 72, 13, 27, 92, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 25, 6, 24, 10, 75});
    }

    public void a(I1lI1I1lll i1lI1I1lll, DailyPlanEnEntity dailyPlanEnEntity) {
        i1lI1I1lll.bindLong(1, dailyPlanEnEntity.getId());
        i1lI1I1lll.bindLong(2, dailyPlanEnEntity.getFirstTagId());
        if (dailyPlanEnEntity.getFirstTagTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, dailyPlanEnEntity.getFirstTagTitle());
        }
        i1lI1I1lll.bindLong(4, dailyPlanEnEntity.getSecondTagId());
        if (dailyPlanEnEntity.getSecondTagTitle() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, dailyPlanEnEntity.getSecondTagTitle());
        }
        i1lI1I1lll.bindLong(6, dailyPlanEnEntity.getPlanId());
        if (dailyPlanEnEntity.getPlanName() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, dailyPlanEnEntity.getPlanName());
        }
        if (dailyPlanEnEntity.getPlanImgUrl() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, dailyPlanEnEntity.getPlanImgUrl());
        }
        i1lI1I1lll.bindLong(9, dailyPlanEnEntity.getPlanDayId());
        i1lI1I1lll.bindLong(10, dailyPlanEnEntity.getPlanDayCount());
        i1lI1I1lll.bindLong(11, dailyPlanEnEntity.getPlanDayNumber());
        i1lI1I1lll.bindLong(12, dailyPlanEnEntity.getPlanDayStatus());
        i1lI1I1lll.bindLong(13, dailyPlanEnEntity.getDateCompleted());
        if (dailyPlanEnEntity.getAudioLink() == null) {
            i1lI1I1lll.bindNull(14);
        } else {
            i1lI1I1lll.bindString(14, dailyPlanEnEntity.getAudioLink());
        }
        if (dailyPlanEnEntity.getAudioProgress() == null) {
            i1lI1I1lll.bindNull(15);
        } else {
            i1lI1I1lll.bindString(15, dailyPlanEnEntity.getAudioProgress());
        }
        if (dailyPlanEnEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(16);
        } else {
            i1lI1I1lll.bindString(16, dailyPlanEnEntity.getVerseContent());
        }
        if (dailyPlanEnEntity.getReference() == null) {
            i1lI1I1lll.bindNull(17);
        } else {
            i1lI1I1lll.bindString(17, dailyPlanEnEntity.getReference());
        }
        if (dailyPlanEnEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(18);
        } else {
            i1lI1I1lll.bindString(18, dailyPlanEnEntity.getInspiration());
        }
        if (dailyPlanEnEntity.getDate() == null) {
            i1lI1I1lll.bindNull(19);
        } else {
            i1lI1I1lll.bindString(19, dailyPlanEnEntity.getDate());
        }
    }
}
