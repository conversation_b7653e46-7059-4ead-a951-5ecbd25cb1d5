package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1111IIl11;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.llIl1lII1I;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.DailyPlanEsEntity;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class l11l1IIl1I extends lll1l11l1I<DailyPlanEsEntity> {
    final /* synthetic */ II1I111llI_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    l11l1IIl1I(II1I111llI_Impl iI1I111llI_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iI1I111llI_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, DailyPlanEsEntity dailyPlanEsEntity) throws CertificateParsingException {
        a(i1lI1I1lll, dailyPlanEsEntity);
        if (l111Il1lI1.llII1lIIlI(I1I1lI1II1.a(new byte[]{79, 42, 8, 7, 0, 80, 0, 74, 13, 44, 123, 117, 4, 84, 81, 99, 85, 0, 36, 100, 82, 50}))) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{84, 13, 1, 19, 55, 5, 102, 82, 88, 21, 9, 102, 97, 90, Byte.MAX_VALUE, 82, 23, 81, 3}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        if (llIl1lII1I.Ill1lIIlIl(503)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{112, 83, 5, 35, 87, 99, 94}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 87, 87, 94, 9, 29, 109, 71, 15, 86, 10, 61, 0, 17, 106, 67, 81, 91, 8, 85, 80, 21, 17, 84, 92, 6, 1, 78, 82, 86, 8, 65, 70, 71, 105, 67, 4, 3, 109, 94, 7, 87, 72, 2, 3, 11, 71, 68, 68, 102, 16, 81, 87, 106, 77, 93, 65, 14, 4, 2, 30, 80, 18, 86, 86, 92, 88, 83, 58, 16, 83, 80, 60, 94, 0, 2, 73, 2, 70, 82, 83, 86, 10, 84, 111, 65, 88, 83, 106, 22, 8, 22, 94, 85, 1, 31, 85, 67, 90, 86, 11, 59, 91, 83, 3, 27, 4, 18, 9, 3, 91, 104, 94, 88, 9, 85, 80, 25, 89, 68, 89, 3, 15, 61, 91, 93, 6, 108, 64, 65, 90, 87, 73, 4, 66, 91, 2, 89, 59, 6, 4, 27, 106, 94, 84, 89, 72, 80, 64, 89, 88, 90, 106, 6, 0, 27, 109, 83, 14, 70, 91, 71, 86, 27, 5, 20, 94, 86, 13, 104, 0, 3, 28, 61, 91, 66, 93, 91, 1, 66, 80, 25, 89, 68, 89, 3, 15, 61, 86, 81, 24, 108, 70, 71, 87, 67, 16, 23, 82, 27, 3, 83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93, 84, 25, 2, 0, 23, 86, 89, 14, 108, 89, 90, 88, 92, 5, 72, 82, 86, 22, 83, 13, 13, 58, 18, 71, 88, 87, 75, 1, 67, 67, 85, 21, 84, 68, 23, 14, 22, 87, 80, 77, 83, 71, 86, 80, 82, 23, 1, 92, 84, 6, 87, 72, 2, 12, 12, 70, 71, 89, 75, 5, 68, 89, 90, 87, 84, 25, 2, 5, 3, 70, 85, 1, 26, 21, 101, 119, 123, 48, 33, 97, 23, 75, 89, 17, 14, 9, 11, 83, 31, 15, 21, 68, 0, 25, 25, 6, 24, 10, 78, 94, 78, 13, 28, 94, 31, 10, 31, 9, 27, 90, 72, 13, 27, 92, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 25, 6, 24, 10, 75});
        if (I1111IIl11.I1lllI1llI(266156473L)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{120, 42, 42, 7, 14, 5, 1, 119, 125, 84, 119, 104, 7, 14, 6}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, DailyPlanEsEntity dailyPlanEsEntity) throws CertificateParsingException {
        i1lI1I1lll.bindLong(1, dailyPlanEsEntity.getId());
        i1lI1I1lll.bindLong(2, dailyPlanEsEntity.getFirstTagId());
        if (dailyPlanEsEntity.getFirstTagTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, dailyPlanEsEntity.getFirstTagTitle());
        }
        i1lI1I1lll.bindLong(4, dailyPlanEsEntity.getSecondTagId());
        if (dailyPlanEsEntity.getSecondTagTitle() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, dailyPlanEsEntity.getSecondTagTitle());
        }
        i1lI1I1lll.bindLong(6, dailyPlanEsEntity.getPlanId());
        if (dailyPlanEsEntity.getPlanName() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, dailyPlanEsEntity.getPlanName());
        }
        if (dailyPlanEsEntity.getPlanImgUrl() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, dailyPlanEsEntity.getPlanImgUrl());
        }
        i1lI1I1lll.bindLong(9, dailyPlanEsEntity.getPlanDayId());
        i1lI1I1lll.bindLong(10, dailyPlanEsEntity.getPlanDayCount());
        i1lI1I1lll.bindLong(11, dailyPlanEsEntity.getPlanDayNumber());
        i1lI1I1lll.bindLong(12, dailyPlanEsEntity.getPlanDayStatus());
        i1lI1I1lll.bindLong(13, dailyPlanEsEntity.getDateCompleted());
        if (dailyPlanEsEntity.getAudioLink() == null) {
            i1lI1I1lll.bindNull(14);
        } else {
            i1lI1I1lll.bindString(14, dailyPlanEsEntity.getAudioLink());
        }
        if (dailyPlanEsEntity.getAudioProgress() == null) {
            i1lI1I1lll.bindNull(15);
        } else {
            i1lI1I1lll.bindString(15, dailyPlanEsEntity.getAudioProgress());
        }
        if (dailyPlanEsEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(16);
        } else {
            i1lI1I1lll.bindString(16, dailyPlanEsEntity.getVerseContent());
        }
        if (dailyPlanEsEntity.getReference() == null) {
            i1lI1I1lll.bindNull(17);
        } else {
            i1lI1I1lll.bindString(17, dailyPlanEsEntity.getReference());
        }
        if (dailyPlanEsEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(18);
        } else {
            i1lI1I1lll.bindString(18, dailyPlanEsEntity.getInspiration());
        }
        if (dailyPlanEsEntity.getDate() == null) {
            i1lI1I1lll.bindNull(19);
        } else {
            i1lI1I1lll.bindString(19, dailyPlanEsEntity.getDate());
        }
        if (Il1l11I11I.I1II1111ll(272049997L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{95, 37, 10, 44, 45, 13, 14, 124, 123, 85, 125, 0, 65, 104, 102, 0, 20, 8, 19, 92, 65, 88}));
        }
    }
}
