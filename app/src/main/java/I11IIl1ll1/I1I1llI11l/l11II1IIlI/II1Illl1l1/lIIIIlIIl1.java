package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.media.content.II1I11IlI1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import com.ebook.bible.db.entity.VerseActionsEnEntity;
import java.security.cert.CertStoreException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class lIIIIlIIl1 extends lll1l11l1I<VerseActionsEnEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lIIIIlIIl1(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VerseActionsEnEntity verseActionsEnEntity) throws NoSuchMethodException, CertStoreException {
        if (lIIlI111II.llllI1l1II(7093)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{112, 43}));
        }
        a(i1lI1I1lll, verseActionsEnEntity);
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 86, 0, 67, 13, 13, 11, 17, 106, 82, 94, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 11, 86, 80, 77, 83, 92, 64, 105, 89, 0, 19, 109, 67, 6, 68, 16, 3, 8, 7, 91, 67, 80, 21, 4, 82, 95, 90, 82, 107, 91, 23, 12, 0, 87, 66, 1, 31, 85, 81, 89, 88, 14, 59, 92, 86, 14, 82, 4, 78, 5, 1, 93, 86, 64, 77, 1, 66, 111, 91, 76, 89, 87, 7, 19, 2, 30, 80, 23, 86, 71, 64, 83, 104, 11, 17, 95, 85, 6, 69, 4, 78, 5, 20, 80, 69, 67, 92, 59, 83, 95, 91, 77, 81, 91, 22, 1, 78, 82, 70, 4, 65, 70, 86, 105, 84, 10, 8, 93, 69, 3, 27, 4, 20, 0, 16, 70, 82, 111, 91, 11, 95, 91, 88, 88, 70, 94, 2, 77, 2, 68, 85, 19, 64, 80, 108, 88, 88, 17, 1, 82, 27, 3, 85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85, 1, 31, 85, 81, 89, 88, 14, 59, 92, 88, 23, 82, 59, 15, 10, 6, 92, 81, 73, 102, 16, 89, 93, 80, 89, 24, 85, 0, 14, 13, 89, 111, 9, 90, 82, 91, 90, 94, 2, 12, 70, 104, 14, 88, 0, 11, 3, 27, 106, 67, 89, 84, 1, 80, 25, 21, 111, 117, 121, 55, 36, 49, 18, 24, 15, 70, 89, 95, 95, 81, 77, 91, 30, 23, 83, 30, 72, 93, 73, 93, 25, 8, 28, 6, 72, 15, 28, 10, 21, 11, 25, 93, 77, 93, 30, 15, 77, 12, 25, 12, 31});
        if (IlIIlI11I1.I1lllI1llI(265834299L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{86, 38, 48, 18, 56, 70, 99, 102, 113, 62, 82, 91, 84, 105, Byte.MAX_VALUE, 80, 15, 45, 39, 116, 84}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VerseActionsEnEntity verseActionsEnEntity) throws CertStoreException {
        if (II1I11IlI1.IlIllIll1I(241930443L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{99, 34, 22, 54, 81, 83, 6, 71, 115, 80, 83, 65, 7, 96, 65, 5}));
        }
        i1lI1I1lll.bindLong(1, verseActionsEnEntity.getId());
        i1lI1I1lll.bindLong(2, verseActionsEnEntity.getNewTestament());
        i1lI1I1lll.bindLong(3, verseActionsEnEntity.getBookNumber());
        if (verseActionsEnEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, verseActionsEnEntity.getBookName());
        }
        i1lI1I1lll.bindLong(5, verseActionsEnEntity.getChapterNumber());
        i1lI1I1lll.bindLong(6, verseActionsEnEntity.getVerseNumber());
        if (verseActionsEnEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, verseActionsEnEntity.getVerseContent());
        }
        if (verseActionsEnEntity.getVerseColor() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, verseActionsEnEntity.getVerseColor());
        }
        if (verseActionsEnEntity.getVerseBookMark() == null) {
            i1lI1I1lll.bindNull(9);
        } else {
            i1lI1I1lll.bindString(9, verseActionsEnEntity.getVerseBookMark());
        }
        if (verseActionsEnEntity.getVerseNote() == null) {
            i1lI1I1lll.bindNull(10);
        } else {
            i1lI1I1lll.bindString(10, verseActionsEnEntity.getVerseNote());
        }
        i1lI1I1lll.bindLong(11, verseActionsEnEntity.getBookMarkModifyTime());
        i1lI1I1lll.bindLong(12, verseActionsEnEntity.getNoteModifyTime());
        i1lI1I1lll.bindLong(13, verseActionsEnEntity.getHighlightModifyTime());
    }
}
