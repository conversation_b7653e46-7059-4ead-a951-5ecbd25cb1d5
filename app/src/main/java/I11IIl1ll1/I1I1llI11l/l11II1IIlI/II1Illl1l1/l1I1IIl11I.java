package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import com.ebook.bible.db.entity.DevotionCommonEntity;
import java.io.NotActiveException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class l1I1IIl11I extends lll1l11l1I<DevotionCommonEntity> {
    final /* synthetic */ IIII1IIl1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    l1I1IIl11I(IIII1IIl1I_Impl iIII1IIl1I_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iIII1IIl1I_Impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws CloneNotSupportedException {
        if (lIIlI111II.llIIlI1llI(171655972L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{99, 47, 81, 93, 50, 113, 99, 99, 80, 85, 105, 2, 5}));
        }
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 87, 83, 65, 10, 16, 91, 88, 13, 104, 16, 3, 7, 14, 80, 87, 16, 17, 4, 89, 84, 85, 21, 84, 81, 3, 21, 7, 82, 28, 1, 95, 84, 93, 81, 66, 4, 3, 87, 87, 79, 87, 22, 7, 3, 7, 71, 82, 94, 90, 1, 80, 28, 85, 88, 70, 92, 2, 77, 2, 70, 89, 21, 95, 80, 83, 26, 87, 12, 10, 65, 71, 10, 69, 5, 22, 12, 13, 91, 87, 28, 89, 20, 66, 81, 76, 92, 70, 85, 78, 1, 19, 71, 95, 21, 86, 85, 31, 86, 86, 16, 16, 90, 88, 17, 104, 10, 3, 8, 7, 85, 27, 80, 88, 17, 68, 88, 90, 75, 107, 84, 20, 0, 22, 83, 66, 1, 31, 85, 82, 67, 67, 13, 11, 64, 104, 10, 89, 16, 16, 10, 6, 64, 84, 85, 89, 72, 80, 95, 71, 80, 83, 92, 12, 0, 14, 109, 92, 8, 93, 94, 83, 31, 23, 51, 37, 126, 98, 38, 100, 68, 74, 11, 23, 89, 91, 89, 95, 76, 15, 28, 21, 9, 29, 25, 93, 77, 93, 30, 15, 77, 12, 25, 12, 26, 8, 73, 91, 30, 8, 79, 8, 72, 93, 73, 93, 25, 8, 25});
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, DevotionCommonEntity devotionCommonEntity) throws NotActiveException {
        if (androidx.interpolator.view.animation.IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{101, 15, 10, 32, 84, 120, 98, 74, 115, 17}), 6128)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{69, 21, 48, 4, 58, 98, 70, 72, 108, 45, 6, 106, 113, 122, 119, 80, 3, 22, 1, 116, 106, 9, 68, 83, 81, 0, 83, 15, 29, 1, 88}));
        }
        i1lI1I1lll.bindLong(1, devotionCommonEntity.getId());
        if (devotionCommonEntity.getDate() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, devotionCommonEntity.getDate());
        }
        if (devotionCommonEntity.getLanguage() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, devotionCommonEntity.getLanguage());
        }
        if (devotionCommonEntity.getReference() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, devotionCommonEntity.getReference());
        }
        if (devotionCommonEntity.getAri() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, devotionCommonEntity.getAri());
        }
        if (devotionCommonEntity.getTitle() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, devotionCommonEntity.getTitle());
        }
        if (devotionCommonEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, devotionCommonEntity.getInspiration());
        }
        if (devotionCommonEntity.getPrayer() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, devotionCommonEntity.getPrayer());
        }
        if (devotionCommonEntity.getQuote() == null) {
            i1lI1I1lll.bindNull(9);
        } else {
            i1lI1I1lll.bindString(9, devotionCommonEntity.getQuote());
        }
        if (devotionCommonEntity.getAuthorName() == null) {
            i1lI1I1lll.bindNull(10);
        } else {
            i1lI1I1lll.bindString(10, devotionCommonEntity.getAuthorName());
        }
        if (devotionCommonEntity.getAuthorAvatar() == null) {
            i1lI1I1lll.bindNull(11);
        } else {
            i1lI1I1lll.bindString(11, devotionCommonEntity.getAuthorAvatar());
        }
        if (devotionCommonEntity.getAuthorIntroduce() == null) {
            i1lI1I1lll.bindNull(12);
        } else {
            i1lI1I1lll.bindString(12, devotionCommonEntity.getAuthorIntroduce());
        }
        if (devotionCommonEntity.getOriginal_link() == null) {
            i1lI1I1lll.bindNull(13);
        } else {
            i1lI1I1lll.bindString(13, devotionCommonEntity.getOriginal_link());
        }
    }
}
