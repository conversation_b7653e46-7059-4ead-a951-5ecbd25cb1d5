package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.media.content.II1I11IlI1;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.llIl1lII1I;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.room.CoroutinesRoom;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.DevotionCommonEntity;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.IOException;
import java.io.NotSerializableException;
import java.net.PortUnreachableException;
import java.security.DigestException;
import java.security.KeyManagementException;
import java.security.cert.CertificateEncodingException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeoutException;
import kotlin.coroutines.d;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
public final class IIII1IIl1I_Impl implements IIII1IIl1I {
    private final RoomDatabase a;
    private final lll1l11l1I<DevotionCommonEntity> b;

    static /* synthetic */ DevotionCommonEntity a(IIII1IIl1I_Impl iIII1IIl1I_Impl, Cursor cursor) throws CharConversionException, PortUnreachableException, CloneNotSupportedException {
        if (IIlI1Il1lI.l1ll11I11l(I1I1lI1II1.a(new byte[]{68, 52, 49, 16, 49, 70, 14, Byte.MAX_VALUE, 117, 7, 106, 90, Byte.MAX_VALUE, 15, 77, 120, 6, 11, 1, 93, 4, 82, 68, 94, 106, 84, 115}), 161941089L)) {
            Log.i(I1I1lI1II1.a(new byte[]{78, 20, 56, 36, 16, 1, 15, 113, 113, 54, 2, 68, 76, 104, 86, 82, 59, 14, 35}), I1I1lI1II1.a(new byte[]{83, 53, 42, 18}));
            return null;
        }
        DevotionCommonEntity devotionCommonEntityA = iIII1IIl1I_Impl.a(cursor);
        if (!l111Il1lI1.llll111lI1(I1I1lI1II1.a(new byte[]{91, 2, 55, 34, 33, 82}), 4156)) {
            return devotionCommonEntityA;
        }
        Log.v(I1I1lI1II1.a(new byte[]{96, 60, 58, 16, 35, 83, 91, 99, 104, 81}), I1I1lI1II1.a(new byte[]{15, 21, 12, 92, 45, 95, 77, 97, 97, 80, 106, 100, 13, 87, 122, 77, 10, 46, 12}));
        return null;
    }

    public IIII1IIl1I_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new l1I1IIl11I(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIII1IIl1I
    public Object a(DevotionCommonEntity devotionCommonEntity, d<? super Long> dVar) throws TimeoutException {
        if (llIl1lII1I.ll1I1lII11(I1I1lI1II1.a(new byte[]{100, 33, 41, 51, 9, 116, 71}), 9296)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{99, 35, 16, 87, 26, 86, 103, 82, 90, 3, 73, 68}));
        }
        return CoroutinesRoom.execute(this.a, true, new I11lII1Il1(this, devotionCommonEntity), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIII1IIl1I
    public Object a(IlIlIIll11 ilIlIIll11, d<? super List<DevotionCommonEntity>> dVar) throws EOFException {
        if (IIlI1Il1lI.IlII1Illll(364048670L)) {
            Log.w(I1I1lI1II1.a(new byte[]{110, 16, 82, 32, 47, 77, 95, 74, 14, 39, 2, 67, 98, 1, 93, 4, 54, 22, 44, 83, 8, 82, 117, 7, Byte.MAX_VALUE, 94, 0}), I1I1lI1II1.a(new byte[]{125, 18, 42, 93, 33, 100, 70, 121, 74, 84, 121, 8, 115, 80, 4, 102, 12}));
            return null;
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new I1I1Il1I1I(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIII1IIl1I
    public int a(IlIlIIll11 ilIlIIll11) throws DigestException, NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int i = cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
            cursorQuery.close();
            if (IIlI1ll1ll.IlIIlIllI1(I1I1lI1II1.a(new byte[]{90, 1, 42, 84, 3, 4, 98, 100, 126, 13, 3, 86, 101, 72, 108, 65, 82, 43, 87, 103, 122, 6, 100, 102, 82, 91, 66, 3, 0, 64}), I1I1lI1II1.a(new byte[]{4, 5, 5, 84, 9, 84}))) {
                throw new DigestException(I1I1lI1II1.a(new byte[]{97, 61, 86, 50, 3, 67, 77, 104, 111}));
            }
            return i;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIII1IIl1I
    public int b(IlIlIIll11 ilIlIIll11) throws IOException, CertificateEncodingException {
        if (II1I11IlI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{111, 50, 44, 16, 37, 118, 82, 64, 106, 46, 96, 74, 99, 117, 124, 88, 19, 38, 48, 65, 123}), 483328441L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{82, 47, 83, 55, 8, 81, 69, 125}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    public static List<Class<?>> a() throws KeyManagementException {
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (lIlIII1I1l.IlIIlIllI1(I1I1lI1II1.a(new byte[]{4, 37, 37, 33, 32, 122, 94, 102}), 251885144L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{89, 54, 83, 14, 7, 118, 71, 119, 85, 19, 82, 90, 114, 76, 93, 126, 84, 47, 33, 101, 71, 27, 101, 5, 81}));
        }
        return listEmptyList;
    }

    private DevotionCommonEntity a(Cursor cursor) throws CharConversionException, PortUnreachableException, CloneNotSupportedException {
        int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{94, 0}));
        int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
        int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{91, 5, 12, 2, 23, 84, 80, 85}));
        int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
        int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{86, 22, 11}));
        int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{67, 13, 22, 9, 7}));
        int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
        int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{71, 22, 3, 28, 7, 71}));
        int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
        int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{86, 17, 22, 13, 13, 71, 104, 94, 88, 9, 85}));
        int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{86, 17, 22, 13, 13, 71, 104, 81, 79, 5, 68, 81, 71}));
        int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{86, 17, 22, 13, 13, 71, 104, 89, 87, 16, 66, 95, 81, 76, 87, 80}));
        int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursor, I1I1lI1II1.a(new byte[]{88, 22, 11, 2, 11, 91, 86, 92, 102, 8, 89, 94, 94}));
        DevotionCommonEntity devotionCommonEntity = new DevotionCommonEntity();
        if (columnIndex != -1) {
            devotionCommonEntity.setId(cursor.getLong(columnIndex));
        }
        if (columnIndex2 != -1) {
            devotionCommonEntity.setDate(cursor.isNull(columnIndex2) ? null : cursor.getString(columnIndex2));
        }
        if (columnIndex3 != -1) {
            devotionCommonEntity.setLanguage(cursor.isNull(columnIndex3) ? null : cursor.getString(columnIndex3));
        }
        if (columnIndex4 != -1) {
            devotionCommonEntity.setReference(cursor.isNull(columnIndex4) ? null : cursor.getString(columnIndex4));
        }
        if (columnIndex5 != -1) {
            devotionCommonEntity.setAri(cursor.isNull(columnIndex5) ? null : cursor.getString(columnIndex5));
        }
        if (columnIndex6 != -1) {
            devotionCommonEntity.setTitle(cursor.isNull(columnIndex6) ? null : cursor.getString(columnIndex6));
        }
        if (columnIndex7 != -1) {
            devotionCommonEntity.setInspiration(cursor.isNull(columnIndex7) ? null : cursor.getString(columnIndex7));
        }
        if (columnIndex8 != -1) {
            devotionCommonEntity.setPrayer(cursor.isNull(columnIndex8) ? null : cursor.getString(columnIndex8));
        }
        if (columnIndex9 != -1) {
            devotionCommonEntity.setQuote(cursor.isNull(columnIndex9) ? null : cursor.getString(columnIndex9));
        }
        if (columnIndex10 != -1) {
            devotionCommonEntity.setAuthorName(cursor.isNull(columnIndex10) ? null : cursor.getString(columnIndex10));
        }
        if (columnIndex11 != -1) {
            devotionCommonEntity.setAuthorAvatar(cursor.isNull(columnIndex11) ? null : cursor.getString(columnIndex11));
        }
        if (columnIndex12 != -1) {
            devotionCommonEntity.setAuthorIntroduce(cursor.isNull(columnIndex12) ? null : cursor.getString(columnIndex12));
        }
        if (columnIndex13 != -1) {
            devotionCommonEntity.setOriginal_link(cursor.isNull(columnIndex13) ? null : cursor.getString(columnIndex13));
        }
        return devotionCommonEntity;
    }
}
