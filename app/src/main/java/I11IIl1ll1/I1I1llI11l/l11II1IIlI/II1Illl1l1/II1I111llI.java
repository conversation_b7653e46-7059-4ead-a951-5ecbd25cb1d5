package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import com.ebook.bible.db.entity.DailyPlanCommonEntity;
import com.ebook.bible.db.entity.DailyPlanEnEntity;
import com.ebook.bible.db.entity.DailyPlanEsEntity;
import com.ebook.bible.db.entity.DailyPlanPtEntity;
import java.util.List;
import kotlin.Metadata;
import kotlin.coroutines.d;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0005\u0010\u0006J\u0017\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0007\u0010\u0006J\u001f\u0010\n\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\n\u0010\u000bJ\u001f\u0010\f\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\f\u0010\u000bJ\u001f\u0010\r\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\r\u0010\u000bJ#\u0010\r\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\r\u0010\u000eJ\u001f\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\t\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u000f\u0010\u000bJ\u0017\u0010\u0005\u001a\u00020\u00112\u0006\u0010\u0003\u001a\u00020\u0010H'¢\u0006\u0004\b\u0005\u0010\u0012J\u0017\u0010\u0005\u001a\u00020\u00112\u0006\u0010\u0003\u001a\u00020\u0013H'¢\u0006\u0004\b\u0005\u0010\u0014J\u0017\u0010\u0005\u001a\u00020\u00112\u0006\u0010\u0003\u001a\u00020\u0015H'¢\u0006\u0004\b\u0005\u0010\u0016J\u001b\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0005\u0010\u000eJ\u001b\u0010\f\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\f\u0010\u000eJ\u001f\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0018\u001a\u00020\u0017H'¢\u0006\u0004\b\u0005\u0010\u0019\u0082\u0002\u0004\n\u0002\b\u0019"}, d2 = {"LI11IIl1ll1/I1I1llI11l/l11II1IIlI/II1Illl1l1/II1I111llI;", "", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;", "p0", "", "a", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)I", "e", "", "Lcom/ebook/bible/db/entity/DailyPlanCommonEntity;", "f", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Ljava/util/List;", "b", "c", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;Lkotlin/coroutines/d;)Ljava/lang/Object;", "d", "Lcom/ebook/bible/db/entity/I1llllIllI;", "", "(Lcom/ebook/bible/db/entity/I1llllIllI;)J", "Lcom/ebook/bible/db/entity/llIl1IlllI;", "(Lcom/ebook/bible/db/entity/llIl1IlllI;)J", "Lcom/ebook/bible/db/entity/lIlIl1Il11;", "(Lcom/ebook/bible/db/entity/lIlIl1Il11;)J", "", "p1", "(ILjava/lang/String;)I"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface II1I111llI {
    int a(int p0, String p1);

    int a(IlIlIIll11 p0);

    long a(DailyPlanEnEntity p0);

    long a(DailyPlanPtEntity p0);

    long a(DailyPlanEsEntity p0);

    Object a(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);

    Object b(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);

    List<DailyPlanCommonEntity> b(IlIlIIll11 p0);

    Object c(IlIlIIll11 ilIlIIll11, d<? super List<DailyPlanCommonEntity>> dVar);

    List<DailyPlanCommonEntity> c(IlIlIIll11 p0);

    List<DailyPlanCommonEntity> d(IlIlIIll11 p0);

    int e(IlIlIIll11 p0);

    List<DailyPlanCommonEntity> f(IlIlIIll11 p0);
}
