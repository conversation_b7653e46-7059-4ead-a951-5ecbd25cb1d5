package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.accounts.utils.IIIlIl1I1l;
import android.database.Cursor;
import android.support.v4.graphics.drawable.l11Il111ll;
import com.ebook.bible.db.entity.PrayerCommonEntity;
import java.net.MalformedURLException;
import java.security.NoSuchProviderException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class l1IIll1I1l implements Callable<List<PrayerCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ IllllI11lI_Impl b;

    l1IIll1I1l(IllllI11lI_Impl illllI11lI_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = illllI11lI_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ List<PrayerCommonEntity> call() throws Exception {
        List<PrayerCommonEntity> listA = a();
        if (IIIlIl1I1l.Ill1lIIlIl(503)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{7, 2, 18, 40, 21, 12, 123, 115, 120, 87, 85, 126, 123, 15, 113, 92, 84, 42, 59, 98, 81, 3, 0, 119, 100, 66, 86, 34, 5}));
        }
        return listA;
    }

    public List<PrayerCommonEntity> a() throws Exception {
        if (l11Il111ll.IlII1Illll(231597532L)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{89, 17, 46, 84, 35, 80, 66, 96, 85, 80, 98, 105, 70, 88, 112, 82}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(IllllI11lI_Impl.a(this.b), this.a, false, null);
        try {
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                arrayList.add(this.b.a(cursorQuery));
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
