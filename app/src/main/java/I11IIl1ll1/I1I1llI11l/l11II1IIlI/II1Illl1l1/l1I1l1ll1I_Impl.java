package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.lI1lI11Ill;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import com.ebook.bible.db.entity.ChapterAudioCommonEntity;
import com.ebook.bible.db.entity.ChapterAudioEnEntity;
import com.ebook.bible.db.entity.ChapterAudioEsEntity;
import com.ebook.bible.db.entity.ChapterAudioPtEntity;
import java.io.InterruptedIOException;
import java.io.NotSerializableException;
import java.net.BindException;
import java.net.SocketTimeoutException;
import java.security.KeyException;
import java.security.KeyStoreException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateEncodingException;
import java.util.Collections;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
public final class l1I1l1ll1I_Impl implements l1I1l1ll1I {
    private final RoomDatabase a;
    private final lll1l11l1I<ChapterAudioEnEntity> b;
    private final lll1l11l1I<ChapterAudioEsEntity> c;
    private final lll1l11l1I<ChapterAudioPtEntity> d;

    public l1I1l1ll1I_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new I1lll111Il(this, roomDatabase);
        this.c = new I1II1lIl1I(this, roomDatabase);
        this.d = new lll1lll1l1(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.l1I1l1ll1I
    public long a(ChapterAudioEnEntity chapterAudioEnEntity) throws KeyException, SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{123, 17, 6, 53, 83, 88, 83, 122}), 192515766L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{124, 9, 18, 83, 49, 65, 95, 93, 91, 33, 67, 97, 96, 114}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(chapterAudioEnEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.l1I1l1ll1I
    public long a(ChapterAudioEsEntity chapterAudioEsEntity) throws SocketTimeoutException, UnrecoverableKeyException, NotSerializableException, ReflectiveOperationException, KeyStoreException, BindException {
        if (Il1lII1l1l.Il1IIlI1II(8121)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{110, 1, 85, 61, 83, 83, 0, 116, 110, 11}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.c.insertAndReturnId(chapterAudioEsEntity);
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{118, 61, 86, 38, 40, 70, 70}), 7347)) {
                throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{82, 16, 48, 35, 39}));
            }
            return jInsertAndReturnId;
        } catch (Throwable th) {
            this.a.endTransaction();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.l1I1l1ll1I
    public long a(ChapterAudioPtEntity chapterAudioPtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.d.insertAndReturnId(chapterAudioPtEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.l1I1l1ll1I
    public ChapterAudioCommonEntity a(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        ChapterAudioCommonEntity chapterAudioCommonEntity;
        if (II1I11IlI1.l11I11I11l(I1I1lI1II1.a(new byte[]{122, 55, 15}))) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{66, 82, 37, 20, 83, 123, 86, 0, 85, 3, 124, 101, 70, 9, 70, 109, 12, 43, 47, 71}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 88, 17, 84, 89, 90, 102, 65, 71, 14}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 94, 67, 102, 0, 95, 71, 91, 85, 91, 84, 6}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 83, 95, 78, 10, 92, 95, 84, 93, 107, 69, 3, 21, 10}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            if (cursorQuery.moveToFirst()) {
                ChapterAudioCommonEntity chapterAudioCommonEntity2 = new ChapterAudioCommonEntity();
                if (columnIndex != -1) {
                    chapterAudioCommonEntity2.setBookName(cursorQuery.isNull(columnIndex) ? null : cursorQuery.getString(columnIndex));
                }
                if (columnIndex2 != -1) {
                    chapterAudioCommonEntity2.setChapterNumber(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    chapterAudioCommonEntity2.setChapterAudioUrl(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    chapterAudioCommonEntity2.setAudioIsDownload(cursorQuery.getInt(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    chapterAudioCommonEntity2.setAudioDownloadPath(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    chapterAudioCommonEntity2.setId(cursorQuery.getLong(columnIndex6));
                }
                chapterAudioCommonEntity = chapterAudioCommonEntity2;
            } else {
                chapterAudioCommonEntity = null;
            }
            return chapterAudioCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }

    public static List<Class<?>> a() throws InterruptedIOException {
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (lI1lI11Ill.I1lllI1llI(191738391L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{88, 34, 4, 51, 45, 80, 109, 7, 11, 82, 5, 68, 119, 86, 100, 69, 90, 89, 87, 7, 97, 57, 102, 66, 102, 123, 115, 80}));
        }
        return listEmptyList;
    }
}
