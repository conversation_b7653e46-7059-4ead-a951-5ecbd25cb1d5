package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.llIl1lII1I;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import com.ebook.bible.db.entity.VerseActionsEsEntity;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class ll1ll1lIl1 extends lll1l11l1I<VerseActionsEsEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    ll1ll1lIl1(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VerseActionsEsEntity verseActionsEsEntity) throws NoSuchFieldException {
        if (Il1lII1l1l.I1lllI1llI(193448456L)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{86, 11}));
        }
        a(i1lI1I1lll, verseActionsEsEntity);
        if (llIl1lII1I.IIll1I11lI(I1I1lI1II1.a(new byte[]{124, 92, 44, 52, 27, 2, 98, 83, 95, 32, 85, 95, 93, 104, 114, 87, 38, 36, 55, 2, 85, 37, 69, 5, 69, 120, 112, 49, 42}))) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{120, 62, 56, 49, 18, 88, 121, 3, 67, 6, 7, 74, 13, 82, 80, 102, 24, 17, 43, 85, 84, 57, 101, 71, 106, 103}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        if (l1l1IllI11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{70, 29, 54, 35, 12, Byte.MAX_VALUE, 86, 83, 88, 10, 66, 99, 113, 77, 101, 111, 1, 37, 53, 85, 6, 55, 89, 79}), 4202)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{78, 14, 18, 0, 51, 114, 2, 104, 117, 80, 84, 2, 102, 64, 6, 4, 84, 16, 8, 83}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 86, 0, 67, 13, 13, 11, 17, 106, 82, 67, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 11, 86, 80, 77, 83, 92, 64, 105, 89, 0, 19, 109, 67, 6, 68, 16, 3, 8, 7, 91, 67, 80, 21, 4, 82, 95, 90, 82, 107, 91, 23, 12, 0, 87, 66, 1, 31, 85, 81, 89, 88, 14, 59, 92, 86, 14, 82, 4, 78, 5, 1, 93, 86, 64, 77, 1, 66, 111, 91, 76, 89, 87, 7, 19, 2, 30, 80, 23, 86, 71, 64, 83, 104, 11, 17, 95, 85, 6, 69, 4, 78, 5, 20, 80, 69, 67, 92, 59, 83, 95, 91, 77, 81, 91, 22, 1, 78, 82, 70, 4, 65, 70, 86, 105, 84, 10, 8, 93, 69, 3, 27, 4, 20, 0, 16, 70, 82, 111, 91, 11, 95, 91, 88, 88, 70, 94, 2, 77, 2, 68, 85, 19, 64, 80, 108, 88, 88, 17, 1, 82, 27, 3, 85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85, 1, 31, 85, 81, 89, 88, 14, 59, 92, 88, 23, 82, 59, 15, 10, 6, 92, 81, 73, 102, 16, 89, 93, 80, 89, 24, 85, 0, 14, 13, 89, 111, 9, 90, 82, 91, 90, 94, 2, 12, 70, 104, 14, 88, 0, 11, 3, 27, 106, 67, 89, 84, 1, 80, 25, 21, 111, 117, 121, 55, 36, 49, 18, 24, 15, 70, 89, 95, 95, 81, 77, 91, 30, 23, 83, 30, 72, 93, 73, 93, 25, 8, 28, 6, 72, 15, 28, 10, 21, 11, 25, 93, 77, 93, 30, 15, 77, 12, 25, 12, 31});
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{79, 13, 42, 32, 39, 123, 94, 69, 78, 60, 93, 114, 98, 126, 80, 95, 47, 34, 22, 113, 101, 19, 119, 86, 2, 110, 86, 80}), 219256069L)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{116, 47, 91, 0, 41, 70, Byte.MAX_VALUE, 89, 106, 41, 74, 0, 89}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VerseActionsEsEntity verseActionsEsEntity) {
        i1lI1I1lll.bindLong(1, verseActionsEsEntity.getId());
        i1lI1I1lll.bindLong(2, verseActionsEsEntity.getNewTestament());
        i1lI1I1lll.bindLong(3, verseActionsEsEntity.getBookNumber());
        if (verseActionsEsEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, verseActionsEsEntity.getBookName());
        }
        i1lI1I1lll.bindLong(5, verseActionsEsEntity.getChapterNumber());
        i1lI1I1lll.bindLong(6, verseActionsEsEntity.getVerseNumber());
        if (verseActionsEsEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, verseActionsEsEntity.getVerseContent());
        }
        if (verseActionsEsEntity.getVerseColor() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, verseActionsEsEntity.getVerseColor());
        }
        if (verseActionsEsEntity.getVerseBookMark() == null) {
            i1lI1I1lll.bindNull(9);
        } else {
            i1lI1I1lll.bindString(9, verseActionsEsEntity.getVerseBookMark());
        }
        if (verseActionsEsEntity.getVerseNote() == null) {
            i1lI1I1lll.bindNull(10);
        } else {
            i1lI1I1lll.bindString(10, verseActionsEsEntity.getVerseNote());
        }
        i1lI1I1lll.bindLong(11, verseActionsEsEntity.getBookMarkModifyTime());
        i1lI1I1lll.bindLong(12, verseActionsEsEntity.getNoteModifyTime());
        i1lI1I1lll.bindLong(13, verseActionsEsEntity.getHighlightModifyTime());
    }
}
