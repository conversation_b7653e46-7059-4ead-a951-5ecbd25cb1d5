package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.accounts.utils.IIIlIl1I1l;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.VerseEnEntity;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I1Ill1lIII extends lll1l11l1I<VerseEnEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I1Ill1lIII(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VerseEnEntity verseEnEntity) throws KeyManagementException {
        a(i1lI1I1lll, verseEnEntity);
        if (I1I1IIIIl1.I1lIllll1l(239196580L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{102, 61, 82, 81, 48, 1, 121, 117, 108, 83, 8, 64, 90, 87, 90, 86, 41, 38, 45}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 82, 13, 104, 16, 3, 7, 14, 80, 87, 16, 17, 4, 89, 84, 85, 21, 84, 92, 17, 62, 12, 87, 71, 62, 71, 80, 64, 66, 86, 8, 1, 92, 67, 3, 27, 4, 0, 10, 13, 94, 104, 94, 76, 9, 82, 85, 71, 89, 24, 85, 0, 14, 13, 89, 111, 15, 82, 88, 86, 86, 27, 5, 7, 90, 86, 19, 67, 1, 16, 58, 12, 64, 90, 82, 92, 22, 80, 28, 85, 79, 81, 71, 17, 4, 61, 92, 69, 12, 81, 80, 65, 86, 27, 5, 18, 87, 69, 16, 82, 59, 1, 10, 12, 65, 82, 94, 77, 4, 25, 16, 99, 120, 120, 96, 39, 50, 66, 26, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 27, 91, 78, 90, 78, 10, 27, 15, 21, 91, 28, 15, 28});
        if (IIIlIl1I1l.l11I11I11l(333503964L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{122, 52, 26, 36, 52, 68, 7, 120, 118, 13, 92, 103, 96, 73, 93, 114, 39, 56, 24, 103, 1, 36, 1}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VerseEnEntity verseEnEntity) throws KeyManagementException {
        i1lI1I1lll.bindLong(1, verseEnEntity.getId());
        i1lI1I1lll.bindLong(2, verseEnEntity.getNewTestament());
        i1lI1I1lll.bindLong(3, verseEnEntity.getBookNumber());
        if (verseEnEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, verseEnEntity.getBookName());
        }
        i1lI1I1lll.bindLong(5, verseEnEntity.getChapterNumber());
        i1lI1I1lll.bindLong(6, verseEnEntity.getVerseNumber());
        if (verseEnEntity.getVerseContent() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, verseEnEntity.getVerseContent());
        }
        if (Il11II1llI.IlIllIll1I(1291)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{90, 5, 81, 28, 52, 122, 123, 3, 108}));
        }
    }
}
