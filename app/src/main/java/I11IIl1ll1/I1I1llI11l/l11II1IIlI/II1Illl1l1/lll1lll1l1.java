package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.ChapterAudioPtEntity;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class lll1lll1l1 extends lll1l11l1I<ChapterAudioPtEntity> {
    final /* synthetic */ l1I1l1ll1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lll1lll1l1(l1I1l1ll1I_Impl l1i1l1ll1i_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = l1i1l1ll1i_impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 80, 94, 86, 21, 16, 87, 69, 60, 86, 17, 6, 12, 13, 106, 71, 68, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 0, 93, 95, 10, 108, 91, 82, 91, 82, 5, 72, 82, 84, 11, 86, 20, 22, 0, 16, 106, 89, 69, 84, 6, 85, 66, 85, 21, 84, 86, 10, 0, 18, 70, 85, 19, 108, 84, 70, 82, 94, 10, 59, 71, 69, 15, 87, 72, 2, 4, 23, 81, 94, 95, 102, 13, 67, 111, 81, 86, 67, 91, 14, 14, 3, 86, 80, 77, 83, 84, 70, 82, 94, 10, 59, 86, 88, 20, 89, 8, 13, 4, 6, 106, 71, 81, 77, 12, 80, 28, 85, 80, 80, 85, 75, 65, 52, 115, 124, 52, 118, 102, 19, 30, 8, 73, 91, 30, 8, 79, 8, 72, 93, 73, 12, 64, 91, 92, 80, 2, 24, 15, 25, 25, 4, 28, 75});
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, ChapterAudioPtEntity chapterAudioPtEntity) {
        if (chapterAudioPtEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, chapterAudioPtEntity.getBookName());
        }
        i1lI1I1lll.bindLong(2, chapterAudioPtEntity.getChapterNumber());
        if (chapterAudioPtEntity.getChapterAudioUrl() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, chapterAudioPtEntity.getChapterAudioUrl());
        }
        i1lI1I1lll.bindLong(4, chapterAudioPtEntity.getAudioIsDownload());
        if (chapterAudioPtEntity.getAudioDownloadPath() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, chapterAudioPtEntity.getAudioDownloadPath());
        }
        i1lI1I1lll.bindLong(6, chapterAudioPtEntity.getId());
    }
}
