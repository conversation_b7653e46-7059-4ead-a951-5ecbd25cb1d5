package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.StreamCorruptedException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class lllI111lll implements Callable<List<Integer>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    lllI111lll(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ List<Integer> call() throws Exception {
        if (lIIlI111II.lIll1IIl11(4182)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{14, 32, 37, 52, 91, 79}));
        }
        return a();
    }

    public List<Integer> a() throws Exception {
        if (IIlII1IIIl.l111IIlII1(I1I1lI1II1.a(new byte[]{122}), 211140353L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{114, 28, 81, 18, 8}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                arrayList.add(cursorQuery.isNull(0) ? null : Integer.valueOf(cursorQuery.getInt(0)));
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
