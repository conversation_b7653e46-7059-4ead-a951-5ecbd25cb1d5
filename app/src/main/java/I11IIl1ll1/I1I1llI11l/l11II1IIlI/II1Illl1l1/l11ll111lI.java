package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.interpolator.view.animation.ll1l11I1II;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class l11ll111lI implements Callable<Integer> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    l11ll111lI(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ Integer call() throws Exception {
        if (ll1l11I1II.l11I11I11l(I1I1lI1II1.a(new byte[]{121, 42, 20, 40, 47, 67, 4, 120, 80, 61, 74, 2, 113, 92, 89, 124, 36, 42, 86, 0, 102, 45, 125, Byte.MAX_VALUE}))) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{97, 83, 27, 1, 6, 88, 111, 85, 97, 17, 64, 115, 69, 94, 103, 0, 10, 56, 80, 118, 113, 22, 10, 1, 0, 93, 71, 4, 10, 85}));
        }
        return a();
    }

    public Integer a() throws Exception {
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            return numValueOf;
        } finally {
            cursorQuery.close();
        }
    }
}
