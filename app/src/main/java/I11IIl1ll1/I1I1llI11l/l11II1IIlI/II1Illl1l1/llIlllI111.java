package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class llIlllI111 implements Callable<Integer> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    llIlllI111(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public Integer call() throws Exception {
        Integer numValueOf = null;
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            if (cursorQuery.moveToFirst() && !cursorQuery.isNull(0)) {
                numValueOf = Integer.valueOf(cursorQuery.getInt(0));
            }
            cursorQuery.close();
            if (IllIIIIII1.Il1IIlI1II(8278)) {
                throw new ArithmeticException(I1I1lI1II1.a(new byte[]{71}));
            }
            return numValueOf;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }
}
