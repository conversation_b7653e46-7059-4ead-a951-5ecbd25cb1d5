package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.accounts.utils.lIIIIII11I;
import androidx.core.location.I1111IIl11;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.VerseAnnotationPtEntity;
import java.security.cert.CertPathBuilderException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I1I11l11l1 extends lll1l11l1I<VerseAnnotationPtEntity> {
    final /* synthetic */ lIIllIlIl1_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I1I11l11l1(lIIllIlIl1_Impl liillilil1_impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = liillilil1_impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws CertPathBuilderException, InterruptedException {
        if (I1111IIl11.IlII1Illll(272852754L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{65, 23, 18, 19, 80, 120, 86, 73, 118, 2, 95, 5, 81}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 86, 13, 89, 11, 22, 4, 22, 92, 88, 94, 102, 20, 68, 111, 65, 88, 86, 89, 7, 1, 66, 26, 80, 8, 87, 85, 31, 86, 85, 10, 11, 89, 104, 13, 86, 9, 7, 5, 78, 85, 84, 88, 88, 20, 68, 85, 71, 102, 90, 64, 15, 3, 7, 64, 80, 77, 83, 67, 86, 68, 68, 0, 59, 92, 66, 14, 85, 1, 16, 5, 78, 85, 65, 85, 75, 23, 85, 111, 84, 87, 90, 90, 22, 0, 22, 91, 95, 15, 108, 86, 92, 88, 67, 0, 10, 70, 87, 79, 87, 18, 7, 23, 17, 80, 104, 81, 87, 10, 95, 68, 84, 77, 93, 90, 12, 62, 16, 87, 86, 4, 65, 80, 93, 85, 82, 5, 77, 18, 97, 34, 123, 49, 39, 54, 66, 29, 89, 69, 85, 8, 89, 86, 29, 6, 24, 21, 82, 72, 78, 13, 28, 94, 31, 10, 31, 9, 27, 90, 77});
        if (lIIIIII11I.ll1I1lII11(I1I1lI1II1.a(new byte[]{15, 16, 38, 86, 38, 66, 96, 93, 104, 0, 81, 106, 76, 116, 80, 115, 18, 52, 3, 67, 103, 85, 6}), 286326415L)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{123, 2, 13, 83, 42, 6, 85, 73, 110, 34, 114, 83, 102, 65, 0}));
        }
        return strA;
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, VerseAnnotationPtEntity verseAnnotationPtEntity) {
        i1lI1I1lll.bindLong(1, verseAnnotationPtEntity.getId());
        if (verseAnnotationPtEntity.getBookName() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, verseAnnotationPtEntity.getBookName());
        }
        i1lI1I1lll.bindLong(3, verseAnnotationPtEntity.getChapterNumber());
        i1lI1I1lll.bindLong(4, verseAnnotationPtEntity.getVerseNumber());
        if (verseAnnotationPtEntity.getVerseAnnotationContent() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, verseAnnotationPtEntity.getVerseAnnotationContent());
        }
        if (verseAnnotationPtEntity.getVerseAnnotationReference() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, verseAnnotationPtEntity.getVerseAnnotationReference());
        }
    }
}
