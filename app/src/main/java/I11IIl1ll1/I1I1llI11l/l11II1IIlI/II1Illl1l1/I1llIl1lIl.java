package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.media.content.Il1llIl111;
import android.util.Log;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.WorshipSongsPtEntity;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I1llIl1lIl extends lll1l11l1I<WorshipSongsPtEntity> {
    final /* synthetic */ IlII1llI1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I1llIl1lIl(IlII1llI1I_Impl ilII1llI1I_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = ilII1llI1I_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, WorshipSongsPtEntity worshipSongsPtEntity) throws InterruptedException {
        a(i1lI1I1lll, worshipSongsPtEntity);
        if (IIIlIll111.Il1IIlI1II(3180)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{6, 61, 52, 15, 17, 86, 100, 2}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 68, 89, 69, 22, 12, 91, 71, 60, 68, 11, 12, 2, 17, 106, 71, 68, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 4, 91, 66, 18, 71, 106, 71, 87, 80, 58, 10, 83, 90, 6, 87, 72, 2, 8, 7, 81, 94, 81, 102, 10, 81, 93, 80, 89, 24, 85, 17, 14, 12, 85, 111, 21, 90, 65, 95, 83, 87, 73, 4, 65, 88, 13, 80, 59, 17, 16, 0, 65, 94, 68, 85, 1, 80, 28, 85, 74, 91, 91, 5, 62, 14, 91, 94, 10, 83, 25, 83, 69, 88, 11, 3, 109, 71, 17, 88, 3, 16, 0, 17, 70, 87, 28, 89, 23, 95, 94, 82, 102, 82, 84, 20, 14, 16, 91, 68, 4, 83, 25, 83, 95, 83, 5, 77, 18, 97, 34, 123, 49, 39, 54, 66, 29, 8, 28, 6, 72, 15, 28, 10, 21, 11, 25, 93, 77, 93, 30, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 30});
    }

    public void a(I1lI1I1lll i1lI1I1lll, WorshipSongsPtEntity worshipSongsPtEntity) {
        if (worshipSongsPtEntity.getFirstTagName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, worshipSongsPtEntity.getFirstTagName());
        }
        if (worshipSongsPtEntity.getMediaName() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, worshipSongsPtEntity.getMediaName());
        }
        if (worshipSongsPtEntity.getSongTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, worshipSongsPtEntity.getSongTitle());
        }
        if (worshipSongsPtEntity.getSongSubtitle() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, worshipSongsPtEntity.getSongSubtitle());
        }
        if (worshipSongsPtEntity.getSongLink() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, worshipSongsPtEntity.getSongLink());
        }
        if (worshipSongsPtEntity.getSongProgress() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, worshipSongsPtEntity.getSongProgress());
        }
        i1lI1I1lll.bindLong(7, worshipSongsPtEntity.getSongFavorite());
        i1lI1I1lll.bindLong(8, worshipSongsPtEntity.getId());
        if (Il1llIl111.Il1IIlI1II(I1I1lI1II1.a(new byte[]{7, 47, 49, 84, 13, 118, 85, 73, 117, 9, 69, 123, 126, 117, 78, 126, 17, 13, 3, 3, 89, 18}), 319843233L)) {
            Log.e(I1I1lI1II1.a(new byte[]{5, 32, 32, 47, 43, 69, 89, 102, 106, 39, 74, 103, 123, 74, 102, 3, 43, 44, 48, 2, 81, 6, 123, 82, 91, 6, 98, 17}), I1I1lI1II1.a(new byte[]{125, 18, 19, 54, 32, 77, 91, 104}));
        }
    }
}
