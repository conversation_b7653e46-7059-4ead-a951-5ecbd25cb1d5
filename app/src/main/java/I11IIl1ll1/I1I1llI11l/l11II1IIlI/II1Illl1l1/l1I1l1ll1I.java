package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import com.ebook.bible.db.entity.ChapterAudioCommonEntity;
import com.ebook.bible.db.entity.ChapterAudioEnEntity;
import com.ebook.bible.db.entity.ChapterAudioEsEntity;
import com.ebook.bible.db.entity.ChapterAudioPtEntity;
import kotlin.Metadata;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

@Metadata(d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u00020\u0001J\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0005\u0010\u0006J\u0017\u0010\u0005\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\u0007H'¢\u0006\u0004\b\u0005\u0010\tJ\u0017\u0010\u0005\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\nH'¢\u0006\u0004\b\u0005\u0010\u000bJ\u0017\u0010\u0005\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\fH'¢\u0006\u0004\b\u0005\u0010\r"}, d2 = {"LI11IIl1ll1/I1I1llI11l/l11II1IIlI/II1Illl1l1/l1I1l1ll1I;", "", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;", "p0", "Lcom/ebook/bible/db/entity/ChapterAudioCommonEntity;", "a", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Lcom/ebook/bible/db/entity/ChapterAudioCommonEntity;", "Lcom/ebook/bible/db/entity/l11ll111lI;", "", "(Lcom/ebook/bible/db/entity/l11ll111lI;)J", "Lcom/ebook/bible/db/entity/lIIllIIlll;", "(Lcom/ebook/bible/db/entity/lIIllIIlll;)J", "Lcom/ebook/bible/db/entity/l1l11llIl1;", "(Lcom/ebook/bible/db/entity/l1l11llIl1;)J"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface l1I1l1ll1I {
    long a(ChapterAudioEnEntity p0);

    long a(ChapterAudioPtEntity p0);

    long a(ChapterAudioEsEntity p0);

    ChapterAudioCommonEntity a(IlIlIIll11 p0);
}
