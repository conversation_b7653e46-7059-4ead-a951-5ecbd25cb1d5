package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.lll1l11l1I;
import androidx.room.lllIII11lI;
import com.ebook.bible.db.entity.VerseInspirationEntity;
import java.io.InterruptedIOException;
import java.io.NotSerializableException;
import java.io.SyncFailedException;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
public final class IIlllll1Il_Impl implements IIlllll1Il {
    private final RoomDatabase a;
    private final lll1l11l1I<VerseInspirationEntity> b;
    private final lllIII11lI c;

    public IIlllll1Il_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new ll1I1l1l1l(this, roomDatabase);
        this.c = new IIIl11Illl(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIlllll1Il
    public long a(VerseInspirationEntity verseInspirationEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(verseInspirationEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIlllll1Il
    public void a(int i, String str) throws SyncFailedException, NotSerializableException, CertPathValidatorException, ReflectiveOperationException, KeyStoreException, InterruptedIOException {
        this.a.assertNotSuspendingTransaction();
        I1lI1I1lll i1lI1I1lllAcquire = this.c.acquire();
        if (str == null) {
            i1lI1I1lllAcquire.bindNull(1);
        } else {
            i1lI1I1lllAcquire.bindString(1, str);
        }
        i1lI1I1lllAcquire.bindLong(2, i);
        this.a.beginTransaction();
        try {
            i1lI1I1lllAcquire.executeUpdateDelete();
            this.a.setTransactionSuccessful();
        } finally {
            this.a.endTransaction();
            this.c.release(i1lI1I1lllAcquire);
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.IIlllll1Il
    public List<VerseInspirationEntity> a(int i) throws NotSerializableException, KeyManagementException, CertificateEncodingException {
        I1I1lI1II1.a(new byte[]{100, 33, 46, 32, 33, 97, 23, 26, 25, 34, 98, Byte.MAX_VALUE, 120, 25, 66, 80, 16, 18, 7, 109, 89, 15, 64, 69, 90, 68, 86, 17, 13, 93, 89, 60, 67, 5, 0, 9, 7, 21, 96, 120, 124, 54, 117, 16, 69, 85, 85, 91, 61, 8, 6, 18, 13, 65, 12});
        RoomSQLiteQuery roomSQLiteQueryAcquire = RoomSQLiteQuery.acquire(I1I1lI1II1.a(new byte[]{100, 33, 46, 32, 33, 97, 23, 26, 25, 34, 98, Byte.MAX_VALUE, 120, 25, 66, 80, 16, 18, 7, 109, 89, 15, 64, 69, 90, 68, 86, 17, 13, 93, 89, 60, 67, 5, 0, 9, 7, 21, 96, 120, 124, 54, 117, 16, 69, 85, 85, 91, 61, 8, 6, 18, 13, 65, 12}), 1);
        roomSQLiteQueryAcquire.bindLong(1, i);
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, roomSQLiteQueryAcquire, false, null);
        try {
            int columnIndexOrThrow = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndexOrThrow2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndexOrThrow3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndexOrThrow4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                arrayList.add(new VerseInspirationEntity(cursorQuery.getInt(columnIndexOrThrow), cursorQuery.getInt(columnIndexOrThrow2), cursorQuery.isNull(columnIndexOrThrow3) ? null : cursorQuery.getString(columnIndexOrThrow3), cursorQuery.getLong(columnIndexOrThrow4)));
            }
            return arrayList;
        } finally {
            cursorQuery.close();
            roomSQLiteQueryAcquire.release();
        }
    }

    public static List<Class<?>> a() throws NoSuchFieldException {
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (androidx.interpolator.view.animation.IllllI11lI.l11I11I11l(I1I1lI1II1.a(new byte[]{64, 18, 52, 3, 10, 112, 95, 98, 73, 34, 83, 81, 66}), 248887786L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{122, 54, 58, 10, 37, 67, 0, 84, 75, 18, 8, 1, 111, 75, 1, 99, 51, 35, 6, 6, 105, 81, 6, 90, 92}));
        }
        return listEmptyList;
    }
}
