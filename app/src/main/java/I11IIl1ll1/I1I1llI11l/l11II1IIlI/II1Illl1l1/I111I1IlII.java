package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import android.media.content.lll1IIII11;
import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class I111I1IlII implements Callable<List<WorshipSongsCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ IlII1llI1I_Impl b;

    I111I1IlII(IlII1llI1I_Impl ilII1llI1I_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = ilII1llI1I_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public List<WorshipSongsCommonEntity> call() throws Exception {
        if (androidx.core.location.I1Ill1lIII.I1lIllll1l(lI111IIIII.l1l11I11II.llIllI1l11.lIl1I1111l.I1llIII1Il.TYPE_COLOR)) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{80, 12, 55, 46, 22, 118, 91, 73, 108, 80, 101, 8, 98, 96, 121, 65, 49, 4, 11, 4, 99, 19, 113, 87, 7, 97}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(this.b.a, this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 94, 81, 88, 92}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 89, 81, 84, 1}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 65, 94, 68, 85, 1}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 70, 66, 82, 77, 13, 68, 92, 80}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 89, 94, 94, 82}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 69, 69, 95, 94, 22, 85, 67, 70}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 83, 86, 70, 86, 22, 89, 68, 80}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                WorshipSongsCommonEntity worshipSongsCommonEntity = new WorshipSongsCommonEntity();
                if (columnIndex != -1) {
                    worshipSongsCommonEntity.setFirstTagName(cursorQuery.isNull(columnIndex) ? null : cursorQuery.getString(columnIndex));
                }
                if (columnIndex2 != -1) {
                    worshipSongsCommonEntity.setMediaName(cursorQuery.isNull(columnIndex2) ? null : cursorQuery.getString(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    worshipSongsCommonEntity.setSongTitle(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    worshipSongsCommonEntity.setSongSubtitle(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    worshipSongsCommonEntity.setSongLink(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    worshipSongsCommonEntity.setSongProgress(cursorQuery.isNull(columnIndex6) ? null : cursorQuery.getString(columnIndex6));
                }
                if (columnIndex7 != -1) {
                    worshipSongsCommonEntity.setSongFavorite(cursorQuery.getInt(columnIndex7));
                }
                if (columnIndex8 != -1) {
                    worshipSongsCommonEntity.setId(cursorQuery.getLong(columnIndex8));
                }
                arrayList.add(worshipSongsCommonEntity);
            }
            cursorQuery.close();
            if (lll1IIII11.I11II1I1I1(816782372L)) {
                throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{92, 29, 18, 41, 35, 66, 110, 115, 76, 50, 104, 4, 80, 65, 124, 71, 59, 11, 83, 65, 2, 25, 6, 89, 10, 82, 6}));
            }
            return arrayList;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }
}
