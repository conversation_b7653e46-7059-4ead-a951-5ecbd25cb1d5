package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.interpolator.view.animation.ll1l11I1II;
import com.ebook.bible.db.entity.BookVerseCommonEntity;
import java.security.cert.CertStoreException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class l1lI1lll1I implements Callable<BookVerseCommonEntity> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    l1lI1lll1I(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ BookVerseCommonEntity call() throws Exception {
        BookVerseCommonEntity bookVerseCommonEntityA = a();
        if (android.support.v4.graphics.drawable.lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{114, 43, 17, 84, 81, 87, 2, 121, 12, 53, 119, 104, 115, 125, 7, 84, 21, 18, 13}), 10470)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{114, 3, 1, 10, 81, 126, 124, 114, 123, 20, 87, 103, 96, 10, 112, 114, 43, 6, 27}));
        }
        return bookVerseCommonEntityA;
    }

    public BookVerseCommonEntity a() throws Exception {
        BookVerseCommonEntity bookVerseCommonEntity;
        int i;
        int i2;
        int i3;
        if (ll1l11I1II.I111IlIl1I(I1I1lI1II1.a(new byte[]{94, 0, 8, 34, 85, 97, Byte.MAX_VALUE, 93, 8, 60, 123, 99, 98, 126, 68, 82, 14, 19, 0, 0, 88, 22, 114, 67, 124, 81, 70, 42, 30, 86, Byte.MAX_VALUE}), 184917259L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{67, 1, 12, 48, 51, 103, Byte.MAX_VALUE, 113, 96, 21, 126}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 23, 61, 11, 7, 66, 104, 68, 92, 23, 68, 81, 88, 92, 90, 65}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 87, 16, 85, 94, 65}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 85, 11, 66}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 85, 95, 86, 15, 93, 81, 71, 82}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 95, 77, 1}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 88, 68, 92, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 93, 94, 87, 81, 8, 89, 87, 93, 77, 107, 88, 13, 5, 11, 84, 73, 62, 71, 92, 94, 83}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 2, 13, 92, 68, 4, 93, 65}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 19, 7, 84, 85, 19, 86, 91, 80, 83}));
            if (cursorQuery.moveToFirst()) {
                BookVerseCommonEntity bookVerseCommonEntity2 = new BookVerseCommonEntity();
                int i4 = -1;
                if (columnIndex != -1) {
                    i = columnIndex6;
                    bookVerseCommonEntity2.setId(cursorQuery.getLong(columnIndex));
                    i4 = -1;
                } else {
                    i = columnIndex6;
                }
                if (columnIndex2 != i4) {
                    bookVerseCommonEntity2.setNewTestament(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != i4) {
                    bookVerseCommonEntity2.setBookNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != i4) {
                    bookVerseCommonEntity2.setBookName(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                    i4 = -1;
                }
                if (columnIndex5 != i4) {
                    bookVerseCommonEntity2.setChapterNumber(cursorQuery.getInt(columnIndex5));
                }
                int i5 = i;
                if (i5 != i4) {
                    bookVerseCommonEntity2.setVerseNumber(cursorQuery.getInt(i5));
                }
                if (columnIndex7 != i4) {
                    bookVerseCommonEntity2.setVerseContent(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i4 = -1;
                }
                if (columnIndex8 != i4) {
                    bookVerseCommonEntity2.setVerseColor(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i4 = -1;
                }
                if (columnIndex9 != i4) {
                    bookVerseCommonEntity2.setVerseBookMark(cursorQuery.isNull(columnIndex9) ? null : cursorQuery.getString(columnIndex9));
                    i2 = -1;
                } else {
                    i2 = i4;
                }
                if (columnIndex10 != i2) {
                    bookVerseCommonEntity2.setVerseNote(cursorQuery.isNull(columnIndex10) ? null : cursorQuery.getString(columnIndex10));
                    i2 = -1;
                }
                if (columnIndex11 != i2) {
                    bookVerseCommonEntity2.setBookMarkModifyTime(cursorQuery.getLong(columnIndex11));
                }
                if (columnIndex12 != i2) {
                    bookVerseCommonEntity2.setNoteModifyTime(cursorQuery.getLong(columnIndex12));
                }
                if (columnIndex13 != i2) {
                    bookVerseCommonEntity2.setHighlightModifyTime(cursorQuery.getLong(columnIndex13));
                }
                if (columnIndex14 != i2) {
                    bookVerseCommonEntity2.setVerseAnnotationContent(cursorQuery.isNull(columnIndex14) ? null : cursorQuery.getString(columnIndex14));
                    i3 = columnIndex15;
                    i2 = -1;
                } else {
                    i3 = columnIndex15;
                }
                if (i3 != i2) {
                    bookVerseCommonEntity2.setVerseAnnotationReference(cursorQuery.isNull(i3) ? null : cursorQuery.getString(i3));
                }
                bookVerseCommonEntity = bookVerseCommonEntity2;
            } else {
                bookVerseCommonEntity = null;
            }
            return bookVerseCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }
}
