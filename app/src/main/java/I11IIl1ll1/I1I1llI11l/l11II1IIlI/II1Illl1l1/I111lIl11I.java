package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.PrayerCommonEntity;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class I111lIl11I extends lll1l11l1I<PrayerCommonEntity> {
    final /* synthetic */ IllllI11lI_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I111lIl11I(IllllI11lI_Impl illllI11lI_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = illllI11lI_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, PrayerCommonEntity prayerCommonEntity) throws NoSuchFieldException {
        if (lI1lllIII1.Ill1lIIlIl(287403710L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{66, 32, 7, 3, 91, 121, 91, 66, 109, 33, 97, 115, 99, 81, 92, 114, 47, 52}));
        }
        a(i1lI1I1lll, prayerCommonEntity);
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        if (l11Il1lI11.l1l1l1IIlI(241332264L)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{90, 46, 87, 23, 82, 80, 117}));
        }
        return I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 67, 68, 86, 28, 1, 64, 104, 23, 86, 6, 14, 0, 2, 21, 31, 80, 80, 0, 80, 28, 85, 73, 70, 84, 27, 4, 16, 109, 68, 8, 94, 80, 83, 26, 87, 1, 5, 70, 82, 3, 27, 4, 27, 0, 3, 71, 87, 28, 89, 18, 85, 66, 70, 92, 84, 25, 2, 19, 7, 84, 85, 19, 86, 91, 80, 83, 87, 73, 4, 91, 89, 16, 71, 13, 16, 4, 22, 92, 88, 94, 89, 72, 80, 64, 71, 88, 77, 80, 16, 1, 78, 82, 95, 19, 90, 82, 90, 88, 86, 9, 59, 94, 94, 13, 92, 4, 78, 5, 3, 64, 83, 89, 86, 59, 92, 89, 91, 82, 84, 25, 2, 13, 3, 92, 87, 20, 82, 82, 86, 86, 30, 69, 50, 115, 123, 54, 114, 55, 66, 77, 12, 64, 91, 92, 80, 2, 24, 15, 25, 25, 4, 28, 78, 94, 78, 13, 28, 94, 31, 10, 31, 9, 27, 90, 72, 13, 27, 92, 27, 91, 78, 90, 75});
    }

    public void a(I1lI1I1lll i1lI1I1lll, PrayerCommonEntity prayerCommonEntity) {
        if (l1l1IllI11.l111l1I1Il(I1I1lI1II1.a(new byte[]{14, 85, 23, 7, 36, 109, 98, 118, 87, 1, 86, 120, 12, 114, 102, 92, 18, 59, 45, 4, 99, 85, 69, 80, 3, 114, 123, 17, 42, 10, 93}), 194433254L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{102, 93, 12, 1, 81, 6, 99, 90, 67, 81, 74, 122, 82, 110, 64, 0, 58, 55, 19, 119, 68}));
        }
        i1lI1I1lll.bindLong(1, prayerCommonEntity.getId());
        i1lI1I1lll.bindLong(2, prayerCommonEntity.getPrayerTime());
        if (prayerCommonEntity.getDate() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, prayerCommonEntity.getDate());
        }
        if (prayerCommonEntity.getYear() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, prayerCommonEntity.getYear());
        }
        if (prayerCommonEntity.getVerse() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, prayerCommonEntity.getVerse());
        }
        if (prayerCommonEntity.getReference() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, prayerCommonEntity.getReference());
        }
        if (prayerCommonEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(7);
        } else {
            i1lI1I1lll.bindString(7, prayerCommonEntity.getInspiration());
        }
        if (prayerCommonEntity.getPrayer() == null) {
            i1lI1I1lll.bindNull(8);
        } else {
            i1lI1I1lll.bindString(8, prayerCommonEntity.getPrayer());
        }
        if (prayerCommonEntity.getOriginalLink() == null) {
            i1lI1I1lll.bindNull(9);
        } else {
            i1lI1I1lll.bindString(9, prayerCommonEntity.getOriginalLink());
        }
        if (prayerCommonEntity.getAudioLink() == null) {
            i1lI1I1lll.bindNull(10);
        } else {
            i1lI1I1lll.bindString(10, prayerCommonEntity.getAudioLink());
        }
        if (prayerCommonEntity.getLanguage() == null) {
            i1lI1I1lll.bindNull(11);
        } else {
            i1lI1I1lll.bindString(11, prayerCommonEntity.getLanguage());
        }
    }
}
