package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import com.ebook.bible.db.entity.WorshipSongsEsEntity;
import java.io.InterruptedIOException;
import java.net.MalformedURLException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class IIllIIll1I extends lll1l11l1I<WorshipSongsEsEntity> {
    final /* synthetic */ IlII1llI1I_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIllIIll1I(IlII1llI1I_Impl ilII1llI1I_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = ilII1llI1I_Impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws MalformedURLException, InterruptedIOException {
        if (l1lI1I1l11.llll111lI1(I1I1lI1II1.a(new byte[]{116, 16, 9, 8, 21, 98, 116, 103, Byte.MAX_VALUE, 13, 106, 121, 101, 91, 83}))) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{125, 85, 48, 36, 1, 109}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 68, 89, 69, 22, 12, 91, 71, 60, 68, 11, 12, 2, 17, 106, 82, 67, 102, 16, 81, 82, 89, 92, 84, 21, 74, 1, 4, 91, 66, 18, 71, 106, 71, 87, 80, 58, 10, 83, 90, 6, 87, 72, 2, 8, 7, 81, 94, 81, 102, 10, 81, 93, 80, 89, 24, 85, 17, 14, 12, 85, 111, 21, 90, 65, 95, 83, 87, 73, 4, 65, 88, 13, 80, 59, 17, 16, 0, 65, 94, 68, 85, 1, 80, 28, 85, 74, 91, 91, 5, 62, 14, 91, 94, 10, 83, 25, 83, 69, 88, 11, 3, 109, 71, 17, 88, 3, 16, 0, 17, 70, 87, 28, 89, 23, 95, 94, 82, 102, 82, 84, 20, 14, 16, 91, 68, 4, 83, 25, 83, 95, 83, 5, 77, 18, 97, 34, 123, 49, 39, 54, 66, 29, 8, 28, 6, 72, 15, 28, 10, 21, 11, 25, 93, 77, 93, 30, 94, 20, 95, 89, 90, 80, 31, 90, 72, 18, 7, 74, 30});
        if (lI11IlI1lI.IlII1Illll(228168686L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{115, 21, 33, 12, 20, 123, 64, 70, 96, 2, 101, 8, 90, 0, 112, 113, 6, 10, 87, 126, 93, 5, 92}));
        }
        return strA;
    }

    @Override // androidx.room.lll1l11l1I
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void bind(I1lI1I1lll i1lI1I1lll, WorshipSongsEsEntity worshipSongsEsEntity) {
        if (worshipSongsEsEntity.getFirstTagName() == null) {
            i1lI1I1lll.bindNull(1);
        } else {
            i1lI1I1lll.bindString(1, worshipSongsEsEntity.getFirstTagName());
        }
        if (worshipSongsEsEntity.getMediaName() == null) {
            i1lI1I1lll.bindNull(2);
        } else {
            i1lI1I1lll.bindString(2, worshipSongsEsEntity.getMediaName());
        }
        if (worshipSongsEsEntity.getSongTitle() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, worshipSongsEsEntity.getSongTitle());
        }
        if (worshipSongsEsEntity.getSongSubtitle() == null) {
            i1lI1I1lll.bindNull(4);
        } else {
            i1lI1I1lll.bindString(4, worshipSongsEsEntity.getSongSubtitle());
        }
        if (worshipSongsEsEntity.getSongLink() == null) {
            i1lI1I1lll.bindNull(5);
        } else {
            i1lI1I1lll.bindString(5, worshipSongsEsEntity.getSongLink());
        }
        if (worshipSongsEsEntity.getSongProgress() == null) {
            i1lI1I1lll.bindNull(6);
        } else {
            i1lI1I1lll.bindString(6, worshipSongsEsEntity.getSongProgress());
        }
        i1lI1I1lll.bindLong(7, worshipSongsEsEntity.getSongFavorite());
        i1lI1I1lll.bindLong(8, worshipSongsEsEntity.getId());
    }
}
