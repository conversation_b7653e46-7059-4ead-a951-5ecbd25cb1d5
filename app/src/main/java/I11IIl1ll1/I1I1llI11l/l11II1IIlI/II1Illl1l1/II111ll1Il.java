package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import androidx.room.RoomDatabase;
import androidx.room.lllIII11lI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II111ll1Il extends lllIII11lI {
    final /* synthetic */ II1I111llI_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    II111ll1Il(II1I111llI_Impl iI1I111llI_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iI1I111llI_Impl;
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() {
        I1I1lI1II1.a(new byte[]{98, 52, 38, 36, 54, 112, 23, 84, 88, 13, 92, 73, 106, 73, 88, 84, 12, 62, 7, 92, 111, 21, 82, 87, 95, 83, 23, 54, 33, 102, 23, 19, 91, 5, 12, 58, 11, 88, 80, 111, 76, 22, 92, 16, 8, 25, 11, 21, 53, 41, 39, 96, 117, 65, 67, 89, 82, 88, 104, 12, 0, 18, 10, 67, 8});
        return I1I1lI1II1.a(new byte[]{98, 52, 38, 36, 54, 112, 23, 84, 88, 13, 92, 73, 106, 73, 88, 84, 12, 62, 7, 92, 111, 21, 82, 87, 95, 83, 23, 54, 33, 102, 23, 19, 91, 5, 12, 58, 11, 88, 80, 111, 76, 22, 92, 16, 8, 25, 11, 21, 53, 41, 39, 96, 117, 65, 67, 89, 82, 88, 104, 12, 0, 18, 10, 67, 8});
    }
}
