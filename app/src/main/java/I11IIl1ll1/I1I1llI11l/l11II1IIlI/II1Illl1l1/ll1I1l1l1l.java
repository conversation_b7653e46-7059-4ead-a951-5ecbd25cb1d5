package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.media.content.lIIlI111II;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import com.ebook.bible.db.entity.VerseInspirationEntity;
import java.io.IOException;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
class ll1I1l1l1l extends lll1l11l1I<VerseInspirationEntity> {
    final /* synthetic */ IIlllll1Il_Impl a;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    ll1I1l1l1l(IIlllll1Il_Impl iIlllll1Il_Impl, RoomDatabase roomDatabase) {
        super(roomDatabase);
        this.a = iIlllll1Il_Impl;
    }

    @Override // androidx.room.lll1l11l1I
    public /* synthetic */ void bind(I1lI1I1lll i1lI1I1lll, VerseInspirationEntity verseInspirationEntity) throws UnrecoverableEntryException {
        if (ll1l11I1II.IlII1Illll(201317621L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{69, 30, 7, 33, 91, 91, 96, 69, 9, 8, 124, 83, 121, 76, 71, 120, 37, 8, 21, 90, 71, 53, 125, 122, 66, 64, 65, 42, 2, 92, 1}));
        }
        a(i1lI1I1lll, verseInspirationEntity);
        if (android.support.v4.graphics.drawable.I111lIl11I.IlIIlIllI1(I1I1lI1II1.a(new byte[]{95, 37, 41, 13, 42, 71, 125, 105, 95, 21, 91, 98, 108, 80, 98, 70, 4, 47, 50, 85, 93, 7, 80, 82, 11, 15, 86, 9, 83}), I1I1lI1II1.a(new byte[]{98, 52, 39, 2, 48, 122, 82, 8, 65, 53, 74, 120, 93, 82, 117, 98, 3, 54}))) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{100, 17, 33, 93, 27, 115, 7, 96, 124, 54, 64}));
        }
    }

    @Override // androidx.room.lllIII11lI
    public String createQuery() throws NoSuchMethodException, IOException {
        if (lIIlI111II.l111I1ll1l(2722)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{92, 42, 16, 1, 48, 116, 67, Byte.MAX_VALUE, 123, 16, 121, 93, 108, 124, 115, 111, 24, 84, 37, 83, 1, 54}));
        }
        String strA = I1I1lI1II1.a(new byte[]{126, 42, 49, 32, 48, 97, 23, Byte.MAX_VALUE, 107, 68, 98, 117, 101, 117, 117, 118, 39, 65, 43, 124, 100, 46, 19, 85, 69, 83, 69, 22, 1, 109, 94, 13, 68, 20, 11, 23, 3, 65, 94, 95, 87, 59, 68, 81, 87, 85, 81, 85, 66, 73, 2, 66, 92, 0, 93, 106, 90, 82, 87, 73, 4, 66, 91, 2, 89, 59, 6, 4, 27, 106, 94, 84, 89, 72, 80, 89, 91, 74, 68, 92, 16, 0, 22, 91, 95, 15, 83, 25, 83, 95, 83, 5, 77, 18, 97, 34, 123, 49, 39, 54, 66, 29, 8, 28, 6, 72, 15, 28, 91, 76, 88, 89, 11, 7, 74, 13, 28, 65, 3, 28, 26});
        if (I1IllIll1l.l1Il11I1Il(I1I1lI1II1.a(new byte[]{92, 45, 51, 93, 82, 71, 79, 98, 119, 46, 122, 71, 66, 111, 67, 3, 43}), 309476639L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{91, 87, 16, 16, 47, 68, 92, 106, 77, 21, 90, 97, 90, 106, 94, 123, 32, 81, 45, 121, 116, 43, 87, 89, 120, 80, 91, 35, 5, 93, 4, 7}));
        }
        return strA;
    }

    public void a(I1lI1I1lll i1lI1I1lll, VerseInspirationEntity verseInspirationEntity) {
        i1lI1I1lll.bindLong(1, verseInspirationEntity.getPlanId());
        i1lI1I1lll.bindLong(2, verseInspirationEntity.getPlanDayId());
        if (verseInspirationEntity.getInspiration() == null) {
            i1lI1I1lll.bindNull(3);
        } else {
            i1lI1I1lll.bindString(3, verseInspirationEntity.getInspiration());
        }
        i1lI1I1lll.bindLong(4, verseInspirationEntity.getId());
    }
}
