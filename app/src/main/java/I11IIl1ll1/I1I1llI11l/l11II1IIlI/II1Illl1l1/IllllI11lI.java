package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import com.ebook.bible.db.entity.PrayerCommonEntity;
import java.util.List;
import kotlin.Metadata;
import kotlin.coroutines.d;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J#\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0006\u0010\u0007J\u0017\u0010\u0006\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0006\u0010\tJ\u001b\u0010\u0006\u001a\u00020\n2\u0006\u0010\u0003\u001a\u00020\u0005H§@ø\u0001\u0000¢\u0006\u0004\b\u0006\u0010\u000bJ\u0017\u0010\f\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\f\u0010\t\u0082\u0002\u0004\n\u0002\b\u0019"}, d2 = {"LI11IIl1ll1/I1I1llI11l/l11II1IIlI/II1Illl1l1/IllllI11lI;", "", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;", "p0", "", "Lcom/ebook/bible/db/entity/PrayerCommonEntity;", "a", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;Lkotlin/coroutines/d;)Ljava/lang/Object;", "", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)I", "", "(Lcom/ebook/bible/db/entity/PrayerCommonEntity;Lkotlin/coroutines/d;)Ljava/lang/Object;", "b"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface IllllI11lI {
    int a(IlIlIIll11 p0);

    Object a(PrayerCommonEntity prayerCommonEntity, d<? super Long> dVar);

    Object a(IlIlIIll11 ilIlIIll11, d<? super List<PrayerCommonEntity>> dVar);

    int b(IlIlIIll11 p0);
}
