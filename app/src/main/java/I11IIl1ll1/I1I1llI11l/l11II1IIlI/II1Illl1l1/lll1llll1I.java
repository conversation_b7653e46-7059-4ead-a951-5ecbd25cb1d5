package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import java.io.StreamCorruptedException;
import java.security.cert.CertificateExpiredException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class lll1llll1I implements Callable<WorshipSongsCommonEntity> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ IlII1llI1I_Impl b;

    lll1llll1I(IlII1llI1I_Impl ilII1llI1I_Impl, IlIlIIll11 ilIlIIll11) {
        this.b = ilII1llI1I_Impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ WorshipSongsCommonEntity call() throws Exception {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{15, 22, 80, 48, 10, 12, 116, 65, 78, 30, 104, 66, 7, 12, 123, 12, 18, 3, 43, 80, 123, 41, 4, 124, 90, 120, 4, 93, 61, 100}), 161006129L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{122, 6, 47, 36, 3}));
        }
        return a();
    }

    public WorshipSongsCommonEntity a() throws Exception {
        WorshipSongsCommonEntity worshipSongsCommonEntity;
        if (llIlII1IlI.l111l1I1Il(I1I1lI1II1.a(new byte[]{121, 41, 86, 48, 53, 116, 90, 87, 8, 29, 98, 8, 64}), 1319984134L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{99, 8, 33, 21, 40, 101, 3, 65, 65, 42, 70, 4}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(this.b.a, this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 94, 81, 88, 92}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 89, 81, 84, 1}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 65, 94, 68, 85, 1}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 70, 66, 82, 77, 13, 68, 92, 80}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 89, 94, 94, 82}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 69, 69, 95, 94, 22, 85, 67, 70}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 11, 12, 2, 61, 83, 86, 70, 86, 22, 89, 68, 80}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            if (cursorQuery.moveToFirst()) {
                WorshipSongsCommonEntity worshipSongsCommonEntity2 = new WorshipSongsCommonEntity();
                if (columnIndex != -1) {
                    worshipSongsCommonEntity2.setFirstTagName(cursorQuery.isNull(columnIndex) ? null : cursorQuery.getString(columnIndex));
                }
                if (columnIndex2 != -1) {
                    worshipSongsCommonEntity2.setMediaName(cursorQuery.isNull(columnIndex2) ? null : cursorQuery.getString(columnIndex2));
                }
                if (columnIndex3 != -1) {
                    worshipSongsCommonEntity2.setSongTitle(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                }
                if (columnIndex4 != -1) {
                    worshipSongsCommonEntity2.setSongSubtitle(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                }
                if (columnIndex5 != -1) {
                    worshipSongsCommonEntity2.setSongLink(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                }
                if (columnIndex6 != -1) {
                    worshipSongsCommonEntity2.setSongProgress(cursorQuery.isNull(columnIndex6) ? null : cursorQuery.getString(columnIndex6));
                }
                if (columnIndex7 != -1) {
                    worshipSongsCommonEntity2.setSongFavorite(cursorQuery.getInt(columnIndex7));
                }
                if (columnIndex8 != -1) {
                    worshipSongsCommonEntity2.setId(cursorQuery.getLong(columnIndex8));
                }
                worshipSongsCommonEntity = worshipSongsCommonEntity2;
            } else {
                worshipSongsCommonEntity = null;
            }
            return worshipSongsCommonEntity;
        } finally {
            cursorQuery.close();
        }
    }
}
