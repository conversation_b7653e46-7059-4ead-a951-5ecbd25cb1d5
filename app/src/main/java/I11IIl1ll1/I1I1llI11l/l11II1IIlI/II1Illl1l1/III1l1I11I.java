package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.database.Cursor;
import androidx.core.location.lIIlI111II;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import com.ebook.bible.db.entity.BookVerseCommonEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
class III1l1I11I implements Callable<List<BookVerseCommonEntity>> {
    final /* synthetic */ IlIlIIll11 a;
    final /* synthetic */ lIIllIlIl1_Impl b;

    III1l1I11I(lIIllIlIl1_Impl liillilil1_impl, IlIlIIll11 ilIlIIll11) {
        this.b = liillilil1_impl;
        this.a = ilIlIIll11;
    }

    @Override // java.util.concurrent.Callable
    public /* synthetic */ List<BookVerseCommonEntity> call() throws Exception {
        List<BookVerseCommonEntity> listA = a();
        if (l1lI1I1l11.II1111I11I(I1I1lI1II1.a(new byte[]{97, 9, 39, 82, 4}), I1I1lI1II1.a(new byte[]{85, 92, 5, 85, 40, 4, 88, 126, 73, 55, 121, 124, 81, 123, 125, 80, 45}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{111, 43, 91, 15, 16, 12, 78, 122, 13, 15, 89, 93, 98, 125, 0, 101, 41, 38}));
        }
        return listA;
    }

    public List<BookVerseCommonEntity> a() throws Exception {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        if (lIIlI111II.llllI1l1II(1185)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{125, 19, 6, 55}));
        }
        Cursor cursorQuery = Il1IIlI1lI.query(lIIllIlIl1_Impl.a(this.b), this.a, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 23, 61, 11, 7, 66, 104, 68, 92, 23, 68, 81, 88, 92, 90, 65}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 87, 16, 85, 94, 65}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 84, 95, 85, 11, 66}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 85, 95, 86, 15, 93, 81, 71, 82}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 95, 77, 1}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 88, 86, 66, 82, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 88, 68, 92, 59, 93, 95, 81, 80, 82, 76, 61, 21, 11, 95, 85}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 93, 94, 87, 81, 8, 89, 87, 93, 77, 107, 88, 13, 5, 11, 84, 73, 62, 71, 92, 94, 83}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 2, 13, 92, 68, 4, 93, 65}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 86, 94, 87, 11, 68, 81, 65, 80, 91, 91, 61, 19, 7, 84, 85, 19, 86, 91, 80, 83}));
            int i8 = columnIndex14;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                BookVerseCommonEntity bookVerseCommonEntity = new BookVerseCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i9 = -1;
                if (columnIndex != -1) {
                    i = columnIndex9;
                    bookVerseCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i9 = -1;
                } else {
                    i = columnIndex9;
                }
                if (columnIndex2 != i9) {
                    bookVerseCommonEntity.setNewTestament(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != i9) {
                    bookVerseCommonEntity.setBookNumber(cursorQuery.getInt(columnIndex3));
                }
                if (columnIndex4 != i9) {
                    bookVerseCommonEntity.setBookName(cursorQuery.isNull(columnIndex4) ? null : cursorQuery.getString(columnIndex4));
                    i9 = -1;
                }
                if (columnIndex5 != i9) {
                    bookVerseCommonEntity.setChapterNumber(cursorQuery.getInt(columnIndex5));
                }
                if (columnIndex6 != i9) {
                    bookVerseCommonEntity.setVerseNumber(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != i9) {
                    bookVerseCommonEntity.setVerseContent(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i9 = -1;
                }
                if (columnIndex8 != i9) {
                    bookVerseCommonEntity.setVerseColor(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i3 = i;
                    i2 = -1;
                } else {
                    i2 = i9;
                    i3 = i;
                }
                if (i3 != i2) {
                    bookVerseCommonEntity.setVerseBookMark(cursorQuery.isNull(i3) ? null : cursorQuery.getString(i3));
                    i2 = -1;
                }
                if (columnIndex10 != i2) {
                    bookVerseCommonEntity.setVerseNote(cursorQuery.isNull(columnIndex10) ? null : cursorQuery.getString(columnIndex10));
                    i2 = -1;
                }
                if (columnIndex11 != i2) {
                    i4 = columnIndex8;
                    i5 = columnIndex2;
                    bookVerseCommonEntity.setBookMarkModifyTime(cursorQuery.getLong(columnIndex11));
                } else {
                    i4 = columnIndex8;
                    i5 = columnIndex2;
                }
                if (columnIndex12 != i2) {
                    bookVerseCommonEntity.setNoteModifyTime(cursorQuery.getLong(columnIndex12));
                }
                if (columnIndex13 != i2) {
                    bookVerseCommonEntity.setHighlightModifyTime(cursorQuery.getLong(columnIndex13));
                }
                int i10 = i8;
                if (i10 != i2) {
                    bookVerseCommonEntity.setVerseAnnotationContent(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    i7 = columnIndex15;
                    i6 = -1;
                } else {
                    i6 = i2;
                    i7 = columnIndex15;
                }
                if (i7 != i6) {
                    bookVerseCommonEntity.setVerseAnnotationReference(cursorQuery.isNull(i7) ? null : cursorQuery.getString(i7));
                }
                arrayList2.add(bookVerseCommonEntity);
                columnIndex15 = i7;
                i8 = i10;
                columnIndex8 = i4;
                columnIndex9 = i3;
                arrayList = arrayList2;
                columnIndex2 = i5;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }
}
