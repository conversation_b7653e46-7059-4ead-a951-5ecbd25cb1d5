package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.accounts.utils.Ill11ll111;
import android.database.Cursor;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.room.CoroutinesRoom;
import androidx.room.RoomDatabase;
import androidx.room.lll1l11l1I;
import androidx.room.lllIII11lI;
import com.ebook.bible.db.entity.DailyPlanCommonEntity;
import com.ebook.bible.db.entity.DailyPlanEnEntity;
import com.ebook.bible.db.entity.DailyPlanEsEntity;
import com.ebook.bible.db.entity.DailyPlanPtEntity;
import java.io.EOFException;
import java.io.InterruptedIOException;
import java.io.NotSerializableException;
import java.io.SyncFailedException;
import java.net.BindException;
import java.net.PortUnreachableException;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.ProviderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import kotlin.coroutines.d;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

/* loaded from: classes.dex */
public final class II1I111llI_Impl implements II1I111llI {
    private final RoomDatabase a;
    private final lll1l11l1I<DailyPlanEnEntity> b;
    private final lll1l11l1I<DailyPlanEsEntity> c;
    private final lll1l11l1I<DailyPlanPtEntity> d;
    private final lllIII11lI e;

    public II1I111llI_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new II11lI1111(this, roomDatabase);
        this.c = new l11l1IIl1I(this, roomDatabase);
        this.d = new l11lIlllII(this, roomDatabase);
        this.e = new II111ll1Il(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public long a(DailyPlanEnEntity dailyPlanEnEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        if (IlIIlI11I1.IlII1Illll(242030428L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{123, 11, 50, 51, 13, 121, 117, 106, 81, 8, 93, 9, 99, 113}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(dailyPlanEnEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public long a(DailyPlanEsEntity dailyPlanEsEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.c.insertAndReturnId(dailyPlanEsEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public long a(DailyPlanPtEntity dailyPlanPtEntity) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException {
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.d.insertAndReturnId(dailyPlanPtEntity);
            this.a.setTransactionSuccessful();
            return jInsertAndReturnId;
        } finally {
            this.a.endTransaction();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public int a(int i, String str) throws SyncFailedException, NotSerializableException, CertPathValidatorException, ReflectiveOperationException, KeyStoreException, BindException, InterruptedIOException {
        this.a.assertNotSuspendingTransaction();
        I1lI1I1lll i1lI1I1lllAcquire = this.e.acquire();
        if (str == null) {
            i1lI1I1lllAcquire.bindNull(1);
        } else {
            i1lI1I1lllAcquire.bindString(1, str);
        }
        i1lI1I1lllAcquire.bindLong(2, i);
        this.a.beginTransaction();
        try {
            int iExecuteUpdateDelete = i1lI1I1lllAcquire.executeUpdateDelete();
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            this.e.release(i1lI1I1lllAcquire);
            if (lIIlI111II.llIIIl11I1(6730)) {
                throw new BindException(I1I1lI1II1.a(new byte[]{2, 23, 37, 38, 20, 116, 69, 87, 116, 49, 68, 115, 123, 111, 82, 120, 26, 44, 36, 4, 4, 40, 80}));
            }
            return iExecuteUpdateDelete;
        } catch (Throwable th) {
            this.a.endTransaction();
            this.e.release(i1lI1I1lllAcquire);
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public Object a(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws PortUnreachableException, EOFException {
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l1l1lI1lI1(this, ilIlIIll11), dVar);
        if (I111I11Ill.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{118, 39, 84, 44}), 177697874L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{77, 49, 27, 63, 7, 90, 81}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public Object b(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar) throws SyncFailedException, EOFException {
        if (android.support.v4.graphics.drawable.lIIlI111II.IIll1l1lII(5605)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{2, 15, 26, 40, 42, 102, 69, 117, 93, 16, 124, 83, 100, 0, 77, 116, 84, 32, 43, 113, 66, 32, 102, 108}));
        }
        return CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new l1llI1Il1l(this, ilIlIIll11), dVar);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public int a(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        if (Ill11ll111.I1lllI1llI(170556148L)) {
            Log.w(I1I1lI1II1.a(new byte[]{4, 81, 1, 49, 27, 89, 91, 89, 124, 17, 98, 83, 109}), I1I1lI1II1.a(new byte[]{82, 17, 5, 55, 49, 115, 77}));
            return 0;
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    /* JADX WARN: Finally extract failed */
    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public List<DailyPlanCommonEntity> b(IlIlIIll11 ilIlIIll11) throws NotSerializableException, KeyManagementException, CertificateEncodingException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 89, 84}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 68, 89, 65, 85, 81}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 89, 81}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 68, 92, 77, 88, 80}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 91, 86, 93, 92}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 90, 87, 102, 17, 66, 92}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 7, 95, 69, 91, 77}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 10, 69, 93, 87, 92, 70}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 23, 68, 81, 65, 76, 71}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 71, 66, 86, 3, 66, 85, 70, 74}));
            int columnIndex16 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
            int columnIndex17 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
            int columnIndex18 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndex19 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
            int i17 = columnIndex18;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                DailyPlanCommonEntity dailyPlanCommonEntity = new DailyPlanCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i18 = -1;
                if (columnIndex != -1) {
                    i = columnIndex3;
                    dailyPlanCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i18 = -1;
                } else {
                    i = columnIndex3;
                }
                if (columnIndex2 != i18) {
                    dailyPlanCommonEntity.setFirstTagId(cursorQuery.getInt(columnIndex2));
                }
                int i19 = i;
                if (i19 != i18) {
                    dailyPlanCommonEntity.setFirstTagTitle(cursorQuery.isNull(i19) ? null : cursorQuery.getString(i19));
                    i18 = -1;
                }
                if (columnIndex4 != i18) {
                    dailyPlanCommonEntity.setSecondTagId(cursorQuery.getInt(columnIndex4));
                    i18 = -1;
                }
                if (columnIndex5 != i18) {
                    dailyPlanCommonEntity.setSecondTagTitle(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                    i18 = -1;
                }
                if (columnIndex6 != i18) {
                    dailyPlanCommonEntity.setPlanId(cursorQuery.getInt(columnIndex6));
                    i18 = -1;
                }
                if (columnIndex7 != i18) {
                    dailyPlanCommonEntity.setPlanName(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i18 = -1;
                }
                if (columnIndex8 != i18) {
                    dailyPlanCommonEntity.setPlanImgUrl(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i18 = -1;
                }
                if (columnIndex9 != i18) {
                    dailyPlanCommonEntity.setPlanDayId(cursorQuery.getInt(columnIndex9));
                    i18 = -1;
                }
                if (columnIndex10 != i18) {
                    dailyPlanCommonEntity.setPlanDayCount(cursorQuery.getInt(columnIndex10));
                    i18 = -1;
                }
                if (columnIndex11 != i18) {
                    dailyPlanCommonEntity.setPlanDayNumber(cursorQuery.getInt(columnIndex11));
                    i18 = -1;
                }
                if (columnIndex12 != i18) {
                    dailyPlanCommonEntity.setPlanDayStatus(cursorQuery.getInt(columnIndex12));
                    i18 = -1;
                }
                if (columnIndex13 != i18) {
                    i2 = i19;
                    dailyPlanCommonEntity.setDateCompleted(cursorQuery.getLong(columnIndex13));
                    i4 = columnIndex14;
                    i3 = -1;
                } else {
                    i2 = i19;
                    i3 = i18;
                    i4 = columnIndex14;
                }
                if (i4 != i3) {
                    dailyPlanCommonEntity.setAudioLink(cursorQuery.isNull(i4) ? null : cursorQuery.getString(i4));
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = -1;
                } else {
                    int i20 = i3;
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = i20;
                }
                if (i5 != i7) {
                    dailyPlanCommonEntity.setAudioProgress(cursorQuery.isNull(i5) ? null : cursorQuery.getString(i5));
                    i8 = columnIndex13;
                    i10 = columnIndex16;
                    i9 = -1;
                } else {
                    i8 = columnIndex13;
                    i9 = i7;
                    i10 = columnIndex16;
                }
                if (i10 != i9) {
                    dailyPlanCommonEntity.setVerseContent(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    columnIndex16 = i10;
                    i12 = columnIndex17;
                    i11 = -1;
                } else {
                    columnIndex16 = i10;
                    i11 = i9;
                    i12 = columnIndex17;
                }
                if (i12 != i11) {
                    dailyPlanCommonEntity.setReference(cursorQuery.isNull(i12) ? null : cursorQuery.getString(i12));
                    columnIndex17 = i12;
                    i14 = i17;
                    i13 = -1;
                } else {
                    columnIndex17 = i12;
                    i13 = i11;
                    i14 = i17;
                }
                if (i14 != i13) {
                    dailyPlanCommonEntity.setInspiration(cursorQuery.isNull(i14) ? null : cursorQuery.getString(i14));
                    i17 = i14;
                    i16 = columnIndex19;
                    i15 = -1;
                } else {
                    i17 = i14;
                    i15 = i13;
                    i16 = columnIndex19;
                }
                if (i16 != i15) {
                    dailyPlanCommonEntity.setDate(cursorQuery.isNull(i16) ? null : cursorQuery.getString(i16));
                }
                arrayList2.add(dailyPlanCommonEntity);
                columnIndex19 = i16;
                columnIndex13 = i8;
                columnIndex14 = i4;
                arrayList = arrayList2;
                columnIndex10 = i6;
                columnIndex15 = i5;
                columnIndex3 = i2;
            }
            ArrayList arrayList3 = arrayList;
            cursorQuery.close();
            if (I11II1l1lI.llll111lI1(I1I1lI1II1.a(new byte[]{97, 2, 48, 10, 54, Byte.MAX_VALUE, 121, 3, 110, 80, 1, 93, 102, 81, 110, 87, 54}), 167787115L)) {
                throw new KeyManagementException(I1I1lI1II1.a(new byte[]{64, 6, 18, 44, 9, 119, 69, 120}));
            }
            return arrayList3;
        } catch (Throwable th) {
            cursorQuery.close();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public List<DailyPlanCommonEntity> c(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        if (IIll1llI1l.Il1IIlI1II(8039)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{3, 9, 84, 50, 81, 82, 110, 99, 123, 38, 74, 89, 122, 120, 125, 0, 32, 3, 13, 122, 87, 46, 66, 90, 125, 103, 67, 33, 11, 124, 91}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 89, 84}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 68, 89, 65, 85, 81}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 89, 81}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 68, 92, 77, 88, 80}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 91, 86, 93, 92}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 90, 87, 102, 17, 66, 92}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 7, 95, 69, 91, 77}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 10, 69, 93, 87, 92, 70}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 23, 68, 81, 65, 76, 71}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 71, 66, 86, 3, 66, 85, 70, 74}));
            int columnIndex16 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
            int columnIndex17 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
            int columnIndex18 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndex19 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
            int i17 = columnIndex18;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                DailyPlanCommonEntity dailyPlanCommonEntity = new DailyPlanCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i18 = -1;
                if (columnIndex != -1) {
                    i = columnIndex3;
                    dailyPlanCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i18 = -1;
                } else {
                    i = columnIndex3;
                }
                if (columnIndex2 != i18) {
                    dailyPlanCommonEntity.setFirstTagId(cursorQuery.getInt(columnIndex2));
                }
                int i19 = i;
                if (i19 != i18) {
                    dailyPlanCommonEntity.setFirstTagTitle(cursorQuery.isNull(i19) ? null : cursorQuery.getString(i19));
                    i18 = -1;
                }
                if (columnIndex4 != i18) {
                    dailyPlanCommonEntity.setSecondTagId(cursorQuery.getInt(columnIndex4));
                    i18 = -1;
                }
                if (columnIndex5 != i18) {
                    dailyPlanCommonEntity.setSecondTagTitle(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                    i18 = -1;
                }
                if (columnIndex6 != i18) {
                    dailyPlanCommonEntity.setPlanId(cursorQuery.getInt(columnIndex6));
                    i18 = -1;
                }
                if (columnIndex7 != i18) {
                    dailyPlanCommonEntity.setPlanName(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i18 = -1;
                }
                if (columnIndex8 != i18) {
                    dailyPlanCommonEntity.setPlanImgUrl(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i18 = -1;
                }
                if (columnIndex9 != i18) {
                    dailyPlanCommonEntity.setPlanDayId(cursorQuery.getInt(columnIndex9));
                    i18 = -1;
                }
                if (columnIndex10 != i18) {
                    dailyPlanCommonEntity.setPlanDayCount(cursorQuery.getInt(columnIndex10));
                    i18 = -1;
                }
                if (columnIndex11 != i18) {
                    dailyPlanCommonEntity.setPlanDayNumber(cursorQuery.getInt(columnIndex11));
                    i18 = -1;
                }
                if (columnIndex12 != i18) {
                    dailyPlanCommonEntity.setPlanDayStatus(cursorQuery.getInt(columnIndex12));
                    i18 = -1;
                }
                if (columnIndex13 != i18) {
                    i2 = i19;
                    dailyPlanCommonEntity.setDateCompleted(cursorQuery.getLong(columnIndex13));
                    i4 = columnIndex14;
                    i3 = -1;
                } else {
                    i2 = i19;
                    i3 = i18;
                    i4 = columnIndex14;
                }
                if (i4 != i3) {
                    dailyPlanCommonEntity.setAudioLink(cursorQuery.isNull(i4) ? null : cursorQuery.getString(i4));
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = -1;
                } else {
                    int i20 = i3;
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = i20;
                }
                if (i5 != i7) {
                    dailyPlanCommonEntity.setAudioProgress(cursorQuery.isNull(i5) ? null : cursorQuery.getString(i5));
                    i8 = columnIndex13;
                    i10 = columnIndex16;
                    i9 = -1;
                } else {
                    i8 = columnIndex13;
                    i9 = i7;
                    i10 = columnIndex16;
                }
                if (i10 != i9) {
                    dailyPlanCommonEntity.setVerseContent(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    columnIndex16 = i10;
                    i12 = columnIndex17;
                    i11 = -1;
                } else {
                    columnIndex16 = i10;
                    i11 = i9;
                    i12 = columnIndex17;
                }
                if (i12 != i11) {
                    dailyPlanCommonEntity.setReference(cursorQuery.isNull(i12) ? null : cursorQuery.getString(i12));
                    columnIndex17 = i12;
                    i14 = i17;
                    i13 = -1;
                } else {
                    columnIndex17 = i12;
                    i13 = i11;
                    i14 = i17;
                }
                if (i14 != i13) {
                    dailyPlanCommonEntity.setInspiration(cursorQuery.isNull(i14) ? null : cursorQuery.getString(i14));
                    i17 = i14;
                    i16 = columnIndex19;
                    i15 = -1;
                } else {
                    i17 = i14;
                    i15 = i13;
                    i16 = columnIndex19;
                }
                if (i16 != i15) {
                    dailyPlanCommonEntity.setDate(cursorQuery.isNull(i16) ? null : cursorQuery.getString(i16));
                }
                arrayList2.add(dailyPlanCommonEntity);
                columnIndex19 = i16;
                columnIndex13 = i8;
                columnIndex14 = i4;
                arrayList = arrayList2;
                columnIndex10 = i6;
                columnIndex15 = i5;
                columnIndex3 = i2;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public Object c(IlIlIIll11 ilIlIIll11, d<? super List<DailyPlanCommonEntity>> dVar) throws EOFException {
        Object objExecute = CoroutinesRoom.execute(this.a, false, Il1IIlI1lI.createCancellationSignal(), new Il11IlIllI(this, ilIlIIll11), dVar);
        if (android.support.v4.graphics.drawable.lIIlI111II.l111I1ll1l(3576)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{90, 6, 40, 53, 44}));
        }
        return objExecute;
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public List<DailyPlanCommonEntity> d(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 89, 84}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 68, 89, 65, 85, 81}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 89, 81}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 68, 92, 77, 88, 80}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 91, 86, 93, 92}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 90, 87, 102, 17, 66, 92}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 7, 95, 69, 91, 77}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 10, 69, 93, 87, 92, 70}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 23, 68, 81, 65, 76, 71}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 71, 66, 86, 3, 66, 85, 70, 74}));
            int columnIndex16 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
            int columnIndex17 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
            int columnIndex18 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndex19 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
            int i17 = columnIndex18;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                DailyPlanCommonEntity dailyPlanCommonEntity = new DailyPlanCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i18 = -1;
                if (columnIndex != -1) {
                    i = columnIndex3;
                    dailyPlanCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i18 = -1;
                } else {
                    i = columnIndex3;
                }
                if (columnIndex2 != i18) {
                    dailyPlanCommonEntity.setFirstTagId(cursorQuery.getInt(columnIndex2));
                }
                int i19 = i;
                if (i19 != i18) {
                    dailyPlanCommonEntity.setFirstTagTitle(cursorQuery.isNull(i19) ? null : cursorQuery.getString(i19));
                    i18 = -1;
                }
                if (columnIndex4 != i18) {
                    dailyPlanCommonEntity.setSecondTagId(cursorQuery.getInt(columnIndex4));
                    i18 = -1;
                }
                if (columnIndex5 != i18) {
                    dailyPlanCommonEntity.setSecondTagTitle(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                    i18 = -1;
                }
                if (columnIndex6 != i18) {
                    dailyPlanCommonEntity.setPlanId(cursorQuery.getInt(columnIndex6));
                    i18 = -1;
                }
                if (columnIndex7 != i18) {
                    dailyPlanCommonEntity.setPlanName(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i18 = -1;
                }
                if (columnIndex8 != i18) {
                    dailyPlanCommonEntity.setPlanImgUrl(cursorQuery.isNull(columnIndex8) ? null : cursorQuery.getString(columnIndex8));
                    i18 = -1;
                }
                if (columnIndex9 != i18) {
                    dailyPlanCommonEntity.setPlanDayId(cursorQuery.getInt(columnIndex9));
                    i18 = -1;
                }
                if (columnIndex10 != i18) {
                    dailyPlanCommonEntity.setPlanDayCount(cursorQuery.getInt(columnIndex10));
                    i18 = -1;
                }
                if (columnIndex11 != i18) {
                    dailyPlanCommonEntity.setPlanDayNumber(cursorQuery.getInt(columnIndex11));
                    i18 = -1;
                }
                if (columnIndex12 != i18) {
                    dailyPlanCommonEntity.setPlanDayStatus(cursorQuery.getInt(columnIndex12));
                    i18 = -1;
                }
                if (columnIndex13 != i18) {
                    i2 = i19;
                    dailyPlanCommonEntity.setDateCompleted(cursorQuery.getLong(columnIndex13));
                    i4 = columnIndex14;
                    i3 = -1;
                } else {
                    i2 = i19;
                    i3 = i18;
                    i4 = columnIndex14;
                }
                if (i4 != i3) {
                    dailyPlanCommonEntity.setAudioLink(cursorQuery.isNull(i4) ? null : cursorQuery.getString(i4));
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = -1;
                } else {
                    int i20 = i3;
                    i5 = columnIndex15;
                    i6 = columnIndex10;
                    i7 = i20;
                }
                if (i5 != i7) {
                    dailyPlanCommonEntity.setAudioProgress(cursorQuery.isNull(i5) ? null : cursorQuery.getString(i5));
                    i8 = columnIndex13;
                    i10 = columnIndex16;
                    i9 = -1;
                } else {
                    i8 = columnIndex13;
                    i9 = i7;
                    i10 = columnIndex16;
                }
                if (i10 != i9) {
                    dailyPlanCommonEntity.setVerseContent(cursorQuery.isNull(i10) ? null : cursorQuery.getString(i10));
                    columnIndex16 = i10;
                    i12 = columnIndex17;
                    i11 = -1;
                } else {
                    columnIndex16 = i10;
                    i11 = i9;
                    i12 = columnIndex17;
                }
                if (i12 != i11) {
                    dailyPlanCommonEntity.setReference(cursorQuery.isNull(i12) ? null : cursorQuery.getString(i12));
                    columnIndex17 = i12;
                    i14 = i17;
                    i13 = -1;
                } else {
                    columnIndex17 = i12;
                    i13 = i11;
                    i14 = i17;
                }
                if (i14 != i13) {
                    dailyPlanCommonEntity.setInspiration(cursorQuery.isNull(i14) ? null : cursorQuery.getString(i14));
                    i17 = i14;
                    i16 = columnIndex19;
                    i15 = -1;
                } else {
                    i17 = i14;
                    i15 = i13;
                    i16 = columnIndex19;
                }
                if (i16 != i15) {
                    dailyPlanCommonEntity.setDate(cursorQuery.isNull(i16) ? null : cursorQuery.getString(i16));
                }
                arrayList2.add(dailyPlanCommonEntity);
                columnIndex19 = i16;
                columnIndex13 = i8;
                columnIndex14 = i4;
                arrayList = arrayList2;
                columnIndex10 = i6;
                columnIndex15 = i5;
                columnIndex3 = i2;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public int e(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            return cursorQuery.moveToFirst() ? cursorQuery.getInt(0) : 0;
        } finally {
            cursorQuery.close();
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.II1I111llI
    public List<DailyPlanCommonEntity> f(IlIlIIll11 ilIlIIll11) throws NotSerializableException, CertificateEncodingException {
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        if (androidx.recyclerview.widget.content.adapter.lIIlI111II.Il11IIIlI1(9397)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{110, 3, 85, 9, 84, 101, 114, 67, 86, 92, 97, 116, 125, 73, 100, 118, 24, 7, 37, 113, 65, 7}));
        }
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, ilIlIIll11, false, null);
        try {
            int columnIndex = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            int columnIndex2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 89, 84}));
            int columnIndex3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{81, 13, 16, 22, 22, 106, 67, 81, 94, 59, 68, 89, 65, 85, 81}));
            int columnIndex4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 89, 81}));
            int columnIndex5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{68, 1, 1, 10, 12, 81, 104, 68, 88, 3, 111, 68, 92, 77, 88, 80}));
            int columnIndex6 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 83}));
            int columnIndex7 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 91, 86, 93, 92}));
            int columnIndex8 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 92, 90, 87, 102, 17, 66, 92}));
            int columnIndex9 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 13, 84}));
            int columnIndex10 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 7, 95, 69, 91, 77}));
            int columnIndex11 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 10, 69, 93, 87, 92, 70}));
            int columnIndex12 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{71, 8, 3, 11, 61, 81, 86, 73, 102, 23, 68, 81, 65, 76, 71}));
            int columnIndex13 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0, 61, 86, 88, 93, 73, 8, 85, 68, 80, 93}));
            int columnIndex14 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 91, 89, 87, 15}));
            int columnIndex15 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{86, 17, 6, 12, 13, 106, 71, 66, 86, 3, 66, 85, 70, 74}));
            int columnIndex16 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{70, 17, 13, 17, 7}));
            int columnIndex17 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{69, 1, 4, 0, 16, 80, 89, 83, 92}));
            int columnIndex18 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{94, 10, 17, 21, 11, 71, 86, 68, 80, 11, 94}));
            int columnIndex19 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndex(cursorQuery, I1I1lI1II1.a(new byte[]{83, 5, 22, 0}));
            int i16 = columnIndex18;
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                DailyPlanCommonEntity dailyPlanCommonEntity = new DailyPlanCommonEntity();
                ArrayList arrayList2 = arrayList;
                int i17 = -1;
                if (columnIndex != -1) {
                    i = columnIndex8;
                    dailyPlanCommonEntity.setId(cursorQuery.getLong(columnIndex));
                    i17 = -1;
                } else {
                    i = columnIndex8;
                }
                if (columnIndex2 != i17) {
                    dailyPlanCommonEntity.setFirstTagId(cursorQuery.getInt(columnIndex2));
                }
                if (columnIndex3 != i17) {
                    dailyPlanCommonEntity.setFirstTagTitle(cursorQuery.isNull(columnIndex3) ? null : cursorQuery.getString(columnIndex3));
                    i17 = -1;
                }
                if (columnIndex4 != i17) {
                    dailyPlanCommonEntity.setSecondTagId(cursorQuery.getInt(columnIndex4));
                }
                if (columnIndex5 != i17) {
                    dailyPlanCommonEntity.setSecondTagTitle(cursorQuery.isNull(columnIndex5) ? null : cursorQuery.getString(columnIndex5));
                    i17 = -1;
                }
                if (columnIndex6 != i17) {
                    dailyPlanCommonEntity.setPlanId(cursorQuery.getInt(columnIndex6));
                }
                if (columnIndex7 != i17) {
                    dailyPlanCommonEntity.setPlanName(cursorQuery.isNull(columnIndex7) ? null : cursorQuery.getString(columnIndex7));
                    i3 = i;
                    i2 = -1;
                } else {
                    i2 = i17;
                    i3 = i;
                }
                if (i3 != i2) {
                    dailyPlanCommonEntity.setPlanImgUrl(cursorQuery.isNull(i3) ? null : cursorQuery.getString(i3));
                    i2 = -1;
                }
                if (columnIndex9 != i2) {
                    dailyPlanCommonEntity.setPlanDayId(cursorQuery.getInt(columnIndex9));
                    i2 = -1;
                }
                if (columnIndex10 != i2) {
                    dailyPlanCommonEntity.setPlanDayCount(cursorQuery.getInt(columnIndex10));
                    i2 = -1;
                }
                if (columnIndex11 != i2) {
                    dailyPlanCommonEntity.setPlanDayNumber(cursorQuery.getInt(columnIndex11));
                    i2 = -1;
                }
                if (columnIndex12 != i2) {
                    dailyPlanCommonEntity.setPlanDayStatus(cursorQuery.getInt(columnIndex12));
                    i2 = -1;
                }
                if (columnIndex13 != i2) {
                    i4 = columnIndex4;
                    i5 = i3;
                    dailyPlanCommonEntity.setDateCompleted(cursorQuery.getLong(columnIndex13));
                } else {
                    i4 = columnIndex4;
                    i5 = i3;
                }
                int i18 = columnIndex14;
                if (i18 != i2) {
                    dailyPlanCommonEntity.setAudioLink(cursorQuery.isNull(i18) ? null : cursorQuery.getString(i18));
                    i6 = columnIndex15;
                    i2 = -1;
                } else {
                    i6 = columnIndex15;
                }
                if (i6 != i2) {
                    dailyPlanCommonEntity.setAudioProgress(cursorQuery.isNull(i6) ? null : cursorQuery.getString(i6));
                    i7 = columnIndex11;
                    i9 = columnIndex16;
                    i8 = -1;
                } else {
                    i7 = columnIndex11;
                    i8 = i2;
                    i9 = columnIndex16;
                }
                if (i9 != i8) {
                    dailyPlanCommonEntity.setVerseContent(cursorQuery.isNull(i9) ? null : cursorQuery.getString(i9));
                    columnIndex14 = i18;
                    i11 = columnIndex17;
                    i10 = -1;
                } else {
                    columnIndex14 = i18;
                    i10 = i8;
                    i11 = columnIndex17;
                }
                if (i11 != i10) {
                    dailyPlanCommonEntity.setReference(cursorQuery.isNull(i11) ? null : cursorQuery.getString(i11));
                    columnIndex17 = i11;
                    i13 = i16;
                    i12 = -1;
                } else {
                    columnIndex17 = i11;
                    i12 = i10;
                    i13 = i16;
                }
                if (i13 != i12) {
                    dailyPlanCommonEntity.setInspiration(cursorQuery.isNull(i13) ? null : cursorQuery.getString(i13));
                    i16 = i13;
                    i15 = columnIndex19;
                    i14 = -1;
                } else {
                    i16 = i13;
                    i14 = i12;
                    i15 = columnIndex19;
                }
                if (i15 != i14) {
                    dailyPlanCommonEntity.setDate(cursorQuery.isNull(i15) ? null : cursorQuery.getString(i15));
                }
                arrayList2.add(dailyPlanCommonEntity);
                columnIndex19 = i15;
                columnIndex16 = i9;
                columnIndex8 = i5;
                columnIndex11 = i7;
                columnIndex15 = i6;
                arrayList = arrayList2;
                columnIndex4 = i4;
            }
            return arrayList;
        } finally {
            cursorQuery.close();
        }
    }

    public static List<Class<?>> a() {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{126, 2, 91, 0, 42, 2, 96, 86, 107, 22, 116, 116, 1, 108, 94, 125, 1, 44, 22, 102, 122, 39, 11, 123, 122, 112, 91, 0}), 497141732L)) {
            Log.d(I1I1lI1II1.a(new byte[]{109, 41, 10, 48, 12}), I1I1lI1II1.a(new byte[]{91, 13, 80, 60, 87, 100, 96, 101, 96, 37, 116, 120, 96, 115, 91, 123, 80, 51}));
            return null;
        }
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (IIll1llI1l.Il1IIlI1II(3639)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{125, 13, 91, 87, 49, 89, 113, 71, 76, 83, 126, 68, 125, 64, 70, 65, 59, 37, 87, 90, 5, 18, 93, 88, 124, 119}));
        }
        return listEmptyList;
    }
}
