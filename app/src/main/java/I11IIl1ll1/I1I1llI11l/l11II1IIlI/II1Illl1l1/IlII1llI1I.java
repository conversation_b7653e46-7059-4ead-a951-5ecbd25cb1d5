package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import com.ebook.bible.db.entity.WorshipSongsCommonEntity;
import com.ebook.bible.db.entity.WorshipSongsEnEntity;
import com.ebook.bible.db.entity.WorshipSongsEsEntity;
import com.ebook.bible.db.entity.WorshipSongsPtEntity;
import java.util.List;
import kotlin.Metadata;
import kotlin.coroutines.d;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;

@Metadata(d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0005\u0010\u0006J#\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00072\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0005\u0010\tJ\u001d\u0010\n\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\n\u0010\tJ\u0017\u0010\u0005\u001a\u00020\f2\u0006\u0010\u0003\u001a\u00020\u000bH'¢\u0006\u0004\b\u0005\u0010\rJ\u0017\u0010\u0005\u001a\u00020\f2\u0006\u0010\u0003\u001a\u00020\u000eH'¢\u0006\u0004\b\u0005\u0010\u000fJ\u0017\u0010\u0005\u001a\u00020\f2\u0006\u0010\u0003\u001a\u00020\u0010H'¢\u0006\u0004\b\u0005\u0010\u0011J\u0019\u0010\n\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\n\u0010\u0006J\u0017\u0010\u0012\u001a\u00020\b2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0012\u0010\u0013J\u0017\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0014\u0010\u0015\u0082\u0002\u0004\n\u0002\b\u0019"}, d2 = {"LI11IIl1ll1/I1I1llI11l/l11II1IIlI/II1Illl1l1/IlII1llI1I;", "", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;", "p0", "", "a", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Ljava/lang/Integer;", "", "Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;Lkotlin/coroutines/d;)Ljava/lang/Object;", "b", "Lcom/ebook/bible/db/entity/lI1lI11Ill;", "", "(Lcom/ebook/bible/db/entity/lI1lI11Ill;)J", "Lcom/ebook/bible/db/entity/IIl1I11lII;", "(Lcom/ebook/bible/db/entity/IIl1I11lII;)J", "Lcom/ebook/bible/db/entity/II11l1lIl1;", "(Lcom/ebook/bible/db/entity/II11l1lIl1;)J", "c", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Lcom/ebook/bible/db/entity/WorshipSongsCommonEntity;", "d", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)I"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface IlII1llI1I {
    long a(WorshipSongsPtEntity p0);

    long a(WorshipSongsEsEntity p0);

    long a(WorshipSongsEnEntity p0);

    Integer a(IlIlIIll11 p0);

    Object a(IlIlIIll11 ilIlIIll11, d<? super List<WorshipSongsCommonEntity>> dVar);

    Integer b(IlIlIIll11 p0);

    Object b(IlIlIIll11 ilIlIIll11, d<? super WorshipSongsCommonEntity> dVar);

    WorshipSongsCommonEntity c(IlIlIIll11 p0);

    int d(IlIlIIll11 p0);
}
