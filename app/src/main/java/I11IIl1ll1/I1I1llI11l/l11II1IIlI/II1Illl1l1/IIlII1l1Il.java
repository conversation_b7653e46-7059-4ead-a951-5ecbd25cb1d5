package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.constraintlayout.widget.Il1lII1l1l;
import com.ebook.bible.db.entity.PrayerCommonEntity;
import java.io.InvalidObjectException;
import java.util.concurrent.Callable;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIlII1l1Il implements Callable<Long> {
    final /* synthetic */ PrayerCommonEntity a;
    final /* synthetic */ IllllI11lI_Impl b;

    IIlII1l1Il(IllllI11lI_Impl illllI11lI_Impl, PrayerCommonEntity prayerCommonEntity) {
        this.b = illllI11lI_Impl;
        this.a = prayerCommonEntity;
    }

    @Override // java.util.concurrent.Callable
    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public Long call() throws Exception {
        if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{97, 13, 16, 23, 81, 81, 90, 97, 15, 35}), 603330660L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{126, 53, 47, 54, 85, 65, 94, 103, 105, 62, 5, 106, 79, 118, 71, Byte.MAX_VALUE, 13}));
        }
        IllllI11lI_Impl.a(this.b).beginTransaction();
        try {
            long jInsertAndReturnId = IllllI11lI_Impl.b(this.b).insertAndReturnId(this.a);
            IllllI11lI_Impl.a(this.b).setTransactionSuccessful();
            Long lValueOf = Long.valueOf(jInsertAndReturnId);
            IllllI11lI_Impl.a(this.b).endTransaction();
            if (Il1lII1l1l.l11I11I11l(741620465L)) {
                throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 13, 87, 38, 15, 82, 89, 84, 15, 39, 72, 90, 67, 87}));
            }
            return lValueOf;
        } catch (Throwable th) {
            IllllI11lI_Impl.a(this.b).endTransaction();
            throw th;
        }
    }
}
