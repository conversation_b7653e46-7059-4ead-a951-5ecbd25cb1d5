package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import com.ebook.bible.db.entity.BookChapterNumberEntity;
import com.ebook.bible.db.entity.BookVerseCommonEntity;
import com.ebook.bible.db.entity.VerseActionsCommonEntity;
import com.ebook.bible.db.entity.VerseActionsEnEntity;
import com.ebook.bible.db.entity.VerseActionsEsEntity;
import com.ebook.bible.db.entity.VerseActionsPtEntity;
import com.ebook.bible.db.entity.VerseAnnotationCommonEntity;
import com.ebook.bible.db.entity.VerseAnnotationEnEntity;
import com.ebook.bible.db.entity.VerseAnnotationEsEntity;
import com.ebook.bible.db.entity.VerseAnnotationPtEntity;
import com.ebook.bible.db.entity.VerseEnEntity;
import com.ebook.bible.db.entity.VerseEsEntity;
import com.ebook.bible.db.entity.VersePtEntity;
import java.util.List;
import kotlin.Metadata;
import kotlin.coroutines.d;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIlllI1;

@Metadata(d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\bg\u0018\u00002\u00020\u0001J\u001f\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0006\u0010\u0007J#\u0010\b\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\b\u0010\tJ#\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\n\u0010\tJ#\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u000b\u0010\tJ#\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\f\u0010\tJ#\u0010\r\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\r\u0010\tJ#\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u000e\u0010\tJ\u0017\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u000e\u0010\u0010J#\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0011\u0010\tJ\u0019\u0010\u0006\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0003\u001a\u00020\u0012H'¢\u0006\u0004\b\u0006\u0010\u0014J\u0019\u0010\u000e\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0003\u001a\u00020\u0012H'¢\u0006\u0004\b\u000e\u0010\u0014J\u0019\u0010\r\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\r\u0010\u0016J\u0019\u0010\u0018\u001a\u0004\u0018\u00010\u00172\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0018\u0010\u0019J\u0017\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H'¢\u0006\u0004\b\u0011\u0010\u0010J\u001d\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0006\u0010\tJ\u001d\u0010\u0018\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b\u0018\u0010\tJ\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020\u001aH'¢\u0006\u0004\b\u0006\u0010\u001cJ\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020\u001dH'¢\u0006\u0004\b\u0006\u0010\u001eJ\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020\u001fH'¢\u0006\u0004\b\u0006\u0010 J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020!H'¢\u0006\u0004\b\u0006\u0010\"J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020#H'¢\u0006\u0004\b\u0006\u0010$J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020%H'¢\u0006\u0004\b\u0006\u0010&J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020'H'¢\u0006\u0004\b\u0006\u0010(J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020)H'¢\u0006\u0004\b\u0006\u0010*J\u0017\u0010\u0006\u001a\u00020\u001b2\u0006\u0010\u0003\u001a\u00020+H'¢\u0006\u0004\b\u0006\u0010,J\u001b\u0010-\u001a\u00020\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b-\u0010\tJ\u001b\u0010.\u001a\u00020\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b.\u0010\tJ\u001b\u0010/\u001a\u00020\u000f2\u0006\u0010\u0003\u001a\u00020\u0002H§@ø\u0001\u0000¢\u0006\u0004\b/\u0010\t\u0082\u0002\u0004\n\u0002\b\u0019"}, d2 = {"LI11IIl1ll1/I1I1llI11l/l11II1IIlI/II1Illl1l1/lIIllIlIl1;", "", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;", "p0", "", "Lcom/ebook/bible/db/entity/BookVerseCommonEntity;", "a", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Ljava/util/List;", "h", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;Lkotlin/coroutines/d;)Ljava/lang/Object;", "i", "f", "g", "e", "b", "", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)I", "c", "LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIlllI1;", "Lcom/ebook/bible/db/entity/BookChapterNumberEntity;", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIlllI1;)Lcom/ebook/bible/db/entity/BookChapterNumberEntity;", "Lcom/ebook/bible/db/entity/VerseAnnotationCommonEntity;", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Lcom/ebook/bible/db/entity/VerseAnnotationCommonEntity;", "Lcom/ebook/bible/db/entity/VerseActionsCommonEntity;", "d", "(LlIIlIlIlI1/ll1Ill11l1/I1IlllI1lI/III11II1II/IlIlIIll11;)Lcom/ebook/bible/db/entity/VerseActionsCommonEntity;", "Lcom/ebook/bible/db/entity/l1I11l11ll;", "", "(Lcom/ebook/bible/db/entity/l1I11l11ll;)J", "Lcom/ebook/bible/db/entity/II111ll1Il;", "(Lcom/ebook/bible/db/entity/II111ll1Il;)J", "Lcom/ebook/bible/db/entity/Ill1I111I1;", "(Lcom/ebook/bible/db/entity/Ill1I111I1;)J", "Lcom/ebook/bible/db/entity/l1l1II1111;", "(Lcom/ebook/bible/db/entity/l1l1II1111;)J", "Lcom/ebook/bible/db/entity/I11I11II1I;", "(Lcom/ebook/bible/db/entity/I11I11II1I;)J", "Lcom/ebook/bible/db/entity/llIl1I11II;", "(Lcom/ebook/bible/db/entity/llIl1I11II;)J", "Lcom/ebook/bible/db/entity/lIIlllI1Il;", "(Lcom/ebook/bible/db/entity/lIIlllI1Il;)J", "Lcom/ebook/bible/db/entity/l11I11I11l;", "(Lcom/ebook/bible/db/entity/l11I11I11l;)J", "Lcom/ebook/bible/db/entity/IlI1l1Il1l;", "(Lcom/ebook/bible/db/entity/IlI1l1Il1l;)J", "k", "l", "j"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: classes.dex */
public interface lIIllIlIl1 {
    long a(VerseAnnotationEsEntity p0);

    long a(VerseActionsEsEntity p0);

    long a(VersePtEntity p0);

    long a(VerseActionsPtEntity p0);

    long a(VerseEsEntity p0);

    long a(VerseActionsEnEntity p0);

    long a(VerseAnnotationEnEntity p0);

    long a(VerseEnEntity p0);

    long a(VerseAnnotationPtEntity p0);

    BookChapterNumberEntity a(IlIlIlllI1 p0);

    Object a(IlIlIIll11 ilIlIIll11, d<? super BookVerseCommonEntity> dVar);

    List<BookVerseCommonEntity> a(IlIlIIll11 p0);

    int b(IlIlIIll11 p0);

    BookChapterNumberEntity b(IlIlIlllI1 p0);

    Object b(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    int c(IlIlIIll11 p0);

    Object c(IlIlIIll11 ilIlIIll11, d<? super List<Integer>> dVar);

    VerseActionsCommonEntity d(IlIlIIll11 p0);

    Object d(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);

    VerseAnnotationCommonEntity e(IlIlIIll11 p0);

    Object e(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    Object f(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    Object g(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    Object h(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    Object i(IlIlIIll11 ilIlIIll11, d<? super List<BookVerseCommonEntity>> dVar);

    Object j(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);

    Object k(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);

    Object l(IlIlIIll11 ilIlIIll11, d<? super Integer> dVar);
}
