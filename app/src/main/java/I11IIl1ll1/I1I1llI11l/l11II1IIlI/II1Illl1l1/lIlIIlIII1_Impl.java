package I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1;

import Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.Il1IIlI1lI;
import android.accounts.utils.I1lllI11II;
import android.database.Cursor;
import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.lll1l11l1I;
import androidx.room.lllIII11lI;
import com.ebook.bible.db.entity.l1lllll11l;
import java.io.IOException;
import java.io.InvalidObjectException;
import java.io.NotSerializableException;
import java.net.SocketTimeoutException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateExpiredException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.I1lI1I1lll;

/* loaded from: classes.dex */
public final class lIlIIlIII1_Impl implements lIlIIlIII1 {
    private final RoomDatabase a;
    private final lll1l11l1I<l1lllll11l> b;
    private final lllIII11lI c;

    public lIlIIlIII1_Impl(RoomDatabase roomDatabase) {
        this.a = roomDatabase;
        this.b = new l111llllI1(this, roomDatabase);
        this.c = new IllllI11Il(this, roomDatabase);
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIlIIlIII1
    public long a(l1lllll11l l1lllll11lVar) throws SocketTimeoutException, NotSerializableException, ReflectiveOperationException, KeyStoreException, CertificateException {
        if (I1lllI11II.llII1lIIlI(I1I1lI1II1.a(new byte[]{82, 33, 81, 12, 56, 2, 14, 6, 9, 29, 98, 2, 70, 97, 66, 122, 44, 49, 13, 2, 68, 21, 64, 92, 103}))) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{115, 48, 86, 33, 33, 91, 103, 120, 86, 54, 117, 9, 101, 72, 118, 13, 85, 38, 12, 69, 94, 11}));
        }
        this.a.assertNotSuspendingTransaction();
        this.a.beginTransaction();
        try {
            long jInsertAndReturnId = this.b.insertAndReturnId(l1lllll11lVar);
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            if (IIIlIll111.IlII1Illll(721519048L)) {
                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{71, 54, 42, 29, 52, 95, 84, 71, 117, 62, 72, 5, 98, 78, 88, 98, 39, 24, 43, 10, 65, 10, 95, 64, 102, 90, 123, 1, 92, 11, 14}));
            }
            return jInsertAndReturnId;
        } catch (Throwable th) {
            this.a.endTransaction();
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIlIIlIII1
    public void a() throws IOException, CertPathValidatorException, ReflectiveOperationException, KeyStoreException {
        if (android.media.content.lIIllIlIl1.I1lIllll1l(574525468L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{97, 92, 54, 81, 36}));
        }
        this.a.assertNotSuspendingTransaction();
        I1lI1I1lll i1lI1I1lllAcquire = this.c.acquire();
        this.a.beginTransaction();
        try {
            i1lI1I1lllAcquire.executeUpdateDelete();
            this.a.setTransactionSuccessful();
            this.a.endTransaction();
            this.c.release(i1lI1I1lllAcquire);
            if (lIIlI111II.IIlI1Il1lI(1129)) {
                throw new SecurityException(I1I1lI1II1.a(new byte[]{0, 60, 35, 20, 56, 114, 101, 121, 125, 44, 120, 120, 83, 112, 96, 71, 56, 40}));
            }
        } catch (Throwable th) {
            this.a.endTransaction();
            this.c.release(i1lI1I1lllAcquire);
            throw th;
        }
    }

    @Override // I11IIl1ll1.I1I1llI11l.l11II1IIlI.II1Illl1l1.lIlIIlIII1
    public List<l1lllll11l> b() throws NotSerializableException, KeyManagementException, CertificateEncodingException {
        I1I1lI1II1.a(new byte[]{100, 33, 46, 32, 33, 97, 23, 26, 25, 34, 98, Byte.MAX_VALUE, 120, 25, 86, 90, 13, 10, 61, 81, 88, 14, 92, 70, 86, 105, 95, 12, 23, 70, 88, 17, 78});
        RoomSQLiteQuery roomSQLiteQueryAcquire = RoomSQLiteQuery.acquire(I1I1lI1II1.a(new byte[]{100, 33, 46, 32, 33, 97, 23, 26, 25, 34, 98, Byte.MAX_VALUE, 120, 25, 86, 90, 13, 10, 61, 81, 88, 14, 92, 70, 86, 105, 95, 12, 23, 70, 88, 17, 78}), 0);
        this.a.assertNotSuspendingTransaction();
        Cursor cursorQuery = Il1IIlI1lI.query(this.a, roomSQLiteQueryAcquire, false, null);
        try {
            int columnIndexOrThrow = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 66, 93, 91, 1, 66}));
            int columnIndexOrThrow2 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{85, 11, 13, 14, 61, 91, 86, 93, 92}));
            int columnIndexOrThrow3 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{84, 12, 3, 21, 22, 80, 69, 111, 87, 17, 93, 82, 80, 75}));
            int columnIndexOrThrow4 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{65, 1, 16, 22, 7, 106, 89, 69, 84, 6, 85, 66}));
            int columnIndexOrThrow5 = Il111III1l.Il111IlI1I.I1lIl1lIlI.IIIIIII1Il.lIllI1lIlI.getColumnIndexOrThrow(cursorQuery, I1I1lI1II1.a(new byte[]{94, 0}));
            ArrayList arrayList = new ArrayList(cursorQuery.getCount());
            while (cursorQuery.moveToNext()) {
                arrayList.add(new l1lllll11l(cursorQuery.getInt(columnIndexOrThrow), cursorQuery.isNull(columnIndexOrThrow2) ? null : cursorQuery.getString(columnIndexOrThrow2), cursorQuery.getInt(columnIndexOrThrow3), cursorQuery.getInt(columnIndexOrThrow4), cursorQuery.getLong(columnIndexOrThrow5)));
            }
            return arrayList;
        } finally {
            cursorQuery.close();
            roomSQLiteQueryAcquire.release();
        }
    }

    public static List<Class<?>> c() throws InvalidObjectException {
        List<Class<?>> listEmptyList = Collections.emptyList();
        if (l1l1IllI11.IlII1Illll(305920056L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{15, 14, 13, 43, 91, 90, 110, Byte.MAX_VALUE, 74}));
        }
        return listEmptyList;
    }
}
