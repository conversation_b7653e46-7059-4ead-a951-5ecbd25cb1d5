package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.llIllIlll1;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.core.location.lI1lI11Ill;
import java.security.GeneralSecurityException;
import java.security.NoSuchProviderException;
import java.security.SignatureException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;
import llIII1lllI.I1Il1l1I1I.l1Il11l1Il.Il11lI1Ill.IIl11IIIlI;

/* loaded from: classes.dex */
class l1lll111ll extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 a = null;
    private /* synthetic */ l11Il111ll b;

    l1lll111ll(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l11Il111ll l11il111ll) {
        this.b = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchFieldException {
        super.b(l11il111ll, l111l1i1il);
        List<IIl11IIIlI> list = (List) l11il111ll.b(llIllIlll1.class);
        l11il111ll.a(llIllIlll1.class);
        HashMap map = new HashMap();
        for (IIl11IIIlI iIl11IIIlI : list) {
            String accessKey = iIl11IIIlI.getAccessKey();
            List arrayList = (List) map.get(accessKey);
            if (arrayList == null) {
                arrayList = new ArrayList();
            }
            arrayList.add(iIl11IIIlI);
            map.put(accessKey, arrayList);
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
            if (lIIllIlIl1.Il1IIlI1II(235319969L)) {
                throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{78, 19, 22, 41, 39, 121, 114, 2, 76, 19, 105, 100, 71, 82}));
            }
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchProviderException {
        super.a(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException {
        if (Il1IIllIll.llII1lIIlI(I1I1lI1II1.a(new byte[]{125, 20, 39, 87, 85, 1, 82, 69, 118, 20, 5, 73}))) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{117, 34, 17, 28, 9, 125, 88, 113, 9, 34, 9, 114, 101, 13, 124, 76, 13, 85, 49, 67, 105, 81}));
        }
        super.a(l11il111ll, l111l1i1il, th);
        l11il111ll.a(llIllIlll1.class);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
            if (lIIlI111II.llIIIl11I1(6556)) {
                throw new CertificateException(I1I1lI1II1.a(new byte[]{124, 8, 91, 41, 17, 95, 67, 84, 86, 17, 81, 88, 123, 84, 114, 97, 42, 82, 90, 93, 8, 15, 73, 108, 65, 113, 78, 36, 10, 100, 83}));
            }
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws SignatureException {
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
        } catch (Throwable unused) {
            if (lI1lI11Ill.IlIllIll1I(I1I1lI1II1.a(new byte[]{96}), 196279621L)) {
                throw new SignatureException(I1I1lI1II1.a(new byte[]{91, 1, 13, 23, 38, 94, 116, 114}));
            }
        }
    }
}
