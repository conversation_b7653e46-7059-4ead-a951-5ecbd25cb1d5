package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.I11lI11III;
import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.I11lII1Il1;
import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.II11I1l11I;
import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.III1Il1II1;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.IllIIll11l;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l1ll11l111;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.lIIl1I11II;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.llIIII1IlI;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.llIllIlll1;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.File;
import java.lang.reflect.Field;
import java.net.BindException;
import java.net.UnknownServiceException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.l1lllIll1I;
import llIII1lllI.I1Il1l1I1I.l1Il11l1Il.Il11lI1Ill.lll1llll1I;

/* loaded from: classes.dex */
public final class lIlIII1111 {
    private final Field a;

    public static l11Il111ll<Object> a(File file, l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll l11il111ll, llll1IllIl.IllIII1lIl.Il1IIIlI1l.lIlIII1111 liliii1111, Map<String, List<lll1llll1I>> map, String str) throws BindException {
        ArrayList arrayList = new ArrayList();
        arrayList.add(I11lI11III.a().a(IllIIll11l.class).a(file, l11il111ll.d()).a(liliii1111.a(IllIIll11l.class)).b());
        arrayList.add(I11lI11III.a().a(llIllIlll1.class).a(l11il111ll, null, map, null, str).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(new l1lll111ll(null, l11il111ll), liliii1111.a(llIllIlll1.class))).b());
        arrayList.add(I11lI11III.a().a(llIIII1IlI.class).a(l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll.g()).a(new l1lll111II(null, l11il111ll)).b());
        II11I1l11I iI11I1l11I = new II11I1l11I();
        I11lII1Il1 i11lII1Il1A = iI11I1l11I.a(I1I1lI1II1.a(new byte[]{85, 22, 3, 11, 1, 93, 104, 74, 80, 20}));
        II11I1l11I iI11I1l11I2 = new II11I1l11I();
        I11lII1Il1 i11lII1Il1A2 = iI11I1l11I2.a(I1I1lI1II1.a(new byte[]{71, 5, 22, 6, 10}));
        ArrayList arrayList2 = new ArrayList();
        arrayList2.add(I11lI11III.a().a(l1ll11l111.class).b());
        arrayList2.add(I11lI11III.a().a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.llIllIlll1.class).a(l11il111ll, file).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(b(null, l11il111ll), liliii1111.a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.llIllIlll1.class))).b());
        arrayList2.add(I11lI11III.a().a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.l11Il111ll.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.l11Il111ll.class))).b());
        arrayList2.add(I11lI11III.a().a(l1lllIll1I.class).a(l11il111ll).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(l1lllIll1I.class))).b());
        arrayList2.add(I11lI11III.a().a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.lIlIII1111.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.lIlIII1111.class))).b());
        arrayList2.add(I11lI11III.a().a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.llIIII1IlI.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.llIIII1IlI.class))).b());
        arrayList2.add(I11lI11III.a().a(lI11I11111.l11Il1lIll.ll1IlllIII.II1Ill1111.IllIIll11l.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(null)).b());
        i11lII1Il1A2.a(arrayList2);
        I11lII1Il1 i11lII1Il1A3 = iI11I1l11I2.a(I1I1lI1II1.a(new byte[]{81, 17, 14, 9}));
        ArrayList arrayList3 = new ArrayList();
        arrayList3.add(I11lI11III.a().a(lIIl1I11II.class).b());
        arrayList3.add(I11lI11III.a().a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.l11Il111ll.class).a(l11il111ll, file).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(b(null, l11il111ll), liliii1111.a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.l11Il111ll.class))).b());
        arrayList3.add(I11lI11III.a().a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.lIlIII1111.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.lIlIII1111.class))).b());
        arrayList3.add(I11lI11III.a().a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.l1lllIll1I.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.l1lllIll1I.class))).b());
        arrayList3.add(I11lI11III.a().a(lI1I1lIIIl.lI1l111ll1.IIlIlI11ll.lI1lIll11I.llIllIlll1.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(null)).b());
        i11lII1Il1A3.a(arrayList3);
        i11lII1Il1A.a(iI11I1l11I2.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class)).a(I11lI11III.a().a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.llIIII1IlI.class).a(a(l11il111ll)).b());
        I11lII1Il1 i11lII1Il1A4 = iI11I1l11I.a(I1I1lI1II1.a(new byte[]{85, 22, 3, 11, 1, 93, 104, 67, 80, 10, 87, 92, 80, 102, 82, 92, 14, 4}));
        II11I1l11I iI11I1l11I3 = new II11I1l11I();
        I11lII1Il1 i11lII1Il1A5 = iI11I1l11I3.a(I1I1lI1II1.a(new byte[]{71, 5, 22, 6, 10}));
        ArrayList arrayList4 = new ArrayList();
        arrayList4.add(I11lI11III.a().a(l1ll11l111.class).b());
        arrayList4.add(I11lI11III.a().a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.llIllIlll1.class).a(l11il111ll, file).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(b(null, l11il111ll), liliii1111.a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.llIllIlll1.class))).b());
        arrayList4.add(I11lI11III.a().a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.l11Il111ll.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.l11Il111ll.class))).b());
        arrayList4.add(I11lI11III.a().a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.l1lllIll1I.class).a(l11il111ll).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.l1lllIll1I.class))).b());
        arrayList4.add(I11lI11III.a().a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.lIlIII1111.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.lIlIII1111.class))).b());
        arrayList4.add(I11lI11III.a().a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.IllIIll11l.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(null, liliii1111.a(Ill11Illll.I1lllIII1I.llIllI1l11.l111I11lII.IllIIll11l.class))).b());
        i11lII1Il1A5.a(arrayList4);
        I11lII1Il1 i11lII1Il1A6 = iI11I1l11I3.a(I1I1lI1II1.a(new byte[]{81, 17, 14, 9}));
        ArrayList arrayList5 = new ArrayList();
        arrayList5.add(I11lI11III.a().a(lIIl1I11II.class).b());
        arrayList5.add(I11lI11III.a().a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.l11Il111ll.class).a(l11il111ll, file).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(b(null, l11il111ll), liliii1111.a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.l11Il111ll.class))).b());
        arrayList5.add(I11lI11III.a().a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.lIlIII1111.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(a(null, l11il111ll), liliii1111.a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.lIlIII1111.class))).b());
        arrayList5.add(I11lI11III.a().a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.llIllIlll1.class).a(new IIlIlII11l.IIIIl11111.l11Il1lIll.l11Il111ll(null, liliii1111.a(IlI111IlII.I11ll1IlII.lI1l1IlI1l.I1ll1lll11.llIllIlll1.class))).b());
        i11lII1Il1A6.a(arrayList5);
        i11lII1Il1A4.a(iI11I1l11I3.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class)).a(I11lI11III.a().a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.llIIII1IlI.class).a(a(l11il111ll)).b());
        I11lII1Il1 i11lII1Il1A7 = iI11I1l11I.a(I1I1lI1II1.a(new byte[]{85, 22, 3, 11, 1, 93, 104, 93, 64, 5, 66, 83, 93, 80, 66, 80, 61, 7, 11, 94, 85}));
        II11I1l11I iI11I1l11I4 = new II11I1l11I();
        iI11I1l11I4.a(I1I1lI1II1.a(new byte[]{71, 5, 22, 6, 10})).a(Collections.emptyList());
        iI11I1l11I4.a(I1I1lI1II1.a(new byte[]{81, 17, 14, 9})).a(Collections.emptyList());
        i11lII1Il1A7.a(iI11I1l11I4.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class)).a(I11lI11III.a().a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.llIIII1IlI.class).a(a(l11il111ll)).b());
        arrayList.add(iI11I1l11I.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.lIlIII1111.class));
        arrayList.add(I11lI11III.a().a(III1Il1II1.class).a(new lI1Ill11I1(null, l11il111ll)).b());
        l11Il111ll<Object> l11il111llA = Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.llIllIlll1.a((List<Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l1ll11l111>) arrayList);
        if (!IIll1llI1l.Il1IIlI1II(7292)) {
            return l11il111llA;
        }
        Log.i(I1I1lI1II1.a(new byte[]{121, 21, 3, 84, 48, 76, 68, 67, 113, 17, 120, 7, 88, 81, 82}), I1I1lI1II1.a(new byte[]{100, 15, 84, 60, 41, 114, 98, 68, 88, 53, 3, 69, 102, 11, 70, 103, 10, 82, 19}));
        return null;
    }

    private static IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 a(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll l11il111ll) throws UnknownServiceException {
        if (android.support.v4.graphics.drawable.l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{1, 33}), 6305)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{90, 60, 51, 14, 54, 102, 65, 83, 124, 21, 92}));
        }
        return new llII1ll111(null, l11il111ll);
    }

    private static IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 b(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll l11il111ll) {
        return new II1I11IlI1(null, l11il111ll);
    }

    private static IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 a(l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll l11il111ll) {
        return new IIlll1ll1I(l11il111ll);
    }

    public lIlIII1111(Field field) {
        llll1IllIl.IllIII1lIl.Il1IIIlI1l.lIlIII1111.a(field);
        this.a = field;
    }
}
