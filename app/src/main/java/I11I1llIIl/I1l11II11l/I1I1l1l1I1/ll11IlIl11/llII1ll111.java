package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.security.GeneralSecurityException;
import java.security.KeyStoreException;
import java.security.NoSuchProviderException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;

/* loaded from: classes.dex */
class llII1ll111 extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 a;
    private /* synthetic */ l11Il111ll b;

    llII1ll111(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l11Il111ll l11il111ll) {
        this.a = liliii1111;
        this.b = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException {
        super.a(l11il111ll, l111l1i1il, th);
        if (this.a != null) {
            l11il111ll.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class);
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
            if (IIlIIlIII1.I1lllI1llI(424064419L)) {
                throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{69, 45, 7, 86, 36, 123, 103, 9, 116, 34}));
            }
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchProviderException, CloneNotSupportedException {
        if (IIlII1IIIl.l111IIlII1(I1I1lI1II1.a(new byte[]{123}), 192832717L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{113, 93, 45, 83, 59, 69, 6, 72, 1, 40, 106, 73, 103, 92, 71, 90, 22, 32, 54, 100, 90, 6, 119, 64, 10, 115, 82, 18, 84}));
        }
        super.a(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws TimeoutException {
        super.b(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
        } catch (Throwable unused) {
            if (Il11II1llI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{97, 15, 13}), 299556503L)) {
                throw new TimeoutException(I1I1lI1II1.a(new byte[]{1, 84, 84, 63, 42}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws KeyStoreException {
        if (llIlII1IlI.IllIlI1l1I(I1I1lI1II1.a(new byte[]{126, 86, 10, 32, 44, 65, 103, 105, 120, 32, 1, 114, 65, 115, 88, 5, 38, 59, 37, 116, 9, 0, 67, 81, 103, 0, 103, 42, 10, 5}), 223694791L)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{122, 16, 53, 0, 37, 96, 112, 103, 74, 5, 97, 126, Byte.MAX_VALUE, 124, 65, 12, 48, 55, 42, 5, 1, 11, 68, 96, 69, 14, 67, 38}));
        }
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
            if (Il1lII1l1l.Il1IIlI1II(4801)) {
                throw new UnknownError(I1I1lI1II1.a(new byte[]{96, 61, 19, 33, 22, 64, 78, Byte.MAX_VALUE, 112, 18}));
            }
        } catch (Throwable unused) {
        }
    }
}
