package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.III1Il1II1;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Pair;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.security.GeneralSecurityException;
import java.security.NoSuchProviderException;
import java.security.SignatureException;
import java.security.cert.CertificateEncodingException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;

/* loaded from: classes.dex */
class lI1Ill11I1 extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 a = null;
    private /* synthetic */ l11Il111ll b;

    lI1Ill11I1(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l11Il111ll l11il111ll) {
        this.b = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void c(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws CertificateEncodingException {
        if (lIIlI111II.I1111l111I(5424)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{103, 82, 18, 7, 83, 69, 126, 114, 125, 86, 69, 93, 76, 91, 96, 111, 8, 47, 37}));
        }
        super.c(l11il111ll, l111l1i1il);
        Pair pair = (Pair) l11il111ll.a(III1Il1II1.class);
        if (this.a != null) {
            Object obj = pair.first;
            ((Long) pair.second).longValue();
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException {
        if (android.accounts.utils.lIIlI111II.l11l1IIl1I(5354)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{85, 28, 1, 32, 45, 84, 82, 119, 82, 82, 1, 85, 102, 11, 118, 79, 84}));
        }
        super.a(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws SignatureException {
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
            if (IIlI1ll1ll.lI11llll1I(I1I1lI1II1.a(new byte[]{78, 7, 24, 1, 37, 86, 100, 120, 11, 55, 123, 124, 5}), I1I1lI1II1.a(new byte[]{92, 9, 40, 34, 39, 116, 123, 70, 15, 23, 115, 4, 84, 126, 4, 112, 84, 45, 33, 115, 86, 51, 74, 118, 65, 79, 95, 28, 45, 106}))) {
                throw new SignatureException(I1I1lI1II1.a(new byte[]{121, 21, 49, 54, 87, 100, 113, 90, 116, 34, 87, 3, 66, 123, 124, 91, 52, 6, 33, 118, 86, 27, 71, 91, 107, 90, 83, 45}));
            }
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchProviderException {
        super.a(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
            if (lI1lllIII1.Il1IIlI1II(8750)) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{66, 83, 85, 39, 24, 87, 88, 64, 105, 30, 101, 67, 69, Byte.MAX_VALUE, 12, 99, 22, 89}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) {
        super.b(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
            if (IIlII1IIIl.IlIllIll1I(452925726L)) {
                throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{92, 7, 13}));
            }
        } catch (Throwable unused) {
        }
    }
}
