package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.IIIlIl1I1l;
import android.accounts.utils.lI1l1I1l1l;
import android.media.content.lIIllIlIl1;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.NotActiveException;
import java.io.UTFDataFormatException;
import java.net.BindException;
import java.security.GeneralSecurityException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;

/* loaded from: classes.dex */
class II1I11IlI1 extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 a;
    private /* synthetic */ l11Il111ll b;

    II1I11IlI1(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l11Il111ll l11il111ll) {
        this.a = liliii1111;
        this.b = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws CertPathValidatorException, NoSuchProviderException {
        if (IIIlIl1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{110, 37, 38, 7, 5, 115, 6, 86, 120, 8, 71, 74, 83, 13, 71, 89, 14, 23, 51, 123, 91, 8, 120, 6, 101, 90, 100, 47, 49, 116}), 6426)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{6, 50, 11, 50, 13, 66, 82, 101}));
        }
        super.a(l11il111ll, l111l1i1il);
        if (this.a != null) {
            l11il111ll.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class);
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
            if (IlIIlI11I1.IlII1Illll(163295742L)) {
                Log.e(I1I1lI1II1.a(new byte[]{86, 46, 9, 39, 80, 95}), I1I1lI1II1.a(new byte[]{93, 9, 4, 46, 39, 82, 115, 81, 11, 30, 81, 73, 65}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchAlgorithmException, NotActiveException, UTFDataFormatException {
        if (lIIllIlIl1.I1lIllll1l(182364427L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{79, 53, 19, 54, 14, 124, 82}));
        }
        super.b(l11il111ll, l111l1i1il);
        if (this.a != null) {
            l11il111ll.b(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class);
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
            if (android.media.content.II1I11IlI1.II1111I11I(I1I1lI1II1.a(new byte[]{91, 39, 85, 38, 16, 93, 97, 84, 10, 87}), I1I1lI1II1.a(new byte[]{14, 41, 54, 35, 7, 115, 101, 115, 86, 50, 126}))) {
                throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{125, 8, 41, 19, 13, 90, 120, 98, 75, 42, 86, 72, 122, 112, 82, 67, 84, 6, 53, 1, 117, 87, 6, 123, 102, 125}));
            }
        } catch (Throwable unused) {
            if (I1lllI11II.I111IlIl1I(220190361L)) {
                throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{121, 9, 15, 22, 56, 119, 67, 81, 84, 60, 98, 82, 121, 78, 94, 124}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException {
        super.a(l11il111ll, l111l1i1il, th);
        if (this.a != null) {
            l11il111ll.a(Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.l11Il111ll.class);
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws KeyStoreException, BindException {
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
            if (lI1l1I1l1l.I1lllI1llI(311562137L)) {
                throw new KeyStoreException(I1I1lI1II1.a(new byte[]{6, 11, 51, 63, 85, 91, 99, 65, 75, 33, 68, 95, 89, 76, 91, 89, 22}));
            }
        } catch (Throwable unused) {
            if (IIll1llI1l.Il1IIlI1II(6146)) {
                throw new BindException(I1I1lI1II1.a(new byte[]{96, 11, 81, 34, 43, 65, 79, 86, 86, 15, 65, 85, 70, 84, 113, 91, 17, 6, 27, 113, 71}));
            }
        }
    }
}
