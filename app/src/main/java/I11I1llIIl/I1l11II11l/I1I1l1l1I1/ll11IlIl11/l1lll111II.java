package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import Ill1IlI1II.I1l11II11l.I1l11II11l.I111I1lI11.llIIII1IlI;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.util.Log;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.FileNotFoundException;
import java.net.SocketTimeoutException;
import java.security.GeneralSecurityException;
import java.security.NoSuchProviderException;
import java.security.SignatureException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;
import llIII1lllI.I1Il1l1I1I.l1Il11l1Il.Il11lI1Ill.IIl11IIIlI;

/* loaded from: classes.dex */
class l1lll111II extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 a = null;
    private /* synthetic */ l11Il111ll b;

    l1lll111II(I11I1l11ll.IIIlI11I1I.llIllI1l11.IlllI11lll.lIlIII1111 liliii1111, l11Il111ll l11il111ll) {
        this.b = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException, ClassNotFoundException, FileNotFoundException {
        if (IIll1llI1l.Il1IIlI1II(1317)) {
            Log.d(I1I1lI1II1.a(new byte[]{71, 7, 9, 93, 32, 113, 95, Byte.MAX_VALUE, 119, 29, 71, 99, 64, 14, 85, 95, 35, 25, 58, 80, 96, 2, 119, 111, 112, 81, 122}), I1I1lI1II1.a(new byte[]{92, 8, 0, 93, 27, 93, 91, 0, 115, 61, 91, 102, 12, 122, 108, 100, 5, 85, 5, 81, 101}));
            return;
        }
        super.a(l11il111ll, l111l1i1il, th);
        if (this.a != null) {
            ((IIl11IIIlI) l11il111ll.b(llIIII1IlI.class)).getChannel();
        }
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
            if (IllllI11Il.Il1IIlI1II(670)) {
                throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{85, 61, 9, 0, 22, 2, 120, 117, 88, 8, 119, 66, 119, 0, 95, Byte.MAX_VALUE}));
            }
        } catch (Throwable unused) {
            if (l1lI1I1l11.llll111lI1(I1I1lI1II1.a(new byte[]{6, 42, 86, 49, 50, 5, 71, Byte.MAX_VALUE, 107, 38, 5, 86, 123, 94}))) {
                throw new SignatureException(I1I1lI1II1.a(new byte[]{112, 0, 6, 3, 39, 112, 2, 72, 122, 1, 122, 101, 2, 126, 3, 126, 43, 55, 85, 106, 119, 20, 94, 112, 4, 83, 94, 50, 43, 83}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) {
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchProviderException {
        super.a(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) {
        super.b(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
        } catch (Throwable unused) {
            if (lIIlI111II.I1IlI11II1(4645)) {
                throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{102, 5, 49, 0, 13, 0, 110, 101, 77, 12, 4, 91, 4, 106, 88}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void c(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws SocketTimeoutException {
        super.c(l11il111ll, l111l1i1il);
        try {
            this.b.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 50, 12, 18, 80, 91, 89, 87, 1, 117, 94, 81}), "");
            if (llIlII1IlI.I1II1111ll(296123190L)) {
                throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{113, 82, 8, 41, 6, 91, 117, 6, 108, 29, Byte.MAX_VALUE}));
            }
        } catch (Throwable unused) {
        }
    }
}
