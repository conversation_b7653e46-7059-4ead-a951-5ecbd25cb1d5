package I11I1llIIl.I1l11II11l.I1I1l1l1I1.ll11IlIl11;

import Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l111l1I1Il;
import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.io.UTFDataFormatException;
import java.security.GeneralSecurityException;
import java.security.NoSuchProviderException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1l1III11l.llI111llII.I1I1l1l1I1.lI1lIII1Il.l11Il111ll;

/* loaded from: classes.dex */
class IIlll1ll1I extends IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111 {
    private /* synthetic */ l11Il111ll a;

    IIlll1ll1I(l11Il111ll l11il111ll) {
        this.a = l11il111ll;
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws GeneralSecurityException {
        super.a(l11il111ll, l111l1i1il, th);
        try {
            this.a.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 29, 1, 80, 71, 68, 80, 11, 94}), th.toString());
        } catch (Throwable unused) {
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il, Throwable th) throws UTFDataFormatException {
        super.b(l11il111ll, l111l1i1il, th);
        try {
            this.a.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 33, 13, 3, 92, 89, 117, 65, 7, 85, 64, 65, 80, 91, 91}), th.toString());
            if (lIIlI111II.l111lI11I1(5521)) {
                throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{99, 16, 10, 63, 23, 124, 98, 71, 112, 92, 4, 92, 7, 85, 125, 98, 14, 3, 20, 126, 122, 15}));
            }
        } catch (Throwable unused) {
            if (l11Il1lI11.I1lllI1llI(438676377L)) {
                throw new StackOverflowError(I1I1lI1II1.a(new byte[]{0, 8, 19, 61, 11, 124, 95, 88, 120, 38, 2, 2, 71, 75, 115, 1, 41, 57, 52, 102, 65, 15, 80, 87, 73}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void a(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) throws NoSuchProviderException {
        super.a(l11il111ll, l111l1i1il);
        try {
            this.a.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 49, 17, 3, 71, 67}), "");
        } catch (Throwable unused) {
            if (androidx.constraintlayout.widget.lIIlI111II.l1l11llIl1(303070362L)) {
                throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{4, 1, 37, 63, 11, 113, 79, Byte.MAX_VALUE, 86, 6, 103, 113, 92, 111, 5, 120, 56, 15, 13, 4, 87, 88, 126, 70}));
            }
        }
    }

    @Override // IIlIlII11l.IIIIl11111.l11Il1lIll.lIlIII1111
    public final <T> void b(Il11Il1Ill.l1l11I11II.II1I11llII.l1Il1I1lI1.l11Il111ll<T> l11il111ll, l111l1I1Il l111l1i1il) {
        super.b(l11il111ll, l111l1i1il);
        try {
            this.a.f().put(l111l1i1il.getClass().getSimpleName() + I1I1lI1II1.a(new byte[]{88, 10, 39, 11, 6}), "");
        } catch (Throwable unused) {
        }
    }
}
