package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import android.accounts.utils.I1lllI11II;
import android.content.Context;
import android.util.LruCache;
import androidx.interpolator.view.animation.ll1l11I1II;
import java.util.ArrayList;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lI1llI1lIl.I11III1l1I.IIlIllllII.lIll1lIlIl.lIlIIlIII1;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.llIl1IlllI;

/* loaded from: classes.dex */
public class l11lIlllII implements lIlIIlIII1 {
    private LruCache<String, llIl1IlllI> a;
    private List<String> b;

    @Override // ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.l1I11I11Il
    public void init(Context context) {
        this.a = new LruCache<>(50);
        this.b = new ArrayList();
    }

    @Override // lI1llI1lIl.I11III1l1I.IIlIllllII.lIll1lIlIl.lIlIIlIII1
    public void autowire(Object obj) {
        a(obj, null);
    }

    private void a(Object obj, Class<?> cls) {
        if (cls == null) {
            cls = obj.getClass();
        }
        llIl1IlllI llil1illliA = a(cls);
        if (llil1illliA != null) {
            llil1illliA.inject(obj);
        }
        Class<? super Object> superclass = cls.getSuperclass();
        if (superclass == null || superclass.getName().startsWith(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83}))) {
            return;
        }
        a(obj, superclass);
    }

    private llIl1IlllI a(Class<?> cls) {
        if (I1lllI11II.lll1111l11(I1I1lI1II1.a(new byte[]{96, 16, 87, 6, 3, 65, 79, 4, 105, 3, 68, 4, 121, 117, 124, 2}), 169650336L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{109, 12, 46, 84, 35, 68, 0, 86, 123, 14, 83, 0, 120, 84, Byte.MAX_VALUE, 77, 58, 21}));
        }
        String name = cls.getName();
        try {
            if (!this.b.contains(name)) {
                llIl1IlllI llil1illli = this.a.get(name);
                if (llil1illli == null) {
                    llil1illli = (llIl1IlllI) Class.forName(cls.getName() + I1I1lI1II1.a(new byte[]{19, 64, 35, 55, 13, 64, 67, 85, 75, 64, 20, 113, 64, 77, 91, 66, 11, 19, 7, 86})).getConstructor(new Class[0]).newInstance(new Object[0]);
                }
                this.a.put(name, llil1illli);
                return llil1illli;
            }
        } catch (Exception unused) {
            this.b.add(name);
        }
        if (ll1l11I1II.l11I11I11l(I1I1lI1II1.a(new byte[]{64, 6, 24, 36, 7, 96, 88, 93, 119, 80, 7, 124, 113, 1, 117, 100, 58, 39, 20, 113}))) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{79, 39, 84, 81, 87, 90, 83, 84, 105, 21, 125, 126, 66, 108, 64, 90, 7, 41, 15, 126, 121, 40}));
        }
        return null;
    }
}
