package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import IIll1I1III.IlI1lIlIll.IIIl1I111I.I11lI1I1I1.IIl1II1lII;
import Il11lI1Ill.l1lI11lI1I.lIllllI1lI.IlIlIIlII1.llII1ll111;
import java.security.SignatureException;
import l1Il1Ill11.I1II1Il1ll.I11llI1I1I.lIl1lIIIII.IIlIllIl1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.lIlIIlIII1;

/* loaded from: classes.dex */
class llllIlII1I implements llII1ll111 {
    final /* synthetic */ IIl1II1lII a;
    final /* synthetic */ int b;
    final /* synthetic */ IIlIllIl1l c;

    llllIlII1I(IIl1II1lII iIl1II1lII, int i, IIlIllIl1l iIlIllIl1l) {
        this.a = iIl1II1lII;
        this.b = i;
        this.c = iIlIllIl1l;
    }

    @Override // Il11lI1Ill.l1lI11lI1I.lIllllI1lI.IlIlIIlII1.llII1ll111
    public void onContinue(IIlIllIl1l iIlIllIl1l) throws InterruptedException {
        this.a.countDown();
        l1l1I1llII.a(this.b + 1, this.a, iIlIllIl1l);
    }

    @Override // Il11lI1Ill.l1lI11lI1I.lIllllI1lI.IlIlIIlII1.llII1ll111
    public void onInterrupt(Throwable th) throws SignatureException {
        IIlIllIl1l iIlIllIl1l = this.c;
        if (th == null) {
            th = new lIlIIlIII1(I1I1lI1II1.a(new byte[]{121, 11, 66, 8, 7, 70, 68, 81, 94, 1, 30}));
        }
        iIlIllIl1l.setTag(th);
        this.a.a();
    }
}
