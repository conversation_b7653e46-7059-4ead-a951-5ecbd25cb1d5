package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import ll11l1lI1l.l1I1I11111.I1l11II11l.lll1l1IIlI.ll111l1llI;

/* loaded from: classes.dex */
/* synthetic */ class III1Il1II1 {
    static final /* synthetic */ int[] a;

    static {
        int[] iArr = new int[ll111l1llI.valuesCustom().length];
        a = iArr;
        try {
            iArr[ll111l1llI.PROVIDER.ordinal()] = 1;
        } catch (NoSuchFieldError unused) {
        }
        try {
            a[ll111l1llI.FRAGMENT.ordinal()] = 2;
        } catch (NoSuchFieldError unused2) {
        }
    }
}
