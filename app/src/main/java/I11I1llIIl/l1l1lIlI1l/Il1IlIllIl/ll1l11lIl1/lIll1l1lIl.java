package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import IIll1I1III.IlI1lIlIll.IIIl1I111I.I11lI1I1I1.IIl1II1lII;
import Il11lI1Ill.l1lI11lI1I.lIllllI1lI.IlIlIIlII1.llII1ll111;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateNotYetValidException;
import java.util.concurrent.TimeUnit;
import l1Il1Ill11.I1II1Il1ll.I11llI1I1I.lIl1lIIIII.IIlIllIl1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.lIlIIlIII1;

/* loaded from: classes.dex */
class lIll1l1lIl implements Runnable {
    final /* synthetic */ IIlIllIl1l a;
    final /* synthetic */ llII1ll111 b;
    final /* synthetic */ l1l1I1llII c;

    lIll1l1lIl(l1l1I1llII l1l1i1llii, IIlIllIl1l iIlIllIl1l, llII1ll111 llii1ll111) {
        this.c = l1l1i1llii;
        this.a = iIlIllIl1l;
        this.b = llii1ll111;
    }

    @Override // java.lang.Runnable
    public void run() throws UnrecoverableKeyException, CertificateNotYetValidException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{4, 28, 21, 32, 82, 93, 85, 85, 14, 23, 123, 65, 122, Byte.MAX_VALUE, 85, 119, 82, 2, 52, 123, 0, 7}), 173264190L)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{71, 92, 18, 32, 44, 98, 66, 0, 0, 23, 123, 119}));
        }
        IIl1II1lII iIl1II1lII = new IIl1II1lII(Il1IIlI1II.f.size());
        try {
            l1l1I1llII.a(0, iIl1II1lII, this.a);
            iIl1II1lII.await(this.a.getTimeout(), TimeUnit.SECONDS);
            if (iIl1II1lII.getCount() > 0) {
                this.b.onInterrupt(new lIlIIlIII1(I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 11, 91, 67, 85, 75, 7, 85, 64, 65, 86, 70, 21, 18, 19, 13, 81, 85, 18, 64, 92, 93, 81, 23, 17, 13, 95, 82, 7, 23, 11, 23, 17, 76})));
            } else if (this.a.getTag() != null) {
                this.b.onInterrupt((Throwable) this.a.getTag());
            } else {
                this.b.onContinue(this.a);
            }
        } catch (Exception e) {
            this.b.onInterrupt(e);
        }
        if (android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{100, 38, 86, 32, 20, 88, 77, 4, 118, 52, 120, 100, 125, 125, 99, 92, 23, 3, 54, 68, 64, 27, 4, 65, 91, 88, 70, 44, 61, 74, 6}), 339235158L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{123, 29, 90}));
        }
    }
}
