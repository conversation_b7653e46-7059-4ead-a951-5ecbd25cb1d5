package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.lI1II1llI1;
import IIll1I1III.IlI1lIlIll.IIIl1I111I.I11lI1I1I1.IIl1II1lII;
import Il11lI1Ill.l1lI11lI1I.lIllllI1lI.IlIlIIlII1.llII1ll111;
import android.content.Context;
import android.util.Log;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lI1lI11Ill;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.IOException;
import java.net.UnknownHostException;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.ll1ll1lIl1;
import l1Il1Ill11.I1II1Il1ll.I11llI1I1I.lIl1lIIIII.IIlIllIl1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lI1llI1lIl.I11III1l1I.IIlIllllII.lIll1lIlIl.Il11II1llI;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.lIlIIlIII1;

/* loaded from: classes.dex */
public class l1l1I1llII implements Il11II1llI {
    private static boolean a;
    private static final Object b = new Object();

    static /* synthetic */ Object a() {
        Object obj = b;
        if (lI1lI11Ill.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{103, 15, 53, 61, 42, 126, 86, Byte.MAX_VALUE, 11, 9, 114, 95, 101, 11, 89, 111, 23, 52, 83}))) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{1, 12, 20, 54, 19, 4, 1, 99, 8, 8, 98, 101, 68}));
        }
        return obj;
    }

    static /* synthetic */ void a(int i, IIl1II1lII iIl1II1lII, IIlIllIl1l iIlIllIl1l) throws InterruptedException {
        if (IIlI1ll1ll.Il1IIlI1II(9711)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{83, 39, 45, 1, 91, 114, 77, 119, 124, 34, 83, 83, 112, 77, 1, 124}));
        }
        b(i, iIl1II1lII, iIlIllIl1l);
    }

    static /* synthetic */ boolean a(boolean z) throws IOException {
        if (llIlI11III.Il1IIlI1II(772)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{85, 19, 46, 11, 32, 94, 97, 120, 126, 15, 0, 73, 114, 116, 125}));
        }
        a = z;
        if (android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{79, 18, 40, 16, 36, 111, 70, 69, 116, 35, 72, 105, 6, 104, 122, 64, 52, 23, 36, 93, 94, 47, 114, 70}), 182819863L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{124, 6, 36, 83, 40, 123, 117, 67, 112, 45, 97, 4, 115, 118, 112, 82, 42, 35, 7, 91, 94, 9, 118, 67, 80, 112, 83, 42, 5, 64, 115}));
        }
        return z;
    }

    @Override // lI1llI1lIl.I11III1l1I.IIlIllllII.lIll1lIlIl.Il11II1llI
    public void doInterceptions(IIlIllIl1l iIlIllIl1l, llII1ll111 llii1ll111) throws UnknownHostException {
        if (ll1ll1lIl1.a(Il1IIlI1II.e)) {
            b();
            if (!a) {
                llii1ll111.onInterrupt(new lIlIIlIII1(I1I1lI1II1.a(new byte[]{126, 10, 22, 0, 16, 86, 82, 64, 77, 11, 66, 67, 21, 80, 90, 92, 22, 8, 3, 94, 89, 27, 82, 65, 90, 89, 89, 69, 16, 83, 92, 6, 68, 68, 22, 10, 13, 21, 90, 69, 90, 12, 16, 68, 92, 84, 81, 27})));
                return;
            }
            lII1111lIl.a.execute(new lIll1l1lIl(this, iIlIllIl1l, llii1ll111));
        } else {
            llii1ll111.onContinue(iIlIllIl1l);
        }
        if (IIlI1ll1ll.l1Il11I1Il(I1I1lI1II1.a(new byte[]{0, 15, 49, 4, 43, 119, 92, 103, 1, 46, 89, 119, 1, 120, 121, 121, 22, 44, 3, Byte.MAX_VALUE, 91, 85, 106, 100, 120, 85, 125, 12, 33}), 604368382L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{113, 41, 35, 55, 42, 121, 114, 3, 72, 3, 88, 101, 7, 13, 1, 84, 33, 21, 3}));
        }
    }

    private static void b(int i, IIl1II1lII iIl1II1lII, IIlIllIl1l iIlIllIl1l) {
        if (i < Il1IIlI1II.f.size()) {
            Il1IIlI1II.f.get(i).process(iIlIllIl1l, new llllIlII1I(iIl1II1lII, i, iIlIllIl1l));
        }
    }

    @Override // ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.l1I11I11Il
    public void init(Context context) {
        lII1111lIl.a.execute(new l1lll1I1l1(this, context));
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{5, 0, 83, 82, 83, 2, 97, 123, 117, 28, 117, 104}), 945586053L)) {
            Log.d(I1I1lI1II1.a(new byte[]{77, 15, 38, 52, 15, 0, 89, 81, 91, 22}), I1I1lI1II1.a(new byte[]{83, 9, 26, 92, 42, 64, 99, 64, 118, 28, 105, 115, 83, 105, 7, 81, 84, 21, 87}));
        }
    }

    private static void b() throws UnknownHostException {
        if (l1l1I111I1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{77, 1, 46, 50, 1, 99, 111, 86, 83, 29, 124, 100, 113, 86, 76, 2, 24, 34, 16, 84}), 8059)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{70, 81, 46}));
        }
        synchronized (b) {
            while (!a) {
                try {
                    b.wait(lI1II1llI1.MIN_BACKOFF_MILLIS);
                } catch (InterruptedException e) {
                    throw new lIlIIlIII1(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 45, 94, 68, 80, 75, 87, 80, 18, 21, 13, 64, 16, 8, 93, 92, 71, 22, 84, 10, 23, 70, 23, 23, 88, 11, 66, 8, 23, 86, 95, 16, 77, 13, 93, 85, 21, 92, 70, 71, 13, 19, 67, 18, 66, 4, 82, 70, 92, 88, 23, 88, 68, 105}) + e.getMessage() + I1I1lI1II1.a(new byte[]{106}));
                }
            }
        }
    }
}
