package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import l111llllI1.IlI11I11ll.l1ll1lI1l1.l11Il1lIll.lIl11II1ll;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.I1IlIIlIl1;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.l1I11I11Il;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.lll1I1I11l;
import ll1l111llI.l1IlIllI11.l1ll1lI1l1.llIl1IlIll.I1lllI11II;

/* loaded from: classes.dex */
class Il1IIlI1II {
    static Map<String, Class<? extends lll1I1I11l>> a = new HashMap();
    static Map<String, I1lllI11II> b = new HashMap();
    static Map<Class, l1I11I11Il> c = new HashMap();
    static Map<String, I1lllI11II> d = new HashMap();
    static Map<Integer, Class<? extends I1IlIIlIl1>> e = new lIl11II1ll(I1I1lI1II1.a(new byte[]{122, 11, 16, 0, 66, 65, 95, 81, 87, 68, 95, 94, 80, 25, 93, 91, 22, 4, 16, 81, 85, 17, 71, 90, 65, 69, 23, 16, 23, 87, 23, 16, 86, 9, 7, 69, 18, 71, 94, 95, 75, 13, 68, 73, 21, 98, 17, 70, 63}));
    static List<I1IlIIlIl1> f = new ArrayList();
}
