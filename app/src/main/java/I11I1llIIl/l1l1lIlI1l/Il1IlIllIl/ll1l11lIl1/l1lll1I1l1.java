package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import I1ll1I1I11.l11I1lIIl1.l11IIl1l1I.llI1l1ll1I.I1l11I1I1I;
import android.accounts.utils.lIIIIII11I;
import android.content.Context;
import android.util.Log;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;
import java.util.Map$Entry;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.ll1ll1lIl1;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.lIlIIlIII1;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.I1IlIIlIl1;

/* loaded from: classes.dex */
class l1lll1I1l1 implements Runnable {
    final /* synthetic */ Context a;
    final /* synthetic */ l1l1I1llII b;

    l1lll1I1l1(l1l1I1llII l1l1i1llii, Context context) {
        this.b = l1l1i1llii;
        this.a = context;
    }

    @Override // java.lang.Runnable
    public void run() throws IllegalAccessException, InstantiationException, IOException, IllegalArgumentException, InvocationTargetException {
        if (lIIIIII11I.IllIlI1l1I(I1I1lI1II1.a(new byte[]{99, 54}), 690062406L)) {
            Log.d(I1I1lI1II1.a(new byte[]{102, 3, 3, 87, 87, 64, 94, 96}), I1I1lI1II1.a(new byte[]{93, 7, 85, 18, 22, 68, 96, 121, 15, 81, 86, 83, 79, 67, 80}));
            return;
        }
        if (ll1ll1lIl1.a(Il1IIlI1II.e)) {
            Iterator<Map$Entry<Integer, Class<? extends I1IlIIlIl1>>> it = Il1IIlI1II.e.entrySet().iterator();
            while (it.hasNext()) {
                Class<? extends I1IlIIlIl1> value = it.next().getValue();
                try {
                    I1IlIIlIl1 i1IlIIlIl1NewInstance = value.getConstructor(new Class[0]).newInstance(new Object[0]);
                    i1IlIIlIl1NewInstance.init(this.a);
                    Il1IIlI1II.f.add(i1IlIIlIl1NewInstance);
                } catch (Exception e) {
                    throw new lIlIIlIII1(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 37, 98, 95, 64, 77, 81, 71, 66, 8, 12, 91, 68, 65, 90, 91, 71, 83, 69, 6, 1, 66, 67, 12, 69, 68, 7, 23, 16, 90, 69, 17, 25, 10, 81, 93, 80, 25, 9, 21, 57}) + value.getName() + I1I1lI1II1.a(new byte[]{106, 72, 66, 23, 7, 84, 68, 95, 87, 68, 13, 16, 110}) + e.getMessage() + I1I1lI1II1.a(new byte[]{106}));
                }
            }
            l1l1I1llII.a(true);
            I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 16, 80, 10, 68, 85, 71, 90, 81, 69, 22, 14, 16, 65, 16, 8, 93, 92, 71, 22, 88, 19, 1, 64, 25}));
            synchronized (l1l1I1llII.a()) {
                l1l1I1llII.a().notifyAll();
            }
        }
    }
}
