package I11I1llIIl.l1l1lIlI1l.Il1IlIllIl.ll1l11lIl1;

import I1ll1I1I11.l11I1lIIl1.l11IIl1l1I.llI1l1ll1I.I1l11I1I1I;
import android.content.Context;
import android.media.content.Il1llIl111;
import android.net.Uri;
import android.util.Log;
import androidx.core.location.IIlIIlIII1;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.StreamCorruptedException;
import java.lang.reflect.InvocationTargetException;
import java.security.InvalidKeyException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import java.security.cert.CertificateException;
import java.util.HashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Map$Entry;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.I1IIIIllI1;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.III1l1lIlI;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.IllIIll11l;
import l1IIllI111.II111IIl1l.IlI11IIIlI.lI11IIIllI.ll1ll1lIl1;
import l1Il1Ill11.I1II1Il1ll.I11llI1I1I.lIl1lIIIII.IIlIllIl1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.II1l1lIllI;
import lIll1II1I1.l1l11I11II.IllIlll111.lIIl1l1l1I.lIlIIlIII1;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.Il1l11I11I;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.l1I11I11Il;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.l1lIl11l1l;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.lll1I1I11l;
import ll111Il1II.l11Il1lIll.I1lIl1lIlI.Il1I111Il1.llllIllIII;
import ll11l1lI1l.l1I1I11111.I1l11II11l.lll1l1IIlI.III1IIII1l;
import ll1l111llI.l1IlIllI11.l1ll1lI1l1.llIl1IlIll.I1lllI11II;

/* loaded from: classes.dex */
public class lII1111lIl {
    static ThreadPoolExecutor a;
    private static Context b;
    private static boolean c;

    private static void a() throws IllegalAccessException, InstantiationException, KeyManagementException, IllegalArgumentException, InvocationTargetException {
        c = false;
        b(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 103, 86, 91, 65, 70, 69, 14, 3, 1, 40, 95, 89, 2, Byte.MAX_VALUE, 126, 84}));
        b(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 103, 86, 91, 65, 70, 69, 14, 123, 1, 13, 122, 124, 95, 90, 91, 44}));
        b(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 101, 75, 91, 67, 11, 5, 7, 64, 67, 69, 23, 89, 2, Byte.MAX_VALUE, 91, 9, 45, 94, 91, 15, 91}));
        b(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 101, 75, 91, 67, 11, 5, 7, 64, 67, 69, 23, 124, 2, Byte.MAX_VALUE, 91, 9, 85, 94, 126, 42, 126}));
    }

    private static void b(String str) throws IllegalAccessException, InstantiationException, KeyManagementException, IllegalArgumentException, InvocationTargetException {
        if (lIIlI111II.llIIIl11I1(439774439L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{79, 2, 55, 53, 26, 86, 15, 66, 1, 10, 89, 68}));
        }
        if (IllIIll11l.a(str)) {
            return;
        }
        try {
            Object objNewInstance = Class.forName(str).getConstructor(new Class[0]).newInstance(new Object[0]);
            if (objNewInstance instanceof llllIllIII) {
                a((llllIllIII) objNewInstance);
            } else if (objNewInstance instanceof l1lIl11l1l) {
                a((l1lIl11l1l) objNewInstance);
            } else if (objNewInstance instanceof Il1l11I11I) {
                a((Il1l11I11I) objNewInstance);
            } else {
                I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{69, 1, 5, 12, 17, 65, 82, 66, 25, 2, 81, 89, 89, 92, 80, 25, 66, 2, 14, 83, 67, 18, 19, 91, 82, 91, 82, 95, 68}) + str + I1I1lI1II1.a(new byte[]{23, 23, 10, 10, 23, 89, 83, 16, 80, 9, 64, 92, 80, 84, 81, 91, 22, 18, 66, 93, 94, 4, 19, 90, 85, 22, 126, 55, 11, 71, 67, 6, 101, 11, 13, 17, 77, 124, 103, 66, 86, 18, 89, 84, 80, 75, 115, 71, 13, 20, 18, 29, 121, 40, 93, 65, 86, 68, 84, 0, 20, 70, 88, 17, 112, 22, 13, 16, 18, 27}));
            }
        } catch (Exception e) {
            I1l11I1I1I.c.error(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{69, 1, 5, 12, 17, 65, 82, 66, 25, 7, 92, 81, 70, 74, 20, 80, 16, 19, 13, 64, 10}) + str, e);
        }
    }

    private static void a(llllIllIII llllilliii) throws StreamCorruptedException, InvalidKeyException {
        b();
        if (llllilliii != null) {
            llllilliii.loadInto(Il1IIlI1II.a);
        }
        if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{6, 80, 23, 40, 6, 81, 79, 85, 87}), 794258086L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{86, 7, 43, 2}));
        }
    }

    private static void a(Il1l11I11I il1l11I11I) throws StreamCorruptedException {
        b();
        if (il1l11I11I != null) {
            il1l11I11I.loadInto(Il1IIlI1II.e);
        }
    }

    private static void a(l1lIl11l1l l1lil11l1l) throws StreamCorruptedException {
        b();
        if (l1lil11l1l != null) {
            l1lil11l1l.loadInto(Il1IIlI1II.d);
        }
        if (IIlIIlIII1.I1lllI1llI(329253914L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{78, 51, 21, 45, 50, 122, 71, 106, 86, 14, 104, 5, 95, 96, 102, 125, 17, 43, 37, 3, 105, 54, 82}));
        }
    }

    private static void b() throws StreamCorruptedException {
        if (IllIIIIII1.IlIllIll1I(I1I1lI1II1.a(new byte[]{124, 84, 5, 93, 50, 86, 83, 69, 14, 9, 68, 7}), 198834188L)) {
            Log.d(I1I1lI1II1.a(new byte[]{95, 83, 12, 10}), I1I1lI1II1.a(new byte[]{96, 87, 37, 0, 22, 97, 114, 71, 116, 54, 116, 83, 91, 107, 89}));
            return;
        }
        if (!c) {
            c = true;
        }
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{4, 62, 85, 86, 17, Byte.MAX_VALUE, 89, 116, 99, 61, 122, 7, 123, 107, 76, 92, 26, 20, 85, 99, 98, 50, 101, 2, 105, 92}), 190739212L)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{0, 82, 58, 21, 33, 2, 3, 7, 82, 33, 70, 125}));
        }
    }

    public static synchronized void a(Context context, ThreadPoolExecutor threadPoolExecutor) throws lIlIIlIII1 {
        Set<String> setA;
        b = context;
        a = threadPoolExecutor;
        try {
            long jCurrentTimeMillis = System.currentTimeMillis();
            a();
            if (c) {
                I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{123, 11, 3, 1, 66, 71, 88, 69, 77, 1, 66, 16, 88, 88, 68, 21, 0, 24, 66, 83, 66, 14, 70, 65, 86, 68, 26, 4, 17, 70, 88, 78, 69, 1, 5, 12, 17, 65, 82, 66, 25, 20, 92, 69, 82, 80, 90, 27}));
            } else {
                if (I1l11I1I1I.b() || III1l1lIlI.a(context)) {
                    I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{101, 17, 12, 69, 21, 92, 67, 88, 25, 0, 85, 82, 64, 94, 20, 88, 13, 5, 7, 18, 95, 19, 19, 91, 86, 65, 23, 12, 10, 65, 67, 2, 91, 8, 78, 69, 16, 80, 85, 69, 80, 8, 84, 16, 71, 86, 65, 65, 7, 19, 66, 95, 81, 17, 29}));
                    setA = I1IIIIllI1.a(b, I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23}));
                    if (!setA.isEmpty()) {
                        context.getSharedPreferences(I1I1lI1II1.a(new byte[]{100, 52, 61, 36, 48, 122, 98, 100, 124, 54, 111, 115, 116, 122, 124, 112}), 0).edit().putStringSet(I1I1lI1II1.a(new byte[]{101, 43, 55, 49, 39, 103, 104, 125, 120, 52}), setA).apply();
                    }
                    III1l1lIlI.b(context);
                } else {
                    I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{123, 11, 3, 1, 66, 71, 88, 69, 77, 1, 66, 16, 88, 88, 68, 21, 4, 19, 13, 95, 16, 2, 82, 86, 91, 83, 25}));
                    setA = new HashSet<>(context.getSharedPreferences(I1I1lI1II1.a(new byte[]{100, 52, 61, 36, 48, 122, 98, 100, 124, 54, 111, 115, 116, 122, 124, 112}), 0).getStringSet(I1I1lI1II1.a(new byte[]{101, 43, 55, 49, 39, 103, 104, 125, 120, 52}), new HashSet()));
                }
                I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{113, 13, 12, 1, 66, 71, 88, 69, 77, 1, 66, 16, 88, 88, 68, 21, 4, 8, 12, 91, 67, 9, 86, 81, 31, 22, 90, 4, 20, 18, 68, 10, 77, 1, 66, 88, 66}) + setA.size() + I1I1lI1II1.a(new byte[]{27, 68, 1, 10, 17, 65, 23}) + (System.currentTimeMillis() - jCurrentTimeMillis) + I1I1lI1II1.a(new byte[]{23, 9, 17, 75}));
                jCurrentTimeMillis = System.currentTimeMillis();
                for (String str : setA) {
                    if (str.startsWith(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 103, 86, 91, 65}))) {
                        llllIllIII llllilliii = (llllIllIII) Class.forName(str).getConstructor(new Class[0]).newInstance(new Object[0]);
                        llllilliii.loadInto(Il1IIlI1II.a);
                    } else if (str.startsWith(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 124, 87, 64, 80, 16, 2, 7, 66, 68, 14, 65, 70}))) {
                        Il1l11I11I il1l11I11I = (Il1l11I11I) Class.forName(str).getConstructor(new Class[0]).newInstance(new Object[0]);
                        il1l11I11I.loadInto(Il1IIlI1II.e);
                    } else if (str.startsWith(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 3, 89, 94, 82, 88, 6, 81, 30, 84, 87, 80, 71, 13, 8, 6, 28, 81, 19, 92, 64, 71, 83, 69, 75, 22, 93, 66, 23, 82, 23, 76, 36, 48, 90, 66, 68, 92, 22, 20, 20, 101, 75, 91, 67, 11, 5, 7, 64, 67}))) {
                        l1lIl11l1l l1lil11l1l = (l1lIl11l1l) Class.forName(str).getConstructor(new Class[0]).newInstance(new Object[0]);
                        l1lil11l1l.loadInto(Il1IIlI1II.d);
                    }
                }
            }
            I1l11I1I1I.c.info(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{123, 11, 3, 1, 66, 71, 88, 95, 77, 68, 85, 92, 80, 84, 81, 91, 22, 65, 4, 91, 94, 8, 64, 93, 86, 82, 27, 69, 7, 93, 68, 23, 23}) + (System.currentTimeMillis() - jCurrentTimeMillis) + I1I1lI1II1.a(new byte[]{23, 9, 17, 75}));
            if (Il1IIlI1II.a.size() == 0) {
                I1l11I1I1I.c.error(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{121, 11, 66, 8, 3, 69, 71, 89, 87, 3, 16, 86, 92, 85, 81, 70, 66, 22, 7, 64, 85, 65, 85, 90, 70, 88, 83, 73, 68, 81, 95, 6, 84, 15, 66, 28, 13, 64, 69, 16, 90, 11, 94, 86, 92, 94, 65, 71, 3, 21, 11, 93, 94, 65, 67, 89, 86, 87, 68, 0, 69}));
            }
            if (I1l11I1I1I.b()) {
                I1l11I1I1I.c.debug(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), String.format(Locale.getDefault(), I1I1lI1II1.a(new byte[]{123, 11, 5, 12, 17, 65, 94, 83, 74, 39, 85, 94, 65, 92, 70, 21, 10, 0, 17, 18, 81, 13, 65, 80, 82, 82, 78, 69, 6, 87, 82, 13, 23, 8, 13, 4, 6, 80, 83, 28, 25, 35, 66, 95, 64, 73, 125, 91, 6, 4, 26, 105, 21, 5, 110, 25, 19, Byte.MAX_VALUE, 89, 17, 1, 64, 84, 6, 71, 16, 13, 23, 43, 91, 83, 85, 65, 63, 21, 84, 104, 21, 20, 101, 16, 14, 20, 91, 84, 4, 65, 124, 93, 82, 82, 29, 63, 23, 83, 62}), Integer.valueOf(Il1IIlI1II.a.size()), Integer.valueOf(Il1IIlI1II.e.size()), Integer.valueOf(Il1IIlI1II.d.size())));
            }
        } catch (Exception e) {
            throw new lIlIIlIII1(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 37, 98, 95, 64, 77, 81, 71, 66, 8, 12, 91, 68, 65, 95, 90, 84, 95, 68, 17, 13, 81, 68, 67, 84, 1, 12, 17, 7, 71, 23, 85, 65, 7, 85, 64, 65, 80, 91, 91, 67, 65, 57}) + e.getMessage() + I1I1lI1II1.a(new byte[]{106}));
        }
    }

    public static IIlIllIl1l a(String str) {
        if (l1lI1I1l11.IlII1Illll(307257587L)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{114, 81, 13, 28, 15, 115, 90, 73, 110, 29, 118, 106}));
        }
        I1lllI11II i1lllI11II = Il1IIlI1II.d.get(str);
        if (i1lllI11II == null) {
            return null;
        }
        return new IIlIllIl1l(i1lllI11II.getPath(), i1lllI11II.getGroup());
    }

    /* JADX WARN: Removed duplicated region for block: B:51:0x07ad A[DONT_GENERATE] */
    /* JADX WARN: Removed duplicated region for block: B:53:0x07af A[Catch: all -> 0x08ed, TRY_ENTER, TryCatch #2 {, blocks: (B:5:0x0006, B:9:0x003c, B:11:0x004a, B:13:0x0056, B:15:0x005c, B:16:0x0179, B:18:0x0187, B:19:0x02e0, B:49:0x07a4, B:53:0x07af, B:54:0x07d4, B:23:0x0414, B:24:0x0554, B:21:0x02e6, B:22:0x0413, B:25:0x0555, B:27:0x0577, B:29:0x0585, B:30:0x058d, B:32:0x0593, B:33:0x05bb, B:34:0x060f, B:35:0x0666, B:39:0x0678, B:40:0x067d, B:42:0x068b, B:48:0x079e, B:46:0x06a7, B:47:0x079d, B:55:0x07d5, B:56:0x083c, B:57:0x083d, B:58:0x08ec), top: B:67:0x0006, inners: #0, #1 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public static synchronized void a(IIlIllIl1l iIlIllIl1l) {
        if (lII1llllI1.lI11llll1I(I1I1lI1II1.a(new byte[]{93, 16, 42}), 211649064L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{89, 49, 27, 18, 87, 81, 78, 98, 96, 60, 119, 113, 118, 124, 85, 80, 54, 57, 1, 88, 73, 47, 87, 12, 101, 101, 116, 10, 87, 74, 103, 82}));
        }
        if (iIlIllIl1l == null) {
            throw new II1l1lIllI(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 42, 95, 16, 69, 86, 71, 65, 1, 0, 16, 86, 17}));
        }
        I1lllI11II i1lllI11II = Il1IIlI1II.b.get(iIlIllIl1l.getPath());
        if (i1lllI11II != null) {
            iIlIllIl1l.setDestination(i1lllI11II.getDestination());
            iIlIllIl1l.setType(i1lllI11II.getType());
            iIlIllIl1l.setPriority(i1lllI11II.getPriority());
            iIlIllIl1l.setExtra(i1lllI11II.getExtra());
            Uri uri = iIlIllIl1l.getUri();
            if (uri != null) {
                Map<String, String> mapA = IllIIll11l.a(uri);
                Map<String, Integer> paramsType = i1lllI11II.getParamsType();
                if (ll1ll1lIl1.a(paramsType)) {
                    for (Map$Entry<String, Integer> map$Entry : paramsType.entrySet()) {
                        a(iIlIllIl1l, map$Entry.getValue(), map$Entry.getKey(), mapA.get(map$Entry.getKey()));
                    }
                    iIlIllIl1l.getExtras().putStringArray(I1I1lI1II1.a(new byte[]{64, 9, 42, 31, 5, 113, 3, 92, 118, 14, 5, 95, 1, 11, 0, 4}), (String[]) paramsType.keySet().toArray(new String[0]));
                }
                iIlIllIl1l.withString(I1I1lI1II1.a(new byte[]{121, 48, 7, 55, 51, 98, 65, 73, 92, 85, 8, 113, 94, 105, 80, 3, 37}), uri.toString());
            }
            int i = III1Il1II1.a[i1lllI11II.getType().ordinal()];
            if (i == 1) {
                Class<?> destination = i1lllI11II.getDestination();
                l1I11I11Il l1i11i11il = Il1IIlI1II.c.get(destination);
                if (l1i11i11il == null) {
                    try {
                        l1i11i11il = (l1I11I11Il) destination.getConstructor(new Class[0]).newInstance(new Object[0]);
                        l1i11i11il.init(b);
                        Il1IIlI1II.c.put(destination, l1i11i11il);
                    } catch (Exception e) {
                        I1l11I1I1I.c.error(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{126, 10, 11, 17, 66, 69, 69, 95, 79, 13, 84, 85, 71, 25, 82, 84, 11, 13, 7, 86, 17}), e);
                        throw new lIlIIlIII1(I1I1lI1II1.a(new byte[]{126, 10, 11, 17, 66, 69, 69, 95, 79, 13, 84, 85, 71, 25, 82, 84, 11, 13, 7, 86, 17}));
                    }
                }
                iIlIllIl1l.setProvider(l1i11i11il);
                iIlIllIl1l.greenChannel();
            } else if (i == 2) {
                iIlIllIl1l.greenChannel();
            }
            if (!l1lI1I1l11.I1II1111ll(1206661323L)) {
            }
        } else {
            if (!Il1IIlI1II.a.containsKey(iIlIllIl1l.getGroup())) {
                throw new II1l1lIllI(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 48, 88, 85, 71, 92, 20, 92, 17, 65, 12, 93, 16, 19, 92, 64, 71, 83, 23, 8, 5, 70, 84, 11, 23, 16, 10, 0, 66, 69, 86, 68, 81, 68, 107}) + iIlIllIl1l.getPath() + I1I1lI1II1.a(new byte[]{106, 72, 66, 12, 12, 21, 80, 66, 86, 17, 64, 16, 110}) + iIlIllIl1l.getGroup() + I1I1lI1II1.a(new byte[]{106}));
            }
            try {
                if (I1l11I1I1I.b()) {
                    I1l11I1I1I.c.debug(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), String.format(Locale.getDefault(), I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 5, 71, 88, 69, 73, 68, 107, 21, 70, 100, 20, 70, 22, 0, 16, 70, 67, 65, 95, 90, 82, 82, 94, 11, 3, 30, 23, 23, 69, 13, 5, 2, 7, 71, 23, 82, 64, 68, 107, 21, 70, 100}), iIlIllIl1l.getGroup(), iIlIllIl1l.getPath()));
                }
                a(iIlIllIl1l.getGroup(), (lll1I1I11l) null);
                if (I1l11I1I1I.b()) {
                    I1l11I1I1I.c.debug(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), String.format(Locale.getDefault(), I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 5, 71, 88, 69, 73, 68, 107, 21, 70, 100, 20, 93, 3, 18, 66, 83, 92, 19, 86, 84, 87, 79, 23, 7, 1, 87, 89, 67, 91, 11, 3, 1, 7, 81, 27, 16, 77, 22, 89, 87, 82, 92, 70, 21, 0, 24, 66, 105, 21, 18, 110}), iIlIllIl1l.getGroup(), iIlIllIl1l.getPath()));
                }
                a(iIlIllIl1l);
                if (!l1lI1I1l11.I1II1111ll(1206661323L)) {
                    throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{92, 19, 24, 21, 26, 123}));
                }
            } catch (Exception e2) {
                throw new lIlIIlIII1(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3, 34, 81, 68, 84, 85, 20, 80, 26, 2, 7, 66, 68, 8, 92, 91, 19, 65, 95, 0, 10, 18, 91, 12, 86, 0, 11, 11, 5, 21, 80, 66, 86, 17, 64, 16, 88, 92, 64, 84, 76, 65, 57}) + e2.getMessage() + I1I1lI1II1.a(new byte[]{106}));
            }
        }
    }

    private static void a(IIlIllIl1l iIlIllIl1l, Integer num, String str, String str2) {
        if (androidx.interpolator.view.animation.lIIlI111II.l1l11llIl1(7457)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{117, 43, 8, 43, 5, 4, 99, 6, 80, 51, 100, 120, 99, 109, 90, 94, 84}));
        }
        if (IllIIll11l.a(str) || IllIIll11l.a(str2)) {
            return;
        }
        try {
            if (num == null) {
                iIlIllIl1l.withString(str, str2);
            } else if (num.intValue() == III1IIII1l.BOOLEAN.ordinal()) {
                iIlIllIl1l.withBoolean(str, Boolean.parseBoolean(str2));
            } else if (num.intValue() == III1IIII1l.BYTE.ordinal()) {
                iIlIllIl1l.withByte(str, Byte.parseByte(str2));
            } else if (num.intValue() == III1IIII1l.SHORT.ordinal()) {
                iIlIllIl1l.withShort(str, Short.parseShort(str2));
            } else if (num.intValue() == III1IIII1l.INT.ordinal()) {
                iIlIllIl1l.withInt(str, Integer.parseInt(str2));
            } else if (num.intValue() == III1IIII1l.LONG.ordinal()) {
                iIlIllIl1l.withLong(str, Long.parseLong(str2));
            } else if (num.intValue() == III1IIII1l.FLOAT.ordinal()) {
                iIlIllIl1l.withFloat(str, Float.parseFloat(str2));
            } else if (num.intValue() == III1IIII1l.DOUBLE.ordinal()) {
                iIlIllIl1l.withDouble(str, Double.parseDouble(str2));
            } else if (num.intValue() == III1IIII1l.STRING.ordinal()) {
                iIlIllIl1l.withString(str, str2);
            } else if (num.intValue() != III1IIII1l.PARCELABLE.ordinal()) {
                if (num.intValue() == III1IIII1l.OBJECT.ordinal()) {
                    iIlIllIl1l.withString(str, str2);
                } else {
                    iIlIllIl1l.withString(str, str2);
                }
            }
        } catch (Throwable th) {
            I1l11I1I1I.c.warning(I1I1lI1II1.a(new byte[]{118, 54, 13, 16, 22, 80, 69, 10, 3}), I1I1lI1II1.a(new byte[]{123, 11, 5, 12, 17, 65, 94, 83, 74, 39, 85, 94, 65, 92, 70, 21, 17, 4, 22, 100, 81, 13, 70, 80, 19, 80, 86, 12, 8, 87, 83, 66, 23}) + th.getMessage());
        }
    }

    public static synchronized void a(String str, lll1I1I11l lll1i1i11l) throws IllegalAccessException, NoSuchMethodException, InstantiationException, InvocationTargetException {
        if (lII1llllI1.IlIllIll1I(729306304L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{101, 6, 8, 14, 35, 80, Byte.MAX_VALUE}));
        }
        if (Il1IIlI1II.a.containsKey(str)) {
            Il1IIlI1II.a.get(str).getConstructor(new Class[0]).newInstance(new Object[0]).loadInto(Il1IIlI1II.b);
            Il1IIlI1II.a.remove(str);
        }
        if (lll1i1i11l != null) {
            lll1i1i11l.loadInto(Il1IIlI1II.b);
        }
    }
}
