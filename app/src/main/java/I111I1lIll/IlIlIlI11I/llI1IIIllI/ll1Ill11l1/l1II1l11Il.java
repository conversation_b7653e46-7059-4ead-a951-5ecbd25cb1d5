package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.Ill11ll111;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import androidx.core.location.I111I11Ill;
import androidx.core.location.lIIlI111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.CharConversionException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import l1lI1IlllI.l111ll1lll.ll11llll11.lIIl11IIlI.lIIllIIlll;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class l1II1l11Il implements I1IIlllI1I<Uri, Bitmap> {
    private final lIIllIIlll a;
    private final lIllI1lIlI b;

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ IIll11lI1I<Bitmap> a(Uri uri, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws NumberFormatException, IOException {
        IIll11lI1I<Bitmap> iIll11lI1IA = a(uri, i, i2, iIIll1l1l1);
        if (I111I11Ill.lI11llll1I(I1I1lI1II1.a(new byte[]{115, 39, 48, 7, 47, 92, 6, 99, 67, 35, 1, 115}), 302913601L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 50, 19, 18, 91, 91, 115, 72, 92, 42, 71, 88, Byte.MAX_VALUE, 88, 114, 122}));
        }
        return iIll11lI1IA;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ boolean a(Uri uri, IIIll1l1l1 iIIll1l1l1) throws IOException, KeyManagementException {
        boolean zA = a(uri, iIIll1l1l1);
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{79, 43, 82, 2, 19, 96, 98, 120, 92, 54, Byte.MAX_VALUE}), 418209685L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{122, 11, 54, 17, 53, 100, 95, 83, 109, 39, 0, 71, 118, 95, 124, 111, 46, 82, 0, 100, 88}));
        }
        return zA;
    }

    public l1II1l11Il(lIIllIIlll liilliilll, lIllI1lIlI lilli1lili) {
        this.a = liilliilll;
        this.b = lilli1lili;
    }

    public boolean a(Uri uri, IIIll1l1l1 iIIll1l1l1) throws CharConversionException {
        if (Ill11ll111.l111l1I1Il(I1I1lI1II1.a(new byte[]{14, 53, 81, 2, 21, 6, 115, 124, 106, 9, 118, 122, 80, 104, 99, 102, 15, 11, 6, 103, 119}), 793540600L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{122, 84, 38}));
        }
        return I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 75, 1, 67, 95, 64, 75, 87, 80}).equals(uri.getScheme());
    }

    public IIll11lI1I<Bitmap> a(Uri uri, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws NumberFormatException, FileNotFoundException {
        if (lIIlI111II.ll1I111ll1(9522)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{118, 15, 19, 34, 91, 87, Byte.MAX_VALUE, 102, 67, 60, 1, 124, 1, 106, 90, 77, 59, 39, 20, 107, 6, 80, 103, 121, 71, 3, 126}));
        }
        IIll11lI1I<Drawable> iIll11lI1IA = this.a.a(uri, i, i2, iIIll1l1l1);
        if (iIll11lI1IA == null) {
            return null;
        }
        return llI1lIlIlI.a(this.b, iIll11lI1IA.d(), i, i2);
    }
}
