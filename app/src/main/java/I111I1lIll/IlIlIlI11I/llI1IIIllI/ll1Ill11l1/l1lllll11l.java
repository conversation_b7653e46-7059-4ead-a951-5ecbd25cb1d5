package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I111I1IlII;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Il111lllll;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.l11Il1lI11;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIIllIlIl1;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.llIl1IlllI;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lIIIIII11I;
import android.graphics.Bitmap;
import android.graphics.Bitmap$Config;
import android.graphics.BitmapFactory;
import android.graphics.BitmapFactory$Options;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.DisplayMetrics;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.I11II1l1lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.InvalidClassException;
import java.io.NotActiveException;
import java.io.ObjectStreamException;
import java.io.SyncFailedException;
import java.io.UnsupportedEncodingException;
import java.net.PortUnreachableException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.security.DigestException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertStoreException;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.lllllIll1l;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public final class l1lllll11l {
    private final lIllI1lIlI l;
    private final DisplayMetrics m;
    private final lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I n;
    private final List<Il111lllll> o;
    private final II1IIl1I1I p = II1IIl1I1I.a();
    static final String a = I1I1lI1II1.a(new byte[]{115, 11, 21, 11, 17, 84, 90, 64, 85, 1, 66});
    private static final String f = I1I1lI1II1.a(new byte[]{94, 9, 3, 2, 7, 26, 65, 94, 93, 74, 71, 81, 69, 23, 67, 87, 15, 17});
    private static final String g = I1I1lI1II1.a(new byte[]{94, 9, 3, 2, 7, 26, 79, 29, 80, 7, 95});
    public static final l11Il1lI11<llIl1IlllI> b = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 125, 11, 71, 94, 70, 88, 89, 69, 14, 4, 16, 28, 116, 4, 80, 90, 87, 83, 113, 10, 22, 95, 86, 23}), llIl1IlllI.DEFAULT);

    @Deprecated
    public static final l11Il1lI11<I11II1111l> c = I11II1111l.h;
    public static final l11Il1lI11<Boolean> d = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 125, 11, 71, 94, 70, 88, 89, 69, 14, 4, 16, 28, 118, 8, 75, 119, 90, 66, 90, 4, 20, 97, 94, 25, 82}), false);
    public static final l11Il1lI11<Boolean> e = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 68, 73, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 125, 11, 71, 94, 70, 88, 89, 69, 14, 4, 16, 28, 113, 13, 95, 90, 68, 126, 86, 23, 0, 69, 86, 17, 82, 32, 7, 6, 13, 81, 82}));
    private static final Set<String> h = Collections.unmodifiableSet(new HashSet(Arrays.asList(I1I1lI1II1.a(new byte[]{94, 9, 3, 2, 7, 26, 65, 94, 93, 74, 71, 81, 69, 23, 67, 87, 15, 17}), I1I1lI1II1.a(new byte[]{94, 9, 3, 2, 7, 26, 79, 29, 80, 7, 95}))));
    private static final l1l1I1l1lI i = new llI1l11IlI();
    private static final Set<lIIllIlIl1> j = Collections.unmodifiableSet(EnumSet.of(lIIllIlIl1.JPEG, lIIllIlIl1.PNG_A, lIIllIlIl1.PNG));
    private static final Queue<BitmapFactory$Options> k = llIIlI1llI.a(0);

    private boolean a(lIIllIlIl1 liillilil1) {
        if (!Il1lII1l1l.IlII1Illll(4752)) {
            return true;
        }
        Log.e(I1I1lI1II1.a(new byte[]{84, 87, 50, 10, 35, 0, 82, 8, Byte.MAX_VALUE, 12, 102, 3, 89, 85, Byte.MAX_VALUE, 86, 56, 57, 40, 2}), I1I1lI1II1.a(new byte[]{67, 86, 47, 36, 54, 96, 93, 101, 15, 19, 92, 83, 99, 117, 118, 115, 13, 13, 49, 90}));
        return false;
    }

    private static int c(double d2) {
        return (int) (d2 + 0.5d);
    }

    public boolean a(InputStream inputStream) {
        return true;
    }

    public l1lllll11l(List<Il111lllll> list, DisplayMetrics displayMetrics, lIllI1lIlI lilli1lili, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) {
        this.o = list;
        this.m = (DisplayMetrics) llllIllIII.a(displayMetrics);
        this.l = (lIllI1lIlI) llllIllIII.a(lilli1lili);
        this.n = (lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) llllIllIII.a(il1IllIl1I);
    }

    public boolean a(ByteBuffer byteBuffer) throws NotActiveException {
        if (lII1llllI1.l11I11I11l(517953682L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{5, 28, 45, 47, 58, 86, 95, 126, 86, 42, 67, 81, 3, 9, 5, 82, 46, 47, 47, 87, 3, 15, 85, 4, 106, 124, 69, 93, 80, 103, 6, 11}));
        }
        return true;
    }

    public IIll11lI1I<Bitmap> a(InputStream inputStream, int i2, int i3, IIIll1l1l1 iIIll1l1l1) throws ClassNotFoundException, IOException {
        IIll11lI1I<Bitmap> iIll11lI1IA = a(inputStream, i2, i3, iIIll1l1l1, i);
        if (ll1l11I1II.IlII1Illll(172447869L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{126, 48, 32, 1, 81, 108, 111, 81, 105, 21}));
        }
        return iIll11lI1IA;
    }

    public IIll11lI1I<Bitmap> a(InputStream inputStream, int i2, int i3, IIIll1l1l1 iIIll1l1l1, l1l1I1l1lI l1l1i1l1li) throws ClassNotFoundException, IOException {
        llllIllIII.a(inputStream.markSupported(), I1I1lI1II1.a(new byte[]{110, 11, 23, 69, 15, 64, 68, 68, 25, 20, 66, 95, 67, 80, 80, 80, 66, 0, 12, 18, 121, 15, 67, 64, 71, 101, 67, 23, 1, 83, 90, 67, 67, 12, 3, 17, 66, 70, 66, 64, 73, 11, 66, 68, 70, 25, 89, 84, 16, 10, 74, 27}));
        byte[] bArr = (byte[]) this.n.a(65536, byte[].class);
        BitmapFactory$Options bitmapFactory$OptionsA = a();
        bitmapFactory$OptionsA.inTempStorage = bArr;
        llIl1IlllI llil1illli = (llIl1IlllI) iIIll1l1l1.a(b);
        I11II1111l i11II1111l = (I11II1111l) iIIll1l1l1.a(I11II1111l.h);
        boolean zBooleanValue = ((Boolean) iIIll1l1l1.a(d)).booleanValue();
        l11Il1lI11<Boolean> l11il1li11 = e;
        try {
            return lI1l11lIIl.a(a(inputStream, bitmapFactory$OptionsA, i11II1111l, llil1illli, llil1illli == llIl1IlllI.PREFER_ARGB_8888_DISALLOW_HARDWARE ? false : iIIll1l1l1.a(l11il1li11) != null && ((Boolean) iIIll1l1l1.a(l11il1li11)).booleanValue(), i2, i3, zBooleanValue, l1l1i1l1li), this.l);
        } finally {
            c(bitmapFactory$OptionsA);
            this.n.a((lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) bArr);
        }
    }

    private Bitmap a(InputStream inputStream, BitmapFactory$Options bitmapFactory$Options, I11II1111l i11II1111l, llIl1IlllI llil1illli, boolean z, int i2, int i3, boolean z2, l1l1I1l1lI l1l1i1l1li) throws InterruptedException, DigestException, IOException, InvalidKeyException, ClassNotFoundException, CertStoreException {
        int i4;
        int iRound;
        int iRound2;
        long jA = lllllIll1l.a();
        int[] iArrA = a(inputStream, bitmapFactory$Options, l1l1i1l1li, this.l);
        int i5 = iArrA[0];
        int i6 = iArrA[1];
        String str = bitmapFactory$Options.outMimeType;
        boolean z3 = (i5 == -1 || i6 == -1) ? false : z;
        int iB = I111I1IlII.b(this.o, inputStream, this.n);
        int iA = II1111I11I.a(iB);
        boolean zB = II1111I11I.b(iB);
        int i7 = i2 == Integer.MIN_VALUE ? i5 : i2;
        int i8 = i3 == Integer.MIN_VALUE ? i6 : i3;
        lIIllIlIl1 liillilil1A = I111I1IlII.a(this.o, inputStream, this.n);
        a(liillilil1A, inputStream, l1l1i1l1li, this.l, i11II1111l, iA, i5, i6, i7, i8, bitmapFactory$Options);
        a(inputStream, llil1illli, z3, zB, bitmapFactory$Options, i7, i8);
        int i9 = bitmapFactory$Options.inSampleSize;
        if (a(liillilil1A)) {
            if (i5 < 0 || i6 < 0 || !z2) {
                float f2 = a(bitmapFactory$Options) ? bitmapFactory$Options.inTargetDensity / bitmapFactory$Options.inDensity : 1.0f;
                int i10 = bitmapFactory$Options.inSampleSize;
                float f3 = i5;
                float f4 = i10;
                int iCeil = (int) Math.ceil(f3 / f4);
                int iCeil2 = (int) Math.ceil(i6 / f4);
                iRound = Math.round(iCeil * f2);
                iRound2 = Math.round(iCeil2 * f2);
                String str2 = a;
                if (Log.isLoggable(str2, 2)) {
                    Log.v(str2, I1I1lI1II1.a(new byte[]{116, 5, 14, 6, 23, 89, 86, 68, 92, 0, 16, 68, 84, 75, 83, 80, 22, 65, 57}) + iRound + I1I1lI1II1.a(new byte[]{79}) + iRound2 + I1I1lI1II1.a(new byte[]{106, 68, 4, 10, 16, 21, 68, 95, 76, 22, 83, 85, 21, 98}) + i5 + I1I1lI1II1.a(new byte[]{79}) + i6 + I1I1lI1II1.a(new byte[]{106, 72, 66, 22, 3, 88, 71, 92, 92, 55, 89, 74, 80, 3, 20}) + i10 + I1I1lI1II1.a(new byte[]{27, 68, 22, 4, 16, 82, 82, 68, 125, 1, 94, 67, 92, 77, 77, 15, 66}) + bitmapFactory$Options.inTargetDensity + I1I1lI1II1.a(new byte[]{27, 68, 6, 0, 12, 70, 94, 68, 64, 94, 16}) + bitmapFactory$Options.inDensity + I1I1lI1II1.a(new byte[]{27, 68, 6, 0, 12, 70, 94, 68, 64, 68, 93, 69, 89, 77, 93, 69, 14, 8, 7, 64, 10, 65}) + f2);
                }
            } else {
                iRound = i7;
                iRound2 = i8;
            }
            if (iRound > 0 && iRound2 > 0) {
                a(bitmapFactory$Options, this.l, iRound, iRound2);
            }
        }
        Bitmap bitmapB = b(inputStream, bitmapFactory$Options, l1l1i1l1li, this.l);
        l1l1i1l1li.a(this.l, bitmapB);
        if (Log.isLoggable(a, 2)) {
            i4 = iB;
            a(i5, i6, str, bitmapFactory$Options, bitmapB, i2, i3, jA);
        } else {
            i4 = iB;
        }
        if (bitmapB == null) {
            return null;
        }
        bitmapB.setDensity(this.m.densityDpi);
        Bitmap bitmapA = II1111I11I.a(this.l, bitmapB, i4);
        if (bitmapB.equals(bitmapA)) {
            return bitmapA;
        }
        this.l.a(bitmapB);
        return bitmapA;
    }

    private static void a(lIIllIlIl1 liillilil1, InputStream inputStream, l1l1I1l1lI l1l1i1l1li, lIllI1lIlI lilli1lili, I11II1111l i11II1111l, int i2, int i3, int i4, int i5, int i6, BitmapFactory$Options bitmapFactory$Options) throws IOException, InvalidKeyException {
        float fA;
        int iMin;
        int iFloor;
        int iFloor2;
        if (i3 <= 0 || i4 <= 0) {
            String str = a;
            if (Log.isLoggable(str, 3)) {
                Log.d(str, I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 84, 85, 65, 92, 70, 88, 11, 15, 7, 18, 84, 8, 94, 80, 93, 69, 94, 10, 10, 65, 23, 5, 88, 22, 88, 69}) + liillilil1 + I1I1lI1II1.a(new byte[]{23, 19, 11, 17, 10, 21, 67, 81, 75, 3, 85, 68, 21, 98}) + i5 + I1I1lI1II1.a(new byte[]{79}) + i6 + I1I1lI1II1.a(new byte[]{106}));
                return;
            }
            return;
        }
        if (i2 == 90 || i2 == 270) {
            fA = i11II1111l.a(i4, i3, i5, i6);
        } else {
            fA = i11II1111l.a(i3, i4, i5, i6);
        }
        if (fA <= 0.0f) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 67, 90, 5, 92, 85, 21, 78, 93, 65, 10, 65, 4, 83, 83, 21, 92, 71, 9, 22}) + fA + I1I1lI1II1.a(new byte[]{23, 2, 16, 10, 15, 15, 23}) + i11II1111l + I1I1lI1II1.a(new byte[]{27, 68, 17, 10, 23, 71, 84, 85, 3, 68, 107}) + i3 + I1I1lI1II1.a(new byte[]{79}) + i4 + I1I1lI1II1.a(new byte[]{106, 72, 66, 17, 3, 71, 80, 85, 77, 94, 16, 107}) + i5 + I1I1lI1II1.a(new byte[]{79}) + i6 + I1I1lI1II1.a(new byte[]{106}));
        }
        I1IIIIIlI1 i1IIIIIlI1B = i11II1111l.b(i3, i4, i5, i6);
        if (i1IIIIIlI1B == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 66, 86, 17, 94, 84, 21, 78, 93, 65, 10, 65, 12, 71, 92, 13, 19, 71, 92, 67, 89, 1, 13, 92, 80}));
        }
        float f2 = i3;
        float f3 = i4;
        int iC = i3 / c(fA * f2);
        int iC2 = i4 / c(fA * f3);
        if (i1IIIIIlI1B == I1IIIIIlI1.MEMORY) {
            iMin = Math.max(iC, iC2);
        } else {
            iMin = Math.min(iC, iC2);
        }
        int iMax = Math.max(1, Integer.highestOneBit(iMin));
        if (i1IIIIIlI1B == I1IIIIIlI1.MEMORY && iMax < 1.0f / fA) {
            iMax <<= 1;
        }
        bitmapFactory$Options.inSampleSize = iMax;
        if (liillilil1 == lIIllIlIl1.JPEG) {
            float fMin = Math.min(iMax, 8);
            iFloor = (int) Math.ceil(f2 / fMin);
            iFloor2 = (int) Math.ceil(f3 / fMin);
            int i7 = iMax / 8;
            if (i7 > 0) {
                iFloor /= i7;
                iFloor2 /= i7;
            }
        } else if (liillilil1 == lIIllIlIl1.PNG || liillilil1 == lIIllIlIl1.PNG_A) {
            float f4 = iMax;
            iFloor = (int) Math.floor(f2 / f4);
            iFloor2 = (int) Math.floor(f3 / f4);
        } else if (liillilil1 == lIIllIlIl1.WEBP || liillilil1 == lIIllIlIl1.WEBP_A) {
            float f5 = iMax;
            iFloor = Math.round(f2 / f5);
            iFloor2 = Math.round(f3 / f5);
        } else if (i3 % iMax != 0 || i4 % iMax != 0) {
            int[] iArrA = a(inputStream, bitmapFactory$Options, l1l1i1l1li, lilli1lili);
            int i8 = iArrA[0];
            iFloor2 = iArrA[1];
            iFloor = i8;
        } else {
            iFloor = i3 / iMax;
            iFloor2 = i4 / iMax;
        }
        double dA = i11II1111l.a(iFloor, iFloor2, i5, i6);
        bitmapFactory$Options.inTargetDensity = a(dA);
        bitmapFactory$Options.inDensity = b(dA);
        if (a(bitmapFactory$Options)) {
            bitmapFactory$Options.inScaled = true;
        } else {
            bitmapFactory$Options.inTargetDensity = 0;
            bitmapFactory$Options.inDensity = 0;
        }
        String str2 = a;
        if (Log.isLoggable(str2, 2)) {
            Log.v(str2, I1I1lI1II1.a(new byte[]{116, 5, 14, 6, 23, 89, 86, 68, 92, 68, 67, 83, 84, 85, 93, 91, 5, 77, 66, 65, 95, 20, 65, 86, 86, 12, 23, 62}) + i3 + I1I1lI1II1.a(new byte[]{79}) + i4 + I1I1lI1II1.a(new byte[]{106, 72, 66, 17, 3, 71, 80, 85, 77, 94, 16, 107}) + i5 + I1I1lI1II1.a(new byte[]{79}) + i6 + I1I1lI1II1.a(new byte[]{106, 72, 66, 21, 13, 66, 82, 66, 25, 11, 86, 16, 65, 78, 91, 21, 17, 2, 3, 94, 85, 5, 9, 21, 104}) + iFloor + I1I1lI1II1.a(new byte[]{79}) + iFloor2 + I1I1lI1II1.a(new byte[]{106, 72, 66, 0, 26, 84, 84, 68, 25, 23, 83, 81, 89, 92, 20, 83, 3, 2, 22, 93, 66, 91, 19}) + fA + I1I1lI1II1.a(new byte[]{27, 68, 18, 10, 21, 80, 69, 16, 86, 2, 16, 2, 21, 74, 85, 88, 18, 13, 7, 18, 67, 8, 73, 80, 9, 22}) + iMax + I1I1lI1II1.a(new byte[]{27, 68, 3, 1, 8, 64, 68, 68, 92, 0, 16, 67, 86, 88, 88, 80, 66, 7, 3, 81, 68, 14, 65, 15, 19}) + dA + I1I1lI1II1.a(new byte[]{27, 68, 22, 4, 16, 82, 82, 68, 25, 0, 85, 94, 70, 80, 64, 76, 88, 65}) + bitmapFactory$Options.inTargetDensity + I1I1lI1II1.a(new byte[]{27, 68, 6, 0, 12, 70, 94, 68, 64, 94, 16}) + bitmapFactory$Options.inDensity);
        }
    }

    private static int a(double d2) {
        return c((d2 / (r1 / r0)) * c(b(d2) * d2));
    }

    private static int b(double d2) {
        if (d2 > 1.0d) {
            d2 = 1.0d / d2;
        }
        return (int) Math.round(d2 * 2.147483647E9d);
    }

    private void a(InputStream inputStream, llIl1IlllI llil1illli, boolean z, boolean z2, BitmapFactory$Options bitmapFactory$Options, int i2, int i3) throws SocketTimeoutException, ClassNotFoundException {
        boolean zHasAlpha;
        if (this.p.a(i2, i3, bitmapFactory$Options, llil1illli, z, z2)) {
            if (I11II1l1lI.IlIIlIllI1(I1I1lI1II1.a(new byte[]{101, 16, 1, 19, 41, 12, 103, 87, 77, 92, 100, 3, 114, 13, 12}), I1I1lI1II1.a(new byte[]{94, 6, 9, 13, 52, 112, 99, 65, 73, 40, 125, 97, 2, 84, 115, 92, 6, 85, 84, 107, 116, 84, 73}))) {
                throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{88, 55, 49, 34, 39, 77, 112, 113, 122, 18, 9, 64, 122, 115, 12, 122, 24, 56, 21, 67, 113, 49, 90, 71, 121, 68, 4, 17, 17, 83, 99}));
            }
            return;
        }
        if (llil1illli == llIl1IlllI.PREFER_ARGB_8888 || llil1illli == llIl1IlllI.PREFER_ARGB_8888_DISALLOW_HARDWARE) {
            bitmapFactory$Options.inPreferredConfig = Bitmap$Config.ARGB_8888;
            return;
        }
        try {
            zHasAlpha = I111I1IlII.a(this.o, inputStream, this.n).hasAlpha();
        } catch (IOException e2) {
            String str = a;
            if (Log.isLoggable(str, 3)) {
                Log.d(str, I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 84, 92, 16, 85, 66, 88, 80, 90, 80, 66, 22, 10, 87, 68, 9, 86, 71, 19, 66, 95, 0, 68, 91, 90, 2, 80, 1, 66, 13, 3, 70, 23, 81, 85, 20, 88, 81, 21, 86, 70, 21, 12, 14, 22, 18, 86, 19, 92, 88, 19, 94, 82, 4, 0, 87, 69, 79, 23, 2, 13, 23, 15, 84, 67, 16}) + llil1illli, e2);
            }
            zHasAlpha = false;
        }
        bitmapFactory$Options.inPreferredConfig = zHasAlpha ? Bitmap$Config.ARGB_8888 : Bitmap$Config.RGB_565;
        if (bitmapFactory$Options.inPreferredConfig == Bitmap$Config.RGB_565) {
            bitmapFactory$Options.inDither = true;
        }
        if (I1I1IIIIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{90, 10, 48, 14, 8, 113, 94, 5, 107, 39, 93, 126, 108, 126, 7, 65, 13, 3, 38, 2, 120, 12, 118, 115, 69, 126}), 5279)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{86, 33, 17, 43, 33, 126, 89, 68, 94, 9, 86, 81}));
        }
    }

    private static int[] a(InputStream inputStream, BitmapFactory$Options bitmapFactory$Options, l1l1I1l1lI l1l1i1l1li, lIllI1lIlI lilli1lili) throws IOException, InvalidKeyException {
        bitmapFactory$Options.inJustDecodeBounds = true;
        b(inputStream, bitmapFactory$Options, l1l1i1l1li, lilli1lili);
        bitmapFactory$Options.inJustDecodeBounds = false;
        return new int[]{bitmapFactory$Options.outWidth, bitmapFactory$Options.outHeight};
    }

    private static Bitmap b(InputStream inputStream, BitmapFactory$Options bitmapFactory$Options, l1l1I1l1lI l1l1i1l1li, lIllI1lIlI lilli1lili) throws IOException, InvalidKeyException {
        if (Ill11ll111.ll1I1lII11(I1I1lI1II1.a(new byte[]{98, 16, 20, 46, 51, 111, 120, 124, 77, 1, 69, 74, 0, 114, 121, 114}))) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{93, 23, 16, 61, 84, 112, 118, 105, 125, 41, 6, 114, 2, 8, 95, 116, 83, 56, 52, 113, 68, 14, 10, Byte.MAX_VALUE, 84, 126, 6}));
        }
        if (bitmapFactory$Options.inJustDecodeBounds) {
            inputStream.mark(10485760);
        } else {
            l1l1i1l1li.a();
        }
        int i2 = bitmapFactory$Options.outWidth;
        int i3 = bitmapFactory$Options.outHeight;
        String str = bitmapFactory$Options.outMimeType;
        II1111I11I.a().lock();
        try {
            try {
                Bitmap bitmapDecodeStream = BitmapFactory.decodeStream(inputStream, null, bitmapFactory$Options);
                II1111I11I.a().unlock();
                if (bitmapFactory$Options.inJustDecodeBounds) {
                    inputStream.reset();
                }
                if (lIIIIII11I.Il1IIlI1II(IronSourceConstants.BN_INSTANCE_UNEXPECTED_RELOAD_SUCCESS)) {
                    throw new SyncFailedException(I1I1lI1II1.a(new byte[]{82, 60, 81, 35, 40, 68, 110, 2, 117, 51, 93, 103}));
                }
                return bitmapDecodeStream;
            } catch (IllegalArgumentException e2) {
                IOException iOExceptionA = a(e2, i2, i3, str, bitmapFactory$Options);
                if (Log.isLoggable(a, 3)) {
                    Log.d(a, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 84, 85, 86, 86, 80, 80, 66, 22, 11, 70, 88, 65, 90, 91, 113, 95, 67, 8, 5, 66, 27, 67, 67, 22, 27, 12, 12, 82, 23, 81, 94, 5, 89, 94, 21, 78, 93, 65, 10, 14, 23, 70, 16, 35, 90, 65, 94, 87, 71, 69, 22, 87, 26, 22, 68, 1}), iOExceptionA);
                }
                if (bitmapFactory$Options.inBitmap == null) {
                    throw iOExceptionA;
                }
                try {
                    inputStream.reset();
                    lilli1lili.a(bitmapFactory$Options.inBitmap);
                    bitmapFactory$Options.inBitmap = null;
                    Bitmap bitmapB = b(inputStream, bitmapFactory$Options, l1l1i1l1li, lilli1lili);
                    II1111I11I.a().unlock();
                    return bitmapB;
                } catch (IOException unused) {
                    throw iOExceptionA;
                }
            }
        } catch (Throwable th) {
            II1111I11I.a().unlock();
            throw th;
        }
    }

    private static boolean a(BitmapFactory$Options bitmapFactory$Options) throws CertPathBuilderException, ObjectStreamException {
        boolean z = false;
        if (Il1IIllIll.ll1I1lII11(I1I1lI1II1.a(new byte[]{90, 32}), 188528693L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{121}));
        }
        if (bitmapFactory$Options.inTargetDensity > 0 && bitmapFactory$Options.inDensity > 0 && bitmapFactory$Options.inTargetDensity != bitmapFactory$Options.inDensity) {
            z = true;
        }
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{96, 3, 48}), 7641)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{6, 49, 23, 80, 85, 71, 91, 5, 10, 44}));
        }
        return z;
    }

    private static void a(int i2, int i3, String str, BitmapFactory$Options bitmapFactory$Options, Bitmap bitmap, int i4, int i5, long j2) throws UnsupportedEncodingException {
        if (androidx.recyclerview.widget.content.adapter.l11Il1lI11.Il1IIlI1II(I1I1lI1II1.a(new byte[]{66, 60, 58, 52, 90, 123, 90, 71, 13, 84, 122, 119, 5, 115, 82}))) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{5, 3, 52, 31, 53, 92, 123, 94}));
        }
        Log.v(a, I1I1lI1II1.a(new byte[]{115, 1, 1, 10, 6, 80, 83, 16}) + a(bitmap) + I1I1lI1II1.a(new byte[]{23, 2, 16, 10, 15, 21, 108}) + i2 + I1I1lI1II1.a(new byte[]{79}) + i3 + I1I1lI1II1.a(new byte[]{106, 68}) + str + I1I1lI1II1.a(new byte[]{23, 19, 11, 17, 10, 21, 94, 94, 123, 13, 68, 93, 84, 73, 20}) + b(bitmapFactory$Options) + I1I1lI1II1.a(new byte[]{23, 2, 13, 23, 66, 110}) + i4 + I1I1lI1II1.a(new byte[]{79}) + i5 + I1I1lI1II1.a(new byte[]{106, 72, 66, 22, 3, 88, 71, 92, 92, 68, 67, 89, 79, 92, 14, 21}) + bitmapFactory$Options.inSampleSize + I1I1lI1II1.a(new byte[]{27, 68, 6, 0, 12, 70, 94, 68, 64, 94, 16}) + bitmapFactory$Options.inDensity + I1I1lI1II1.a(new byte[]{27, 68, 22, 4, 16, 82, 82, 68, 25, 0, 85, 94, 70, 80, 64, 76, 88, 65}) + bitmapFactory$Options.inTargetDensity + I1I1lI1II1.a(new byte[]{27, 68, 22, 13, 16, 80, 86, 84, 3, 68}) + Thread.currentThread().getName() + I1I1lI1II1.a(new byte[]{27, 68, 6, 16, 16, 84, 67, 89, 86, 10, 10, 16}) + lllllIll1l.a(j2));
    }

    private static String b(BitmapFactory$Options bitmapFactory$Options) {
        if (lIIlI111II.llII1ll111(169995054L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{67, 49, 11, 10, 7, 122, 7, 90, 105, 35, 65, 4, 77, 108, 13, 124, 56, 16, 56, 7, 117, 21, 80, 80, 82, 0, 99}));
        }
        return a(bitmapFactory$Options.inBitmap);
    }

    private static String a(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        return I1I1lI1II1.a(new byte[]{108}) + bitmap.getWidth() + I1I1lI1II1.a(new byte[]{79}) + bitmap.getHeight() + I1I1lI1II1.a(new byte[]{106, 68}) + bitmap.getConfig() + (I1I1lI1II1.a(new byte[]{23, 76}) + bitmap.getAllocationByteCount() + I1I1lI1II1.a(new byte[]{30}));
    }

    private static IOException a(IllegalArgumentException illegalArgumentException, int i2, int i3, String str, BitmapFactory$Options bitmapFactory$Options) throws InvalidClassException {
        IOException iOException = new IOException(I1I1lI1II1.a(new byte[]{114, 28, 1, 0, 18, 65, 94, 95, 87, 68, 84, 85, 86, 86, 80, 92, 12, 6, 66, 80, 89, 21, 94, 84, 67, 26, 23, 10, 17, 70, 96, 10, 83, 16, 10, 95, 66}) + i2 + I1I1lI1II1.a(new byte[]{27, 68, 13, 16, 22, 125, 82, 89, 94, 12, 68, 10, 21}) + i3 + I1I1lI1II1.a(new byte[]{27, 68, 13, 16, 22, 120, 94, 93, 92, 48, 73, 64, 80, 3, 20}) + str + I1I1lI1II1.a(new byte[]{27, 68, 11, 11, 32, 92, 67, 93, 88, 20, 10, 16}) + b(bitmapFactory$Options), illegalArgumentException);
        if (android.media.content.lIIllIlIl1.I111IlIl1I(294140112L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{95, 6, 84, 1, 0, 91, 116, 121, 78, 50, 6, 3, 102, 80, 119, 86, 27}));
        }
        return iOException;
    }

    private static void a(BitmapFactory$Options bitmapFactory$Options, lIllI1lIlI lilli1lili, int i2, int i3) throws DigestException, InterruptedIOException {
        Bitmap$Config bitmap$Config;
        if (androidx.core.location.lIIlI111II.IlIlII11Il(5948)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{116, 81, 19, 39, 10, 79, 113, 102, 114, 51, 4, 104, 71, 95, 70, 118, 11, 51, 32, 7, 6, 88, 75, 81, 102, 87}));
        }
        if (Build$VERSION.SDK_INT < 26) {
            bitmap$Config = null;
        } else if (bitmapFactory$Options.inPreferredConfig == Bitmap$Config.HARDWARE) {
            return;
        } else {
            bitmap$Config = bitmapFactory$Options.outConfig;
        }
        if (bitmap$Config == null) {
            bitmap$Config = bitmapFactory$Options.inPreferredConfig;
        }
        bitmapFactory$Options.inBitmap = lilli1lili.b(i2, i3, bitmap$Config);
        if (II1I11IlI1.I111IlIl1I(I1I1lI1II1.a(new byte[]{71, 45, 9, 51, 51}), 239980725L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{92, 29, 20, 18, 44, 115, 111, 119, 123}));
        }
    }

    private static synchronized BitmapFactory$Options a() {
        BitmapFactory$Options bitmapFactory$OptionsPoll;
        if (androidx.constraintlayout.widget.I1IllIll1l.l11I11I11l(243540184L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{15, 32, 5, 17}));
        }
        Queue<BitmapFactory$Options> queue = k;
        synchronized (queue) {
            bitmapFactory$OptionsPoll = queue.poll();
        }
        if (bitmapFactory$OptionsPoll == null) {
            bitmapFactory$OptionsPoll = new BitmapFactory$Options();
            d(bitmapFactory$OptionsPoll);
        }
        return bitmapFactory$OptionsPoll;
    }

    private static void c(BitmapFactory$Options bitmapFactory$Options) {
        d(bitmapFactory$Options);
        Queue<BitmapFactory$Options> queue = k;
        synchronized (queue) {
            queue.offer(bitmapFactory$Options);
        }
        if (android.media.content.II1I11IlI1.IlIIl111lI(I1I1lI1II1.a(new byte[]{15, 8, 82, 84, 82, 83, 14, 71, 90, 14, 68, 100, 67, 120, 108, 94, 32, 49, 17, 5, 82, 7}))) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{121, 87, 9, 48, 40, 13, 78, 99, 73, 30, 81, 7, 123, 96, 78, 77, 20, 44, 37, 121, 99, 19, 90, 120, 125, 4, 79, 23, 84, 66, 102, 45}));
        }
    }

    private static void d(BitmapFactory$Options bitmapFactory$Options) {
        bitmapFactory$Options.inTempStorage = null;
        bitmapFactory$Options.inDither = false;
        bitmapFactory$Options.inScaled = false;
        bitmapFactory$Options.inSampleSize = 1;
        bitmapFactory$Options.inPreferredConfig = null;
        bitmapFactory$Options.inJustDecodeBounds = false;
        bitmapFactory$Options.inDensity = 0;
        bitmapFactory$Options.inTargetDensity = 0;
        bitmapFactory$Options.outWidth = 0;
        bitmapFactory$Options.outHeight = 0;
        bitmapFactory$Options.outMimeType = null;
        bitmapFactory$Options.inBitmap = null;
        bitmapFactory$Options.inMutable = true;
    }
}
