package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.accounts.utils.lIIIIII11I;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class I1l111l1l1 extends I11II1111l {
    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) {
        return 1.0f;
    }

    I1l111l1l1() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) throws BrokenBarrierException {
        if (lIIIIII11I.IllIlI1l1I(I1I1lI1II1.a(new byte[]{78, 44, 8, 50, 22, 89, 91, Byte.MAX_VALUE, 116, 15, 122, 86, 69, 8, Byte.MAX_VALUE, 68, 52, 17, 15, 10, 82, 20, 3, 125, 107, 78, 84, 54, 22}), 965329060L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{7, 10, 3, 63, 48, 121, 97, 64, 87, 14, 69, 8, 82, 123, 89, 88, 36, 82}));
        }
        return I1IIIIIlI1.QUALITY;
    }
}
