package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.llIl1IlllI;
import android.graphics.Bitmap$Config;
import android.graphics.BitmapFactory$Options;
import android.os.Build$VERSION;
import android.util.Log;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.File;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class II1IIl1I1I {
    private static final File a = new File(I1I1lI1II1.a(new byte[]{24, 20, 16, 10, 1, 26, 68, 85, 85, 2, 31, 86, 81}));
    private static volatile II1IIl1I1I d;
    private volatile int b;
    private volatile boolean c = true;

    static II1IIl1I1I a() {
        if (d == null) {
            synchronized (II1IIl1I1I.class) {
                if (d == null) {
                    d = new II1IIl1I1I();
                }
            }
        }
        return d;
    }

    private II1IIl1I1I() {
    }

    boolean a(int i, int i2, BitmapFactory$Options bitmapFactory$Options, llIl1IlllI llil1illli, boolean z, boolean z2) {
        if (l1lI1I1l11.IlIllIll1I(334011195L)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{115, 23, 56, 2, 10, 108, 64, 125, 10, 49, 100, 118, 3, 79, 4, 101, 13, 55, 5, 80, 81}));
        }
        if (!z || Build$VERSION.SDK_INT < 26 || llil1illli == llIl1IlllI.PREFER_ARGB_8888_DISALLOW_HARDWARE || z2) {
            return false;
        }
        boolean z3 = i >= 128 && i2 >= 128 && b();
        if (z3) {
            bitmapFactory$Options.inPreferredConfig = Bitmap$Config.HARDWARE;
            bitmapFactory$Options.inMutable = false;
        }
        return z3;
    }

    private synchronized boolean b() {
        int i = this.b + 1;
        this.b = i;
        if (i >= 50) {
            this.b = 0;
            int length = a.list().length;
            this.c = length < 700;
            if (!this.c && Log.isLoggable(I1I1lI1II1.a(new byte[]{115, 11, 21, 11, 17, 84, 90, 64, 85, 1, 66}), 5)) {
                Log.w(I1I1lI1II1.a(new byte[]{115, 11, 21, 11, 17, 84, 90, 64, 85, 1, 66}), I1I1lI1II1.a(new byte[]{114, 28, 1, 9, 23, 81, 94, 94, 94, 68, 120, 113, 103, 125, 99, 116, 48, 36, 66, 80, 89, 21, 94, 84, 67, 22, 84, 10, 10, 84, 94, 4, 23, 6, 7, 6, 3, 64, 68, 85, 25, 19, 85, 23, 71, 92, 20, 90, 20, 4, 16, 18, 68, 9, 86, 21, 85, 95, 91, 0, 68, 86, 82, 16, 84, 22, 11, 21, 22, 90, 69, 16, 85, 13, 93, 89, 65, 21, 20, 83, 11, 13, 7, 18, 84, 4, 64, 86, 65, 95, 71, 17, 11, 64, 68, 67}) + length + I1I1lI1II1.a(new byte[]{27, 68, 14, 12, 15, 92, 67, 16}) + 700);
            }
        }
        return this.c;
    }
}
