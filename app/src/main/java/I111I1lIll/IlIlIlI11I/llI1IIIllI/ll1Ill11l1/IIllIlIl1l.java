package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.graphics.Bitmap;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.security.MessageDigest;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class IIllIlIl1l extends I11IlIIll1 {
    private static final String c = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 112, 88, 89, 43, 13, 43, 94, 1, 13});
    private static final byte[] d = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 112, 88, 89, 43, 13, 43, 94, 1, 13}).getBytes(b);

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11IlIIll1
    protected Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) {
        return II1111I11I.b(lilli1lili, bitmap, i, i2);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public boolean equals(Object obj) {
        return obj instanceof IIllIlIl1l;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public int hashCode() {
        if (!IllllI11Il.I1lI11IIll(I1I1lI1II1.a(new byte[]{0, 86, 82, 1, 46, 101, 83, 100, 67, 52, 119}), IronSourceConstants.BN_INSTANCE_RELOAD_ERROR)) {
            return c.hashCode();
        }
        Log.e(I1I1lI1II1.a(new byte[]{100, 53, 21, 86, 87, 95, 82, 4, 14, 34, 124, 7, 7, 78, 78, 92, 27}), I1I1lI1II1.a(new byte[]{126, 22, 11, 10, 32, 115, 99, 5, 94, 40, 66, 3, 88, 95, 110, 103, 53, 39, 20, 92, 89, 34, 82, 124, 82, 115}));
        return 0;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public void a(MessageDigest messageDigest) {
        if (IIll1llI1l.Ill1lIIlIl(2146)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{120, 38, 27, 93, 80, 65, 86, 6, 85, 15}));
        }
        messageDigest.update(d);
        if (IIlII1IIIl.Il11lIlI1I(I1I1lI1II1.a(new byte[]{88, 8, 36, 46, 27, 113, Byte.MAX_VALUE, 83, 87, 39, 90, 114, 79, 72, 90, 86, 46, 89, 15, 65, 99}), I1I1lI1II1.a(new byte[]{82, 1, 45, 3, 11, Byte.MAX_VALUE, 96, 0, 76, 33, 125, 73, 102, 1, 122, 102, 16, 23, 48}))) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{98, 47, 13, 53, 27, 101, 115, 102, 15, 20, 65, 7, 80, 12, 118, 83, 44, 10, 0, 1, 113}));
        }
    }
}
