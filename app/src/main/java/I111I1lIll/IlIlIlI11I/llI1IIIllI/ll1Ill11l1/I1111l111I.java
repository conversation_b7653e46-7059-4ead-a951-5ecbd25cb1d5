package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.II1l1lIllI;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1111l111I implements II1l1lIllI<Long> {
    private final ByteBuffer a = ByteBuffer.allocate(8);

    I1111l111I() {
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.II1l1lIllI
    public /* synthetic */ void a(byte[] bArr, Long l, MessageDigest messageDigest) throws NoSuchAlgorithmException, CloneNotSupportedException {
        if (I111lIl11I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{80, 18, 24, 42, 17, 112, 91, 116, 15, 53, 123, 97, 66}), 355575569L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{90, 18, 26, 11, 50, 102, 79, 105, 113, 7}));
        }
        a(bArr, l, messageDigest);
        if (llIlI11III.I111IlIl1I(I1I1lI1II1.a(new byte[]{101, 52, 84, 31, 48, 97, 99, 123, 12, 47, 74, 85, 121, 72, 114, 103, 9, 44}), 401118468L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{79, 92, 21, 18, 35, 87, 78, 116, 91, 8, 120, 121, 1, 90, 114, 119, 27, 11, 12, 65, 3, 56, 123, 2, 67, 111, 99}));
        }
    }

    public void a(byte[] bArr, Long l, MessageDigest messageDigest) {
        if (IlIIlI11I1.IlII1Illll(177841774L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{98, 20, 46, 81, 48, 101, 120, 98, 14, 86, 6}));
        }
        messageDigest.update(bArr);
        synchronized (this.a) {
            this.a.position(0);
            messageDigest.update(this.a.putLong(l.longValue()).array());
        }
    }
}
