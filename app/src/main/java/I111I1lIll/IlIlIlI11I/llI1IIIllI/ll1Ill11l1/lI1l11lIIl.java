package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.I1llllIlII;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.graphics.Bitmap;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.security.InvalidKeyException;
import java.security.InvalidParameterException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class lI1l11lIIl implements I1llllIlII, IIll11lI1I<Bitmap> {
    private final Bitmap a;
    private final lIllI1lIlI b;

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public /* synthetic */ Bitmap d() {
        Bitmap bitmapB = b();
        if (IIlI1Il1lI.lll1111l11(I1I1lI1II1.a(new byte[]{96, 3, 13, 40, 56, 64, 101, 72, 115, 0, 2, 99, 119, 65, 71, 125, 17}), 168140385L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{64, 30, 24, 1, 43, 109, 81, 71, 1, 29, 97, 117, 88, 90, 119, 122, 40, 11, 22, 64, 103}));
        }
        return bitmapB;
    }

    public static lI1l11lIIl a(Bitmap bitmap, lIllI1lIlI lilli1lili) {
        if (bitmap == null) {
            if (IIl1l1IllI.l11I11I11l(I1I1lI1II1.a(new byte[]{65, 41, 1, 49, 10, 67, 109, 0, 75, 18, 114}), 4472)) {
                throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{77, 29, 36, 16, 80, 88, 116, 114, 80, 19, 117, 84, 93, 112, 77, 94, 84}));
            }
            return null;
        }
        lI1l11lIIl li1l11liil = new lI1l11lIIl(bitmap, lilli1lili);
        if (IIlI1ll1ll.l1l1Il1I11(I1I1lI1II1.a(new byte[]{109, 13, 14, 15, 8, 5, 95, 0, 75, 20, 87, 117, 3, 80, 3, 69, 37, 23, 91, 117, 0, 84, 105, 86, 102, 69, 70, 12}), 161183384L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{69, 3, 85, 41, 45, 80, 110, 85, 86, 82, Byte.MAX_VALUE, 70, 79, 12, 117, 98, 40, 21, 56, 122, 104, 25, 95, 102, 74, 80, 124, 28, 60, 1, 86, 85}));
        }
        return li1l11liil;
    }

    public lI1l11lIIl(Bitmap bitmap, lIllI1lIlI lilli1lili) {
        this.a = (Bitmap) llllIllIII.a(bitmap, I1I1lI1II1.a(new byte[]{117, 13, 22, 8, 3, 69, 23, 93, 76, 23, 68, 16, 91, 86, 64, 21, 0, 4, 66, 92, 69, 13, 95}));
        this.b = (lIllI1lIlI) llllIllIII.a(lilli1lili, I1I1lI1II1.a(new byte[]{117, 13, 22, 8, 3, 69, 103, 95, 86, 8, 16, 93, 64, 74, 64, 21, 12, 14, 22, 18, 82, 4, 19, 91, 70, 90, 91}));
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public Class<Bitmap> c() {
        if (l11Il111ll.I1lllI1llI(368)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{112, 14, 82, 49, 46, 5, 90, 85, 64, 93, 0, 121, 100, 13, 81, 66, 4, 81, 9, 117, 9, 56}));
        }
        return Bitmap.class;
    }

    public Bitmap b() {
        if (lI11IlI1lI.I1lI11IIll(I1I1lI1II1.a(new byte[]{94, 11, 37, 45, 47, 2, 77, 101, 108, 53, 115, 94, 92, 82, 71}), 555980661L)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{67, 85, 48, 28, 39, 69, 91, 101, 72}));
        }
        return this.a;
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public int e() {
        return llIIlI1llI.a(this.a);
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public void f() {
        this.b.a(this.a);
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.I1llllIlII
    public void a() throws InvalidKeyException {
        this.a.prepareToDraw();
        if (I1Ill1lIII.Ill1lIIlIl(184858889L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{118, 41, 51}));
        }
    }
}
