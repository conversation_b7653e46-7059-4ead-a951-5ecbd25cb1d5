package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIlII1Illl;
import IIlII11III.IlI11I11ll.llll111lll.IlIl1IlllI.llIIII1IlI;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.content.Context;
import android.graphics.Bitmap;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public abstract class I11IlIIll1 implements lIlII1Illl<Bitmap> {
    protected abstract Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2);

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIlII1Illl
    public final IIll11lI1I<Bitmap> a(Context context, IIll11lI1I<Bitmap> iIll11lI1I, int i, int i2) throws IllegalAccessException {
        if (!llIIlI1llI.a(i, i2)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{116, 5, 12, 11, 13, 65, 23, 81, 73, 20, 92, 73, 21, 77, 70, 84, 12, 18, 4, 93, 66, 12, 82, 65, 90, 89, 89, 69, 11, 92, 23, 20, 94, 0, 22, 13, 88, 21}) + i + I1I1lI1II1.a(new byte[]{23, 11, 16, 69, 10, 80, 94, 87, 81, 16, 10, 16}) + i2 + I1I1lI1II1.a(new byte[]{23, 8, 7, 22, 17, 21, 67, 88, 88, 10, 16, 95, 71, 25, 81, 68, 23, 0, 14, 18, 68, 14, 19, 79, 86, 68, 88, 69, 5, 92, 83, 67, 89, 11, 22, 69, 54, 84, 69, 87, 92, 16, 30, 99, 124, 99, 113, 106, 45, 51, 43, 117, 121, 47, 114, 121}));
        }
        lIllI1lIlI lilli1liliA = llIIII1IlI.a(context).a();
        Bitmap bitmapD = iIll11lI1I.d();
        if (i == Integer.MIN_VALUE) {
            i = bitmapD.getWidth();
        }
        if (i2 == Integer.MIN_VALUE) {
            i2 = bitmapD.getHeight();
        }
        Bitmap bitmapA = a(lilli1liliA, bitmapD, i, i2);
        return bitmapD.equals(bitmapA) ? iIll11lI1I : lI1l11lIIl.a(bitmapA, lilli1liliA);
    }
}
