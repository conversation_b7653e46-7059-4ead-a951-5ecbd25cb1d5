package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.util.Log;
import androidx.core.location.I11II1l1lI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.FileNotFoundException;
import java.io.InvalidClassException;
import java.net.BindException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l11I1I11I1 implements Lock {
    @Override // java.util.concurrent.locks.Lock
    public void lock() throws InvalidClassException {
        if (llIlI11III.I1lllI1llI(7732)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{114, 40, 50, 39, 36, 83, 100, 89, 115, 80, 1, 87, 100, 109, Byte.MAX_VALUE, 114, 87, 56, 80, 67, 88, 17, 6, 0, 90, 102, 15, 82}));
        }
    }

    @Override // java.util.concurrent.locks.Lock
    public void lockInterruptibly() throws InterruptedException, BindException {
        if (I11II1l1lI.l1l1l1IIlI(875821979L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{113, 62, 59, 50, 15, 102, 120, 86, 79, 9, 67, 115}));
        }
    }

    @Override // java.util.concurrent.locks.Lock
    public boolean tryLock() {
        if (!lI11IlI1lI.IlIllIll1I(166483359L)) {
            return true;
        }
        Log.v(I1I1lI1II1.a(new byte[]{120, 37, 48}), I1I1lI1II1.a(new byte[]{0, 60, 48, 54, 37, 12, 118, 97}));
        return false;
    }

    @Override // java.util.concurrent.locks.Lock
    public boolean tryLock(long j, TimeUnit timeUnit) throws InterruptedException {
        return true;
    }

    l11I1I11I1() {
    }

    @Override // java.util.concurrent.locks.Lock
    public void unlock() throws FileNotFoundException {
        if (lIIlI111II.Il1lII1l1l(3952)) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{98, 42}));
        }
    }

    @Override // java.util.concurrent.locks.Lock
    public Condition newCondition() {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{100, 12, 13, 16, 14, 81, 23, 94, 86, 16, 16, 82, 80, 25, 87, 84, 14, 13, 7, 86}));
    }
}
