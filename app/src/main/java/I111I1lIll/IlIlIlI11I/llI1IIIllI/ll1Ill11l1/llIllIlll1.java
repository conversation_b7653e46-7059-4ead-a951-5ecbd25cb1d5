package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.graphics.Bitmap;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.security.MessageDigest;
import java.security.cert.CertificateExpiredException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class llIllIlll1 extends I11IlIIll1 {
    private static final String c = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 89, 85, 125, 89, 14, 40, 14, 94, 92, 80});
    private static final byte[] d = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 89, 85, 125, 89, 14, 40, 14, 94, 92, 80}).getBytes(b);

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11IlIIll1
    protected Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) {
        return II1111I11I.c(lilli1lili, bitmap, i, i2);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public boolean equals(Object obj) throws TimeoutException {
        if (IIll1llI1l.Il1IIlI1II(4845)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{78, 46, 32, 60, 5, 66, 77, 5, 95, 60, 125, 67, 1, 82, 99, 81, 24, 87, 87, 124, 88, 37, 101, 113, 120}));
        }
        return obj instanceof llIllIlll1;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public int hashCode() {
        return c.hashCode();
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public void a(MessageDigest messageDigest) throws CertificateExpiredException {
        messageDigest.update(d);
        if (lIIlI111II.llIIIl11I1(225503643L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{102, 54, 26, 1, 14, 81, 81, 126, 113, 22, 92, 4, 64, 79, 1, 66, 13, 86, 56, 11, 83, 56, 123, 96, 64, 68, 110, 54, 42}));
        }
    }
}
