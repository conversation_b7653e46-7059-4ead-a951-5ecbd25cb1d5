package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.l11Il1lI11;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class I11II1111l {
    public static final I11II1111l a = new Il111I11Il();
    public static final I11II1111l b;
    public static final I11II1111l c;
    public static final I11II1111l d;
    public static final I11II1111l e;
    public static final I11II1111l f;
    public static final I11II1111l g;
    public static final l11Il1lI11<I11II1111l> h;

    public abstract float a(int i, int i2, int i3, int i4);

    public abstract I1IIIIIlI1 b(int i, int i2, int i3, int i4);

    static {
        Il1I11lIII il1I11lIII = new Il1I11lIII();
        b = il1I11lIII;
        c = new I1111IIl11();
        d = new I1lll111Il();
        e = new ll1ll1IlIl();
        f = new I1l111l1l1();
        g = il1I11lIII;
        h = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 125, 11, 71, 94, 70, 88, 89, 69, 14, 4, 16, 28, 116, 14, 68, 91, 64, 87, 90, 21, 8, 87, 100, 23, 69, 5, 22, 0, 5, 76}), il1I11lIII);
    }
}
