package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.support.v4.graphics.drawable.Il1IIllIll;
import android.util.Log;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I111I11Ill;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.llIIll1Il1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class I11IIIIlIl implements I1111IIlIl {
    private final InputStream a;

    I11IIIIlIl(InputStream inputStream) {
        this.a = inputStream;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int a() throws IOException {
        if (Il1IIllIll.IlIIl111lI(I1I1lI1II1.a(new byte[]{14}), 466763785L)) {
            Log.d(I1I1lI1II1.a(new byte[]{2, 42, 44, 84, 16, 108, 118}), I1I1lI1II1.a(new byte[]{83, 1, 90, 35, 42, 108, 116, 100, 79, 7, 81, 103, 77, 126, 120, 108, 23, 85, 54, 69, 115, 34, 0, 99, 84, 112, 80, 19, 86, 122, 65, 39}));
            return 0;
        }
        int i = ((this.a.read() << 8) & llIIll1Il1.ACTION_POINTER_INDEX_MASK) | (this.a.read() & 255);
        if (!l111Il1lI1.IlIIl111lI(I1I1lI1II1.a(new byte[]{95, 7, 85, 39, 15, 67, 0, 1, 120, 82, 95, 97, 89, 122, 100, 82, 18, 40, 90, 95, 90, 17, 69, 79, 75, 97, 121, 28}), 203250681L)) {
            return i;
        }
        Log.d(I1I1lI1II1.a(new byte[]{125, 14, 24, 54, 48, 0, 68, 114, 99, 14, 104}), I1I1lI1II1.a(new byte[]{109, 35}));
        return 0;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public short b() throws IOException {
        short s = (short) (this.a.read() & 255);
        if (l1IIll1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{83, 16, 52, 36, 24, 98, 81, 122, 112, 52, 123}), 10074)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{124, 20, 52, 0, 51, 79, Byte.MAX_VALUE, 69, 120, 29, 9, 82, 103, 106}));
        }
        return s;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public long a(long j) throws IOException {
        if (I111I11Ill.Ill1lIIlIl(323)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{6, 38, 80, 15, 12, 2, 116, 94, 74, 21, 66, 92, 114, 81, 121, 86, 47, 25, 10, 91, 97, 12, 96, 119, 105, 83, 120, 1, 10, 125, 85, 20}));
        }
        if (j < 0) {
            return 0L;
        }
        long j2 = j;
        while (j2 > 0) {
            long jSkip = this.a.skip(j2);
            if (jSkip <= 0) {
                if (this.a.read() == -1) {
                    break;
                }
                jSkip = 1;
            }
            j2 -= jSkip;
        }
        return j - j2;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int a(byte[] bArr, int i) throws IOException, BrokenBarrierException {
        int i2 = i;
        while (i2 > 0) {
            int i3 = this.a.read(bArr, i - i2, i2);
            if (i3 == -1) {
                break;
            }
            i2 -= i3;
        }
        int i4 = i - i2;
        if (I111I11Ill.lll1111l11(I1I1lI1II1.a(new byte[]{124, 47, 13, 40, 8, 94, 2, 96, 1, 11, 7, 104, 5, 76, 122, 120, 84, 24, 15, 126, 70}), 9947)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{95, 50, 52, 92, 58, 77}));
        }
        return i4;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int c() throws IOException {
        return this.a.read();
    }
}
