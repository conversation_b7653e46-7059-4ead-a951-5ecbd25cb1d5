package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.lIIIIII11I;
import android.graphics.Bitmap;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.IOException;
import java.io.NotActiveException;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.I1lI11I11I;

/* loaded from: classes.dex */
public class l1IllIllll implements I1IIlllI1I<ByteBuffer, Bitmap> {
    private final l1lllll11l a;

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ boolean a(ByteBuffer byteBuffer, IIIll1l1l1 iIIll1l1l1) throws InvalidKeyException, IOException {
        if (IIIlIll111.I111IlIl1I(196038706L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{93, 82, 43, 39, 54, 94, 81, 96, 77, 84, 5, 73, 68, 76, 112, 64, 85, 45, 42, 11, 73, 85, 11, 124, 93}));
        }
        boolean zA = a(byteBuffer, iIIll1l1l1);
        if (Il11II1llI.I1II1111ll(I1I1lI1II1.a(new byte[]{69, 80, 27, 34, 50, 121, 70, 115, 108, 62, Byte.MAX_VALUE, 106, 102, 119, 99, 7, 55, 7, 26}))) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{14, 42, 40, 19, 80, 68, 123, 82, 65, 39, 71, 122, 65, 76, 124, 77, 47, 42, 40, 106, 89, 2}));
        }
        return zA;
    }

    public l1IllIllll(l1lllll11l l1lllll11lVar) {
        this.a = l1lllll11lVar;
    }

    public boolean a(ByteBuffer byteBuffer, IIIll1l1l1 iIIll1l1l1) throws NotActiveException {
        if (lIIIIII11I.l1l1Il1I11(I1I1lI1II1.a(new byte[]{83, 47, 11, 4, 91}), I1I1lI1II1.a(new byte[]{99, 20, 82, 49, 39, 81, 109, 90, 125, 11, 86, 84, 68, 117, 114, 13, 59}))) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{84, 3, 83, 22, 3, 125, Byte.MAX_VALUE, 87, 112, 60, 69, 8, 96, 126, 101, 4, 10}));
        }
        boolean zA = this.a.a(byteBuffer);
        if (lI1lllIII1.Ill1lIIlIl(1263047369L)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{110, 45, 45, 13, 45, 3, 86, 81, 15, 15, 104, 119, 79, 117, 3, 71, 4}));
        }
        return zA;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public IIll11lI1I<Bitmap> a(ByteBuffer byteBuffer, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws IOException {
        return this.a.a(I1lI11I11I.b(byteBuffer), i, i2, iIIll1l1l1);
    }
}
