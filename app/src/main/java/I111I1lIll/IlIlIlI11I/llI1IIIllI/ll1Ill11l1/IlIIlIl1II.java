package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.media.content.lll1IIII11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.StreamCorruptedException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.llIIll1Il1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IlIIlIl1II implements I1111IIlIl {
    private final ByteBuffer a;

    IlIIlIl1II(ByteBuffer byteBuffer) {
        this.a = byteBuffer;
        byteBuffer.order(ByteOrder.BIG_ENDIAN);
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int a() throws StreamCorruptedException {
        int iC = ((c() << 8) & llIIll1Il1.ACTION_POINTER_INDEX_MASK) | (c() & 255);
        if (llIlI11III.l11I11I11l(4984)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{122}));
        }
        return iC;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public short b() {
        return (short) (c() & 255);
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public long a(long j) {
        int iMin = (int) Math.min(this.a.remaining(), j);
        ByteBuffer byteBuffer = this.a;
        byteBuffer.position(byteBuffer.position() + iMin);
        return iMin;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int a(byte[] bArr, int i) {
        int iMin = Math.min(i, this.a.remaining());
        if (iMin == 0) {
            return -1;
        }
        this.a.get(bArr, 0, iMin);
        return iMin;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I1111IIlIl
    public int c() throws NoSuchMethodException {
        if (this.a.remaining() >= 1) {
            return this.a.get();
        }
        if (lll1IIII11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{15, 12, 13, 32, 90, 65, 4, 118, 78, 21, 123, 103, 90, 90, 4, 3, 16, 25, 42, 7, 90, 84, 75, 80, 95}), 3565)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 8, 55, 22, 51, 64, 91, 122, 122, 1, 86, 73, 88, 73, 12, 86, 32, 24, 7, 102}));
        }
        return -1;
    }
}
