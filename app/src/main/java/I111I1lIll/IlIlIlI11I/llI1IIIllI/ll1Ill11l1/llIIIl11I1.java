package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Il111lllll;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIIllIlIl1;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.IOException;
import java.io.InputStream;
import java.io.InvalidObjectException;
import java.io.ObjectStreamException;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.security.InvalidParameterException;
import java.security.cert.CertificateException;
import java.util.concurrent.CancellationException;
import java.util.concurrent.RejectedExecutionException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;
import lIII11l1II.Il1I11IlII.lIl1I1111l.lll1IIIl11.lIlllIl1lI;
import lIIII1l1I1.l11lI1l1ll.l1Il11l1Il.Il11lI1Ill.llI1l11lII;

/* loaded from: classes.dex */
public final class llIIIl11I1 implements Il111lllll {
    private static final String b = I1I1lI1II1.a(new byte[]{115, 2, 14, 17, 43, 88, 86, 87, 92, 44, 85, 81, 81, 92, 70, 101, 3, 19, 17, 87, 66});
    private static final String c = I1I1lI1II1.a(new byte[]{114, 28, 11, 3, 98, 53});
    static final byte[] a = I1I1lI1II1.a(new byte[]{114, 28, 11, 3, 98, 53}).getBytes(Charset.forName(I1I1lI1II1.a(new byte[]{98, 48, 36, 72, 90})));
    private static final int[] d = {0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8};

    private static int a(int i, int i2) {
        return i + 2 + (i2 * 12);
    }

    private static boolean a(int i) {
        return (i & 65496) == 65496 || i == 19789 || i == 18761;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Il111lllll
    public lIIllIlIl1 a(InputStream inputStream) throws IOException {
        return a(new I11IIIIlIl((InputStream) llllIllIII.a(inputStream)));
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Il111lllll
    public lIIllIlIl1 a(ByteBuffer byteBuffer) throws IOException {
        return a(new IlIIlIl1II((ByteBuffer) llllIllIII.a(byteBuffer)));
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Il111lllll
    public int a(InputStream inputStream, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) throws IOException, CertificateException {
        if (Il11II1llI.Il1IIlI1II(640269274L)) {
            Log.i(I1I1lI1II1.a(new byte[]{78, 85, 49, 38, 33, 5, 89, 103, 8, 15, 81}), I1I1lI1II1.a(new byte[]{15, 48, 23}));
            return 0;
        }
        int iA = a(new I11IIIIlIl((InputStream) llllIllIII.a(inputStream)), (lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) llllIllIII.a(il1IllIl1I));
        if (l1lll111II.l11I11I11l(8091)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{103, 60, 91, 20, 3, 79, 89, 98, 79, 50, 66, 66, 124, 64, 119, 81, 47, 87, 32, 11, 8, 20, 5, 100, 123, 116, 109}));
        }
        return iA;
    }

    private lIIllIlIl1 a(I1111IIlIl i1111IIlIl) throws IOException, CloneNotSupportedException {
        int iA = i1111IIlIl.a();
        if (iA == 65496) {
            lIIllIlIl1 liillilil1 = lIIllIlIl1.JPEG;
            if (IIlIIlIII1.Il1IIlI1II(840621478L)) {
                throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{109, 93, 10, 53, 46, 123, 84, 97, 10, 54, 98, 68, 91, 107, 92, 13, 13, 3, 17, 120, 106}));
            }
            return liillilil1;
        }
        int iA2 = ((iA << 16) & llI1l11lII.CATEGORY_MASK) | (i1111IIlIl.a() & llI1l11lII.USER_MASK);
        if (iA2 == -1991225785) {
            i1111IIlIl.a(21L);
            return i1111IIlIl.c() >= 3 ? lIIllIlIl1.PNG_A : lIIllIlIl1.PNG;
        }
        if ((iA2 >> 8) == 4671814) {
            return lIIllIlIl1.GIF;
        }
        if (iA2 != 1380533830) {
            return lIIllIlIl1.UNKNOWN;
        }
        i1111IIlIl.a(4L);
        if ((((i1111IIlIl.a() << 16) & llI1l11lII.CATEGORY_MASK) | (i1111IIlIl.a() & llI1l11lII.USER_MASK)) != 1464156752) {
            lIIllIlIl1 liillilil12 = lIIllIlIl1.UNKNOWN;
            if (lIIlI111II.l111I1ll1l(9978)) {
                throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{91, 32, 54, 14, 26, 80, 113}));
            }
            return liillilil12;
        }
        int iA3 = ((i1111IIlIl.a() << 16) & llI1l11lII.CATEGORY_MASK) | (i1111IIlIl.a() & llI1l11lII.USER_MASK);
        if ((iA3 & lIlllIl1lI.SOURCE_ANY) != 1448097792) {
            return lIIllIlIl1.UNKNOWN;
        }
        int i = iA3 & 255;
        if (i == 88) {
            i1111IIlIl.a(4L);
            return (i1111IIlIl.c() & 16) != 0 ? lIIllIlIl1.WEBP_A : lIIllIlIl1.WEBP;
        }
        if (i == 76) {
            i1111IIlIl.a(4L);
            return (i1111IIlIl.c() & 8) != 0 ? lIIllIlIl1.WEBP_A : lIIllIlIl1.WEBP;
        }
        lIIllIlIl1 liillilil13 = lIIllIlIl1.WEBP;
        if (llIl1lII1I.I1lIllll1l(372066685L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{89, 14, 51, 54, 33, 120, 97, 86, 94, 37, 70, 114, 119, 78, 87, 81, 24, 41, 4, 65, 72}));
        }
        return liillilil13;
    }

    private int a(I1111IIlIl i1111IIlIl, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) throws IOException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{69, 1, 43}), 198452376L)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{116, 6, 46, 49, 56, 4, 89, 83, 0, 81, 87, 99, 93, 118, 92}));
        }
        int iA = i1111IIlIl.a();
        if (!a(iA)) {
            String str = b;
            if (Log.isLoggable(str, 3)) {
                Log.d(str, I1I1lI1II1.a(new byte[]{103, 5, 16, 22, 7, 71, 23, 84, 86, 1, 67, 94, 18, 77, 20, 93, 3, 15, 6, 94, 85, 65, 94, 84, 84, 95, 84, 69, 10, 71, 90, 1, 82, 22, 88, 69}) + iA);
            }
            return -1;
        }
        int iB = b(i1111IIlIl);
        if (iB != -1) {
            byte[] bArr = (byte[]) il1IllIl1I.a(iB, byte[].class);
            try {
                return a(i1111IIlIl, bArr, iB);
            } finally {
                il1IllIl1I.a((lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) bArr);
            }
        }
        String str2 = b;
        if (Log.isLoggable(str2, 3)) {
            Log.d(str2, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 64, 81, 71, 74, 81, 21, 7, 25, 11, 84, 16, 18, 86, 82, 94, 83, 89, 17, 68, 94, 82, 13, 80, 16, 10, 73, 66, 90, 69, 16, 92, 28, 89, 86, 21, 74, 81, 82, 15, 4, 12, 70, 16, 15, 92, 65, 19, 80, 88, 16, 10, 86}));
        }
        if (IlIIlI11I1.IlII1Illll(228139058L)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{97, 93, 43, 81, 13, 82, 102, 114, 104, 46}));
        }
        return -1;
    }

    private int a(I1111IIlIl i1111IIlIl, byte[] bArr, int i) throws IOException {
        int iA = i1111IIlIl.a(bArr, i);
        if (iA != i) {
            String str = b;
            if (Log.isLoggable(str, 3)) {
                Log.d(str, I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 66, 85, 84, 93, 20, 80, 26, 8, 4, 18, 67, 4, 84, 88, 86, 88, 67, 69, 0, 83, 67, 2, 27, 68, 14, 0, 12, 82, 67, 88, 3, 68}) + i + I1I1lI1II1.a(new byte[]{27, 68, 3, 6, 22, 64, 86, 92, 85, 29, 16, 66, 80, 88, 80, 15, 66}) + iA);
            }
            return -1;
        }
        if (a(bArr, i)) {
            return a(new l111I1lIII(bArr, i));
        }
        String str2 = b;
        if (Log.isLoggable(str2, 3)) {
            Log.d(str2, I1I1lI1II1.a(new byte[]{122, 13, 17, 22, 11, 91, 80, 16, 83, 20, 85, 87, 21, 92, 76, 92, 4, 65, 18, 64, 85, 0, 94, 87, 95, 83}));
        }
        if (Il1IIllIll.I11II1I1I1(I1I1lI1II1.a(new byte[]{122}), 433909333L)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{122}));
        }
        return -1;
    }

    private boolean a(byte[] bArr, int i) throws ObjectStreamException {
        if (androidx.core.location.I1111IIl11.I1lllI1llI(163676065L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{67, 10, 8, 35, 44, 120, 120, 87, 86, 41, 73, 69, 119, 112, 6, 90, 38, 24, 42, 118, 83, 84, 2, 103, 118, 80, 121, 44}));
        }
        boolean z = bArr != null && i > a.length;
        if (z) {
            int i2 = 0;
            while (true) {
                byte[] bArr2 = a;
                if (i2 >= bArr2.length) {
                    break;
                }
                if (bArr[i2] != bArr2[i2]) {
                    return false;
                }
                i2++;
            }
        }
        return z;
    }

    private int b(I1111IIlIl i1111IIlIl) throws IOException {
        short sB;
        int iA;
        long j;
        long jA;
        if (l1lll111II.Il1IIlI1II(249417786L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{92, 61, 85, 19, 35, 109, 6, 102, 112, 52, 115, 82, 99, 125, 70, 6, 42, 86, 14}));
        }
        do {
            short sB2 = i1111IIlIl.b();
            if (sB2 != 255) {
                String str = b;
                if (Log.isLoggable(str, 3)) {
                    Log.d(str, I1I1lI1II1.a(new byte[]{98, 10, 9, 11, 13, 66, 89, 16, 74, 1, 87, 93, 80, 87, 64, 124, 6, 92}) + ((int) sB2));
                }
                return -1;
            }
            sB = i1111IIlIl.b();
            if (sB == 218) {
                if (!lIIlI111II.I1lll11llI(5575)) {
                    return -1;
                }
                Log.i(I1I1lI1II1.a(new byte[]{124, 62, 17, 16, 1}), I1I1lI1II1.a(new byte[]{117, 40, 40, 36, 17, 7}));
                return 0;
            }
            if (sB == 217) {
                String str2 = b;
                if (Log.isLoggable(str2, 3)) {
                    Log.d(str2, I1I1lI1II1.a(new byte[]{113, 11, 23, 11, 6, 21, 122, 113, 107, 47, 117, 98, 106, 124, 123, 124, 66, 8, 12, 18, 85, 25, 90, 83, 19, 69, 82, 2, 9, 87, 89, 23}));
                }
                return -1;
            }
            iA = i1111IIlIl.a() - 2;
            if (sB == 225) {
                if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{91, 0, 10, 52, 54, 125, 114, 123, 1, 5, 71, 0, 88, 124, 98, 113, 85, 44, 85, 116, 113, 27, 80, 83, 67, 126, 120, 44, 10, 99}), 390682430L)) {
                    throw new InstantiationError(I1I1lI1II1.a(new byte[]{114, 9, 50, 22, 55, 109, 124, 2, 0, 83, 65, 126, 5, 94, 109, 124, 86, 4, 7, 5, 4, 46, 93, 13, 85, 95, 124, 46, 18, 125, 118}));
                }
                return iA;
            }
            j = iA;
            jA = i1111IIlIl.a(j);
        } while (jA == j);
        String str3 = b;
        if (Log.isLoggable(str3, 3)) {
            Log.d(str3, I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 67, 91, 92, 73, 20, 80, 12, 14, 23, 85, 88, 65, 87, 84, 71, 87, 27, 69, 16, 75, 71, 6, 13, 68}) + ((int) sB) + I1I1lI1II1.a(new byte[]{27, 68, 21, 4, 12, 65, 82, 84, 25, 16, 95, 16, 70, 82, 93, 69, 88, 65}) + iA + I1I1lI1II1.a(new byte[]{27, 68, 0, 16, 22, 21, 86, 83, 77, 17, 81, 92, 89, 64, 20, 70, 9, 8, 18, 66, 85, 5, 9, 21}) + jA);
        }
        return -1;
    }

    private static int a(l111I1lIII l111i1liii) throws IllegalAccessException, UnknownHostException {
        ByteOrder byteOrder;
        int length = c.length();
        short sB = l111i1liii.b(length);
        if (sB == 18761) {
            byteOrder = ByteOrder.LITTLE_ENDIAN;
        } else if (sB == 19789) {
            byteOrder = ByteOrder.BIG_ENDIAN;
        } else {
            String str = b;
            if (Log.isLoggable(str, 3)) {
                Log.d(str, I1I1lI1II1.a(new byte[]{98, 10, 9, 11, 13, 66, 89, 16, 92, 10, 84, 89, 84, 87, 90, 80, 17, 18, 66, 15, 16}) + ((int) sB));
            }
            byteOrder = ByteOrder.BIG_ENDIAN;
        }
        l111i1liii.a(byteOrder);
        int iA = l111i1liii.a(length + 4) + length;
        short sB2 = l111i1liii.b(iA);
        for (int i = 0; i < sB2; i++) {
            int iA2 = a(iA, i);
            short sB3 = l111i1liii.b(iA2);
            if (sB3 == 274) {
                short sB4 = l111i1liii.b(iA2 + 2);
                if (sB4 < 1 || sB4 > 12) {
                    String str2 = b;
                    if (Log.isLoggable(str2, 3)) {
                        Log.d(str2, I1I1lI1II1.a(new byte[]{112, 11, 22, 69, 11, 91, 65, 81, 85, 13, 84, 16, 83, 86, 70, 88, 3, 21, 66, 81, 95, 5, 86, 21, 14, 22}) + ((int) sB4));
                    }
                } else {
                    int iA3 = l111i1liii.a(iA2 + 4);
                    if (iA3 < 0) {
                        String str3 = b;
                        if (Log.isLoggable(str3, 3)) {
                            Log.d(str3, I1I1lI1II1.a(new byte[]{121, 1, 5, 4, 22, 92, 65, 85, 25, 16, 89, 86, 83, 25, 87, 90, 15, 17, 13, 92, 85, 15, 71, 21, 80, 89, 66, 11, 16}));
                        }
                    } else {
                        String str4 = b;
                        if (Log.isLoggable(str4, 3)) {
                            Log.d(str4, I1I1lI1II1.a(new byte[]{112, 11, 22, 69, 22, 84, 80, 121, 87, 0, 85, 72, 8}) + i + I1I1lI1II1.a(new byte[]{23, 16, 3, 2, 54, 76, 71, 85, 4}) + ((int) sB3) + I1I1lI1II1.a(new byte[]{23, 2, 13, 23, 15, 84, 67, 115, 86, 0, 85, 13}) + ((int) sB4) + I1I1lI1II1.a(new byte[]{23, 7, 13, 8, 18, 90, 89, 85, 87, 16, 115, 95, 64, 87, 64, 8}) + iA3);
                        }
                        int i2 = iA3 + d[sB4];
                        if (i2 > 4) {
                            if (Log.isLoggable(str4, 3)) {
                                Log.d(str4, I1I1lI1II1.a(new byte[]{112, 11, 22, 69, 0, 76, 67, 85, 25, 7, 95, 69, 91, 77, 20, 11, 66, 85, 78, 18, 94, 14, 71, 21, 92, 68, 94, 0, 10, 70, 86, 23, 94, 11, 12, 73, 66, 86, 88, 94, 77, 13, 94, 69, 92, 87, 83, 25, 66, 7, 13, 64, 93, 0, 71, 118, 92, 82, 82, 88}) + ((int) sB4));
                            }
                        } else {
                            int i3 = iA2 + 8;
                            if (i3 < 0 || i3 > l111i1liii.a()) {
                                if (Log.isLoggable(str4, 3)) {
                                    Log.d(str4, I1I1lI1II1.a(new byte[]{126, 8, 14, 0, 5, 84, 91, 16, 77, 5, 87, 102, 84, 85, 65, 80, 45, 7, 4, 65, 85, 21, 14}) + i3 + I1I1lI1II1.a(new byte[]{23, 16, 3, 2, 54, 76, 71, 85, 4}) + ((int) sB3));
                                }
                            } else if (i2 < 0 || i2 + i3 > l111i1liii.a()) {
                                if (Log.isLoggable(str4, 3)) {
                                    Log.d(str4, I1I1lI1II1.a(new byte[]{126, 8, 14, 0, 5, 84, 91, 16, 87, 17, 93, 82, 80, 75, 20, 90, 4, 65, 0, 75, 68, 4, 64, 21, 85, 89, 69, 69, 48, 123, 23, 23, 86, 3, 66, 1, 3, 65, 86, 16, 77, 5, 87, 100, 76, 73, 81, 8}) + ((int) sB3));
                                }
                            } else {
                                return l111i1liii.b(i3);
                            }
                        }
                    }
                }
            }
        }
        return -1;
    }
}
