package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.content.res.AssetFileDescriptor;
import android.media.MediaMetadataRetriever;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.io.FileNotFoundException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class Il1IIIIlll implements llll1I1l11<AssetFileDescriptor> {
    private Il1IIIIlll() {
    }

    /* synthetic */ Il1IIIIlll(I1111l111I i1111l111I) {
        this();
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.llll1I1l11
    public void a(MediaMetadataRetriever mediaMetadataRetriever, AssetFileDescriptor assetFileDescriptor) throws IllegalArgumentException, FileNotFoundException {
        if (lIlIII1I1l.II1111I11I(I1I1lI1II1.a(new byte[]{110, 38, 54, 7, 49, 64, 4, 3, 112, 53}), I1I1lI1II1.a(new byte[]{92, 86, 20, 39, 27, 109, 69, 9, 108, 62, 105, 123, 126, 90, 93, 95, 84}))) {
            throw new FileNotFoundException(I1I1lI1II1.a(new byte[]{117, 22, 35, 55, 50, 70, Byte.MAX_VALUE, 99, 80, 18, 94, 1, 111, 87, 80, 0, 32, 46, 41, 121, 98, 9, 118, 122, 123, 68, 92, 60, 54, 67}));
        }
        mediaMetadataRetriever.setDataSource(assetFileDescriptor.getFileDescriptor(), assetFileDescriptor.getStartOffset(), assetFileDescriptor.getLength());
    }
}
