package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.lI1l1I1l1l;
import android.graphics.Bitmap;
import android.util.Log;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.Il1l11I11I;
import java.io.InvalidClassException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class lll1llIIll implements IIll11lI1I<Bitmap> {
    private final Bitmap a;

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public void f() {
        if (lI1l1I1l1l.Ill1lIIlIl(4904)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{118, 55, 42, 48, 87, 13, 84, 66, 99, 8, 65, 119, 97, 105, 98, 67}));
        }
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public /* synthetic */ Bitmap d() throws InvalidClassException {
        Bitmap bitmapA = a();
        if (Il1l11I11I.l1ll11I11l(I1I1lI1II1.a(new byte[]{4, 48, 85, 9, 26, 111, 124, 1, 117, 80, 5, 1, 96, 108, Byte.MAX_VALUE, 102, 16, 50}), 669039943L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{123, 2, 58, 36, 86, 79, 98, 1, 84, 42, 6, 98, 93, 107, 64, 65, 53, 50, 51, 66, 122, 20, 120, 5, 74, 93, 125, 38}));
        }
        return bitmapA;
    }

    lll1llIIll(Bitmap bitmap) {
        this.a = bitmap;
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public Class<Bitmap> c() {
        return Bitmap.class;
    }

    public Bitmap a() {
        Bitmap bitmap = this.a;
        if (!lIIlI111II.llIIl11lIl(172414022L)) {
            return bitmap;
        }
        Log.e(I1I1lI1II1.a(new byte[]{118, 22, 46, 14, 0, 94, 97, 1, 65, 52, 66, 92, 116, 11, 13, 93, 87, 43, 43, 115, 6}), I1I1lI1II1.a(new byte[]{94, 2, 15, 18, 49, 112, 103, 116, 106, 22, 117}));
        return null;
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public int e() {
        if (androidx.core.location.lIIlI111II.l111I1ll1l(9517)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{114, 10, 12, 9, 90, 124, 82, 1, 96, 1, 103, Byte.MAX_VALUE}));
        }
        return llIIlI1llI.a(this.a);
    }
}
