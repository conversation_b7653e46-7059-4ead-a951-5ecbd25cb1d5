package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import IIl11IllI1.IIIII1I11I.I111IlIl1l.Il1IIIlI1l.llII1ll111;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public enum I1IIIIIlI1 {
    MEMORY,
    QUALITY;

    /* renamed from: values, reason: to resolve conflict with enum method */
    public static I1IIIIIlI1[] valuesCustom() throws CertificateEncodingException {
        if (II1I11IlI1.Ill1lIIlIl(llII1ll111.MODE_SUPPORT_MASK)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{86, 47, 49, 47, 50, 116, 64, 91, 87, 32, 68, 92, 76, 119, 101, 82, 86, 39, 11, 65, 104, 12}));
        }
        I1IIIIIlI1[] i1IIIIIlI1Arr = (I1IIIIIlI1[]) values().clone();
        if (androidx.core.location.I1111IIl11.Ill1lIIlIl(202524367L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{93, 47, 10, 61, 45, 116, 102, 70, 83, 49, 113, 100, 13, 79, 121, 92, 48, 57, 16, 74, 91, 7, 116, 97, 67}));
        }
        return i1IIIIIlI1Arr;
    }
}
