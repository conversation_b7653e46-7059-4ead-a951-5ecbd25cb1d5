package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.accounts.utils.lIIIIII11I;
import androidx.core.location.I111I11Ill;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l111I1lIII {
    private final ByteBuffer a;

    l111I1lIII(byte[] bArr, int i) {
        this.a = (ByteBuffer) ByteBuffer.wrap(bArr).order(ByteOrder.BIG_ENDIAN).limit(i);
    }

    void a(ByteOrder byteOrder) throws IllegalAccessException {
        this.a.order(byteOrder);
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{92, 86, 21, 85, 83, 4, 89, 123, 91, 92, 90, 81, 123}), 1399)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{84, 54, 4, 48}));
        }
    }

    int a() throws UnrecoverableEntryException {
        int iRemaining = this.a.remaining();
        if (llIlI11III.IlII1Illll(322781350L)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{6, 46, 59, 63, 43, 87, 97, 66, 108, 52}));
        }
        return iRemaining;
    }

    int a(int i) {
        if (a(i, 4)) {
            return this.a.getInt(i);
        }
        return -1;
    }

    short b(int i) throws UnknownHostException {
        if (I111I11Ill.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{89, 15, 15, 51, 20, 92, 113, 66, 15, 49, 85, 120, 126, 118, 99, 6, 41, 32}), 257458805L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{77, 38, 42, 19, 87, 97, 0, 5, 117, 17, 104, 83, 68, 78, 96}));
        }
        if (a(i, 2)) {
            return this.a.getShort(i);
        }
        return (short) -1;
    }

    private boolean a(int i, int i2) throws NoSuchMethodException {
        if (lIIIIII11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{4, 9, 35, 7, 14}), 1458007377L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{71}));
        }
        return this.a.remaining() - i >= i2;
    }
}
