package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.accounts.utils.lIIlI111II;
import android.graphics.Bitmap;
import android.graphics.Bitmap$Config;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PorterDuff$Mode;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.Shader$TileMode;
import android.media.content.lll1IIII11;
import android.os.Build;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.util.Log;
import androidx.core.location.I111I11Ill;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.lI1lI11Ill;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.net.MalformedURLException;
import java.security.DigestException;
import java.security.UnrecoverableEntryException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.CertificateParsingException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public final class II1111I11I {
    private static final String a = I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 83, 88, 66, 84, 5, 68, 89, 90, 87, 97, 65, 11, 13, 17});
    private static final Paint b = new Paint(6);
    private static final Paint c = new Paint(7);
    private static final Paint d;
    private static final Set<String> e;
    private static final Lock f;

    public static boolean b(int i) {
        switch (i) {
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
                return true;
            default:
                return false;
        }
    }

    static {
        HashSet hashSet = new HashSet(Arrays.asList(I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 90, 0}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 7}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 6}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 1}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 0}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 3}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 2}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 91, 13}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 4}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 80, 13}), I1I1lI1II1.a(new byte[]{111, 48, 91, 86, 85, 118}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 7}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 82, 13}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 6}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 0}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 1}), I1I1lI1II1.a(new byte[]{111, 48, 91, 86, 91, 114}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 81, 12}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 86, 5}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 86, 7}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 86, 0}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 84, 6}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 84, 1}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 84, 13}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 84, 12}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 85, 7}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 85, 2}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 85, 13}), I1I1lI1II1.a(new byte[]{111, 48, 83, 85, 85, 12})));
        e = hashSet;
        f = hashSet.contains(Build.MODEL) ? new ReentrantLock() : new l11I1I11I1();
        Paint paint = new Paint(7);
        d = paint;
        paint.setXfermode(new PorterDuffXfermode(PorterDuff$Mode.SRC_IN));
    }

    public static Lock a() {
        return f;
    }

    public static Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) throws UnrecoverableKeyException, CertificateNotYetValidException {
        float width;
        float height;
        if (llIlII1IlI.I1lI11IIll(I1I1lI1II1.a(new byte[]{83, 11, 83, 54, 3, 108, 97}), 329996034L)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{111, 20, 80}));
        }
        if (bitmap.getWidth() == i && bitmap.getHeight() == i2) {
            if (androidx.core.location.I1111IIl11.lll1111l11(I1I1lI1II1.a(new byte[]{77, 62, 80, 40, 22, 0, 116, 124, 80, 14, 115, 116, 114, 117, 1, 108, 27, 59, 36, 98, 5, 43, 118, 91, 98, 120, 89}), 193918199L)) {
                throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{86, 9, 8, 40, 47, 91, 84}));
            }
            return bitmap;
        }
        Matrix matrix = new Matrix();
        float width2 = 0.0f;
        if (bitmap.getWidth() * i2 > bitmap.getHeight() * i) {
            width = i2 / bitmap.getHeight();
            width2 = (i - (bitmap.getWidth() * width)) * 0.5f;
            height = 0.0f;
        } else {
            width = i / bitmap.getWidth();
            height = (i2 - (bitmap.getHeight() * width)) * 0.5f;
        }
        matrix.setScale(width, width);
        matrix.postTranslate((int) (width2 + 0.5f), (int) (height + 0.5f));
        Bitmap bitmapA = lilli1lili.a(i, i2, b(bitmap));
        a(bitmap, bitmapA);
        a(bitmap, bitmapA, matrix);
        return bitmapA;
    }

    public static Bitmap b(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) throws UnrecoverableEntryException {
        if (lI1lI11Ill.I111IlIl1I(I1I1lI1II1.a(new byte[]{4, 22, 0, 33, 81, 94, 5, 98, 90, 93, 4, 0, 113, 90, 102, 1, 0, 32}), 888219863L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{5, 1, 15, 32, 0, 0, 64, 93, 104}));
        }
        if (bitmap.getWidth() == i && bitmap.getHeight() == i2) {
            String str = a;
            if (Log.isLoggable(str, 2)) {
                Log.v(str, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 85, 93, 68, 68, 81, 71, 94, 81, 65, 66, 18, 11, 72, 85, 65, 94, 84, 71, 85, 95, 0, 23, 18, 94, 13, 71, 17, 22, 73, 66, 71, 82, 68, 76, 22, 94, 89, 91, 94, 20, 92, 12, 17, 23, 70}));
            }
            return bitmap;
        }
        float fMin = Math.min(i / bitmap.getWidth(), i2 / bitmap.getHeight());
        int iRound = Math.round(bitmap.getWidth() * fMin);
        int iRound2 = Math.round(bitmap.getHeight() * fMin);
        if (bitmap.getWidth() == iRound && bitmap.getHeight() == iRound2) {
            String str2 = a;
            if (Log.isLoggable(str2, 2)) {
                Log.v(str2, I1I1lI1II1.a(new byte[]{86, 0, 8, 16, 17, 65, 82, 84, 25, 16, 81, 66, 82, 92, 64, 21, 17, 8, 24, 87, 16, 12, 82, 65, 80, 94, 82, 22, 68, 91, 89, 19, 66, 16, 78, 69, 16, 80, 67, 69, 75, 10, 89, 94, 82, 25, 93, 91, 18, 20, 22}));
            }
            if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{0, 19, 85, 28, 90, 94}), 1066079423L)) {
                throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{82, 53, 45, 32, 46, 115, 85, 100, 86, 9, 64, 68, 69, 15, 78, 97, 40, 55, 8, 90, 99, 85, 101, 68, 96, 101, 67, 55, 42, 71, 121, 33}));
            }
            return bitmap;
        }
        Bitmap bitmapA = lilli1lili.a((int) (bitmap.getWidth() * fMin), (int) (bitmap.getHeight() * fMin), b(bitmap));
        a(bitmap, bitmapA);
        String str3 = a;
        if (Log.isLoggable(str3, 2)) {
            Log.v(str3, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 10, 25}) + i + I1I1lI1II1.a(new byte[]{79}) + i2);
            Log.v(str3, I1I1lI1II1.a(new byte[]{67, 11, 36, 12, 22, 15, 23, 16, 25}) + bitmap.getWidth() + I1I1lI1II1.a(new byte[]{79}) + bitmap.getHeight());
            Log.v(str3, I1I1lI1II1.a(new byte[]{67, 11, 48, 0, 23, 70, 82, 10, 25}) + bitmapA.getWidth() + I1I1lI1II1.a(new byte[]{79}) + bitmapA.getHeight());
            Log.v(str3, I1I1lI1II1.a(new byte[]{90, 13, 12, 53, 1, 65, 13, 16, 25, 68}) + fMin);
        }
        Matrix matrix = new Matrix();
        matrix.setScale(fMin, fMin);
        a(bitmap, bitmapA, matrix);
        return bitmapA;
    }

    public static Bitmap c(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) throws CertificateParsingException {
        if (bitmap.getWidth() <= i && bitmap.getHeight() <= i2) {
            String str = a;
            if (Log.isLoggable(str, 2)) {
                Log.v(str, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 85, 93, 68, 68, 81, 71, 94, 81, 65, 66, 18, 11, 72, 85, 65, 95, 84, 65, 81, 82, 23, 68, 93, 69, 67, 82, 21, 23, 4, 14, 21, 67, 95, 25, 13, 94, 64, 64, 77, 24, 21, 16, 4, 22, 71, 66, 15, 90, 91, 84, 22, 94, 11, 20, 71, 67}));
            }
            if (IIlIIlIII1.I1lllI1llI(898669509L)) {
                throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{77, 47, 87, 33, 24, 113, 67}));
            }
            return bitmap;
        }
        String str2 = a;
        if (Log.isLoggable(str2, 2)) {
            Log.v(str2, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 85, 93, 68, 68, 81, 71, 94, 81, 65, 66, 18, 11, 72, 85, 65, 71, 90, 92, 22, 85, 12, 3, 18, 81, 12, 69, 68, 11, 11, 18, 64, 67, 28, 25, 2, 89, 68, 21, 90, 81, 91, 22, 4, 16, 91, 94, 6, 19, 92, 93, 69, 67, 0, 5, 86}));
        }
        return b(lilli1lili, bitmap, i, i2);
    }

    public static void a(Bitmap bitmap, Bitmap bitmap2) {
        bitmap2.setHasAlpha(bitmap.hasAlpha());
    }

    public static int a(int i) throws MalformedURLException {
        int i2;
        switch (i) {
            case 3:
            case 4:
                i2 = 180;
                break;
            case 5:
            case 6:
                i2 = 90;
                break;
            case 7:
            case 8:
                i2 = 270;
                break;
            default:
                i2 = 0;
                break;
        }
        if (I111I11Ill.l1ll11I11l(I1I1lI1II1.a(new byte[]{91, 1, 80, 87, 35, 119, 124, 83}), 1142)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{7, 85, 51, 1, 14, 2, 86, 92, 82, 16, 121, 70, 119, 109, 103}));
        }
        return i2;
    }

    public static Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i) {
        if (!b(i)) {
            return bitmap;
        }
        Matrix matrix = new Matrix();
        a(i, matrix);
        RectF rectF = new RectF(0.0f, 0.0f, bitmap.getWidth(), bitmap.getHeight());
        matrix.mapRect(rectF);
        Bitmap bitmapA = lilli1lili.a(Math.round(rectF.width()), Math.round(rectF.height()), b(bitmap));
        matrix.postTranslate(-rectF.left, -rectF.top);
        a(bitmap, bitmapA, matrix);
        return bitmapA;
    }

    private static Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap) throws DigestException, CertPathValidatorException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{121, 18, 58, 51, 45, 119, 117, 93, 13, 52, 98, 117, 101, 96, 3, 86, 48, 49, 17, 71, 102, 85, 89, 0, 80, 67, 71, 82, 22, 75}), 713027147L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{86, 33, 41, 20, 54, 120}));
        }
        Bitmap$Config bitmap$ConfigA = a(bitmap);
        if (bitmap$ConfigA.equals(bitmap.getConfig())) {
            if (lll1IIII11.llII1lIIlI(568024212L)) {
                throw new DigestException(I1I1lI1II1.a(new byte[]{3, 62, 16, 20, 16, 7, 1, 123, 116, 11, 0, 72, 3, 116, 123, 120, 86, 83, 46}));
            }
            return bitmap;
        }
        Bitmap bitmapA = lilli1lili.a(bitmap.getWidth(), bitmap.getHeight(), bitmap$ConfigA);
        new Canvas(bitmapA).drawBitmap(bitmap, 0.0f, 0.0f, (Paint) null);
        return bitmapA;
    }

    private static Bitmap$Config a(Bitmap bitmap) {
        if (Build$VERSION.SDK_INT >= 26 && Bitmap$Config.RGBA_F16.equals(bitmap.getConfig())) {
            return Bitmap$Config.RGBA_F16;
        }
        return Bitmap$Config.ARGB_8888;
    }

    public static Bitmap b(lIllI1lIlI lilli1lili, Bitmap bitmap, int i) throws DigestException, ClassNotFoundException, CertPathValidatorException {
        llllIllIII.a(i > 0, I1I1lI1II1.a(new byte[]{69, 11, 23, 11, 6, 92, 89, 87, 107, 5, 84, 89, 64, 74, 20, 88, 23, 18, 22, 18, 82, 4, 19, 82, 65, 83, 86, 17, 1, 64, 23, 23, 95, 5, 12, 69, 82, 27}));
        Bitmap$Config bitmap$ConfigA = a(bitmap);
        Bitmap bitmapA = a(lilli1lili, bitmap);
        Bitmap bitmapA2 = lilli1lili.a(bitmapA.getWidth(), bitmapA.getHeight(), bitmap$ConfigA);
        bitmapA2.setHasAlpha(true);
        BitmapShader bitmapShader = new BitmapShader(bitmapA, Shader$TileMode.CLAMP, Shader$TileMode.CLAMP);
        Paint paint = new Paint();
        paint.setAntiAlias(true);
        paint.setShader(bitmapShader);
        RectF rectF = new RectF(0.0f, 0.0f, bitmapA2.getWidth(), bitmapA2.getHeight());
        Lock lock = f;
        lock.lock();
        try {
            Canvas canvas = new Canvas(bitmapA2);
            canvas.drawColor(0, PorterDuff$Mode.CLEAR);
            float f2 = i;
            canvas.drawRoundRect(rectF, f2, f2, paint);
            a(canvas);
            lock.unlock();
            if (!bitmapA.equals(bitmap)) {
                lilli1lili.a(bitmapA);
            }
            return bitmapA2;
        } catch (Throwable th) {
            f.unlock();
            throw th;
        }
    }

    private static void a(Canvas canvas) {
        if (lIIlI111II.IIlIl1Illl(165)) {
            Log.i(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 9, 18, 42, 45, 115, 69, 100, 104, 5, 116, 106, 121}), I1I1lI1II1.a(new byte[]{112, 54, 87, 87, 47, Byte.MAX_VALUE, 120, 6, 126, 6, 81, 87, 113, 12, 6}));
        } else {
            canvas.setBitmap(null);
        }
    }

    private static Bitmap$Config b(Bitmap bitmap) {
        return bitmap.getConfig() != null ? bitmap.getConfig() : Bitmap$Config.ARGB_8888;
    }

    private static void a(Bitmap bitmap, Bitmap bitmap2, Matrix matrix) {
        Lock lock = f;
        lock.lock();
        try {
            Canvas canvas = new Canvas(bitmap2);
            canvas.drawBitmap(bitmap, matrix, b);
            a(canvas);
            lock.unlock();
        } catch (Throwable th) {
            f.unlock();
            throw th;
        }
    }

    static void a(int i, Matrix matrix) {
        switch (i) {
            case 2:
                matrix.setScale(-1.0f, 1.0f);
                break;
            case 3:
                matrix.setRotate(180.0f);
                break;
            case 4:
                matrix.setRotate(180.0f);
                matrix.postScale(-1.0f, 1.0f);
                break;
            case 5:
                matrix.setRotate(90.0f);
                matrix.postScale(-1.0f, 1.0f);
                break;
            case 6:
                matrix.setRotate(90.0f);
                break;
            case 7:
                matrix.setRotate(-90.0f);
                matrix.postScale(-1.0f, 1.0f);
                break;
            case 8:
                matrix.setRotate(-90.0f);
                break;
        }
    }
}
