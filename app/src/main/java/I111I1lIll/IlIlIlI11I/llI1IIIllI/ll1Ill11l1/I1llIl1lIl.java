package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.graphics.Bitmap;
import android.media.content.II1I11IlI1;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.core.location.llIl1lII1I;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.ObjectStreamException;
import java.nio.ByteBuffer;
import java.security.DigestException;
import java.security.MessageDigest;
import java.security.ProviderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public final class I1llIl1lIl extends I11IlIIll1 {
    private static final String c = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 8, 88, 89, 43, 13, 83, 94, 121, 13});
    private static final byte[] d = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 8, 88, 89, 43, 13, 83, 94, 121, 13}).getBytes(b);
    private final int e;

    public I1llIl1lIl(int i) throws ClassNotFoundException {
        llllIllIII.a(i > 0, I1I1lI1II1.a(new byte[]{69, 11, 23, 11, 6, 92, 89, 87, 107, 5, 84, 89, 64, 74, 20, 88, 23, 18, 22, 18, 82, 4, 19, 82, 65, 83, 86, 17, 1, 64, 23, 23, 95, 5, 12, 69, 82, 27}));
        this.e = i;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11IlIIll1
    protected Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) throws DigestException, ClassNotFoundException, CertPathValidatorException {
        Bitmap bitmapB = II1111I11I.b(lilli1lili, bitmap, this.e);
        if (IllllI11Il.llII1lIIlI(359357514L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{82, 49, 23, 23, 41, 123, 69, 71, 123, 19, 115, 73, 102, 72, 68, 67, 56, 43, 54, 100, 82, 48, 87, 98, 94, 80, 121, 47, 3, 70}));
        }
        return bitmapB;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public boolean equals(Object obj) throws InstantiationException {
        if (II1I11IlI1.llII1lIIlI(164472841L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{5, 44}));
        }
        if (!(obj instanceof I1llIl1lIl)) {
            return false;
        }
        boolean z = this.e == ((I1llIl1lIl) obj).e;
        if (lIIlI111II.Il1IIIIlll(327047447L)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{79, 83, 26, 10, 18, 4, 92, 96, 91, 50, 106, 100, 97, 93, 5, 70, 11, 83, 17}));
        }
        return z;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public int hashCode() throws CertificateParsingException {
        int iB = llIIlI1llI.b(c.hashCode(), llIIlI1llI.b(this.e));
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{79, 38, 56, 15, 90, 68, 5, 70, 1, 40, 66, 120, 77, 123, 71, 79, 51, 42, 40, 6, 120}), 1487636655L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{91, 21, 9, 15, 39, 67, 89, 89, 81, 41, 96, 83, 64}));
        }
        return iB;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public void a(MessageDigest messageDigest) throws ObjectStreamException {
        messageDigest.update(d);
        messageDigest.update(ByteBuffer.allocate(4).putInt(this.e).array());
        if (llIl1lII1I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{113, 43}), 202711306L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{65, 35}));
        }
    }
}
