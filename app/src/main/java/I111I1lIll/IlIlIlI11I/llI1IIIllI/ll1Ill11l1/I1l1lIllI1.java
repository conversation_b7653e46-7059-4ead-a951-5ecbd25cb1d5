package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.I1llllIlII;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lIIlI111II;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.Log;
import java.io.InvalidObjectException;
import java.io.UnsupportedEncodingException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;

/* loaded from: classes.dex */
public final class I1l1lIllI1 implements I1llllIlII, IIll11lI1I<BitmapDrawable> {
    private final Resources a;
    private final IIll11lI1I<Bitmap> b;

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public /* synthetic */ BitmapDrawable d() {
        BitmapDrawable bitmapDrawableB = b();
        if (!I1lllI11II.lll1111l11(I1I1lI1II1.a(new byte[]{116, 80, 83}), 186784912L)) {
            return bitmapDrawableB;
        }
        Log.w(I1I1lI1II1.a(new byte[]{66, 23, 21, 82, 50, 86}), I1I1lI1II1.a(new byte[]{121, 41, 81, 39, 53, 118}));
        return null;
    }

    public static IIll11lI1I<BitmapDrawable> a(Resources resources, IIll11lI1I<Bitmap> iIll11lI1I) throws InvalidObjectException {
        if (iIll11lI1I == null) {
            if (lIIlI111II.l111I1ll1l(3492)) {
                throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{91, 7, 52, 0, 52, 86, 85, 71, 93, 22, 119, 67, 0, 114, 118, 5, 38, 82, 12, 86}));
            }
            return null;
        }
        return new I1l1lIllI1(resources, iIll11lI1I);
    }

    private I1l1lIllI1(Resources resources, IIll11lI1I<Bitmap> iIll11lI1I) {
        this.a = (Resources) llllIllIII.a(resources);
        this.b = (IIll11lI1I) llllIllIII.a(iIll11lI1I);
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public Class<BitmapDrawable> c() {
        return BitmapDrawable.class;
    }

    public BitmapDrawable b() {
        return new BitmapDrawable(this.a, this.b.d());
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public int e() {
        return this.b.e();
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I
    public void f() throws BrokenBarrierException, UnsupportedEncodingException {
        if (Il1I1lllIl.IlII1Illll(168608338L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{5, 83, 80, 12}));
        }
        this.b.f();
        if (androidx.core.location.I1111IIl11.I1lIllll1l(323613720L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{101, 9, 86, 10, 58, 97}));
        }
    }

    @Override // IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.I1llllIlII
    public void a() {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{118, 14, 32}), 8036)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{97, 38, 3, 61, 14, 79, 77, 101}));
        }
        IIll11lI1I<Bitmap> iIll11lI1I = this.b;
        if (iIll11lI1I instanceof I1llllIlII) {
            ((I1llllIlII) iIll11lI1I).a();
        }
    }
}
