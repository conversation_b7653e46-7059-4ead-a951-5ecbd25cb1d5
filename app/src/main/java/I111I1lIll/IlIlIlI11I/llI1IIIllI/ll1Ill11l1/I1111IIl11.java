package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.core.location.IIlIIlIII1;
import java.io.NotSerializableException;
import java.security.KeyManagementException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class I1111IIl11 extends I11II1111l {
    I1111IIl11() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) throws BrokenBarrierException {
        float fHighestOneBit = Math.min(i2 / i4, i / i3) != 0 ? 1.0f / Integer.highestOneBit(r1) : 1.0f;
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.ll1I1lII11(I1I1lI1II1.a(new byte[]{78, 60, 0, 42}), 213838715L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{121, 32, 14, 11, 83}));
        }
        return fHighestOneBit;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) throws KeyManagementException, NotSerializableException {
        if (lIIlI111II.I1lll11llI(8847)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{101, 83, 91, 38}));
        }
        I1IIIIIlI1 i1IIIIIlI1 = I1IIIIIlI1.QUALITY;
        if (IIlIIlIII1.Ill1lIIlIl(8063)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{102, 5, 81, 45, 83}));
        }
        return i1IIIIIlI1;
    }
}
