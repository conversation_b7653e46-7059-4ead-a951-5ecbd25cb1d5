package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.lIIIIII11I;
import android.graphics.Bitmap;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IllllI11Il;
import java.io.IOException;
import java.io.InputStream;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.I1I1l11II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIII1IlI;

/* loaded from: classes.dex */
public class IlllIII1ll implements I1IIlllI1I<InputStream, Bitmap> {
    private final l1lllll11l a;
    private final lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I b;

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ IIll11lI1I<Bitmap> a(InputStream inputStream, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws CertPathBuilderException, IOException, CertificateEncodingException {
        if (I111lIl11I.I11II1I1I1(594350098L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{126, 6, 52, 40, 18, 108, 122, 117, 120, 30, 72, 0, 92, 95, 99, 122, 38, 22, 24, 0, 71, 86, 99}));
        }
        IIll11lI1I<Bitmap> iIll11lI1IA = a(inputStream, i, i2, iIIll1l1l1);
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{15, 41, 54, 41, 58, 108}), I1I1lI1II1.a(new byte[]{97, 35, 85, 82, 38, 66, 70, 87, 125, 39, 88, 99, 112, 92, 97, 79, 14, 84, 85, 75, 67, 12, 2, 71, 121, 97, 95}))) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{70, 85, 7, 1, 46, 0, 92, 96, 120, 23, 98, 98, 12, 8}));
        }
        return iIll11lI1IA;
    }

    public IlllIII1ll(l1lllll11l l1lllll11lVar, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) {
        this.a = l1lllll11lVar;
        this.b = il1IllIl1I;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public boolean a(InputStream inputStream, IIIll1l1l1 iIIll1l1l1) throws NoSuchProviderException {
        if (lIIIIII11I.III11111Il(I1I1lI1II1.a(new byte[]{81, 82, 80, 38, 22, 80, 102, 93, 109, 40, 105, 122, 81, 14, 114, 108, 50, 37, 8, 97, 115, 48, 107, 96, 73, 7}))) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{99, 60, 38, 1, 23, 65, 67, 98, 91, 62, 120, 117, 70, 8, 100, 69, 46, 0, 44}));
        }
        boolean zA = this.a.a(inputStream);
        if (IllllI11Il.llll111lI1(I1I1lI1II1.a(new byte[]{88, 44, 51, 18, 20, 82, 117, 105, 86, 84, 67, 123, 77, 0, 4, 87, 38, 39, 58, 106}))) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{113, 48, 85, 8}));
        }
        return zA;
    }

    public IIll11lI1I<Bitmap> a(InputStream inputStream, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws IOException, CertificateEncodingException {
        boolean z;
        I1Ill11l11 i1Ill11l11;
        if (inputStream instanceof I1Ill11l11) {
            i1Ill11l11 = (I1Ill11l11) inputStream;
            z = false;
        } else {
            z = true;
            i1Ill11l11 = new I1Ill11l11(inputStream, this.b);
        }
        I1I1l11II1 i1I1l11II1A = I1I1l11II1.a(i1Ill11l11);
        try {
            return this.a.a(new llIIII1IlI(i1I1l11II1A), i, i2, iIIll1l1l1, new ll111IIlI1(i1Ill11l11, i1I1l11II1A));
        } finally {
            i1I1l11II1A.b();
            if (z) {
                i1Ill11l11.b();
            }
        }
    }
}
