package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIl1l1IllI;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.l11Il1lI11;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.llI1l11lII;
import IlIIII1ll1.I11ll1IlII.I1Il1l1I1I.IIll1I1III.I1llIlIll1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.graphics.Bitmap;
import android.graphics.Bitmap$CompressFormat;
import android.util.Log;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.UnknownServiceException;
import java.security.GeneralSecurityException;
import java.security.KeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lI11I11111.l1IIII1I1I.llll111lll.l1Il1Il11l.I1l11I1I1I;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llIIlI1llI;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.lllllIll1l;

/* loaded from: classes.dex */
public class Il1IllIl1I implements IIl1l1IllI<Bitmap> {
    private final lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I d;
    private static final String c = I1I1lI1II1.a(new byte[]{117, 13, 22, 8, 3, 69, 114, 94, 90, 11, 84, 85, 71});
    public static final l11Il1lI11<Integer> a = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 123, 13, 68, 93, 84, 73, 113, 91, 1, 14, 6, 87, 66, 79, 112, 90, 94, 70, 69, 0, 23, 65, 94, 12, 89, 53, 23, 4, 14, 92, 67, 73}), 90);
    public static final l11Il1lI11<Bitmap$CompressFormat> b = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 123, 13, 68, 93, 84, 73, 113, 91, 1, 14, 6, 87, 66, 79, 112, 90, 94, 70, 69, 0, 23, 65, 94, 12, 89, 34, 13, 23, 15, 84, 67}));

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Ill1llllll
    public /* synthetic */ boolean a(Object obj, File file, IIIll1l1l1 iIIll1l1l1) throws GeneralSecurityException, UnknownServiceException {
        if (l111Il1lI1.IIll1I11lI(I1I1lI1II1.a(new byte[]{1, 2, 51, 36, 1, 113, 103, 118, 124, 43, 67, 92}))) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{80, 17, 22, 42, 15, 88, 101, 84, 64, 8, 86, 85}));
        }
        boolean zA = a((IIll11lI1I<Bitmap>) obj, file, iIIll1l1l1);
        if (IllIlllIII.l11I11I11l(803)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{91, 86, 9, 48, 38, 82, 110, 104, 107, 55, 93, 69, 125, 0, 82, 88, 3, 13, 37, 98, 73, 20, 121, 98, 123}));
        }
        return zA;
    }

    public Il1IllIl1I(lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) {
        this.d = il1IllIl1I;
    }

    @Deprecated
    public Il1IllIl1I() {
        this.d = null;
    }

    /* JADX WARN: Removed duplicated region for block: B:33:0x010e A[Catch: all -> 0x0280, TRY_LEAVE, TryCatch #0 {all -> 0x0280, blocks: (B:3:0x002d, B:12:0x0071, B:37:0x027c, B:38:0x027f, B:29:0x0101, B:31:0x0106, B:33:0x010e), top: B:45:0x002d }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public boolean a(IIll11lI1I<Bitmap> iIll11lI1I, File file, IIIll1l1l1 iIIll1l1l1) throws UnknownServiceException {
        boolean z;
        String str;
        Bitmap bitmapD = iIll11lI1I.d();
        Bitmap$CompressFormat bitmap$CompressFormatA = a(bitmapD, iIIll1l1l1);
        I1llIlIll1.a(I1I1lI1II1.a(new byte[]{82, 10, 1, 10, 6, 80, 13, 16, 98, 65, 84, 72, 16, 93, 105, 21, 71, 18}), Integer.valueOf(bitmapD.getWidth()), Integer.valueOf(bitmapD.getHeight()), bitmap$CompressFormatA);
        try {
            long jA = lllllIll1l.a();
            int iIntValue = ((Integer) iIIll1l1l1.a(a)).intValue();
            OutputStream outputStream = null;
            try {
                try {
                    FileOutputStream fileOutputStream = new FileOutputStream(file);
                    try {
                        OutputStream i1l11I1I1I = this.d != null ? new I1l11I1I1I(fileOutputStream, this.d) : fileOutputStream;
                        try {
                            bitmapD.compress(bitmap$CompressFormatA, iIntValue, i1l11I1I1I);
                            i1l11I1I1I.close();
                            try {
                                i1l11I1I1I.close();
                            } catch (IOException unused) {
                            }
                            z = true;
                        } catch (IOException e) {
                            e = e;
                            outputStream = i1l11I1I1I;
                            String str2 = c;
                            if (Log.isLoggable(str2, 3)) {
                                Log.d(str2, I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 68, 86, 68, 85, 94, 86, 86, 80, 80, 66, 35, 11, 70, 93, 0, 67}), e);
                            }
                            if (outputStream != null) {
                                try {
                                    outputStream.close();
                                } catch (IOException unused2) {
                                }
                            }
                            z = false;
                            str = c;
                            if (Log.isLoggable(str, 2)) {
                            }
                            return z;
                        } catch (Throwable th) {
                            th = th;
                            outputStream = i1l11I1I1I;
                            if (outputStream != null) {
                                try {
                                    outputStream.close();
                                } catch (IOException unused3) {
                                }
                            }
                            throw th;
                        }
                    } catch (IOException e2) {
                        e = e2;
                        outputStream = fileOutputStream;
                    } catch (Throwable th2) {
                        th = th2;
                        outputStream = fileOutputStream;
                    }
                } catch (IOException e3) {
                    e = e3;
                }
                str = c;
                if (Log.isLoggable(str, 2)) {
                    Log.v(str, I1I1lI1II1.a(new byte[]{116, 11, 15, 21, 16, 80, 68, 67, 92, 0, 16, 71, 92, 77, 92, 21, 22, 24, 18, 87, 10, 65}) + bitmap$CompressFormatA + I1I1lI1II1.a(new byte[]{23, 11, 4, 69, 17, 92, 77, 85, 25}) + llIIlI1llI.a(bitmapD) + I1I1lI1II1.a(new byte[]{23, 13, 12, 69}) + lllllIll1l.a(jA) + I1I1lI1II1.a(new byte[]{27, 68, 13, 21, 22, 92, 88, 94, 74, 68, 86, 95, 71, 84, 85, 65, 88, 65}) + iIIll1l1l1.a(b) + I1I1lI1II1.a(new byte[]{27, 68, 10, 4, 17, 116, 91, 64, 81, 5, 10, 16}) + bitmapD.hasAlpha());
                }
                return z;
            } catch (Throwable th3) {
                th = th3;
            }
        } finally {
            I1llIlIll1.a();
        }
    }

    private Bitmap$CompressFormat a(Bitmap bitmap, IIIll1l1l1 iIIll1l1l1) throws UnknownServiceException {
        Bitmap$CompressFormat bitmap$CompressFormat = (Bitmap$CompressFormat) iIIll1l1l1.a(b);
        if (bitmap$CompressFormat != null) {
            return bitmap$CompressFormat;
        }
        if (bitmap.hasAlpha()) {
            Bitmap$CompressFormat bitmap$CompressFormat2 = Bitmap$CompressFormat.PNG;
            if (Il11II1llI.IlIllIll1I(3074)) {
                throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{1, 20, 41, 15, 12, 4, 126, 85}));
            }
            return bitmap$CompressFormat2;
        }
        return Bitmap$CompressFormat.JPEG;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIl1l1IllI
    public llI1l11lII a(IIIll1l1l1 iIIll1l1l1) {
        return llI1l11lII.TRANSFORMED;
    }
}
