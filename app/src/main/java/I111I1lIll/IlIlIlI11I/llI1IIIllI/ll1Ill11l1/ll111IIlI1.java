package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.accounts.utils.lI1l1I1l1l;
import android.graphics.Bitmap;
import android.media.content.II1I11IlI1;
import java.io.EOFException;
import java.io.IOException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.I1I1l11II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
class ll111IIlI1 implements l1l1I1l1lI {
    private final I1Ill11l11 a;
    private final I1I1l11II1 b;

    ll111IIlI1(I1Ill11l11 i1Ill11l11, I1I1l11II1 i1I1l11II1) {
        this.a = i1Ill11l11;
        this.b = i1I1l11II1;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.l1l1I1l1lI
    public void a() throws EOFException {
        if (II1I11IlI1.IlIllIll1I(203911198L)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{88, 60, 20, 9, 37, 92, 95, 118, 11, 39, 2, 4, 13, 0, 126, 2, 81, 54, 21, 72, 6, 88, 71, 120, 120, 124, 77}));
        }
        this.a.a();
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.l1l1I1l1lI
    public void a(lIllI1lIlI lilli1lili, Bitmap bitmap) throws IOException {
        if (lI1l1I1l1l.I1lllI1llI(421925766L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{117, 37, 50, 92, 6, 67, 121, 73, 84, 15, 82, 74, 64, 117, 78, 12, 55, 32, 8, 126}));
        }
        IOException iOExceptionA = this.b.a();
        if (iOExceptionA != null) {
            if (bitmap == null) {
                throw iOExceptionA;
            }
            lilli1lili.a(bitmap);
            throw iOExceptionA;
        }
    }
}
