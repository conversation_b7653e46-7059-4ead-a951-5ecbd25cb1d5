package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.l11Il1lI11;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.os.Build$VERSION;
import android.os.ParcelFileDescriptor;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.io.IOException;
import java.io.ObjectStreamException;
import java.security.UnrecoverableKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class llll111lI1<T> implements I1IIlllI1I<T, Bitmap> {
    private final llll1I1l11<T> e;
    private final lIllI1lIlI f;
    private final I1llI1ll1l g;
    private static final String c = I1I1lI1II1.a(new byte[]{97, 13, 6, 0, 13, 113, 82, 83, 86, 0, 85, 66});
    public static final l11Il1lI11<Long> a = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 111, 13, 84, 85, 90, 123, 93, 65, 15, 0, 18, 118, 85, 2, 92, 81, 86, 24, 99, 4, 22, 85, 82, 23, 113, 22, 3, 8, 7}), -1L, new I1111l111I());
    public static final l11Il1lI11<Integer> b = l11Il1lI11.a(I1I1lI1II1.a(new byte[]{84, 11, 15, 75, 0, 64, 90, 64, 77, 1, 83, 88, 27, 94, 88, 92, 6, 4, 76, 94, 95, 0, 87, 27, 65, 83, 68, 10, 17, 64, 84, 6, 25, 6, 11, 17, 15, 84, 71, 30, 111, 13, 84, 85, 90, 123, 93, 65, 15, 0, 18, 118, 85, 2, 92, 81, 86, 24, 113, 23, 5, 95, 82, 44, 71, 16, 11, 10, 12}), 2, new I1III1I1Il());
    private static final I1llI1ll1l d = new I1llI1ll1l();

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public boolean a(T t, IIIll1l1l1 iIIll1l1l1) {
        return true;
    }

    public static I1IIlllI1I<AssetFileDescriptor, Bitmap> a(lIllI1lIlI lilli1lili) {
        if (Il1I1lllIl.llII1lIIlI(I1I1lI1II1.a(new byte[]{91, 8, 3, 50, 11, 112, 82, 92, 108, 51, 68, 5, 124, 99, 77, 108, 51, 7}))) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{82, 85, 48}));
        }
        return new llll111lI1(lilli1lili, new Il1IIIIlll(null));
    }

    public static I1IIlllI1I<ParcelFileDescriptor, Bitmap> b(lIllI1lIlI lilli1lili) {
        llll111lI1 llll111li1 = new llll111lI1(lilli1lili, new Il11Il11Il());
        if (IlIIlI11I1.Ill1lIIlIl(885)) {
            throw new UnrecoverableKeyException(I1I1lI1II1.a(new byte[]{2, 45, 47, 63, 84, 81, 122, 119, 12, 42, 92, 73, 2, 83}));
        }
        return llll111li1;
    }

    llll111lI1(lIllI1lIlI lilli1lili, llll1I1l11<T> llll1i1l11) {
        this(lilli1lili, llll1i1l11, d);
    }

    llll111lI1(lIllI1lIlI lilli1lili, llll1I1l11<T> llll1i1l11, I1llI1ll1l i1llI1ll1l) {
        this.f = lilli1lili;
        this.e = llll1i1l11;
        this.g = i1llI1ll1l;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public IIll11lI1I<Bitmap> a(T t, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws IOException {
        if (androidx.core.location.I1111IIl11.I1lIllll1l(1249572468L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{122, 6, 5, 12}));
        }
        long jLongValue = ((Long) iIIll1l1l1.a(a)).longValue();
        if (jLongValue < 0 && jLongValue != -1) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{101, 1, 19, 16, 7, 70, 67, 85, 93, 68, 86, 66, 84, 84, 81, 21, 15, 20, 17, 70, 16, 3, 86, 21, 93, 89, 89, 72, 10, 87, 80, 2, 67, 13, 20, 0, 78, 21, 88, 66, 25, 32, 117, 118, 116, 108, 120, 97, 61, 39, 48, 115, 125, 36, 31, 21, 84, 95, 65, 0, 10, 8, 23}) + jLongValue);
        }
        Integer num = (Integer) iIIll1l1l1.a(b);
        if (num == null) {
            num = 2;
        }
        I11II1111l i11II1111l = (I11II1111l) iIIll1l1l1.a(I11II1111l.h);
        if (i11II1111l == null) {
            i11II1111l = I11II1111l.g;
        }
        I11II1111l i11II1111l2 = i11II1111l;
        MediaMetadataRetriever mediaMetadataRetrieverA = this.g.a();
        try {
            try {
                this.e.a(mediaMetadataRetrieverA, t);
                Bitmap bitmapA = a(mediaMetadataRetrieverA, jLongValue, num.intValue(), i, i2, i11II1111l2);
                mediaMetadataRetrieverA.release();
                return lI1l11lIIl.a(bitmapA, this.f);
            } catch (RuntimeException e) {
                throw new IOException(e);
            }
        } catch (Throwable th) {
            mediaMetadataRetrieverA.release();
            throw th;
        }
    }

    private static Bitmap a(MediaMetadataRetriever mediaMetadataRetriever, long j, int i, int i2, int i3, I11II1111l i11II1111l) {
        Bitmap bitmapB = (Build$VERSION.SDK_INT < 27 || i2 == Integer.MIN_VALUE || i3 == Integer.MIN_VALUE || i11II1111l == I11II1111l.f) ? null : b(mediaMetadataRetriever, j, i, i2, i3, i11II1111l);
        return bitmapB == null ? a(mediaMetadataRetriever, j, i) : bitmapB;
    }

    private static Bitmap b(MediaMetadataRetriever mediaMetadataRetriever, long j, int i, int i2, int i3, I11II1111l i11II1111l) throws ObjectStreamException {
        try {
            int i4 = Integer.parseInt(mediaMetadataRetriever.extractMetadata(18));
            int i5 = Integer.parseInt(mediaMetadataRetriever.extractMetadata(19));
            int i6 = Integer.parseInt(mediaMetadataRetriever.extractMetadata(24));
            if (i6 == 90 || i6 == 270) {
                i5 = i4;
                i4 = i5;
            }
            float fA = i11II1111l.a(i4, i5, i2, i3);
            Bitmap scaledFrameAtTime = mediaMetadataRetriever.getScaledFrameAtTime(j, i, Math.round(i4 * fA), Math.round(fA * i5));
            if (lIlIII1I1l.l111IIlII1(I1I1lI1II1.a(new byte[]{14, 85, 3, 85, 19, 108, 100, 72, 65}), 321717430L)) {
                throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{79, 3}));
            }
            return scaledFrameAtTime;
        } catch (Throwable th) {
            String str = c;
            if (!Log.isLoggable(str, 3)) {
                return null;
            }
            Log.d(str, I1I1lI1II1.a(new byte[]{114, 28, 1, 0, 18, 65, 94, 95, 87, 68, 68, 66, 76, 80, 90, 82, 66, 21, 13, 18, 84, 4, 80, 90, 87, 83, 23, 3, 22, 83, 90, 6, 23, 11, 12, 69, 13, 71, 82, 95, 18}), th);
            return null;
        }
    }

    private static Bitmap a(MediaMetadataRetriever mediaMetadataRetriever, long j, int i) {
        return mediaMetadataRetriever.getFrameAtTime(j, i);
    }
}
