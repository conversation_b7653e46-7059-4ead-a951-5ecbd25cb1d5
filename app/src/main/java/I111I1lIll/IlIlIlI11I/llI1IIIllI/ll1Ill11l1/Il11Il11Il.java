package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.media.MediaMetadataRetriever;
import android.os.ParcelFileDescriptor;
import androidx.constraintlayout.widget.lIIlI111II;
import java.io.IOException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class Il11Il11Il implements llll1I1l11<ParcelFileDescriptor> {
    Il11Il11Il() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.llll1I1l11
    public void a(MediaMetadataRetriever mediaMetadataRetriever, ParcelFileDescriptor parcelFileDescriptor) throws IOException, CertificateException, IllegalArgumentException {
        if (lIIlI111II.llIIl11lIl(160198676L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{98, 51, 41, 18}));
        }
        mediaMetadataRetriever.setDataSource(parcelFileDescriptor.getFileDescriptor());
        if (android.media.content.lIIlI111II.l1IIl11Ill(9482)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{77, 6, 46, 15, 5, 125, 112, 83, 92, 20, 92, 123, 65, 92, 93, 6, 42, 27, 17, 83}));
        }
    }
}
