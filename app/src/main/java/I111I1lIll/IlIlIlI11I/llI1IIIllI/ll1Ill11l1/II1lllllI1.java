package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import androidx.core.location.IIlIIlIII1;
import java.io.IOException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lII1I11I1I.I1IlllI1lI.IIIl1llIlI.IlIll111II.llllIllIII;

/* loaded from: classes.dex */
public class II1lllllI1<DataType> implements I1IIlllI1I<DataType, BitmapDrawable> {
    private final I1IIlllI1I<DataType, Bitmap> a;
    private final Resources b;

    public II1lllllI1(Resources resources, I1IIlllI1I<DataType, Bitmap> i1IIlllI1I) {
        this.b = (Resources) llllIllIII.a(resources);
        this.a = (I1IIlllI1I) llllIllIII.a(i1IIlllI1I);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public boolean a(DataType datatype, IIIll1l1l1 iIIll1l1l1) throws IOException {
        return this.a.a(datatype, iIIll1l1l1);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public IIll11lI1I<BitmapDrawable> a(DataType datatype, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws IOException, BrokenBarrierException {
        IIll11lI1I<BitmapDrawable> iIll11lI1IA = I1l1lIllI1.a(this.b, this.a.a(datatype, i, i2, iIIll1l1l1));
        if (IIlIIlIII1.Il1IIlI1II(163813416L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{90, 60, 4, 43, 47, 118, 121, 106, 81, 21, 126, 105, 12, 113, 92}));
        }
        return iIll11lI1IA;
    }
}
