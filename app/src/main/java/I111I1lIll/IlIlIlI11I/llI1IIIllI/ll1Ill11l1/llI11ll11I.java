package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIlII1Illl;
import IIlII11III.IlI11I11ll.llll111lll.IlIl1IlllI.llIIII1IlI;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lIIlI111II;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.InvalidObjectException;
import java.io.SyncFailedException;
import java.net.SocketTimeoutException;
import java.security.MessageDigest;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class llI11ll11I implements lIlII1Illl<Drawable> {
    private final lIlII1Illl<Bitmap> c;
    private final boolean d;

    public lIlII1Illl<BitmapDrawable> a() throws ClassNotFoundException {
        if (Il1IIllIll.lll1111l11(I1I1lI1II1.a(new byte[]{6, 55, 81, 17, 90, 99, 116, 81, 95, 86, 9, 4, 124, 80, 126, 116, 59, 23, 6, 115, 118, 9, 118, 82, 123}), 299914457L)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{14, 41, 33, 86, 27, 95, 82, 65, Byte.MAX_VALUE, 21, 96, 115, 79, 65, 86, 4, 4, 46, 3, 92, 67, 51, 105, 66, 69, 120, 64}));
        }
        return this;
    }

    public llI11ll11I(lIlII1Illl<Bitmap> lilii1illl, boolean z) {
        this.c = lilii1illl;
        this.d = z;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.lIlII1Illl
    public IIll11lI1I<Drawable> a(Context context, IIll11lI1I<Drawable> iIll11lI1I, int i, int i2) throws CertPathBuilderException, SyncFailedException, IllegalAccessException, NoSuchMethodException, InvalidObjectException, UnrecoverableEntryException {
        if (IlIIlI11I1.Il1IIlI1II(205365153L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{110, 2, 6, 9, 90, 123, 120, 64, 109, 35, 116, 99, 7, 120, 108, 97, 44, 85, 59, 88, 94, 57, 0, 5, 89, 1, 126, 16, 17}));
        }
        lIllI1lIlI lilli1liliA = llIIII1IlI.a(context).a();
        Drawable drawableD = iIll11lI1I.d();
        IIll11lI1I<Bitmap> iIll11lI1IA = llI1lIlIlI.a(lilli1liliA, drawableD, i, i2);
        if (iIll11lI1IA == null) {
            if (this.d) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 83, 95, 91, 79, 81, 71, 22, 65}) + drawableD + I1I1lI1II1.a(new byte[]{23, 16, 13, 69, 3, 21, 117, 89, 77, 9, 81, 64}));
            }
            return iIll11lI1I;
        }
        IIll11lI1I<Bitmap> iIll11lI1IA2 = this.c.a(context, iIll11lI1IA, i, i2);
        if (iIll11lI1IA2.equals(iIll11lI1IA)) {
            iIll11lI1IA2.f();
            return iIll11lI1I;
        }
        IIll11lI1I<Drawable> iIll11lI1IA3 = a(context, iIll11lI1IA2);
        if (l11Il111ll.IlII1Illll(338581401L)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{86, 21, 16, 14, 16, 4, 79, 98, 1, 93, 9, 86, 81, 94, 113, 121, 47, 83, 7, 0, 69, 17, 103}));
        }
        return iIll11lI1IA3;
    }

    private IIll11lI1I<Drawable> a(Context context, IIll11lI1I<Bitmap> iIll11lI1I) throws CertPathBuilderException, InvalidObjectException {
        IIll11lI1I<BitmapDrawable> iIll11lI1IA = I1l1lIllI1.a(context.getResources(), iIll11lI1I);
        if (Il11II1llI.l1l1l1IIlI(7599)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{97, 13, 13, 2, 41, 100, 14, 99, 115, 30, 81, 104, 97, 92, 13, Byte.MAX_VALUE, 8, 2, 11, 116, 119, 35, 67, 126, 120}));
        }
        return iIll11lI1IA;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public boolean equals(Object obj) throws SocketTimeoutException, CertificateException {
        if (obj instanceof llI11ll11I) {
            boolean zEquals = this.c.equals(((llI11ll11I) obj).c);
            if (Ill11ll111.IlIIlIllI1(I1I1lI1II1.a(new byte[]{91, 13, 20, 46, 44}), 1292787998L)) {
                throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{98, 50, 59, 12, 3, 121, 109, 114, 65, 17, 122, 100, 69, 104, 97}));
            }
            return zEquals;
        }
        if (lIIlI111II.l1l11llIl1(280520340L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{112, 41, 26, 13, 10, 76, 99, 99, 65, 44, 102}));
        }
        return false;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public int hashCode() {
        int iHashCode = this.c.hashCode();
        if (l1lI1I1l11.l1l1l1IIlI(184606377L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{123, 7, 22, 60, 16, 0}));
        }
        return iHashCode;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public void a(MessageDigest messageDigest) {
        this.c.a(messageDigest);
    }
}
