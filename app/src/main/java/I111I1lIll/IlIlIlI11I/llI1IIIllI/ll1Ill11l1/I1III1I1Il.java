package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.II1l1lIllI;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.ObjectStreamException;
import java.nio.ByteBuffer;
import java.security.MessageDigest;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1III1I1Il implements II1l1lIllI<Integer> {
    private final ByteBuffer a = ByteBuffer.allocate(4);

    I1III1I1Il() {
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.II1l1lIllI
    public /* synthetic */ void a(byte[] bArr, Integer num, MessageDigest messageDigest) throws ObjectStreamException, InstantiationException {
        a(bArr, num, messageDigest);
        if (lII1llllI1.IlIIlIllI1(I1I1lI1II1.a(new byte[]{103, 29}), 347144613L)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{111, 84, 36, 48, 3, 86, 83, 102, 72, 14, 100, 94, 125, 78, 93, 118, 9, 2, 32, 6, 91, 35, 113, 125, Byte.MAX_VALUE, 80, 88, 47, 49, 93}));
        }
    }

    public void a(byte[] bArr, Integer num, MessageDigest messageDigest) throws ObjectStreamException {
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.Ill1lIIlIl(4788)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{83, 43, 49, 41, 11, 93, 115, 1, 11, 12, 103, 5, 120}));
        }
        if (num == null) {
            return;
        }
        messageDigest.update(bArr);
        synchronized (this.a) {
            this.a.position(0);
            messageDigest.update(this.a.putInt(num.intValue()).array());
        }
    }
}
