package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.accounts.utils.I1lllI11II;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.NoRouteToHostException;
import java.net.SocketTimeoutException;
import java.security.cert.CRLException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateExpiredException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class I1Ill11l11 extends FilterInputStream {
    private volatile byte[] a;
    private int b;
    private int c;
    private int d;
    private int e;
    private final lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I f;

    public I1Ill11l11(InputStream inputStream, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I) {
        this(inputStream, il1IllIl1I, 65536);
    }

    I1Ill11l11(InputStream inputStream, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I il1IllIl1I, int i) {
        super(inputStream);
        this.d = -1;
        this.f = il1IllIl1I;
        this.a = (byte[]) il1IllIl1I.a(i, byte[].class);
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized int available() throws IOException {
        int iAvailable;
        InputStream inputStream = this.in;
        if (this.a == null || inputStream == null) {
            throw c();
        }
        iAvailable = (this.b - this.e) + inputStream.available();
        if (l1l1I111I1.I111IlIl1I(413387974L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{100, 87, 50, 13, 14, 114, 14}));
        }
        return iAvailable;
    }

    private static IOException c() throws IOException {
        throw new IOException(I1I1lI1II1.a(new byte[]{117, 17, 4, 3, 7, 71, 82, 84, 112, 10, 64, 69, 65, 106, 64, 71, 7, 0, 15, 18, 89, 18, 19, 86, 95, 89, 68, 0, 0}));
    }

    public synchronized void a() {
        if (l1lll111II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{6, 35, 42, 8, 6, 12, 68, 92, 80, 9, 86, 120, 70, 114, 93, 12, 22, 37, 87, 113, 96, 21, 2, 125, 70, 98, 103}))) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{78, 11, 56, 40, 54, 126, 93, 4, 67, 84, 100, Byte.MAX_VALUE, 89, 110}));
        }
        this.c = this.a.length;
    }

    public synchronized void b() {
        if (lI1lllIII1.I1lllI1llI(696077214L)) {
            Log.d(I1I1lI1II1.a(new byte[]{101, 47, 3, 38}), I1I1lI1II1.a(new byte[]{122, 60, 46, 63, 50, 5, 1, 101, 87, 33, 67, 86, 80, 123, 112, 93, 22, 3, 14, 6, 90, 39, Byte.MAX_VALUE}));
            return;
        }
        if (this.a != null) {
            this.f.a((lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) this.a);
            this.a = null;
        }
    }

    @Override // java.io.FilterInputStream, java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws NoSuchFieldException, IOException {
        if (this.a != null) {
            this.f.a((lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) this.a);
            this.a = null;
        }
        InputStream inputStream = this.in;
        this.in = null;
        if (inputStream != null) {
            inputStream.close();
        }
        if (androidx.core.location.I1111IIl11.IlII1Illll(1066726709L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{125, 41, 49, 93, 22, 80, 94, 126, 105, 82, 121, 81}));
        }
    }

    private int a(InputStream inputStream, byte[] bArr) throws IOException, CertificateEncodingException {
        if (androidx.core.location.I1111IIl11.I1lIllll1l(249028900L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{77, 42, 13, 49, 27, 4, 67, 95, 96}));
        }
        int i = this.d;
        if (i != -1) {
            int i2 = this.e - i;
            int i3 = this.c;
            if (i2 < i3) {
                if (i == 0 && i3 > bArr.length && this.b == bArr.length) {
                    int length = bArr.length * 2;
                    if (length <= i3) {
                        i3 = length;
                    }
                    byte[] bArr2 = (byte[]) this.f.a(i3, byte[].class);
                    System.arraycopy(bArr, 0, bArr2, 0, bArr.length);
                    this.a = bArr2;
                    this.f.a((lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.Il1IllIl1I) bArr);
                    bArr = bArr2;
                } else if (i > 0) {
                    System.arraycopy(bArr, i, bArr, 0, bArr.length - i);
                }
                int i4 = this.e - this.d;
                this.e = i4;
                this.d = 0;
                this.b = 0;
                int i5 = inputStream.read(bArr, i4, bArr.length - i4);
                int i6 = this.e;
                if (i5 > 0) {
                    i6 += i5;
                }
                this.b = i6;
                if (!llIlI11III.I111IlIl1I(I1I1lI1II1.a(new byte[]{3, 5, 12, 35, 18, 100, 68, 9, 107, 46, 81, 8, 86, 87, 70, 91, 37, 41, 32, 2, 114, 59}), 370888513L)) {
                    return i5;
                }
                Log.d(I1I1lI1II1.a(new byte[]{116, 55, 50, 40, 48, 95, 95}), I1I1lI1II1.a(new byte[]{79, 51, 27, 20, 20, 6, 85, 101, 126, 49, 121, 2, 90, 12}));
                return 0;
            }
        }
        int i7 = inputStream.read(bArr);
        if (i7 > 0) {
            this.d = -1;
            this.e = 0;
            this.b = i7;
        }
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 54, 56, 32, 4, 116, 93, 64, 123, 55, 86, 123, 77, 117, 85, 65, 58, 89, 32, 121, 3, 56, 68, 119, 81, 65, 117, 4, 52, 123, 5, 1}), 507627532L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{79, 54, 87, 85, 13, 65, 90, 6, 104, 42}));
        }
        return i7;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized void mark(int i) {
        if (lIIlI111II.llI1llI1l1(194454774L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{118, 16, 85, 13, 47, 123, 89, 87, 73, 35, 1, 8, 67, 125, 88, 5, 20, 4, 39, 68, 72, 9, 86, 125, 123, 83, 91, 20, 13, 64, 7, 86}));
        }
        this.c = Math.max(this.c, i);
        this.d = this.e;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public boolean markSupported() throws SocketTimeoutException {
        if (I1Ill1lIII.IlIllIll1I(4921)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{116, 52, 9, 28, 35, 5, 84, 120, 64, 53, 65, 95, 109, 87, 103, 67, 52, 5, 8, 84}));
        }
        if (I1lllI11II.I1lI11IIll(I1I1lI1II1.a(new byte[]{6, 22, 36, 9, 49, 111, 70, 71, 90, 86, 105, 86, 116, 117, 7, 100, 37, 27, 58, 6, 119}), 359284026L)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{98, 22, 5, 23, 3, 66, 86, 2, 96, 46, 123, 65, 2, 81, 119, 82, 44, 16, 4, 86, 116, 3, 0, 81, 117, 2, 126, 86}));
        }
        return true;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized int read() throws IOException {
        byte[] bArr = this.a;
        InputStream inputStream = this.in;
        if (bArr == null || inputStream == null) {
            throw c();
        }
        if (this.e >= this.b && a(inputStream, bArr) == -1) {
            return -1;
        }
        if (bArr != this.a && (bArr = this.a) == null) {
            throw c();
        }
        int i = this.b;
        int i2 = this.e;
        if (i - i2 <= 0) {
            return -1;
        }
        this.e = i2 + 1;
        int i3 = bArr[i2] & 255;
        if (IIlII1IIIl.l1l1Il1I11(I1I1lI1II1.a(new byte[]{66, 16, 81, 35, 33, 68, 83, 66, 104, 87, 4, 2, 82}), 164027531L)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{122, 35, 53, 19, 35, 87, 64, 114, 1, 43, 116, 125}));
        }
        return i3;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized int read(byte[] bArr, int i, int i2) throws IOException {
        int i3;
        int i4;
        int i5 = i;
        synchronized (this) {
            if (android.accounts.utils.IIIlIl1I1l.I1II1111ll(433)) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{1, 60, 49, 53, 8, 103, 6, 122, 11, 22, 97, 121, 98, 91, 119, 120, 17, 3, 6, 11, 1, 11, 96, 3, 101, 120, 117, 54}));
            }
            byte[] bArr2 = this.a;
            if (bArr2 == null) {
                throw c();
            }
            if (i2 == 0) {
                return 0;
            }
            InputStream inputStream = this.in;
            if (inputStream == null) {
                throw c();
            }
            int i6 = this.e;
            int i7 = this.b;
            if (i6 < i7) {
                int i8 = i7 - i6 >= i2 ? i2 : i7 - i6;
                System.arraycopy(bArr2, i6, bArr, i5, i8);
                this.e += i8;
                if (i8 != i2 && inputStream.available() != 0) {
                    i5 += i8;
                    i3 = i2 - i8;
                }
                if (androidx.constraintlayout.widget.I1IllIll1l.I1lllI1llI(941027793L)) {
                    throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{123, 0, 86, 83, 46, 81, 4, 120, 104, 86, 3, 98, 6, 95}));
                }
                return i8;
            }
            i3 = i2;
            while (true) {
                if (this.d == -1 && i3 >= bArr2.length) {
                    i4 = inputStream.read(bArr, i5, i3);
                    if (i4 == -1) {
                        return i3 != i2 ? i2 - i3 : -1;
                    }
                } else {
                    if (a(inputStream, bArr2) == -1) {
                        return i3 != i2 ? i2 - i3 : -1;
                    }
                    if (bArr2 != this.a && (bArr2 = this.a) == null) {
                        throw c();
                    }
                    int i9 = this.b;
                    int i10 = this.e;
                    i4 = i9 - i10 >= i3 ? i3 : i9 - i10;
                    System.arraycopy(bArr2, i10, bArr, i5, i4);
                    this.e += i4;
                }
                i3 -= i4;
                if (i3 == 0) {
                    if (l1lI1I1l11.IlII1Illll(160873444L)) {
                        throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{91, 43, 0, 36, 0, 7, 81, 90, 90, 38, 4, 125, 81, 118, Byte.MAX_VALUE, 89, 27, 17, 12, 126, 96, 55, 86, 123, 2, 84, 102, 14, 22, 88, 69, 58}));
                    }
                    return i2;
                }
                if (inputStream.available() == 0) {
                    return i2 - i3;
                }
                i5 += i4;
            }
        }
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized void reset() throws IOException {
        if (this.a == null) {
            throw new IOException(I1I1lI1II1.a(new byte[]{100, 16, 16, 0, 3, 88, 23, 89, 74, 68, 83, 92, 90, 74, 81, 81}));
        }
        int i = this.d;
        if (-1 == i) {
            throw new IlII1llI1I(I1I1lI1II1.a(new byte[]{122, 5, 16, 14, 66, 93, 86, 67, 25, 6, 85, 85, 91, 25, 93, 91, 20, 0, 14, 91, 84, 0, 71, 80, 87, 26, 23, 21, 11, 65, 13, 67}) + this.e + I1I1lI1II1.a(new byte[]{23, 9, 3, 23, 9, 121, 94, 93, 80, 16, 10, 16}) + this.c);
        }
        this.e = i;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public synchronized long skip(long j) throws IOException {
        if (j < 1) {
            return 0L;
        }
        byte[] bArr = this.a;
        if (bArr == null) {
            throw c();
        }
        InputStream inputStream = this.in;
        if (inputStream == null) {
            throw c();
        }
        int i = this.b;
        int i2 = this.e;
        if (i - i2 >= j) {
            this.e = (int) (i2 + j);
            return j;
        }
        long j2 = i - i2;
        this.e = i;
        if (this.d != -1 && j <= this.c) {
            if (a(inputStream, bArr) == -1) {
                return j2;
            }
            int i3 = this.b;
            int i4 = this.e;
            if (i3 - i4 >= j - j2) {
                this.e = (int) ((i4 + j) - j2);
                return j;
            }
            long j3 = (j2 + i3) - i4;
            this.e = i3;
            if (IIIlIll111.IlII1Illll(265673505L)) {
                throw new ClassFormatError(I1I1lI1II1.a(new byte[]{100, 16, 7, 53, 27, 125, 14, 84, 12, 54, 70, 81, 114, 114, 90, 71, 36, 7, 56, 121, 8, 49, 90, 90, 100, 70, 66, 11}));
            }
            return j3;
        }
        long jSkip = j2 + inputStream.skip(j - j2);
        if (ll1l11I1II.I111IlIl1I(I1I1lI1II1.a(new byte[]{99, 53, 24, 52, 23, 109, 7, 66, 114, 49, 120, 6, 97, 85, 86, 87, 50, 52, 16, 81, 94, 39, 88, 103, 71, 94, 67, 22, 13}), 797141057L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{71, 86, 53, 31, 5, 121, 96, 114, 15, 19, 91, 126, 102, 104, 92, 69, 49, 81, 18, 2, 114, 2, 122, 94}));
        }
        return jSkip;
    }
}
