package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.graphics.Bitmap;
import android.util.Log;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.I1llIIIl1l;

/* loaded from: classes.dex */
class I1IllIll1l extends I1llIIIl1l {
    I1IllIll1l() {
    }

    @Override // lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.I1llIIIl1l, lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI
    public void a(Bitmap bitmap) {
        if (l1lll111II.Il1IIlI1II(239980725L)) {
            Log.v(I1I1lI1II1.a(new byte[]{70, 93}), I1I1lI1II1.a(new byte[]{2, 7, 37, 41, 4, 114, 66, 66, 95, 92, 87, 6, 0, 75, 88, 77, 21, 12, 42, 64, 68, 23, 0}));
        }
    }
}
