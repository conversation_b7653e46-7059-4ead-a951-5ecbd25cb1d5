package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.graphics.Bitmap;
import android.media.content.II1I11IlI1;
import androidx.core.location.Il1l11I11I;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.ProviderException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class I1II1lII11 extends I11IlIIll1 {
    private static final String c = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 8, 125, 124, 83, 13, 43, 123, 1, 80});
    private static final byte[] d = I1I1lI1II1.a(new byte[]{126, 85, 83, 84, 43, 4, 91, 121, 85, 8, 30, 121, 89, 112, 88, 124, 14, 40, 83, 3, 121, 79, 95, 89, 122, 7, 126, 44, 45, 94, 91, 42, 25, 8, 14, 84, 43, 89, 91, 1, 8, 8, 1, 30, 124, 8, 125, 124, 83, 13, 43, 123, 1, 80}).getBytes(b);

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11IlIIll1
    protected Bitmap a(lIllI1lIlI lilli1lili, Bitmap bitmap, int i, int i2) {
        return II1111I11I.a(lilli1lili, bitmap, i, i2);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public boolean equals(Object obj) {
        return obj instanceof I1II1lII11;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public int hashCode() throws InvalidKeyException {
        if (II1I11IlI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{88, 41, 49, 1, 48, 5, 122, 66, 119, 7, 73, 67, 83, 105, 68, 95, 0}), 292767687L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{110, 6, 90, 31, 36, 97}));
        }
        int iHashCode = c.hashCode();
        if (Il1l11I11I.llll111lI1(I1I1lI1II1.a(new byte[]{0, 1, 45, 46, 85, 123, 125, 95, 124, 93, 120, 104, 119, 87, 114, 120, 7, 51, 5, Byte.MAX_VALUE, 121, 42, 101, 80}), 8258)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{113, 61, 27, 18, 13, 119, 80, 9, 0, 14, 116, 113, 101, 115, 81, 100, 13, 44, 32, 81, 126, 48, 106, 67, 118, 93, 110, 40, 28, 92}));
        }
        return iHashCode;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1lI11l1l1
    public void a(MessageDigest messageDigest) {
        messageDigest.update(d);
    }
}
