package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.net.BindException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class I1lll111Il extends I11II1111l {
    I1lll111Il() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) throws BindException, CertificateEncodingException {
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.I1II1111ll(1014961808L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{101, 38, 50, 17, 20, 99, 2, 70, 75, 18, 66, 94, 65, 112, 96, 113, 1, 34, 7, 123, 99, 11, 124, 6, 113, 95, 64, 39, 35, 85, 70, 33}));
        }
        int iCeil = (int) Math.ceil(Math.max(i2 / i4, i / i3));
        float f = 1.0f / (r4 << (Math.max(1, Integer.highestOneBit(iCeil)) >= iCeil ? 0 : 1));
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{1, 21, 22, 47}), 1062)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{90, 32, 40, 60, 52, 119, 1, 102, 114, 82, 104, 99, Byte.MAX_VALUE, 79, 103, 83}));
        }
        return f;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) throws CertificateException {
        if (IIlI1ll1ll.l1l1Il1I11(I1I1lI1II1.a(new byte[]{120, 29, 54, 20, 32, 82, 80, 124, 1, 5, 98}), 310845521L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{66, 3, 51}));
        }
        return I1IIIIIlI1.MEMORY;
    }
}
