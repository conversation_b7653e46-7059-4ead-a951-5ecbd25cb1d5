package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.util.Log;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class Il111I11Il extends I11II1111l {
    Il111I11Il() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) {
        return Math.min(i3 / i, i4 / i2);
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) {
        I1IIIIIlI1 i1IIIIIlI1 = I1IIIIIlI1.QUALITY;
        if (!Il1I1lllIl.II1111I11I(I1I1lI1II1.a(new byte[]{70, 84, 23}), 355503620L)) {
            return i1IIIIIlI1;
        }
        Log.i(I1I1lI1II1.a(new byte[]{95, 50, 54, 39, 20, 4, 69, 123, 106, 55, 124, 5, 122, 9, 94, 122, 5, 49, 91, 0, 95, 35, 118, 80, 81, 97, 80, 15, 51, 94, 69, 81}), I1I1lI1II1.a(new byte[]{4, 1, 15, 17, 42, 113, 71, 100, 107, 17, 94, 102}));
        return null;
    }
}
