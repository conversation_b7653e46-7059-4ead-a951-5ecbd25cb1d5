package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.graphics.Bitmap;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.ll1l11I1II;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.SocketTimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class IIIlIl1I1l implements I1IIlllI1I<Bitmap, Bitmap> {
    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ IIll11lI1I<Bitmap> a(Bitmap bitmap, int i, int i2, IIIll1l1l1 iIIll1l1l1) throws IOException {
        IIll11lI1I<Bitmap> iIll11lI1IA = a(bitmap, i, i2, iIIll1l1l1);
        if (ll1l11I1II.I1lllI1llI(757284004L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{99, 30, 1, 19, 54, 95, 121, 82, 99, 20, 91, 64, 82, 87, 4, 93, 38, 17, 80, 118, 114, 16, 96, 89}));
        }
        return iIll11lI1IA;
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.I1IIlllI1I
    public /* synthetic */ boolean a(Bitmap bitmap, IIIll1l1l1 iIIll1l1l1) throws IOException {
        boolean zA = a(bitmap, iIIll1l1l1);
        if (l1l1I111I1.I1lllI1llI(I1I1lI1II1.a(new byte[]{90, 0, 33, 54, 40, 100, 91, 82, 14, 53, 82, 6, 113, 87, 109, 109, 35, 43, 39, 10, 74}))) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{117, 47, 12, 52, 38, 100, 99, 101, 79, 39, 118, 95, 84, 113, 123, 101, 15, 51, 47, 64, 106, 50, 97, 67, 121, 83, 78}));
        }
        return zA;
    }

    public boolean a(Bitmap bitmap, IIIll1l1l1 iIIll1l1l1) throws InterruptedIOException {
        if (llIl1lII1I.l111l1I1Il(I1I1lI1II1.a(new byte[]{115, 62, 80, 0, 81, 115, 15, 3, 116, 40, 7, Byte.MAX_VALUE, 97, 112, 124, 86, 23, 41, 21, 86, 3, 14, 95, 115, 119, 110, 113, 42, 13, 65, 118, 20}), 1020266486L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{14, 39, 48, 10, 8, 92, 92, 86, 92, 15, 89, 114, 67, 125, 120, 76, 22, 52, 36, 84, 95, 47}));
        }
        return true;
    }

    public IIll11lI1I<Bitmap> a(Bitmap bitmap, int i, int i2, IIIll1l1l1 iIIll1l1l1) {
        return new lll1llIIll(bitmap);
    }
}
