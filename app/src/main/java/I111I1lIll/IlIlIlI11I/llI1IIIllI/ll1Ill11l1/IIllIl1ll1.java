package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIIll1l1l1;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIl1l1IllI;
import I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.llI1l11lII;
import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.accounts.utils.Ill11ll111;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.File;
import java.net.PortUnreachableException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
public class IIllIl1ll1 implements IIl1l1IllI<BitmapDrawable> {
    private final lIllI1lIlI a;
    private final IIl1l1IllI<Bitmap> b;

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.Ill1llllll
    public /* synthetic */ boolean a(Object obj, File file, IIIll1l1l1 iIIll1l1l1) {
        boolean zA = a((IIll11lI1I<BitmapDrawable>) obj, file, iIIll1l1l1);
        if (androidx.constraintlayout.widget.I1IllIll1l.I1lIllll1l(I1I1lI1II1.a(new byte[]{15, 93, 5, 41, 51, 120, 93, 89, 114, 7, 116, 74, 101, 114, 99, 121, 11, 57, 42, 99, 72, 16, 120}))) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{100, 42, 85, 35, 24, 2, 85, 93, 125, 49, 65, 121, 121, 123, 99, 12, 43, 6, 53, 10, 121, 7, 92, 122, 90}));
        }
        return zA;
    }

    public IIllIl1ll1(lIllI1lIlI lilli1lili, IIl1l1IllI<Bitmap> iIl1l1IllI) {
        this.a = lilli1lili;
        this.b = iIl1l1IllI;
    }

    public boolean a(IIll11lI1I<BitmapDrawable> iIll11lI1I, File file, IIIll1l1l1 iIIll1l1l1) {
        return this.b.a(new lI1l11lIIl(iIll11lI1I.d().getBitmap(), this.a), file, iIIll1l1l1);
    }

    @Override // I1IIll1lIl.l11Il1lIll.l111I11lII.Il1l1I1Ill.IIl1l1IllI
    public llI1l11lII a(IIIll1l1l1 iIIll1l1l1) throws PortUnreachableException {
        if (Ill11ll111.I1II1111ll(289153866L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{110, 14, 80, 38, 35, 103, 121, 92, 83}));
        }
        llI1l11lII lli1l11liiA = this.b.a(iIIll1l1l1);
        if (II1I11IlI1.I1II1111ll(I1I1lI1II1.a(new byte[]{70, 92, 50, 34, 37, Byte.MAX_VALUE, 121, 104, Byte.MAX_VALUE, 10, 87, 118, 92, 108}))) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{65, 82, 91, 23, 15, 83, 5, 96, 90, 3, 117, 96, 64}));
        }
        return lli1l11liiA;
    }
}
