package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.media.content.IIl1l1IllI;
import java.security.SignatureException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class ll1ll1IlIl extends I11II1111l {
    ll1ll1IlIl() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) {
        return Math.min(1.0f, a.a(i, i2, i3, i4));
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) throws SignatureException {
        I1IIIIIlI1 i1IIIIIlI1 = I1IIIIIlI1.QUALITY;
        if (IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{68, 15, 19, 13, 24}), 226317002L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{89, 39, 1, 52, 6, 115}));
        }
        return i1IIIIIlI1;
    }
}
