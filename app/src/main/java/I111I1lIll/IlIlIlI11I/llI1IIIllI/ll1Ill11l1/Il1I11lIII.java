package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import android.media.content.II1I11IlI1;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.security.ProviderException;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class Il1I11lIII extends I11II1111l {
    Il1I11lIII() {
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public float a(int i, int i2, int i3, int i4) throws CertPathValidatorException {
        if (lIlIII1I1l.llII1lIIlI(510281408L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{70, 55, 27, 53, 49, 111, 89, 92, 12, 40, 106, 117, 96, 11, 118, 68, 82, 13, 19, 125, 69, 22, 4, 71, 4, 91, 116, 52, 81, 80}));
        }
        float fMax = Math.max(i3 / i, i4 / i2);
        if (lIIlI111II.I1111l111I(188852938L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{103, 9, 5, 44, 43, 64, 6, 118, 114, 13, 119, 67, 101, 14, 102, 120, 27, 86}));
        }
        return fMax;
    }

    @Override // I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1.I11II1111l
    public I1IIIIIlI1 b(int i, int i2, int i3, int i4) {
        if (II1I11IlI1.l1l1l1IIlI(864615595L)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{92, 17, 80, 33, 5, 115, 122, 125, 91, 85, 72, 82, 70, 99, 121, 91}));
        }
        return I1IIIIIlI1.QUALITY;
    }
}
