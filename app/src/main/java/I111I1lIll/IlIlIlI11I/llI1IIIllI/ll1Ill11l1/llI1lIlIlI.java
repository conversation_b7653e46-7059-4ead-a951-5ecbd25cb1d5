package I111I1lIll.IlIlIlI11I.llI1IIIllI.ll1Ill11l1;

import IlIIII1ll1.lIl1l1IllI.ll1Ill11l1.II1IIIl11l.IIll11lI1I;
import android.graphics.Bitmap;
import android.graphics.Bitmap$Config;
import android.graphics.Canvas;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import java.security.UnrecoverableEntryException;
import java.util.concurrent.locks.Lock;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lIlIIl1111.l11l1l1l11.ll1Ill11l1.lI11IIlll1.lIllI1lIlI;

/* loaded from: classes.dex */
final class llI1lIlIlI {
    private static final String a = I1I1lI1II1.a(new byte[]{115, 22, 3, 18, 3, 87, 91, 85, 109, 11, 114, 89, 65, 84, 85, 69});
    private static final lIllI1lIlI b = new I1IllIll1l();

    static IIll11lI1I<Bitmap> a(lIllI1lIlI lilli1lili, Drawable drawable, int i, int i2) throws UnrecoverableEntryException {
        Bitmap bitmapB;
        if (IIlI1Il1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{5, 19, 16, 33, 6, 119, 83, 65, 1, 54, 104, 73, 0, 8}), 732651747L)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{125, 15, 54, 10, 18, 7, 6, 99, 81, 38, 0, 66, 115, 117, 77, 6, 45, 43, 14, 121, 66, 6, 116, 65, 6, 103, 1}));
        }
        Drawable current = drawable.getCurrent();
        boolean z = false;
        if (current instanceof BitmapDrawable) {
            bitmapB = ((BitmapDrawable) current).getBitmap();
        } else if (current instanceof Animatable) {
            bitmapB = null;
        } else {
            bitmapB = b(lilli1lili, current, i, i2);
            z = true;
        }
        if (!z) {
            lilli1lili = b;
        }
        return lI1l11lIIl.a(bitmapB, lilli1lili);
    }

    private static Bitmap b(lIllI1lIlI lilli1lili, Drawable drawable, int i, int i2) {
        if (i == Integer.MIN_VALUE && drawable.getIntrinsicWidth() <= 0) {
            String str = a;
            if (Log.isLoggable(str, 5)) {
                Log.w(str, I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 84, 66, 84, 78, 20}) + drawable + I1I1lI1II1.a(new byte[]{23, 16, 13, 69, 32, 92, 67, 93, 88, 20, 16, 71, 92, 77, 92, 21, 54, 0, 16, 85, 85, 21, 29, 102, 122, 108, 114, 58, 43, 96, 126, 36, 126, 42, 35, 41, 66, 87, 82, 83, 88, 17, 67, 85, 21, 77, 92, 80, 66, 37, 16, 83, 71, 0, 81, 89, 86, 22, 95, 4, 23, 18, 89, 12, 23, 13, 12, 17, 16, 92, 89, 67, 80, 7, 16, 71, 92, 93, 64, 93}));
            }
            return null;
        }
        if (i2 == Integer.MIN_VALUE && drawable.getIntrinsicHeight() <= 0) {
            String str2 = a;
            if (Log.isLoggable(str2, 5)) {
                Log.w(str2, I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 84, 66, 84, 78, 20}) + drawable + I1I1lI1II1.a(new byte[]{23, 16, 13, 69, 32, 92, 67, 93, 88, 20, 16, 71, 92, 77, 92, 21, 54, 0, 16, 85, 85, 21, 29, 102, 122, 108, 114, 58, 43, 96, 126, 36, 126, 42, 35, 41, 66, 87, 82, 83, 88, 17, 67, 85, 21, 77, 92, 80, 66, 37, 16, 83, 71, 0, 81, 89, 86, 22, 95, 4, 23, 18, 89, 12, 23, 13, 12, 17, 16, 92, 89, 67, 80, 7, 16, 88, 80, 80, 83, 93, 22}));
            }
            return null;
        }
        if (drawable.getIntrinsicWidth() > 0) {
            i = drawable.getIntrinsicWidth();
        }
        if (drawable.getIntrinsicHeight() > 0) {
            i2 = drawable.getIntrinsicHeight();
        }
        Lock lockA = II1111I11I.a();
        lockA.lock();
        Bitmap bitmapA = lilli1lili.a(i, i2, Bitmap$Config.ARGB_8888);
        try {
            Canvas canvas = new Canvas(bitmapA);
            drawable.setBounds(0, 0, i, i2);
            drawable.draw(canvas);
            canvas.setBitmap(null);
            lockA.unlock();
            if (IllllI11Il.I11II1I1I1(429035138L)) {
                throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{1, 51, 15, 45, 55, 64, 109, 95, 120, 37, 114, 117, 0, 72, 85, 77, 37, 20, 14, 91, 88, 24, 96, 97, 80, 78}));
            }
            return bitmapA;
        } catch (Throwable th) {
            lockA.unlock();
            throw th;
        }
    }
}
