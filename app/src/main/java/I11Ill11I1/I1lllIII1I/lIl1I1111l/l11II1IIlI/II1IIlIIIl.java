package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class II1IIlIIIl extends I1lIl111lI {
    public ByteBuffer data;
    private final l1I11I11Il<II1IIlIIIl> owner;

    public II1IIlIIIl(l1I11I11Il<II1IIlIIIl> l1i11i11il) {
        this.owner = l1i11i11il;
    }

    public ByteBuffer init(long j, int i) {
        this.timeUs = j;
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer == null || byteBuffer.capacity() < i) {
            this.data = ByteBuffer.allocateDirect(i).order(ByteOrder.nativeOrder());
        }
        this.data.position(0);
        this.data.limit(i);
        ByteBuffer byteBuffer2 = this.data;
        if (l1l1IllI11.IllIlI1l1I(I1I1lI1II1.a(new byte[]{71, 8, 82, 3, 86, 64, 117}), 1023237558L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{97, 11, 84, 28, 42, 81, 69, 69, 11, 55}));
        }
        return byteBuffer2;
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1IIl111I1
    public void clear() {
        super.clear();
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer != null) {
            byteBuffer.clear();
        }
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{78, 10, 33, 84, 12, 114, 110, 7, 1}), 1593)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{64, 86, 48, 52, 27, 69, 116, 118, 76, 37, Byte.MAX_VALUE, 8}));
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1lIl111lI
    public void release() {
        this.owner.releaseOutputBuffer(this);
    }
}
