package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import android.media.MediaCodec$CryptoInfo;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.constraintlayout.widget.Il1lII1l1l;
import com.ironsource.mediationsdk.logger.IronSourceError;
import java.net.PortUnreachableException;
import java.security.InvalidParameterException;
import java.security.cert.CertificateExpiredException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* loaded from: classes.dex */
public final class llll1lI1II {
    public int clearBlocks;
    public int encryptedBlocks;
    private final MediaCodec$CryptoInfo frameworkCryptoInfo;
    public byte[] iv;
    public byte[] key;
    public int mode;
    public int[] numBytesOfClearData;
    public int[] numBytesOfEncryptedData;
    public int numSubSamples;
    private final l1l1I1l11l patternHolder;

    public llll1lI1II() {
        MediaCodec$CryptoInfo mediaCodec$CryptoInfo = new MediaCodec$CryptoInfo();
        this.frameworkCryptoInfo = mediaCodec$CryptoInfo;
        this.patternHolder = ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI.SDK_INT >= 24 ? new l1l1I1l11l(mediaCodec$CryptoInfo) : null;
    }

    public void set(int i, int[] iArr, int[] iArr2, byte[] bArr, byte[] bArr2, int i2, int i3, int i4) throws TimeoutException, BrokenBarrierException, PortUnreachableException {
        if (IlIIlI11I1.I111IlIl1I(493649589L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{5, 33, 15, 1, 18, 70, 93, 70, 118, 8, 6, 97, 100, 117, 94, 6, 10, 27, 6, 95, 88, 18, 4, 103, 74, 123, 126, 61, 32}));
        }
        this.numSubSamples = i;
        this.numBytesOfClearData = iArr;
        this.numBytesOfEncryptedData = iArr2;
        this.key = bArr;
        this.iv = bArr2;
        this.mode = i2;
        this.encryptedBlocks = i3;
        this.clearBlocks = i4;
        this.frameworkCryptoInfo.numSubSamples = i;
        this.frameworkCryptoInfo.numBytesOfClearData = iArr;
        this.frameworkCryptoInfo.numBytesOfEncryptedData = iArr2;
        this.frameworkCryptoInfo.key = bArr;
        this.frameworkCryptoInfo.iv = bArr2;
        this.frameworkCryptoInfo.mode = i2;
        if (ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI.SDK_INT >= 24) {
            l1l1I1l11l.access$100((l1l1I1l11l) lIlII1IIl1.checkNotNull(this.patternHolder), i3, i4);
        }
        if (lIIlI111II.lI11IlI1lI(9947)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{100, 54, 36, 85, 90, 123}));
        }
    }

    public MediaCodec$CryptoInfo getFrameworkCryptoInfo() {
        return this.frameworkCryptoInfo;
    }

    public void increaseClearDataFirstSubSampleBy(int i) throws CertificateExpiredException {
        if (i == 0) {
            if (Il1IIllIll.Il1IIlI1II(IronSourceError.ERROR_DO_BN_LOAD_DURING_SHOW)) {
                throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{116, 1, 53, 39, 41, 126, 117, 7, 1, 92, 122, Byte.MAX_VALUE, 96, Byte.MAX_VALUE, 80, 79, 6}));
            }
            return;
        }
        if (this.numBytesOfClearData == null) {
            int[] iArr = new int[1];
            this.numBytesOfClearData = iArr;
            this.frameworkCryptoInfo.numBytesOfClearData = iArr;
        }
        int[] iArr2 = this.numBytesOfClearData;
        iArr2[0] = iArr2[0] + i;
        if (Il1lII1l1l.l11I11I11l(159950736L)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{114, 87, 3, 52, 27}));
        }
    }
}
