package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1lIl111lI;
import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llII1ll111;
import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llIIlI1llI;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.core.location.lIIlI111II;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.lIlI1IIII1;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.net.MalformedURLException;
import java.security.KeyStoreException;
import java.security.SignatureException;
import java.security.cert.CertStoreException;
import java.util.ArrayDeque;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.lIlII1IIl1;

/* loaded from: classes.dex */
public abstract class I1llIII1Il<I extends llII1ll111, O extends I1lIl111lI, E extends llIIlI1llI> implements I1Illl11II<I, O, E> {
    private int availableInputBufferCount;
    private final I[] availableInputBuffers;
    private int availableOutputBufferCount;
    private final O[] availableOutputBuffers;
    private final Thread decodeThread;
    private I dequeuedInputBuffer;
    private E exception;
    private boolean flushed;
    private final Object lock = new Object();
    private final ArrayDeque<I> queuedInputBuffers = new ArrayDeque<>();
    private final ArrayDeque<O> queuedOutputBuffers = new ArrayDeque<>();
    private boolean released;
    private int skippedOutputBufferCount;

    protected abstract I createInputBuffer();

    protected abstract O createOutputBuffer();

    protected abstract E createUnexpectedDecodeException(Throwable th);

    protected abstract E decode(I i, O o, boolean z);

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public /* synthetic */ Object dequeueInputBuffer() throws llIIlI1llI {
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{85, 1, 37, 41, 14, 93, 95, 125}), 362172518L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{80, 12, 4, 53, 17, 111, 113, 85, 83, 38, 2, 96, 1, 8, 4, 86, 11, 39, 53, 124, 119, 52, 84, 64}));
        }
        return dequeueInputBuffer();
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public /* synthetic */ void queueInputBuffer(Object obj) throws llIIlI1llI, CertStoreException {
        if (lIIlI111II.I1111IIl11(8138)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{114, 28, 80, 17, 46, 2, 5, 124, 86, 37, 71, 74, 96, 78, 98, 79, 54, 21, 36, 120, 89, 85}));
        }
        queueInputBuffer((I1llIII1Il<I, O, E>) obj);
        if (I111lIl11I.l11I11I11l(1738)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{68, 47, 24, 44, 55, 94, 96, 95, 112, 0, 122, 96, 3, 78, 113, 124, 56}));
        }
    }

    static /* synthetic */ void access$000(I1llIII1Il i1llIII1Il) throws ClassNotFoundException {
        i1llIII1Il.run();
        if (IlIIlI11I1.I1lllI1llI(341277950L)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{114, 9, 12, 4, 20, 1, 126, 117, 87, 53, 119, 6, 94, 97, 89, 112, 1, 37, 10, 1, 5, 51, 95}));
        }
    }

    protected I1llIII1Il(I[] iArr, O[] oArr) {
        this.availableInputBuffers = iArr;
        this.availableInputBufferCount = iArr.length;
        for (int i = 0; i < this.availableInputBufferCount; i++) {
            ((I[]) this.availableInputBuffers)[i] = createInputBuffer();
        }
        this.availableOutputBuffers = oArr;
        this.availableOutputBufferCount = oArr.length;
        for (int i2 = 0; i2 < this.availableOutputBufferCount; i2++) {
            ((O[]) this.availableOutputBuffers)[i2] = createOutputBuffer();
        }
        llIll1IlI1 llill1ili1 = new llIll1IlI1(this, I1I1lI1II1.a(new byte[]{114, 28, 13, 53, 14, 84, 78, 85, 75, 94, 99, 89, 88, 73, 88, 80, 38, 4, 1, 93, 84, 4, 65}));
        this.decodeThread = llill1ili1;
        llill1ili1.start();
    }

    protected final void setInitialInputBufferSize(int i) throws KeyStoreException {
        if (Ill11ll111.Il1IIlI1II(9522)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{126, 85, 83, 36, 52, 101, 2, 74, 82, 6, 117, 92, 12, 77, 64, 77, 43, 24, 49, 11, 103, 24, 112, 102, 97}));
        }
        lIlII1IIl1.checkState(this.availableInputBufferCount == this.availableInputBuffers.length);
        for (I i2 : this.availableInputBuffers) {
            i2.ensureSpaceForWrite(i);
        }
        if (llIl1lII1I.I1lIllll1l(555102334L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{126, 42, 13, 17, 20, 13, 86, 115, 87, 46, 106, 64, 108, 78, 110, 94, 20, 6, 5, 98, 2, 44}));
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public final I dequeueInputBuffer() throws IllegalAccessException, llIIlI1llI {
        I i;
        if (II1I11IlI1.I1II1111ll(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 7, 39, 13, 11, 108, 95, 64, 81, 20, 89, 120, 103}))) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{117, 10, 7, 44}));
        }
        synchronized (this.lock) {
            maybeThrowException();
            lIlII1IIl1.checkState(this.dequeuedInputBuffer == null);
            int i2 = this.availableInputBufferCount;
            if (i2 == 0) {
                i = null;
            } else {
                I[] iArr = this.availableInputBuffers;
                int i3 = i2 - 1;
                this.availableInputBufferCount = i3;
                i = iArr[i3];
            }
            this.dequeuedInputBuffer = i;
        }
        return i;
    }

    public final void queueInputBuffer(I i) throws llIIlI1llI {
        synchronized (this.lock) {
            maybeThrowException();
            lIlII1IIl1.checkArgument(i == this.dequeuedInputBuffer);
            this.queuedInputBuffers.addLast(i);
            maybeNotifyDecodeLoop();
            this.dequeuedInputBuffer = null;
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public final O dequeueOutputBuffer() throws SignatureException, llIIlI1llI {
        synchronized (this.lock) {
            maybeThrowException();
            if (!this.queuedOutputBuffers.isEmpty()) {
                return this.queuedOutputBuffers.removeFirst();
            }
            if (I1lllI11II.Ill1lIIlIl(7306)) {
                throw new SignatureException(I1I1lI1II1.a(new byte[]{15, 13, 43, 49, 80, 103, 95, 8, 87, 2, 119, 72, 116, 82, 87, 94, 91, 45}));
            }
            return null;
        }
    }

    protected void releaseOutputBuffer(O o) throws MalformedURLException {
        synchronized (this.lock) {
            releaseOutputBufferInternal(o);
            maybeNotifyDecodeLoop();
        }
        if (android.media.content.II1I11IlI1.IlII1Illll(296623144L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{96, 60, 42, 7}));
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public final void flush() {
        synchronized (this.lock) {
            this.flushed = true;
            this.skippedOutputBufferCount = 0;
            I i = this.dequeuedInputBuffer;
            if (i != null) {
                releaseInputBufferInternal(i);
                this.dequeuedInputBuffer = null;
            }
            while (!this.queuedInputBuffers.isEmpty()) {
                releaseInputBufferInternal(this.queuedInputBuffers.removeFirst());
            }
            while (!this.queuedOutputBuffers.isEmpty()) {
                this.queuedOutputBuffers.removeFirst().release();
            }
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1Illl11II
    public void release() throws InterruptedException {
        synchronized (this.lock) {
            this.released = true;
            this.lock.notify();
        }
        try {
            this.decodeThread.join();
        } catch (InterruptedException unused) {
            Thread.currentThread().interrupt();
        }
        if (android.support.v4.graphics.drawable.lIIlI111II.lI1lIIll11(432840327L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{103, 29, 11, 42, 20, 90, 113, 73, 82, 1, 72, 114, 116}));
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: E extends I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llIIlI1llI */
    private void maybeThrowException() throws E, llIIlI1llI {
        E e = this.exception;
        if (e != null) {
            throw e;
        }
        if (l11Il1lI11.l11I11I11l(I1I1lI1II1.a(new byte[]{118, 49, 17, 61, 90, 115, 94, 99, 80, 85, 106, 104, 2, 73, 82, 65, 52, 55, 15, 101, 7, 45, 94, 76, 66, 5}))) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{5, 61, 18, 80, 12, 126, 96}));
        }
    }

    private void maybeNotifyDecodeLoop() {
        if (l11Il1lI11.Il1IIlI1II(I1I1lI1II1.a(new byte[]{102, 15, 42, 3, 36, 97, 5, 95, 110, 22, 89, 65, 66, 73, 119, 83, 86, 15, 45, 85, 70, 82, 73, 70, 114, 91}))) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{14, 29, 21, 6, 26, 122, 89, 115, 106, 22, 98, 7, 112, 107, 110, 112, 81, 89, 50, 90, 105, 52, 74, 6, 113, 83}));
        }
        if (canDecodeBuffer()) {
            this.lock.notify();
        }
    }

    private void run() {
        if (Il1llIl111.I1II1111ll(I1I1lI1II1.a(new byte[]{78, 47, 56, 9, 59, 89, 103, 113, 81, 53, 94, 98, 7, 81, 113, 126, 5}), 176768174L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 86, 1}));
        }
        do {
            try {
            } catch (InterruptedException e) {
                throw new IllegalStateException(e);
            }
        } while (decode());
    }

    private boolean decode() throws InterruptedException {
        E e;
        synchronized (this.lock) {
            while (!this.released && !canDecodeBuffer()) {
                this.lock.wait();
            }
            if (this.released) {
                return false;
            }
            I iRemoveFirst = this.queuedInputBuffers.removeFirst();
            O[] oArr = this.availableOutputBuffers;
            int i = this.availableOutputBufferCount - 1;
            this.availableOutputBufferCount = i;
            O o = oArr[i];
            boolean z = this.flushed;
            this.flushed = false;
            if (iRemoveFirst.isEndOfStream()) {
                o.addFlag(4);
            } else {
                if (iRemoveFirst.isDecodeOnly()) {
                    o.addFlag(Integer.MIN_VALUE);
                }
                if (iRemoveFirst.isFirstSample()) {
                    o.addFlag(lIlI1IIII1.BUFFER_FLAG_FIRST_SAMPLE);
                }
                try {
                    e = (E) decode(iRemoveFirst, o, z);
                } catch (OutOfMemoryError e2) {
                    e = (E) createUnexpectedDecodeException(e2);
                } catch (RuntimeException e3) {
                    e = (E) createUnexpectedDecodeException(e3);
                }
                if (e != null) {
                    synchronized (this.lock) {
                        this.exception = e;
                    }
                    return false;
                }
            }
            synchronized (this.lock) {
                if (this.flushed) {
                    o.release();
                } else if (o.isDecodeOnly()) {
                    this.skippedOutputBufferCount++;
                    o.release();
                } else {
                    o.skippedOutputBufferCount = this.skippedOutputBufferCount;
                    this.skippedOutputBufferCount = 0;
                    this.queuedOutputBuffers.addLast(o);
                }
                releaseInputBufferInternal(iRemoveFirst);
            }
            if (llIlII1IlI.llII1lIIlI(353216503L)) {
                throw new UnknownError(I1I1lI1II1.a(new byte[]{96, 51, 37, 87, 6, 86, 14, 4, 115, 39, 117, 71, 114, 109, 85, 65, 3, 4, 80, Byte.MAX_VALUE, 71, 21}));
            }
            return true;
        }
    }

    private boolean canDecodeBuffer() {
        if (lI11IlI1lI.III111l111(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 6, 48, 43, 21, 95, 121, 118, 116, 85, 2, 105, 124, 78, 101, 109, 32, 80, 5, 3, 1, 47, 67, 102, 81, 6, 82, 16, 5}), I1I1lI1II1.a(new byte[]{65, 54, 51, 31, 32, 95, 66, 89, 72, 86, 98, 91, 96, 104, 85}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{6, 81, 46, 11, 3, 126, 96, 114, Byte.MAX_VALUE, 3, 3, 116, 124, 11, 112}));
        }
        boolean z = !this.queuedInputBuffers.isEmpty() && this.availableOutputBufferCount > 0;
        if (lIlIII1I1l.I11II1I1I1(1620755431L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{15, 62, 4, 82, 5, 124, 85, 67, 84, 46, 103, 105, 82, 72, 12, 83, 83, 15, 55, 125, 122, 35, 74, 67, 105, 15, 125, 13, 83}));
        }
        return z;
    }

    private void releaseInputBufferInternal(I i) {
        if (IIll1llI1l.Ill1lIIlIl(433)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{2, 53, 19, 40, 37, 111, 88, 71, 91, 49, 85, 118, 94, 113, 117, 114, 12, 25, 43, 118, 117}));
        }
        i.clear();
        I[] iArr = this.availableInputBuffers;
        int i2 = this.availableInputBufferCount;
        this.availableInputBufferCount = i2 + 1;
        iArr[i2] = i;
    }

    private void releaseOutputBufferInternal(O o) throws KeyStoreException {
        o.clear();
        O[] oArr = this.availableOutputBuffers;
        int i = this.availableOutputBufferCount;
        this.availableOutputBufferCount = i + 1;
        oArr[i] = o;
        if (IllIIIIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{64, 30, 51, 8, 83}), 394986260L)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{67, 38, 41, 19, 42, 125, 98, 5, 125, 42, 118, 120, 100, 86, 5, 115, 9, 36, 85, 64, 6, 6, 65, 65, 2, 125, 120, 55}));
        }
    }
}
