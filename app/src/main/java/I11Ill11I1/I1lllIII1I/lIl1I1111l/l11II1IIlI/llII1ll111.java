package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.I111I11Ill;
import androidx.media3.common.IIl1Il11Il;
import androidx.media3.common.lllI111lll;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.nio.ByteBuffer;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class llII1ll111 extends I1IIl111I1 {
    public static final int BUFFER_REPLACEMENT_MODE_DIRECT = 2;
    public static final int BUFFER_REPLACEMENT_MODE_DISABLED = 0;
    public static final int BUFFER_REPLACEMENT_MODE_NORMAL = 1;
    private final int bufferReplacementMode;
    public final llll1lI1II cryptoInfo;
    public ByteBuffer data;
    public IIl1Il11Il format;
    private final int paddingSize;
    public ByteBuffer supplementalData;
    public long timeUs;
    public boolean waitingForKeys;

    static {
        lllI111lll.registerModule(I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 6, 25, 84, 92, 7, 95, 84, 80, 75}));
    }

    public static llII1ll111 newNoDataInstance() throws CertificateEncodingException {
        if (l1lll111II.Il1IIlI1II(211929901L)) {
            Log.v(I1I1lI1II1.a(new byte[]{102, 3, 6, 18, 9, 124, 109, 65, 79}), I1I1lI1II1.a(new byte[]{117, 0, 17, 81, 1, 6, 90, 95, 109, 35, 114, 91, 64, 8, 13, 0, 5, 18, 6, 70, 93, 19, 97, 123, 1}));
            return null;
        }
        llII1ll111 llii1ll111 = new llII1ll111(0);
        if (Il1lII1l1l.I111IlIl1I(4861)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{118, 0, 16, 93, 90, 100, 89, 100, 90, 46, 119, 5, 12, 95, 3, 96, 1, 46, 24, 66, 5, 2, 67, 76, 81}));
        }
        return llii1ll111;
    }

    public llII1ll111(int i) {
        this(i, 0);
    }

    public llII1ll111(int i, int i2) {
        this.cryptoInfo = new llll1lI1II();
        this.bufferReplacementMode = i;
        this.paddingSize = i2;
    }

    public void resetSupplementalData(int i) {
        ByteBuffer byteBuffer = this.supplementalData;
        if (byteBuffer == null || byteBuffer.capacity() < i) {
            this.supplementalData = ByteBuffer.allocate(i);
        } else {
            this.supplementalData.clear();
        }
    }

    public void ensureSpaceForWrite(int i) {
        int i2 = i + this.paddingSize;
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer == null) {
            this.data = createReplacementByteBuffer(i2);
            if (lII1llllI1.llII1lIIlI(223098271L)) {
                throw new IllegalStateException(I1I1lI1II1.a(new byte[]{115, 11, 58, 48, 8, 121, 64, 86}));
            }
            return;
        }
        int iCapacity = byteBuffer.capacity();
        int iPosition = byteBuffer.position();
        int i3 = i2 + iPosition;
        if (iCapacity >= i3) {
            this.data = byteBuffer;
            return;
        }
        ByteBuffer byteBufferCreateReplacementByteBuffer = createReplacementByteBuffer(i3);
        byteBufferCreateReplacementByteBuffer.order(byteBuffer.order());
        if (iPosition > 0) {
            byteBuffer.flip();
            byteBufferCreateReplacementByteBuffer.put(byteBuffer);
        }
        this.data = byteBufferCreateReplacementByteBuffer;
    }

    public final boolean isEncrypted() {
        if (lIIlI111II.lI1IIIl1I1(244251200L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{102, 28, 36, 11, 15, 111, 109, 85, 99, 34, 68}));
        }
        return getFlag(1073741824);
    }

    public final void flip() {
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer != null) {
            byteBuffer.flip();
        }
        ByteBuffer byteBuffer2 = this.supplementalData;
        if (byteBuffer2 != null) {
            byteBuffer2.flip();
        }
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1IIl111I1
    public void clear() {
        if (I111I11Ill.IllIlI1l1I(I1I1lI1II1.a(new byte[]{114, 37, 13, 10, 37, 108, 123, 89, 99, 40, 92, 89, 113, 95, 112, 126, 82, 27, 19, 66, 115, 5}), 887771039L)) {
            Log.d(I1I1lI1II1.a(new byte[]{3, 6, 81, 51, 39, 71, 121, 81}), I1I1lI1II1.a(new byte[]{67, 48, 0, 17, 7, 4, 1, 123, 84, 33, 115, 118, 86, 82, 117, 118, 12, 5, 91, 90}));
            return;
        }
        super.clear();
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer != null) {
            byteBuffer.clear();
        }
        ByteBuffer byteBuffer2 = this.supplementalData;
        if (byteBuffer2 != null) {
            byteBuffer2.clear();
        }
        this.waitingForKeys = false;
    }

    private ByteBuffer createReplacementByteBuffer(int i) {
        int i2 = this.bufferReplacementMode;
        if (i2 == 1) {
            return ByteBuffer.allocate(i);
        }
        if (i2 == 2) {
            return ByteBuffer.allocateDirect(i);
        }
        ByteBuffer byteBuffer = this.data;
        throw new I1IIIIIlI1(byteBuffer == null ? 0 : byteBuffer.capacity(), i);
    }
}
