package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import android.media.MediaCodec$CryptoInfo;
import android.media.MediaCodec$CryptoInfo$Pattern;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class l1l1I1l11l {
    private final MediaCodec$CryptoInfo frameworkCryptoInfo;
    private final MediaCodec$CryptoInfo$Pattern pattern;

    static /* synthetic */ void access$100(l1l1I1l11l l1l1i1l11l, int i, int i2) throws TimeoutException, BrokenBarrierException {
        l1l1i1l11l.set(i, i2);
        if (Il11II1llI.IlII1Illll(I1I1lI1II1.a(new byte[]{2, 43, 58, 48, 49, 119, 81, 106, 90, 30, 92, 103, 120, 86, 119, 5}), 410394507L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{88, 50, 32, 8, 35, 0, 69}));
        }
    }

    private l1l1I1l11l(MediaCodec$CryptoInfo mediaCodec$CryptoInfo) {
        this.frameworkCryptoInfo = mediaCodec$CryptoInfo;
        this.pattern = new MediaCodec$CryptoInfo$Pattern(0, 0);
    }

    private void set(int i, int i2) throws TimeoutException {
        this.pattern.set(i, i2);
        this.frameworkCryptoInfo.setPattern(this.pattern);
        if (l1l1IllI11.I111IlIl1I(189586585L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{111, 92, 44}));
        }
    }
}
