package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.llIIlI1llI;

/* loaded from: classes.dex */
public interface I1Illl11II<I, O, E extends llIIlI1llI> {
    I dequeueInputBuffer() throws llIIlI1llI;

    O dequeueOutputBuffer() throws llIIlI1llI;

    void flush();

    String getName();

    void queueInputBuffer(I i) throws llIIlI1llI;

    void release();
}
