package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import androidx.core.location.llIl1lII1I;
import androidx.media3.common.IIl1Il11Il;
import java.nio.ByteBuffer;
import java.security.KeyStoreException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class l1I1l1Il11 extends I1lIl111lI {
    public static final int COLORSPACE_BT2020 = 3;
    public static final int COLORSPACE_BT601 = 1;
    public static final int COLORSPACE_BT709 = 2;
    public static final int COLORSPACE_UNKNOWN = 0;
    public int colorspace;
    public ByteBuffer data;
    public int decoderPrivate;
    public IIl1Il11Il format;
    public int height;
    public int mode;
    private final l1I11I11Il<l1I1l1Il11> owner;
    public ByteBuffer supplementalData;
    public int width;
    public ByteBuffer[] yuvPlanes;
    public int[] yuvStrides;

    public l1I1l1Il11(l1I11I11Il<l1I1l1Il11> l1i11i11il) {
        this.owner = l1i11i11il;
    }

    @Override // I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI.I1lIl111lI
    public void release() {
        this.owner.releaseOutputBuffer(this);
    }

    public void init(long j, int i, ByteBuffer byteBuffer) {
        this.timeUs = j;
        this.mode = i;
        if (byteBuffer != null && byteBuffer.hasRemaining()) {
            addFlag(268435456);
            int iLimit = byteBuffer.limit();
            ByteBuffer byteBuffer2 = this.supplementalData;
            if (byteBuffer2 == null || byteBuffer2.capacity() < iLimit) {
                this.supplementalData = ByteBuffer.allocate(iLimit);
            } else {
                this.supplementalData.clear();
            }
            this.supplementalData.put(byteBuffer);
            this.supplementalData.flip();
            byteBuffer.position(0);
            return;
        }
        this.supplementalData = null;
    }

    public boolean initForYuvFrame(int i, int i2, int i3, int i4, int i5) throws KeyStoreException {
        this.width = i;
        this.height = i2;
        this.colorspace = i5;
        int i6 = (int) ((i2 + 1) / 2);
        if (!isSafeToMultiply(i3, i2) || !isSafeToMultiply(i4, i6)) {
            if (llIl1lII1I.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 80, 6, 84, 5, 79, 93, 98}), 175444266L)) {
                throw new KeyStoreException(I1I1lI1II1.a(new byte[]{114, 37, 27, 10, 49, 7, 67, 102, 120, 80, 117, 115, 13, 11, 122, 70, 6, 27, 55, 1, 5, 59}));
            }
            return false;
        }
        int i7 = i2 * i3;
        int i8 = i6 * i4;
        int i9 = (i8 * 2) + i7;
        if (!isSafeToMultiply(i8, 2) || i9 < i7) {
            return false;
        }
        ByteBuffer byteBuffer = this.data;
        if (byteBuffer == null || byteBuffer.capacity() < i9) {
            this.data = ByteBuffer.allocateDirect(i9);
        } else {
            this.data.position(0);
            this.data.limit(i9);
        }
        if (this.yuvPlanes == null) {
            this.yuvPlanes = new ByteBuffer[3];
        }
        ByteBuffer byteBuffer2 = this.data;
        ByteBuffer[] byteBufferArr = this.yuvPlanes;
        ByteBuffer byteBufferSlice = byteBuffer2.slice();
        byteBufferArr[0] = byteBufferSlice;
        byteBufferSlice.limit(i7);
        byteBuffer2.position(i7);
        ByteBuffer byteBufferSlice2 = byteBuffer2.slice();
        byteBufferArr[1] = byteBufferSlice2;
        byteBufferSlice2.limit(i8);
        byteBuffer2.position(i7 + i8);
        ByteBuffer byteBufferSlice3 = byteBuffer2.slice();
        byteBufferArr[2] = byteBufferSlice3;
        byteBufferSlice3.limit(i8);
        if (this.yuvStrides == null) {
            this.yuvStrides = new int[3];
        }
        int[] iArr = this.yuvStrides;
        iArr[0] = i3;
        iArr[1] = i4;
        iArr[2] = i4;
        return true;
    }

    public void initForPrivateFrame(int i, int i2) {
        this.width = i;
        this.height = i2;
    }

    private static boolean isSafeToMultiply(int i, int i2) {
        return i >= 0 && i2 >= 0 && (i2 <= 0 || i < Integer.MAX_VALUE / i2);
    }
}
