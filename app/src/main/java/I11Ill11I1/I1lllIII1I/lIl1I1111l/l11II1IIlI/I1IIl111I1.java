package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.media3.common.lIlI1IIII1;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.io.InvalidClassException;
import java.security.AccessControlException;
import java.security.cert.CertificateEncodingException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class I1IIl111I1 {
    private int flags;

    public void clear() {
        this.flags = 0;
    }

    public final boolean isDecodeOnly() {
        return getFlag(Integer.MIN_VALUE);
    }

    public final boolean isFirstSample() {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{126, 53, 21, 0, 20, 121, 101, 70, 93, 40, 102, 125, 82}), 216074146L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{103, 33, 42, 29, 17, 1, 126, 100, 124, 48, 65, 66, Byte.MAX_VALUE, 99, 90, 0, 24, 80, 6, 81}));
        }
        return getFlag(lIlI1IIII1.BUFFER_FLAG_FIRST_SAMPLE);
    }

    public final boolean isEndOfStream() throws TimeoutException, BrokenBarrierException {
        if (I1I1IIIIl1.l11I11I11l(1136415839L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{85, 60, 50, 11, 13, 66, 15, 85}));
        }
        boolean flag = getFlag(4);
        if (lIIllIlIl1.Il1IIlI1II(495738963L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{101, 21, 13, 42, 49, 70, 69, 69}));
        }
        return flag;
    }

    public final boolean isKeyFrame() {
        if (!lI11IlI1lI.l11I11I11l(9270)) {
            return getFlag(1);
        }
        Log.w(I1I1lI1II1.a(new byte[]{64, 61, 46, 36}), I1I1lI1II1.a(new byte[]{112, 35, 11}));
        return false;
    }

    public final boolean isLastSample() {
        return getFlag(536870912);
    }

    public final boolean hasSupplementalData() throws BrokenBarrierException, CertificateEncodingException {
        boolean flag = getFlag(268435456);
        if (IIlI1Il1lI.l11I11I11l(212057801L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{67, 47, 83, 21, 0, 100, 83, 66, 105, 16, 96, 70, 86, 92, 126, 2, 19, 20, 8, 98, 66, 83, 88, 103, 117, 89, 116, 87, 8, 112, 118, 42}));
        }
        return flag;
    }

    public final void setFlags(int i) {
        this.flags = i;
    }

    public final void addFlag(int i) throws InvalidClassException {
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{65, 17, 53, 40, 24, 109, 0, 91, 87, 11, 116, 74, 12, 126, 12, 119, 47, 52, 54, 124, 98}), 238062179L)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{97}));
        }
        this.flags = i | this.flags;
        if (I1IllIll1l.IlII1Illll(938013287L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{116, 6, 87, 39, 14, 6, 67, 116, 90, 9, 9, 73, 82, 119, Byte.MAX_VALUE, 109, 9, 48, 83, 121, Byte.MAX_VALUE, 59, 11, 102, 3, 14, 14, 63, 37}));
        }
    }

    public final void clearFlag(int i) {
        this.flags = (~i) & this.flags;
    }

    protected final boolean getFlag(int i) throws BrokenBarrierException {
        boolean z = (this.flags & i) == i;
        if (l11Il1lI11.Il1IIlI1II(I1I1lI1II1.a(new byte[]{3, 14, 5, 55, 55, 115, 84, 71, 75, 81, 64, 101, 1, 77, 115, 98, 16, 51, 52, 3, 103, 54, 68, 95, 71, 124, 88, 9, 46}))) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{15, 44, 19, 36, 81, 98, 116, 88, 124}));
        }
        return z;
    }
}
