package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import androidx.constraintlayout.widget.I1IllIll1l;
import java.net.UnknownHostException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llIll1IlI1 extends Thread {
    final /* synthetic */ I1llIII1Il this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    llIll1IlI1(I1llIII1Il i1llIII1Il, String str) {
        super(str);
        this.this$0 = i1llIII1Il;
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public void run() throws ClassNotFoundException, UnknownHostException {
        I1llIII1Il.access$000(this.this$0);
        if (I1IllIll1l.I1lI11IIll(I1I1lI1II1.a(new byte[]{78, 10, 43, 14, 11, 116, 88, 114, 94, 51, 6, 122, 111, 74}), I1I1lI1II1.a(new byte[]{7, 37, 85, 16, 48, 70, 96, 73, Byte.MAX_VALUE, 32, 67, Byte.MAX_VALUE, 64, 119, 77, 2, 84, 24, 83, 122, 93, 42, 74, 5, 4, 1, Byte.MAX_VALUE}))) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{123, 80, 55, 9, 9, 1, 85, 113, 64, 86, 96}));
        }
    }
}
