package I11Ill11I1.I1lllIII1I.lIl1I1111l.l11II1IIlI;

import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1IIIIIlI1 extends IllegalStateException {
    public final int currentCapacity;
    public final int requiredCapacity;

    public I1IIIIIlI1(int i, int i2) {
        super(I1I1lI1II1.a(new byte[]{117, 17, 4, 3, 7, 71, 23, 68, 86, 11, 16, 67, 88, 88, 88, 89, 66, 73}) + i + I1I1lI1II1.a(new byte[]{23, 88, 66}) + i2 + I1I1lI1II1.a(new byte[]{30}));
        this.currentCapacity = i;
        this.requiredCapacity = i2;
    }
}
