package I11Ill11I1.I1lllIII1I.IIlIlIlI1I.IlII1ll1Il;

import android.support.v4.graphics.drawable.III1Il1II1;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.LinkedList;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class IlllllII1I<E> {
    private int a;
    private LinkedList<E> b = new LinkedList<>();

    public IlllllII1I(int i) {
        this.a = i;
    }

    public void a(E e) {
        if (this.b.size() >= this.a) {
            this.b.poll();
        }
        this.b.offer(e);
    }

    public ArrayList<E> a() {
        ArrayList<E> arrayList = new ArrayList<>();
        for (int i = 0; i < this.b.size(); i++) {
            arrayList.add(this.b.get(i));
        }
        return arrayList;
    }

    public String toString() throws CertificateParsingException {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < this.b.size(); i++) {
            sb.append(this.b.get(i));
            sb.append(" ");
        }
        String string = sb.toString();
        if (III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{102, 62, 51, 1}), 1151309890L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{82, 21, 46, 87, 26, 1, 65, 102, 79, 87, 126, 6, 95, 64, 80, 111, 20}));
        }
        return string;
    }
}
