package I11Ill11I1.I1lllIII1I.IIlIlIlI1I.IlII1ll1Il;

import androidx.constraintlayout.widget.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.NotSerializableException;
import java.net.PortUnreachableException;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class ll11IlI1l1 {
    private static final IlllllII1I<lII11llIIl> a = new IlllllII1I<>(20);

    public static boolean a(Object obj) throws PortUnreachableException, NotSerializableException, ReflectiveOperationException {
        if (IIll1llI1l.Ill1lIIlIl(2036)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{121, 16, 54, 12, 26, 113, 114, 100, 86, 48, 84, 89, 7, 116, 82, 65}));
        }
        String methodName = obj == null ? Thread.currentThread().getStackTrace()[2].getMethodName() : obj.toString();
        Iterator<lII11llIIl> it = a.a().iterator();
        while (it.hasNext()) {
            lII11llIIl next = it.next();
            if (next.a().equals(methodName)) {
                boolean zB = next.b();
                if (lIIlI111II.I1111l111I(8625)) {
                    throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{109, 30, 15, 16, 6, 64, 112, 119, 124, 55, 121, 99, 126, 15, 80, 111, 7, 51}));
                }
                return zB;
            }
        }
        lII11llIIl lii11lliil = new lII11llIIl(methodName);
        a.a(lii11lliil);
        return lii11lliil.b();
    }
}
