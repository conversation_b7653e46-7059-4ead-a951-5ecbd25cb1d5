package I11Ill11I1.I1lllIII1I.IIlIlIlI1I.IlII1ll1Il;

import android.os.SystemClock;
import android.support.v4.graphics.drawable.Il1IIllIll;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.lI1lI11Ill;
import java.io.NotSerializableException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lII11llIIl {
    private String a;
    private long b = 0;

    public lII11llIIl(String str) {
        this.a = str;
    }

    public String a() {
        String str = this.a;
        if (lI1lI11Ill.I111IlIl1I(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 20, 16, 6, 37, 124, 98, 88, 85, 14, 84, 90, 102, 10, 69, 98, 18, 21, 21, 92, 91, 84, 116, 98, 5, 6, 90, 10, 86, 106, 101, 40}), 248500550L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{14, 28, 51, 20, 20, 120, 92, 123, 8, 53, 89, 71, 112, 95, 89, 108, 3, 82, 46, 67, 81, 15, 89, 111, 103, 97, 95, 60, 7, 94}));
        }
        return str;
    }

    public boolean b() throws NotSerializableException {
        if (Il1IIllIll.llll111lI1(I1I1lI1II1.a(new byte[]{83, 93, 6, 81, 43, 96, 68}), 198676794L)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{67, 22, 26, 80, 58, 126, 14, 73, 90, 3, 121, 97, 70, Byte.MAX_VALUE, 117, 83, 82, 17, 19, 122, 125}));
        }
        long jElapsedRealtime = SystemClock.elapsedRealtime();
        if (Math.abs(jElapsedRealtime - this.b) <= 2000) {
            return true;
        }
        this.b = jElapsedRealtime;
        if (IIlIIlIII1.Ill1lIIlIl(6544)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{101, 81, 36, 39, 59, 70, 82, 83, 93, 83, 113, 65, 114, 120, 112, 4, 87, 2, 17, 80, 96}));
        }
        return false;
    }
}
