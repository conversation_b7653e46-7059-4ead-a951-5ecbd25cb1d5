package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIIIII11I;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager$OnAudioFocusChangeListener;
import android.os.Build$VERSION;
import android.os.Handler;
import android.os.Looper;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.core.location.IllIlllIII;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.InterruptedIOException;
import java.security.KeyManagementException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class ll1ll1IlIl {
    static final I1IllIll1l FOCUS_DEFAULT_ATTR = new l111Il1lI1().setUsage(1).build();
    private final I1IllIll1l mAudioAttributesCompat;
    private final Handler mFocusChangeHandler;
    private final int mFocusGain;
    private final Object mFrameworkAudioFocusRequest;
    private final AudioManager$OnAudioFocusChangeListener mOnAudioFocusChangeListener;
    private final boolean mPauseOnDuck;

    ll1ll1IlIl(int i, AudioManager$OnAudioFocusChangeListener audioManager$OnAudioFocusChangeListener, Handler handler, I1IllIll1l i1IllIll1l, boolean z) {
        this.mFocusGain = i;
        this.mFocusChangeHandler = handler;
        this.mAudioAttributesCompat = i1IllIll1l;
        this.mPauseOnDuck = z;
        if (Build$VERSION.SDK_INT < 26 && handler.getLooper() != Looper.getMainLooper()) {
            this.mOnAudioFocusChangeListener = new lll1lll1l1(audioManager$OnAudioFocusChangeListener, handler);
        } else {
            this.mOnAudioFocusChangeListener = audioManager$OnAudioFocusChangeListener;
        }
        if (Build$VERSION.SDK_INT >= 26) {
            this.mFrameworkAudioFocusRequest = II1IIlIIIl.createInstance(i, getAudioAttributes(), z, this.mOnAudioFocusChangeListener, handler);
        } else {
            this.mFrameworkAudioFocusRequest = null;
        }
    }

    public int getFocusGain() {
        return this.mFocusGain;
    }

    public I1IllIll1l getAudioAttributesCompat() {
        return this.mAudioAttributesCompat;
    }

    public boolean willPauseWhenDucked() {
        boolean z = this.mPauseOnDuck;
        if (android.accounts.utils.IIIlIl1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{95, 18, 26, 20, 16, 64, 93, 125, 117, 11, 95, 82, 92, 97, 1, 116, 43, 43, 20, 126, 90, 49, 124, 113, Byte.MAX_VALUE}), 1292)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{124, 86, 7, 48, 10, 83, 6, 125, 114, 51, 64, 122, Byte.MAX_VALUE, 11, 1, 69, 24, 41, 26, 87, 5, 47, 121, 125}));
        }
        return z;
    }

    public AudioManager$OnAudioFocusChangeListener getOnAudioFocusChangeListener() throws CertificateParsingException {
        if (IllIlllIII.Il1IIlI1II(964269422L)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{81, 92, 53, 86, 5, 100, 92, 90, 93, 16, 1, 1, 67, 118, 108, 7, 9, 34, 6, 126, 100, 48, 11, 88, 1, 2, 92}));
        }
        return this.mOnAudioFocusChangeListener;
    }

    public Handler getFocusChangeHandler() throws InterruptedIOException {
        if (lI1lllIII1.I1lllI1llI(361501828L)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{93, 39, 0, 40, 22, 102, 117, 85, 90, 84, 124, 9, 71, 74, 1, 90, 10, 85, 86, 3, 106, 7, 94, 3, 81, 112, 82, 87, 16}));
        }
        Handler handler = this.mFocusChangeHandler;
        if (lIIIIII11I.IlIllIll1I(255623292L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{68, 18, 87, 53, 26, 100, 86, 125, 113, 52, 5, 65, 121, 111, 91, 6, 59, 50, 80, 89, 121, 56, 101, 112, 97, 108, 78, 35, 22, 95, 96, 9}));
        }
        return handler;
    }

    public boolean equals(Object obj) throws KeyManagementException {
        if (this == obj) {
            return true;
        }
        if (obj instanceof ll1ll1IlIl) {
            ll1ll1IlIl ll1ll1ilil = (ll1ll1IlIl) obj;
            return this.mFocusGain == ll1ll1ilil.mFocusGain && this.mPauseOnDuck == ll1ll1ilil.mPauseOnDuck && Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl.equals(this.mOnAudioFocusChangeListener, ll1ll1ilil.mOnAudioFocusChangeListener) && Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl.equals(this.mFocusChangeHandler, ll1ll1ilil.mFocusChangeHandler) && Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl.equals(this.mAudioAttributesCompat, ll1ll1ilil.mAudioAttributesCompat);
        }
        if (llIlI11III.IlIllIll1I(I1I1lI1II1.a(new byte[]{93, 0, 44, 40, 48, 115, 92, 1, 74, 3, 69, 8, 126, 93, Byte.MAX_VALUE, 0, 50, 80, 36, 75, 118, 9, 80, 69, 3, 100, 116, 12, 51, 7}), 230443190L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{118, 49, 23, 22, 13, 89, 82, 116, 85, 42, 5, 74, 101, 12, 113, 101, 53, 55, 46, 6, 105}));
        }
        return false;
    }

    public int hashCode() throws CertStoreException, CertificateEncodingException {
        if (IllllI11Il.III11111Il(368631743L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{109, 43, 36, 34, 19, 111, 103, 104}));
        }
        int iHash = Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl.hash(Integer.valueOf(this.mFocusGain), this.mOnAudioFocusChangeListener, this.mFocusChangeHandler, this.mAudioAttributesCompat, Boolean.valueOf(this.mPauseOnDuck));
        if (llIlII1IlI.l111l1I1Il(I1I1lI1II1.a(new byte[]{99, 55, 27, 44, 48, 115, 64, 66, 92, 9, 71, 98, 71, 13, 67, 69, 24, 41, 49, 75, 124, 51}), 1061570324L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{114, 28, 33, 4, 3, 2, 68, 64}));
        }
        return iHash;
    }

    AudioAttributes getAudioAttributes() throws KeyManagementException {
        if (lI1l1I1l1l.Il1IIlI1II(I1I1lI1II1.a(new byte[]{116, 21, 13, 40, 48, 113, 111, 1, 75, 84, 84, 74, 123, 112, 89, 0, 20, 48, 20, 6, 7, 44, 113, 126, 118, 92, 98, 83, 32, 65, 93, 15}), 427469315L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{110, 29, 15, 34, 51, 76, 91, 125, 9, 20, 72, 101, 67, 83, 120, 65, 82, 40, 81, 124}));
        }
        I1IllIll1l i1IllIll1l = this.mAudioAttributesCompat;
        if (i1IllIll1l != null) {
            return (AudioAttributes) i1IllIll1l.unwrap();
        }
        return null;
    }

    AudioFocusRequest getAudioFocusRequest() {
        return (AudioFocusRequest) this.mFrameworkAudioFocusRequest;
    }
}
