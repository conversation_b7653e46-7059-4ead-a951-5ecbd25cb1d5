package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Parcel;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import androidx.core.location.IIlIIlIII1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.UnsupportedEncodingException;
import java.security.DigestException;
import java.security.InvalidAlgorithmParameterException;
import java.util.concurrent.TimeoutException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIl1I11lIl extends IlIlIlllI1<MediaBrowserCompat$MediaItem> {
    final /* synthetic */ lIIl1I11II this$1;
    final /* synthetic */ lIIIIlIIl1 val$resultWrapper;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIl1I11lIl(lIIl1I11II liil1i11ii, Object obj, lIIIIlIIl1 liiiiliil1) {
        super(obj);
        this.this$1 = liil1i11ii;
        this.val$resultWrapper = liiiiliil1;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    /* synthetic */ void onResultSent(MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem) throws NoSuchFieldException, DigestException, TimeoutException, ClassNotFoundException, InvalidAlgorithmParameterException {
        onResultSent(mediaBrowserCompat$MediaItem);
        if (IIlIIlIII1.Ill1lIIlIl(3399)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{110, 80, 32, 39, 59, 118, 96, Byte.MAX_VALUE, 117, 87}));
        }
    }

    void onResultSent(MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem) throws NoSuchFieldException, DigestException, TimeoutException, ClassNotFoundException, InvalidAlgorithmParameterException {
        if (android.accounts.utils.IIIlIl1I1l.l11I11I11l(343211347L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{95}));
        }
        if (mediaBrowserCompat$MediaItem == null) {
            this.val$resultWrapper.sendResult(null);
        } else {
            Parcel parcelObtain = Parcel.obtain();
            mediaBrowserCompat$MediaItem.writeToParcel(parcelObtain, 0);
            this.val$resultWrapper.sendResult(parcelObtain);
        }
        if (llIlII1IlI.IlII1Illll(275505549L)) {
            throw new TimeoutException(I1I1lI1II1.a(new byte[]{93, 43, 5, 36, 51, 7, 116, 120, 118, 21, 9, 113, 88, 92, 12}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void detach() throws UnsupportedEncodingException {
        this.val$resultWrapper.detach();
    }
}
