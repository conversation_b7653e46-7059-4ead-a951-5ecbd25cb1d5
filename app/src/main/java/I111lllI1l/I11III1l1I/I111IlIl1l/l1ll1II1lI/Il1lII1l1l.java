package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.I1II1llI1I;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IBinder$DeathRecipient;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.security.InvalidKeyException;
import java.util.HashMap;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class Il1lII1l1l implements IBinder$DeathRecipient {
    public final llIllIlll1 browserInfo;
    public final IIll1IIlII callbacks;
    public final int pid;
    public final String pkg;
    public IlIllIll1I root;
    public final Bundle rootHints;
    public final HashMap<String, List<I1II1llI1I<IBinder, Bundle>>> subscriptions = new HashMap<>();
    final /* synthetic */ lIIIIll1II this$0;
    public final int uid;

    Il1lII1l1l(lIIIIll1II liiiill1ii, String str, int i, int i2, Bundle bundle, IIll1IIlII iIll1IIlII) {
        this.this$0 = liiiill1ii;
        this.pkg = str;
        this.pid = i;
        this.uid = i2;
        this.browserInfo = new llIllIlll1(str, i, i2);
        this.rootHints = bundle;
        this.callbacks = iIll1IIlII;
    }

    @Override // android.os.IBinder$DeathRecipient
    public void binderDied() throws InvalidKeyException {
        if (lIIlI111II.IIl1lIII11(7946)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{124, 5, 46, 87, 19, 2, 111, 116, 126, 44, 87, 87, 89, 75, Byte.MAX_VALUE, 80, 52, 8, 44, 7}));
        }
        this.this$0.mHandler.post(new llIl1IlllI(this));
    }
}
