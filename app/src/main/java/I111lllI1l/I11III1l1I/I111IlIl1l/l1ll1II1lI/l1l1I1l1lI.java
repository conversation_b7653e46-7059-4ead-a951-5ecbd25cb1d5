package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioFocusRequest;
import android.media.AudioManager;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.InvalidClassException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class l1l1I1l1lI {
    static int abandonAudioFocusRequest(AudioManager audioManager, AudioFocusRequest audioFocusRequest) throws InvalidClassException {
        int iAbandonAudioFocusRequest = audioManager.abandonAudioFocusRequest(audioFocusRequest);
        if (Il11II1llI.I1lllI1llI(218248309L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 33, 51, 63, 51, 116, 90, 90, 9, 40, 95, 84, 126, 120, 115, 93}));
        }
        return iAbandonAudioFocusRequest;
    }

    static int requestAudioFocus(AudioManager audioManager, AudioFocusRequest audioFocusRequest) {
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{77, 33, 83, 16, 46, 88, 69, 88, 1, 86, 8, 70, 124, 73, 77, 116, 56, 84, 8, 123, 124, 56, 98, 91, 113, 103, 126, 1, 39, 74, 93, 36}))) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{79, 42, 6, 42, 16, 6, 121, 68, 118, 13, 82, 73, 126, 125, 71, 69, 37, 38, 22, 89, 113, 21, 117, 87, 101, 110, 67}));
        }
        return audioManager.requestAudioFocus(audioFocusRequest);
    }

    private l1l1I1l1lI() {
    }
}
