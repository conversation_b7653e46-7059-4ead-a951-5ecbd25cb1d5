package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.text.TextUtils;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.ll1l11I1II;
import java.io.InterruptedIOException;
import java.io.ObjectStreamException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertStoreException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IlIl1l1III implements l1l1I1llII {
    private String mPackageName;
    private int mPid;
    private int mUid;

    IlIl1l1III(String str, int i, int i2) {
        this.mPackageName = str;
        this.mPid = i;
        this.mUid = i2;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.l1l1I1llII
    public String getPackageName() {
        return this.mPackageName;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.l1l1I1llII
    public int getPid() {
        int i = this.mPid;
        if (android.accounts.utils.IIIlIl1I1l.Il1IIlI1II(5745)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{123, 12, 54, 9, 80, 2, 101, 6, 97, 30}));
        }
        return i;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.l1l1I1llII
    public int getUid() throws InterruptedIOException {
        int i = this.mUid;
        if (androidx.constraintlayout.widget.Il1lII1l1l.Il1IIlI1II(955)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{3, 15, 32, 12, 11, 96, 109, 89, 83, 33, 1}));
        }
        return i;
    }

    public boolean equals(Object obj) throws ObjectStreamException, InterruptedIOException {
        if (lIIllIlIl1.Il1IIlI1II(300993470L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{2, 53, 8}));
        }
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof IlIl1l1III)) {
            return false;
        }
        IlIl1l1III ilIl1l1III = (IlIl1l1III) obj;
        if (this.mPid >= 0 && ilIl1l1III.mPid >= 0) {
            return TextUtils.equals(this.mPackageName, ilIl1l1III.mPackageName) && this.mPid == ilIl1l1III.mPid && this.mUid == ilIl1l1III.mUid;
        }
        boolean z = TextUtils.equals(this.mPackageName, ilIl1l1III.mPackageName) && this.mUid == ilIl1l1III.mUid;
        if (lI11IlI1lI.III11111Il(I1I1lI1II1.a(new byte[]{101, 43, 7, 1, 20, 115}))) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{111, 30, 9, 13, 80, 111, 124, 114, 126, 85, Byte.MAX_VALUE, 68, 90, 12, 5, 89, 23, 85, 59, 69, 103, 44, 105, 6}));
        }
        return z;
    }

    public int hashCode() throws CertPathBuilderException, CertStoreException {
        int iHash = Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.IIIl11Illl.hash(this.mPackageName, Integer.valueOf(this.mUid));
        if (ll1l11I1II.l1l1l1IIlI(I1I1lI1II1.a(new byte[]{124, 49, 49, 84, 12, 2, 67, 6, 72, 92, 95, 73, 81, 123, 85, 115, 27, 21, 24, 2, 5, 44}))) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{15, 29, 23, 34, 20, 100, 125, 118, 123, 29, 66, 104, 114, 78, 78, 122, 55, 84, 20, 81, 103, 9, 6, 70, 118, 100, 110, 21, 83}));
        }
        return iHash;
    }
}
