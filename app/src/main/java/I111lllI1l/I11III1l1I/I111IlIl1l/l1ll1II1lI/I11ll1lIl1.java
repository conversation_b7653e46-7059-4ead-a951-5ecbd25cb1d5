package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.session.MediaSessionManager$RemoteUserInfo;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.security.cert.CRLException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
final class I11ll1lIl1 extends IlIl1l1III {
    final MediaSessionManager$RemoteUserInfo mObject;

    I11ll1lIl1(String str, int i, int i2) {
        super(str, i, i2);
        this.mObject = new MediaSessionManager$RemoteUserInfo(str, i, i2);
    }

    I11ll1lIl1(MediaSessionManager$RemoteUserInfo mediaSessionManager$RemoteUserInfo) {
        super(mediaSessionManager$RemoteUserInfo.getPackageName(), mediaSessionManager$RemoteUserInfo.getPid(), mediaSessionManager$RemoteUserInfo.getUid());
        this.mObject = mediaSessionManager$RemoteUserInfo;
    }

    static String getPackageName(MediaSessionManager$RemoteUserInfo mediaSessionManager$RemoteUserInfo) throws CRLException {
        String packageName = mediaSessionManager$RemoteUserInfo.getPackageName();
        if (l1l1IllI11.llII1lIIlI(504131675L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{92, 12, 91, 53, 91, 90, 65, 7, 118, 5, 8, 105, Byte.MAX_VALUE, 120, 81, 81, 14, 38, 39, 99, 103, 4, 95, 1, 107}));
        }
        return packageName;
    }
}
