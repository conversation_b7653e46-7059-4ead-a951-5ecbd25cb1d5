package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Parcel;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.util.ArrayList;
import java.util.List;

/* loaded from: classes.dex */
class lllIl1lllI extends IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> {
    final /* synthetic */ Il1llllI1I this$1;
    final /* synthetic */ lIIIIlIIl1 val$resultWrapper;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lllIl1lllI(Il1llllI1I il1llllI1I, Object obj, lIIIIlIIl1 liiiiliil1) {
        super(obj);
        this.this$1 = il1llllI1I;
        this.val$resultWrapper = liiiiliil1;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void onResultSent(List<MediaBrowserCompat$MediaItem> list) throws NoSuchFieldException, ClassNotFoundException, InvalidAlgorithmParameterException {
        ArrayList arrayList;
        if (list != null) {
            arrayList = new ArrayList(list.size());
            for (MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem : list) {
                Parcel parcelObtain = Parcel.obtain();
                mediaBrowserCompat$MediaItem.writeToParcel(parcelObtain, 0);
                arrayList.add(parcelObtain);
            }
        } else {
            arrayList = null;
        }
        this.val$resultWrapper.sendResult(arrayList);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void detach() throws UnsupportedEncodingException {
        this.val$resultWrapper.detach();
    }
}
