package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIIIII11I;
import android.media.AudioAttributes;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.util.Log;
import android.util.SparseIntArray;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.Illl1Il11l;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.CharConversionException;
import java.io.InterruptedIOException;
import java.io.InvalidObjectException;
import java.io.UTFDataFormatException;
import java.net.PortUnreachableException;
import java.security.DigestException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateNotYetValidException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class I1IllIll1l implements Illl1Il11l {
    public static final int CONTENT_TYPE_MOVIE = 3;
    public static final int CONTENT_TYPE_MUSIC = 2;
    public static final int CONTENT_TYPE_SONIFICATION = 4;
    public static final int CONTENT_TYPE_SPEECH = 1;
    public static final int CONTENT_TYPE_UNKNOWN = 0;
    static final int FLAG_ALL = 1023;
    static final int FLAG_ALL_PUBLIC = 273;
    public static final int FLAG_AUDIBILITY_ENFORCED = 1;
    static final int FLAG_BEACON = 8;
    static final int FLAG_BYPASS_INTERRUPTION_POLICY = 64;
    static final int FLAG_BYPASS_MUTE = 128;
    static final int FLAG_DEEP_BUFFER = 512;
    public static final int FLAG_HW_AV_SYNC = 16;
    static final int FLAG_HW_HOTWORD = 32;
    static final int FLAG_LOW_LATENCY = 256;
    static final int FLAG_SCO = 4;
    static final int FLAG_SECURE = 2;
    static final int INVALID_STREAM_TYPE = -1;
    private static final int[] SDK_USAGES;
    private static final int SUPPRESSIBLE_CALL = 2;
    private static final int SUPPRESSIBLE_NOTIFICATION = 1;
    private static final SparseIntArray SUPPRESSIBLE_USAGES;
    static final String TAG = I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 116, 67, 68, 75, 13, 82, 69, 65, 92, 71, 118, 13, 12, 18, 83, 68});
    public static final int USAGE_ALARM = 4;
    public static final int USAGE_ASSISTANCE_ACCESSIBILITY = 11;
    public static final int USAGE_ASSISTANCE_NAVIGATION_GUIDANCE = 12;
    public static final int USAGE_ASSISTANCE_SONIFICATION = 13;
    public static final int USAGE_ASSISTANT = 16;
    public static final int USAGE_GAME = 14;
    public static final int USAGE_MEDIA = 1;
    public static final int USAGE_NOTIFICATION = 5;
    public static final int USAGE_NOTIFICATION_COMMUNICATION_DELAYED = 9;
    public static final int USAGE_NOTIFICATION_COMMUNICATION_INSTANT = 8;
    public static final int USAGE_NOTIFICATION_COMMUNICATION_REQUEST = 7;
    public static final int USAGE_NOTIFICATION_EVENT = 10;
    public static final int USAGE_NOTIFICATION_RINGTONE = 6;
    public static final int USAGE_UNKNOWN = 0;
    static final int USAGE_VIRTUAL_SOURCE = 15;
    public static final int USAGE_VOICE_COMMUNICATION = 2;
    public static final int USAGE_VOICE_COMMUNICATION_SIGNALLING = 3;
    static boolean sForceLegacyBehavior;
    public IIlIl1IIl1 mImpl;

    static {
        SparseIntArray sparseIntArray = new SparseIntArray();
        SUPPRESSIBLE_USAGES = sparseIntArray;
        sparseIntArray.put(5, 1);
        sparseIntArray.put(6, 2);
        sparseIntArray.put(7, 2);
        sparseIntArray.put(8, 1);
        sparseIntArray.put(9, 1);
        sparseIntArray.put(10, 1);
        SDK_USAGES = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16};
    }

    public I1IllIll1l() {
    }

    I1IllIll1l(IIlIl1IIl1 iIlIl1IIl1) {
        this.mImpl = iIlIl1IIl1;
    }

    public int getVolumeControlStream() {
        if (l1lll111II.IlII1Illll(I1I1lI1II1.a(new byte[]{117, 39, 8, 15, 14, 100, Byte.MAX_VALUE, 89, Byte.MAX_VALUE, 3, 67, 93, 87, 65, 87, 101, 12, 3, 59, 94, 7, 4, 123, 1, 10, 101, 82, 29, 92, 67, 90, 15}))) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{121, 20, 4, 46, 45, 68, 126, 123, 64, 37, 101, 72, 125, 116, 125, 94, 81, 0, 48, 101, 2}));
        }
        return this.mImpl.getVolumeControlStream();
    }

    public Object unwrap() throws NoSuchAlgorithmException {
        if (l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{86, 8, 35, 92, 83, 119, 6, 124, 95, 93, 120, 73, 89, 90, 80, 115, 5, 6, 1, 120, 82, 8, 69, 113, 113, 110, 112}), 9999)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{68, 2, 26, 13, 43, 87, 114, 94, 94, 38, 102, 3, 67, 125, 110, 64, 0}));
        }
        Object audioAttributes = this.mImpl.getAudioAttributes();
        if (l1l1IllI11.l1Il11I1Il(I1I1lI1II1.a(new byte[]{102, 51, 1, 93, 1, 69, 92, 73}), 10932)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{1, 47, 51, 47, 27, 88, 6, 71, 110, 6, 8, 84, 99, 95, 70, 94, 20, 84, 10, 11, 114, 84, 69, 101, 3, 1, 82, 34, 32}));
        }
        return audioAttributes;
    }

    public int getLegacyStreamType() {
        return this.mImpl.getLegacyStreamType();
    }

    public static I1IllIll1l wrap(Object obj) throws CertificateNotYetValidException {
        if (sForceLegacyBehavior) {
            return null;
        }
        if (Build$VERSION.SDK_INT >= 26) {
            return new I1IllIll1l(new I1II111III((AudioAttributes) obj));
        }
        I1IllIll1l i1IllIll1l = new I1IllIll1l(new IIll1l1111((AudioAttributes) obj));
        if (Il11II1llI.l1l1l1IIlI(3218)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{68, 52, 13, 61, 39, 65, 95, 120, 77, 46, 115, 67}));
        }
        return i1IllIll1l;
    }

    public int getContentType() {
        return this.mImpl.getContentType();
    }

    public int getUsage() {
        int usage = this.mImpl.getUsage();
        if (lI1l1I1l1l.l11I11I11l(7876)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{69, 49, 38, 17, 22, 118, 81, 73, Byte.MAX_VALUE, 13, 125, 106, 123, 77, 91}));
        }
        return usage;
    }

    public int getFlags() throws InvalidObjectException {
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{89, 80, 44, 31, 8, 92, 69, 103, 92, 38, 64, 0, 113, 84, 7, 81, 38, 46, 44, 112, 83, 83, 97, 1, 126, 90, 64, 80, 9}))) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{90, 10, 33, 55, 20, 96, 118, 126}));
        }
        int flags = this.mImpl.getFlags();
        if (I1lllI11II.IlIllIll1I(213786674L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{125, 93, 18, 35, 27, 111, 113, 116, 96, 2, 84, 94, 120, 9, 80, 100, 13, 34, 56, 70, 71, 41, 6, 97, 74}));
        }
        return flags;
    }

    public int hashCode() throws InterruptedIOException {
        if (llIlII1IlI.llll111lI1(I1I1lI1II1.a(new byte[]{90, 46, 22, 7, 85, 67, 1}))) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{117, 87, 47, 60, 15, 69, 85, 100, 123, 60, 67, 88, 97, 106, 97, 7, 52, 23, 87, 1, 97, 35, 99, 103, 113, 92}));
        }
        return this.mImpl.hashCode();
    }

    public String toString() {
        String string = this.mImpl.toString();
        if (lIIlI111II.IlIlII11Il(233319905L)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{4, 28, 48, 54, 58, 66, 123, 94, 119, 43, 65, 119, 2, 107, 101, 82, 86, 4, 56, 106, 105, 10, 113, 113, 81, Byte.MAX_VALUE, 103, 0, 19, 101}));
        }
        return string;
    }

    static String usageToString(int i) throws CharConversionException, InvalidObjectException, CertificateNotYetValidException, CloneNotSupportedException {
        if (android.media.content.IIl1l1IllI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{64, 12, 46, 7, 14, 90, 81, 94, 97, 93, 66, 86, 90, 65, 68}), 194025277L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{98, 10, 36, 54, 19, 123, 70, 85, 14, 38, 88, 94, 122, 11, 88, 88, 12, 8, 50, 4, 71, 49, 118, 108, 114, 103, 89, 55, 20, 83, 121, 37}));
        }
        switch (i) {
            case 0:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 98, 126, 114, 42, Byte.MAX_VALUE, 103, 123});
            case 1:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 122, 117, 125, 45, 113});
            case 2:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 97, Byte.MAX_VALUE, 112, 39, 117, 111, 118, 118, 121, 120, 55, 47, 43, 113, 113, 53, 122, 122, 125});
            case 3:
                String strA = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 97, Byte.MAX_VALUE, 112, 39, 117, 111, 118, 118, 121, 120, 55, 47, 43, 113, 113, 53, 122, 122, 125, 105, 100, 44, 35, 124, 118, 47, 123, 45, 44, 34});
                if (!l11Il111ll.Il1IIlI1II(I1I1lI1II1.a(new byte[]{90, 82, 53, 38, 22, 94, 80, 7, 116, 43, 67, 71, 2, Byte.MAX_VALUE, 77, 83, 44, 40, 3}), 7960)) {
                    return strA;
                }
                Log.w(I1I1lI1II1.a(new byte[]{113, 17, 43, 55, 44, 90, 83, 3, 93, 50, 99, 74, 97, 15, 96, 2, 17, 83, 35, 123, 73}), I1I1lI1II1.a(new byte[]{71, 22, 55, 42, 56}));
                return null;
            case 4:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 118, 124, 120, 54, 125});
            case 5:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47});
            case 6:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47, 61, 96, 121, 47, 116, 97, 124, 120, 114});
            case 7:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47, 61, 113, Byte.MAX_VALUE, 44, 126, 96, 125, Byte.MAX_VALUE, 116, 36, 48, 123, 120, 45, 104, 54, 39, 52, 55, 112, 100, 100});
            case 8:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47, 61, 113, Byte.MAX_VALUE, 44, 126, 96, 125, Byte.MAX_VALUE, 116, 36, 48, 123, 120, 45, 104, 45, 44, 54, 54, 116, 121, 100});
            case 9:
                return I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47, 61, 113, Byte.MAX_VALUE, 44, 126, 96, 125, Byte.MAX_VALUE, 116, 36, 48, 123, 120, 45, 104, 32, 39, 41, 35, 108, 114, 116});
            case 10:
                String strA2 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 121, Byte.MAX_VALUE, 109, 45, 118, 121, 118, 120, 96, 124, 45, 47, 61, 119, 102, 36, 125, 97});
                if (Il1IIllIll.l1Il11I1Il(I1I1lI1II1.a(new byte[]{83, 10, 59, 13, 9, 77, 78, Byte.MAX_VALUE, 94, 20, 126, 121, 96, 87, 66, 90, 39, 2, 43, 115, 0}), I1I1lI1II1.a(new byte[]{79, 28, 1, 45, 54, 96, 6, 69, 9, 53, 93, 120, 93, 67, 64, Byte.MAX_VALUE, 8, 21, 13, 89, 121, 47, 123, 13, Byte.MAX_VALUE, 78, Byte.MAX_VALUE, 61, 32, 104}))) {
                    throw new VerifyError(I1I1lI1II1.a(new byte[]{121, 93, 0, 2, 38, 68}));
                }
                return strA2;
            case 11:
                String strA3 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 118, 99, 106, 45, 99, 100, 116, 119, 119, 112, 61, 32, 33, 113, 117, 50, 96, 124, 113, Byte.MAX_VALUE, 123, 44, 48, 107});
                if (IIlI1ll1ll.llII1lIIlI(197581971L)) {
                    throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{67}));
                }
                return strA3;
            case 12:
                String strA4 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 118, 99, 106, 45, 99, 100, 116, 119, 119, 112, 61, 47, 35, 100, 121, 38, 114, 97, 122, 121, 121, 58, 35, 103, 126, 39, 118, 42, 33, 32});
                if (I1I1IIIIl1.IlII1Illll(702197796L)) {
                    throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{0, 38, 52, 18, 44, 119, 97, 1, 86, 44, 105, 119, 115, 75, 96, 84, 4, 12, 50, 92, 3, 13, 2}));
                }
                return strA4;
            case 13:
                String strA5 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 118, 99, 106, 45, 99, 100, 116, 119, 119, 112, 61, 50, 45, 124, 121, 39, 122, 118, 114, 98, 126, 42, 42});
                if (lIIIIII11I.IIll1I11lI(I1I1lI1II1.a(new byte[]{85, 30, 86, 50, 24, 101, 89, 9, 77, 52, 106, 66, 89, 11, 85, 116, 33}))) {
                    throw new ClassFormatError(I1I1lI1II1.a(new byte[]{99, 39, 80, 87, 24, 67, 6, 99, 119, 30, 65, 4, 84, 126, 120, 123, 32, 52, 51, 118, 5}));
                }
                return strA5;
            case 14:
                String strA6 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 112, 113, 116, 33});
                if (Il1IIllIll.l1l1l1IIlI(216340215L)) {
                    throw new CancellationException(I1I1lI1II1.a(new byte[]{94, 52, 12, 28, 21, 97, 110, 4, 12, 52, 82, 105, 70, 12, 3, 112, 50, 44, 53, 7, 100, 22, 114, 2, 4, 120}));
                }
                return strA6;
            case 15:
            default:
                String str = I1I1lI1II1.a(new byte[]{66, 10, 9, 11, 13, 66, 89, 16, 76, 23, 81, 87, 80, 25}) + i;
                if (androidx.versionedparcelable.custom.entities.IIlII1IIIl.l111IIlII1(I1I1lI1II1.a(new byte[]{98, 33, 84, 84, 12, 83, 97, 82, 95, 38, 5, 106, 89, 92, 123, 118, 32, 3}), 398209176L)) {
                    throw new SecurityException(I1I1lI1II1.a(new byte[]{97, 15, 20, 83, 52, 86, 99, 105, 80, 11, 125, 9, 81, 72, 108, 88, 33, 53, 7, 107, 82, 18, 114, 83}));
                }
                return str;
            case 16:
                String strA7 = I1I1lI1II1.a(new byte[]{98, 55, 35, 34, 39, 106, 118, 99, 106, 45, 99, 100, 116, 119, 96});
                if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{122, 41, 58, 17, 11, 86, 124, 8, 65, 51, 67, 98, 122, 120, 96, 69, 10, 86, 52}), 246894166L)) {
                    throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{92, 37, 26, 7}));
                }
                return strA7;
        }
    }

    public static void setForceLegacyBehavior(boolean z) throws DigestException {
        if (llIlI11III.IlII1Illll(162645703L)) {
            Log.d(I1I1lI1II1.a(new byte[]{126, 33, 14, 21, 87, 82, 0, 7, 9, 28, 86, 69, 122, 9, 68, 5, 45, 24, 86, 68, 122, 6}), I1I1lI1II1.a(new byte[]{110, 10, 0, 18, 87, 100, 71, 99, 80, 92, 104, 71, 70, 77, Byte.MAX_VALUE, 80, 14, 34, 80, 70, 93, 40, 89, 86, 3, 114, 89, 9, 7, 117}));
            return;
        }
        sForceLegacyBehavior = z;
        if (lI1lI11Ill.I1II1111ll(7768)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{121, 81, 59, 48, 40, 96, 97, 85, 10, 82, 103, 8, 98, 82, 67, 97, 7, 45, 50, 80, 65, 37, 88, 93, 74, 79, 80, 11, 83}));
        }
    }

    int getRawLegacyStreamType() {
        return this.mImpl.getRawLegacyStreamType();
    }

    static int toVolumeStreamType(boolean z, int i, int i2) throws PortUnreachableException, UTFDataFormatException {
        if ((i & 1) == 1) {
            return z ? 1 : 7;
        }
        if ((i & 4) == 4) {
            return z ? 0 : 6;
        }
        switch (i2) {
            case 0:
                if (I11II1l1lI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{78, 37, 6, 45, 40, 121, 3, 4, 115, 55, 100, 123, 90, 93, 89, 87, 80, 49, 6, 121, 104, 25, 114, 89, 96, 78, 69, 21, 50, 116, 4, 13}), I1I1lI1II1.a(new byte[]{79, 34, 86, 28, 47, 94, 125, 117, 10, 51, 126, 65, Byte.MAX_VALUE, 64, 76, 102, 10}))) {
                    throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{80, 42, 40, 34}));
                }
                return 3;
            case 1:
            case 12:
            case 14:
            case 16:
                return 3;
            case 2:
                return 0;
            case 3:
                int i3 = z ? 0 : 8;
                if (Il11II1llI.I111IlIl1I(7992)) {
                    throw new ArithmeticException(I1I1lI1II1.a(new byte[]{5, 45, 82, 42, 16, 68, 90, 72, 105, 22, 74, 119, 0, 85, 96, 111, 14, 27, 16}));
                }
                return i3;
            case 4:
                return 4;
            case 5:
            case 7:
            case 8:
            case 9:
            case 10:
                if (Ill11ll111.I11II1I1I1(I1I1lI1II1.a(new byte[]{122, 29, 41, 49, 19, 66, 89, 3, 75}))) {
                    throw new ArithmeticException(I1I1lI1II1.a(new byte[]{85, 34, 27, 17, 11, 123, Byte.MAX_VALUE, 6, 82, 84, 118, 94, 7, 104, 0, 119, 24, 50, 9, 92}));
                }
                return 5;
            case 6:
                return 2;
            case 11:
                return 10;
            case 13:
                if (androidx.versionedparcelable.custom.entities.IllIIIIII1.l1l1l1IIlI(225416844L)) {
                    throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{102, 81, 20, 9, 42, 111, 2, 1, 118, 19, 72, 94, Byte.MAX_VALUE, 1, 80, 91, 10, 6, 35}));
                }
                return 1;
            case 15:
            default:
                if (z) {
                    throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{98, 10, 9, 11, 13, 66, 89, 16, 76, 23, 81, 87, 80, 25, 66, 84, 14, 20, 7, 18}) + i2 + I1I1lI1II1.a(new byte[]{23, 13, 12, 69, 3, 64, 83, 89, 86, 68, 81, 68, 65, 75, 93, 87, 23, 21, 7, 65}));
                }
                if (IIlIIlIII1.Il1IIlI1II(219557471L)) {
                    throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{91, 44, 53, 45, 43, 96, 111, 116, 111, 21, 88, 82, 79, 111}));
                }
                return 3;
        }
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof I1IllIll1l)) {
            return false;
        }
        I1IllIll1l i1IllIll1l = (I1IllIll1l) obj;
        IIlIl1IIl1 iIlIl1IIl1 = this.mImpl;
        if (iIlIl1IIl1 == null) {
            return i1IllIll1l.mImpl == null;
        }
        return iIlIl1IIl1.equals(i1IllIll1l.mImpl);
    }
}
