package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.Parcel;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.io.NotActiveException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llIIlI1llI extends IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> {
    final /* synthetic */ lI1IIIl1I1 this$1;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ lIIIIlIIl1 val$resultWrapper;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    llIIlI1llI(lI1IIIl1I1 li1iiil1i1, Object obj, lIIIIlIIl1 liiiiliil1, Bundle bundle) {
        super(obj);
        this.this$1 = li1iiil1i1;
        this.val$resultWrapper = liiiiliil1;
        this.val$options = bundle;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void onResultSent(List<MediaBrowserCompat$MediaItem> list) throws NoSuchFieldException, InterruptedException, CertificateParsingException, ClassNotFoundException, NotActiveException, InvalidAlgorithmParameterException {
        if (l1IIll1I1l.IlII1Illll(I1I1lI1II1.a(new byte[]{90, 12, 8, 35}), 1392)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{113, 54, 85, 7, 23, 77, 92, 91, 11, 48}));
        }
        if (list == null) {
            this.val$resultWrapper.sendResult(null);
            if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{102}), 10215)) {
                throw new InterruptedException(I1I1lI1II1.a(new byte[]{124, 37, 1, 51, 83}));
            }
            return;
        }
        if ((1 & getFlags()) != 0) {
            list = this.this$1.this$0.applyOptions(list, this.val$options);
        }
        ArrayList arrayList = new ArrayList(list.size());
        for (MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem : list) {
            Parcel parcelObtain = Parcel.obtain();
            mediaBrowserCompat$MediaItem.writeToParcel(parcelObtain, 0);
            arrayList.add(parcelObtain);
        }
        this.val$resultWrapper.sendResult(arrayList);
        if (lIIII1l1lI.Il1IIlI1II(1722449890L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{117, 20, 51, 46, 45, 118, 110, 70, 94, 55, 65, 100, 122, 116, 81, 112, 20, 3, 84, 112, 2, 24}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void detach() throws UnsupportedEncodingException {
        if (I111lIl11I.l111l1I1Il(I1I1lI1II1.a(new byte[]{125, 54, 91}), 169339651L)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{65, 28, 26, 41, 37, 69, Byte.MAX_VALUE, 123, 95, 40, 91, 113, Byte.MAX_VALUE, 91, 89, 2, 27, 49, 0, 92}));
        }
        this.val$resultWrapper.detach();
        if (lIIlI111II.lI1lIIll11(937512746L)) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{1, 10, 5, 84, 81, 67, 71, 89, 106, 23, 90, 121, 89, 91, 5, 81, 19, 45, 90, 120, 99}));
        }
    }
}
