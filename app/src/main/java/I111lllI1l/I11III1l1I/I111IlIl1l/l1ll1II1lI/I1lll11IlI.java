package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioManager;
import android.os.Build$VERSION;
import androidx.core.location.lI1lI11Ill;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.io.NotSerializableException;
import java.io.UTFDataFormatException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1lll11IlI {
    public static final int AUDIOFOCUS_GAIN = 1;
    public static final int AUDIOFOCUS_GAIN_TRANSIENT = 2;
    public static final int AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE = 4;
    public static final int AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK = 3;
    private static final String TAG = I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 120, 86, 94, 122, 11, 93, 64, 84, 77});

    public static int requestAudioFocus(AudioManager audioManager, ll1ll1IlIl ll1ll1ilil) {
        if (audioManager == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 120, 86, 94, 88, 3, 85, 66, 21, 84, 65, 70, 22, 65, 12, 93, 68, 65, 81, 80, 19, 88, 66, 9, 8}));
        }
        if (ll1ll1ilil == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 115, 88, 83, 76, 23, 98, 85, 68, 76, 81, 70, 22, 34, 13, 95, 64, 0, 71, 21, 94, 67, 68, 17, 68, 92, 88, 23, 23, 6, 7, 69, 12, 64, 91, 92}));
        }
        if (Build$VERSION.SDK_INT >= 26) {
            return l1l1I1l1lI.requestAudioFocus(audioManager, ll1ll1ilil.getAudioFocusRequest());
        }
        return audioManager.requestAudioFocus(ll1ll1ilil.getOnAudioFocusChangeListener(), ll1ll1ilil.getAudioAttributesCompat().getLegacyStreamType(), ll1ll1ilil.getFocusGain());
    }

    public static int abandonAudioFocusRequest(AudioManager audioManager, ll1ll1IlIl ll1ll1ilil) {
        if (audioManager == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 120, 86, 94, 88, 3, 85, 66, 21, 84, 65, 70, 22, 65, 12, 93, 68, 65, 81, 80, 19, 88, 66, 9, 8}));
        }
        if (ll1ll1ilil == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 115, 88, 83, 76, 23, 98, 85, 68, 76, 81, 70, 22, 34, 13, 95, 64, 0, 71, 21, 94, 67, 68, 17, 68, 92, 88, 23, 23, 6, 7, 69, 12, 64, 91, 92}));
        }
        if (Build$VERSION.SDK_INT >= 26) {
            return l1l1I1l1lI.abandonAudioFocusRequest(audioManager, ll1ll1ilil.getAudioFocusRequest());
        }
        return audioManager.abandonAudioFocus(ll1ll1ilil.getOnAudioFocusChangeListener());
    }

    public static int getStreamMaxVolume(AudioManager audioManager, int i) throws UTFDataFormatException {
        int streamMaxVolume = audioManager.getStreamMaxVolume(i);
        if (lI1lI11Ill.l1l1l1IIlI(3146)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{69, 55, 82, 60, 46, 112, 88, 96, 116, 47, 104, 65, 4, 112, 88, 108, 27, 85, 36, 86, 82, 16}));
        }
        return streamMaxVolume;
    }

    public static int getStreamMinVolume(AudioManager audioManager, int i) throws NotSerializableException {
        if (Build$VERSION.SDK_INT < 28) {
            return 0;
        }
        int streamMinVolume = IIl111lI1I.getStreamMinVolume(audioManager, i);
        if (lIIlI111II.IIlIl1Illl(7026)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{67, 60, 42, 23, 49, 82, 125, 100, 72, 37, 84, 0, 80, Byte.MAX_VALUE, 109}));
        }
        return streamMinVolume;
    }

    public static boolean isVolumeFixed(AudioManager audioManager) {
        if (android.media.content.IIl1l1IllI.Ill1lIIlIl(2886)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{0, 85, 43, 63, 11, 94, 122, 125, 115, 19, 93, 69, 113, 110, 3, 99, 37, 32, 7, 0, 93, 10, 97, Byte.MAX_VALUE, 81, 71, 1}));
        }
        return llll1lI1II.isVolumeFixed(audioManager);
    }

    private I1lll11IlI() {
    }
}
