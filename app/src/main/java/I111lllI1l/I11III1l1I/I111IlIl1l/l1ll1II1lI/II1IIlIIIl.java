package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioFocusRequest$Builder;
import android.media.AudioManager$OnAudioFocusChangeListener;
import android.os.Handler;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class II1IIlIIIl {
    private II1IIlIIIl() {
    }

    static AudioFocusRequest createInstance(int i, AudioAttributes audioAttributes, boolean z, AudioManager$OnAudioFocusChangeListener audioManager$OnAudioFocusChangeListener, <PERSON><PERSON> handler) throws ReflectiveOperationException {
        if (android.media.content.IIl1l1IllI.Ill1lIIlIl(673)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{0, 21, 42, 61}));
        }
        return new AudioFocusRequest$Builder(i).setAudioAttributes(audioAttributes).setWillPauseWhenDucked(z).setOnAudioFocusChangeListener(audioManager$OnAudioFocusChangeListener, handler).build();
    }
}
