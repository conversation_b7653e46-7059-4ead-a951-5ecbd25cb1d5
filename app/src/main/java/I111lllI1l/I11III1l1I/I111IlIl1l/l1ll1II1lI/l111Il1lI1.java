package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.I111lIl11I;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.io.IOException;
import java.security.KeyManagementException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class l111Il1lI1 {
    final Il111lI1Il mBuilderImpl;

    public l111Il1lI1() {
        if (I1IllIll1l.sForceLegacyBehavior) {
            this.mBuilderImpl = new lll1IlllIl();
        } else if (Build$VERSION.SDK_INT >= 26) {
            this.mBuilderImpl = new II11llll1I();
        } else {
            this.mBuilderImpl = new IIIl11Illl();
        }
    }

    public l111Il1lI1(I1IllIll1l i1IllIll1l) {
        if (I1IllIll1l.sForceLegacyBehavior) {
            this.mBuilderImpl = new lll1IlllIl(i1IllIll1l);
        } else if (Build$VERSION.SDK_INT >= 26) {
            this.mBuilderImpl = new II11llll1I(i1IllIll1l.unwrap());
        } else {
            this.mBuilderImpl = new IIIl11Illl(i1IllIll1l.unwrap());
        }
    }

    public I1IllIll1l build() throws KeyManagementException {
        I1IllIll1l i1IllIll1l = new I1IllIll1l(this.mBuilderImpl.build());
        if (I111lIl11I.l1Il11I1Il(I1I1lI1II1.a(new byte[]{114, 39, 42, 23, 14, 5}), 191112682L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{122, 51, 41, 35, 14, 123, 125, 82, 116, 51, 94, 82, 86, 96, 95, 67, 80, 53, 0}));
        }
        return i1IllIll1l;
    }

    public l111Il1lI1 setUsage(int i) {
        this.mBuilderImpl.setUsage(i);
        return this;
    }

    public l111Il1lI1 setContentType(int i) {
        this.mBuilderImpl.setContentType(i);
        return this;
    }

    public l111Il1lI1 setFlags(int i) {
        this.mBuilderImpl.setFlags(i);
        if (lIlIII1I1l.l1ll11I11l(I1I1lI1II1.a(new byte[]{1, 48, 44, 35, 20, 92, 4, 4, 9, 14, 88, 97, Byte.MAX_VALUE, 114, 6}), 8174)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{69, 19, 49, 3, 17, 69, 67, 86, 92, 16, 93, 71, 93, 80, 122, 96, 4, 86, 42, 74, 126, 36, 80, 121, 118, 83}));
        }
        return this;
    }

    public l111Il1lI1 setLegacyStreamType(int i) throws IOException {
        if (ll1l11I1II.IlII1Illll(163905111L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{5, 52, 16, 33, 7, 109, 82, Byte.MAX_VALUE, 1, 6, 7, 68, 91, 113, 123, 4, 38, 89, 37, 101}));
        }
        this.mBuilderImpl.setLegacyStreamType(i);
        if (l11Il1lI11.Ill1lIIlIl(2782)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{113, 37, 10, 13, 48, 125, 102, 124, 76, 81, 4, 117, 114, 84, 89, 87, 47, 4, 33, 93, Byte.MAX_VALUE, 86, 73, 7, 102, 78, 86, 45}));
        }
        return this;
    }
}
