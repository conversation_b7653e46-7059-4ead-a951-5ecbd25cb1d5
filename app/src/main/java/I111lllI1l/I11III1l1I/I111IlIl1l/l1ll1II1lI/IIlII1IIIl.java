package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIlII1IIIl implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ int val$pid;
    final /* synthetic */ String val$pkg;
    final /* synthetic */ Bundle val$rootHints;
    final /* synthetic */ int val$uid;

    IIlII1IIIl(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, int i, String str, int i2, Bundle bundle) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$uid = i;
        this.val$pkg = str;
        this.val$pid = i2;
        this.val$rootHints = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws RemoteException {
        Il1lII1l1l il1lII1l1l;
        IBinder iBinderAsBinder = this.val$callbacks.asBinder();
        this.this$1.this$0.mConnections.remove(iBinderAsBinder);
        Iterator<Il1lII1l1l> it = this.this$1.this$0.mPendingConnections.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Il1lII1l1l next = it.next();
            if (next.uid == this.val$uid) {
                il1lII1l1l = (TextUtils.isEmpty(this.val$pkg) || this.val$pid <= 0) ? new Il1lII1l1l(this.this$1.this$0, next.pkg, next.pid, next.uid, this.val$rootHints, this.val$callbacks) : null;
                it.remove();
            }
        }
        if (il1lII1l1l == null) {
            il1lII1l1l = new Il1lII1l1l(this.this$1.this$0, this.val$pkg, this.val$pid, this.val$uid, this.val$rootHints, this.val$callbacks);
        }
        this.this$1.this$0.mConnections.put(iBinderAsBinder, il1lII1l1l);
        try {
            iBinderAsBinder.linkToDeath(il1lII1l1l, 0);
        } catch (RemoteException unused) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{126, 38, 11, 11, 6, 80, 69, 16, 80, 23, 16, 81, 89, 75, 81, 84, 6, 24, 66, 86, 85, 0, 87, 27}));
        }
    }
}
