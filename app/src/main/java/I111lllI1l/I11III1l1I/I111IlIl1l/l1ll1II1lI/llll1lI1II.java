package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioManager;
import androidx.interpolator.view.animation.lIIlI111II;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class llll1lI1II {
    static boolean isVolumeFixed(AudioManager audioManager) throws BrokenBarrierException {
        boolean zIsVolumeFixed = audioManager.isVolumeFixed();
        if (lIIlI111II.lllI111lll(391640411L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{5, 30, 11, 14, 22, 79, 94, 102, 88, 6, 96, 106, 67}));
        }
        return zIsVolumeFixed;
    }

    private llll1lI1II() {
    }
}
