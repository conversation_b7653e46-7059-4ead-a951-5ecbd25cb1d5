package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Context;
import android.content.pm.PackageManager$NameNotFoundException;
import android.provider.Settings$Secure;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.util.Log;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.lI1lI11Ill;
import java.io.InvalidObjectException;
import java.security.DigestException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llIIl11lIl implements IlIIlllI1l {
    ContentResolver mContentResolver;
    Context mContext;
    private static final String TAG = I1I1lI1II1.a(new byte[]{122, 1, 6, 12, 3, 102, 82, 67, 74, 13, 95, 94, 120, 88, 90, 84, 5, 4, 16});
    private static final String PERMISSION_STATUS_BAR_SERVICE = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 73, 1, 66, 93, 92, 74, 71, 92, 13, 15, 76, 97, 100, 32, 103, 96, 96, 105, 117, 36, 54, 109, 100, 38, 101, 50, 43, 38, 39});
    private static final String PERMISSION_MEDIA_CONTENT_CONTROL = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 73, 1, 66, 93, 92, 74, 71, 92, 13, 15, 76, Byte.MAX_VALUE, 117, 37, 122, 116, 108, 117, 120, 43, 48, 119, 121, 55, 104, 39, 45, 43, 54, 103, 120, 124});
    private static final String ENABLED_NOTIFICATION_LISTENERS = I1I1lI1II1.a(new byte[]{82, 10, 3, 7, 14, 80, 83, 111, 87, 11, 68, 89, 83, 80, 87, 84, 22, 8, 13, 92, 111, 13, 90, 70, 71, 83, 89, 0, 22, 65});
    private static final boolean DEBUG = IIIII1lll1.DEBUG;

    llIIl11lIl(Context context) {
        this.mContext = context;
        this.mContentResolver = context.getContentResolver();
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIIlllI1l
    public Context getContext() {
        return this.mContext;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIIlllI1l
    public boolean isTrustedForMediaControl(l1l1I1llII l1l1i1llii) throws DigestException, InvalidObjectException, CertificateException {
        if (I111I11Ill.l11I11I11l(204361857L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{14, 83, 58, 2, 32, 108, 77, 91, 67, 62, 67, 106}));
        }
        try {
            if (this.mContext.getPackageManager().getApplicationInfo(l1l1i1llii.getPackageName(), 0) == null) {
                if (I11II1l1lI.Ill1lIIlIl(9725)) {
                    throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{126, 83, 36, 53, 42, 76, 95, 85, 10, 13, 66, 93, 116, 117, 119, 114, 53, 88, 58, 115, 5, 3, 113, 119, 121, 125, 85, 32, 39, 2, 0}));
                }
                return false;
            }
            boolean z = isPermissionGranted(l1l1i1llii, PERMISSION_STATUS_BAR_SERVICE) || isPermissionGranted(l1l1i1llii, PERMISSION_MEDIA_CONTENT_CONTROL) || l1l1i1llii.getUid() == 1000 || isEnabledNotificationListener(l1l1i1llii);
            if (androidx.constraintlayout.widget.I1IllIll1l.l1ll11I11l(I1I1lI1II1.a(new byte[]{88, 50, 27}), 251596575L)) {
                throw new DigestException(I1I1lI1II1.a(new byte[]{112, 13, 82, 52, 43, 95, 15, 125, 112, 50, 68, 84, 82, 95, 91, 92, 44, 59, 16, 74, 7, 12, 6, 109, 67, 122, 65}));
            }
            return z;
        } catch (PackageManager$NameNotFoundException unused) {
            if (DEBUG) {
                Log.d(TAG, I1I1lI1II1.a(new byte[]{103, 5, 1, 14, 3, 82, 82, 16}) + l1l1i1llii.getPackageName() + I1I1lI1II1.a(new byte[]{23, 0, 13, 0, 17, 91, 16, 68, 25, 1, 72, 89, 70, 77}));
            }
            if (androidx.versionedparcelable.custom.entities.IIlII1IIIl.III11111Il(235762062L)) {
                throw new CertificateException(I1I1lI1II1.a(new byte[]{4, 3, 35, 84, 38, 83, 5, 66, 84, 17, 72, 126, 108, 125, 1, 76, 86, 4, 50, 6, 85, 11, 113, 0, 3, 92, 3, 29, 30, 92}));
            }
            return false;
        }
    }

    private boolean isPermissionGranted(l1l1I1llII l1l1i1llii, String str) throws UnrecoverableEntryException {
        if (l1l1i1llii.getPid() >= 0) {
            return this.mContext.checkPermission(str, l1l1i1llii.getPid(), l1l1i1llii.getUid()) == 0;
        }
        boolean z = this.mContext.getPackageManager().checkPermission(str, l1l1i1llii.getPackageName()) == 0;
        if (lI1lI11Ill.I1II1111ll(7783)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{95, 48, 5, 47, 14, 89, 111, 90, 79, 35, 87, 7, 122}));
        }
        return z;
    }

    boolean isEnabledNotificationListener(l1l1I1llII l1l1i1llii) throws BrokenBarrierException {
        if (I111lIl11I.III11111Il(196389323L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{84, 42, 19, 82, 32, 69, 15, 87, 85, 15, 7, 123, 71, 13}));
        }
        String string = Settings$Secure.getString(this.mContentResolver, ENABLED_NOTIFICATION_LISTENERS);
        if (string != null) {
            for (String str : string.split(I1I1lI1II1.a(new byte[]{13}))) {
                ComponentName componentNameUnflattenFromString = ComponentName.unflattenFromString(str);
                if (componentNameUnflattenFromString != null && componentNameUnflattenFromString.getPackageName().equals(l1l1i1llii.getPackageName())) {
                    return true;
                }
            }
        }
        return false;
    }
}
