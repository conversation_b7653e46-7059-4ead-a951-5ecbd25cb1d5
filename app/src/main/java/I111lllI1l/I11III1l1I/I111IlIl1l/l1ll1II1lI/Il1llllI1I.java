package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import IIII11Il1l.Il1IlIlI1l.II111IIl1l.lll111IlIl.I1Il1lllIl;
import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.I1II1llI1I;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIlI111II;
import android.content.Intent;
import android.media.session.MediaSession$Token;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Messenger;
import android.os.Parcel;
import android.service.media.MediaBrowserService;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.media.session.IMediaSession;
import android.support.v4.media.session.MediaSessionCompat$Token;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.lI1lI11Ill;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.InvalidClassException;
import java.net.SocketTimeoutException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.cert.CRLException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class Il1llllI1I implements IIIlIl1I1l {
    Messenger mMessenger;
    final List<Bundle> mRootExtrasList = new ArrayList();
    MediaBrowserService mServiceFwk;
    final /* synthetic */ lIIIIll1II this$0;

    Il1llllI1I(lIIIIll1II liiiill1ii) {
        this.this$0 = liiiill1ii;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void onCreate() throws SocketTimeoutException {
        lI1lIIll11 li1liill11 = new lI1lIIll11(this, this.this$0);
        this.mServiceFwk = li1liill11;
        li1liill11.onCreate();
        if (IIll1llI1l.Ill1lIIlIl(6637)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{83, 5, 81, 9, 91, 77, 4, 98, 97, 87, 6, 120, 66}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public IBinder onBind(Intent intent) {
        return this.mServiceFwk.onBind(intent);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void setSessionToken(MediaSessionCompat$Token mediaSessionCompat$Token) throws GeneralSecurityException {
        if (l1l1I111I1.I111IlIl1I(435522778L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{14, 42, 0, 86, 58, 87, 67, 119, 13, 42, 119, 9, 126, 109, 118, 91, 22, 89, 36, 98, 89, 27, 80, 12}));
        }
        this.this$0.mHandler.postOrRun(new IlIII1l1ll(this, mediaSessionCompat$Token));
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l11I11I11l(168333705L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{66, 30, 8, 93, 54, 80, 77, 102, 76, 45, 4, 5, 118, 10, 88, 70, 33, 84, 21, 7, 121, 36, 66}));
        }
    }

    void setSessionTokenOnHandler(MediaSessionCompat$Token mediaSessionCompat$Token) throws InterruptedException, NoSuchAlgorithmException {
        if (!this.mRootExtrasList.isEmpty()) {
            IMediaSession extraBinder = mediaSessionCompat$Token.getExtraBinder();
            if (extraBinder != null) {
                Iterator<Bundle> it = this.mRootExtrasList.iterator();
                while (it.hasNext()) {
                    I1Il1lllIl.putBinder(it.next(), I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 74, 23, 89, 95, 91, 102, 86, 92, 12, 5, 7, 64}), extraBinder.asBinder());
                }
            }
            this.mRootExtrasList.clear();
        }
        this.mServiceFwk.setSessionToken((MediaSession$Token) mediaSessionCompat$Token.getToken());
        if (android.media.content.Il1llIl111.I111IlIl1I(8466)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{102, 86, 4, 35, 46, 64, 110}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void notifyChildrenChanged(String str, Bundle bundle) throws CertPathBuilderException {
        notifyChildrenChangedForFramework(str, bundle);
        notifyChildrenChangedForCompat(str, bundle);
        if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{81, 38, 33, 3, 5}), 215148039L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{79, 39, 8, 1, 26, 94, 97, 69, 96, 85, 74, 102, 94, 96, 3, 109, 41, 35, 8}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void notifyChildrenChanged(llIllIlll1 llillilll1, String str, Bundle bundle) throws CertificateException {
        notifyChildrenChangedForCompat(llillilll1, str, bundle);
    }

    public IlIllIll1I onGetRoot(String str, int i, Bundle bundle) throws InterruptedException, NoSuchAlgorithmException, SignatureException, KeyStoreException {
        int i2;
        Bundle extras;
        if (Il1I1lllIl.I1lI11IIll(I1I1lI1II1.a(new byte[]{66, 17, 37, 40, 86, 114, 124, 71, 110, 2, 82, 85, 108, 92, 95, 2, 48, 34, 87}), 241398583L)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{94, 12, 6, 51}));
        }
        if (bundle == null || bundle.getInt(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 92, 80, 1, 94, 68, 106, 79, 81, 71, 17, 8, 13, 92}), 0) == 0) {
            i2 = -1;
            extras = null;
        } else {
            bundle.remove(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 92, 80, 1, 94, 68, 106, 79, 81, 71, 17, 8, 13, 92}));
            this.mMessenger = new Messenger(this.this$0.mHandler);
            extras = new Bundle();
            extras.putInt(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 75, 18, 89, 83, 80, 102, 66, 80, 16, 18, 11, 93, 94}), 2);
            I1Il1lllIl.putBinder(extras, I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 90, 85, 74, 23, 85, 94, 82, 92, 70}), this.mMessenger.getBinder());
            if (this.this$0.mSession != null) {
                IMediaSession extraBinder = this.this$0.mSession.getExtraBinder();
                I1Il1lllIl.putBinder(extras, I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 74, 23, 89, 95, 91, 102, 86, 92, 12, 5, 7, 64}), extraBinder == null ? null : extraBinder.asBinder());
            } else {
                this.mRootExtrasList.add(extras);
            }
            int i3 = bundle.getInt(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 81, 85, 8, 89, 94, 82, 102, 68, 92, 6}), -1);
            bundle.remove(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 81, 85, 8, 89, 94, 82, 102, 68, 92, 6}));
            i2 = i3;
        }
        Il1lII1l1l il1lII1l1l = new Il1lII1l1l(this.this$0, str, i2, i, bundle, null);
        this.this$0.mCurConnection = il1lII1l1l;
        IlIllIll1I ilIllIll1IOnGetRoot = this.this$0.onGetRoot(str, i, bundle);
        this.this$0.mCurConnection = null;
        if (ilIllIll1IOnGetRoot == null) {
            return null;
        }
        if (this.mMessenger != null) {
            this.this$0.mPendingConnections.add(il1lII1l1l);
        }
        if (extras == null) {
            extras = ilIllIll1IOnGetRoot.getExtras();
        } else if (ilIllIll1IOnGetRoot.getExtras() != null) {
            extras.putAll(ilIllIll1IOnGetRoot.getExtras());
        }
        IlIllIll1I ilIllIll1I = new IlIllIll1I(ilIllIll1IOnGetRoot.getRootId(), extras);
        if (lII1llllI1.I1lI11IIll(I1I1lI1II1.a(new byte[]{93, 18, 55, 29, 86, 121, 111, 81, 11, 53, 4, 116, 103, 10, 88, 100, 45, 10, 54}), 222476749L)) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{112, 7, 45, 81, 4, 79, 114, 65, 109, 53, 6, 104, 115, 79, 98, 116, 5, 27, 32, 112, 120, 7, 64, 121, 0, 119}));
        }
        return ilIllIll1I;
    }

    public void onLoadChildren(String str, lIIIIlIIl1<List<Parcel>> liiiiliil1) {
        lllIl1lllI lllil1llli = new lllIl1lllI(this, str, liiiiliil1);
        lIIIIll1II liiiill1ii = this.this$0;
        liiiill1ii.mCurConnection = liiiill1ii.mConnectionFromFwk;
        this.this$0.onLoadChildren(str, lllil1llli);
        this.this$0.mCurConnection = null;
        if (lIIII1l1lI.Il1IIlI1II(159994434L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{91}));
        }
    }

    void notifyChildrenChangedForFramework(String str, Bundle bundle) {
        if (l1lll111II.IlII1Illll(I1I1lI1II1.a(new byte[]{91, 14, 24, 42, 84, 115, 84, 91, 83, 62, 3, 122, 116, 99}))) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{102, 39, 32, 20, 14, 94, 71, 100, 67, 85, 73, 68, 90, 108, 64, 69, 39, 35, 48, 88, 69, 59, 86, 125, 6, 15}));
        }
        this.mServiceFwk.notifyChildrenChanged(str);
    }

    void notifyChildrenChangedForCompat(String str, Bundle bundle) {
        this.this$0.mHandler.post(new IllIIIIII1(this, str, bundle));
    }

    void notifyChildrenChangedForCompat(llIllIlll1 llillilll1, String str, Bundle bundle) throws CertificateException {
        this.this$0.mHandler.post(new llII1llll1(this, llillilll1, str, bundle));
        if (lIIlI111II.l1l11llIl1(192980985L)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{83, 39, 48, 23, 23, 80, 100, 72, 84, 46, 101, 97, 95, 76, 124, 82, 18, 37, 15, 69}));
        }
    }

    void notifyChildrenChangedForCompatOnHandler(Il1lII1l1l il1lII1l1l, String str, Bundle bundle) throws NoSuchAlgorithmException {
        List<I1II1llI1I<IBinder, Bundle>> list = il1lII1l1l.subscriptions.get(str);
        if (list != null) {
            for (I1II1llI1I<IBinder, Bundle> i1II1llI1I : list) {
                if (lIIlI11IlI.hasDuplicatedItems(bundle, i1II1llI1I.second)) {
                    this.this$0.performLoadChildren(str, il1lII1l1l, i1II1llI1I.second, bundle);
                }
            }
        }
        if (lII1llllI1.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{96, 20, 14, 87, 7, 80, 111, 64, 91, 16, 113, 118, 119, 88, 71, 96, 58, 84, 46, 68, 106, 56, 68, 126, 74, 3, 120, 54, 47, 64}), 455273098L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{117, 1, 36, 21, 42, 101, 86, 74, 99, 21, 96}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public Bundle getBrowserRootHints() throws InvalidClassException, ReflectiveOperationException {
        if (lI1lI11Ill.I1lllI1llI(277332105L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{92, 92, 42, 86, 24, 116, 100, Byte.MAX_VALUE, 114, 2, 4, 65, 89, 13, 66, 79, 33, 41, 49, 69, 96, 15, 68, 97, 6, Byte.MAX_VALUE, 5, 21}));
        }
        if (this.mMessenger == null) {
            if (androidx.constraintlayout.widget.I1IllIll1l.I1lIllll1l(I1I1lI1II1.a(new byte[]{84, 52, 52, 38, 49, 69, 124, 105, 67, 44, 1, 90, 99, 84, 7, 102, 21, 45, 42, 124, 73, 20, 97, 7, 2, 90, 92, 4}))) {
                throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{117, 44}));
            }
            return null;
        }
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 37, 0, 22, 103, 88, 95, 77, 72, 16, 95, 91, 117, 91, 84, 6, 34, 10, 91, 92, 5, 65, 80, 93, 26, 23, 10, 10, 126, 88, 2, 83, 45, 22, 0, 15, 25, 23, 95, 87, 55, 85, 81, 71, 90, 92, 25, 66, 14, 16, 18, 95, 15, 112, 64, 64, 66, 88, 8, 37, 81, 67, 10, 88, 10, 66, 8, 7, 65, 95, 95, 93, 23}));
        }
        if (this.this$0.mCurConnection.rootHints == null) {
            return null;
        }
        return new Bundle(this.this$0.mCurConnection.rootHints);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public llIllIlll1 getCurrentBrowserInfo() throws InvalidAlgorithmParameterException {
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 37, 0, 22, 103, 88, 95, 77, 72, 16, 95, 91, 117, 91, 84, 6, 34, 10, 91, 92, 5, 65, 80, 93, 26, 23, 10, 10, 126, 88, 2, 83, 45, 22, 0, 15, 25, 23, 95, 87, 55, 85, 81, 71, 90, 92, 25, 66, 14, 16, 18, 95, 15, 112, 64, 64, 66, 88, 8, 37, 81, 67, 10, 88, 10, 66, 8, 7, 65, 95, 95, 93, 23}));
        }
        llIllIlll1 llillilll1 = this.this$0.mCurConnection.browserInfo;
        if (lI1l1I1l1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 41, 52, 13, 33, 7, 109, 9, 91, 3, 96, 105, 5, 87, 4, 69, 33, 36, 47, 68, 67, 85, 85, 71, 0, 88, 110, 22, 51, 71}))) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{92, 42, 55, 14, 15, 115, 117, 97, 76, 42, 8, 67, 87, 96, 101, 103, 39, 55}));
        }
        return llillilll1;
    }
}
