package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import androidx.core.location.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II11llll1I extends IIIl11Illl {
    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIl11Illl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ IIIl11Illl setUsage(int i) {
        if (Ill11ll111.Ill1lIIlIl(1438)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{68}));
        }
        return setUsage(i);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIl11Illl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setUsage(int i) throws ClassNotFoundException {
        II11llll1I usage = setUsage(i);
        if (lIlIII1I1l.I1lllI1llI(225070316L)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{114, 84, 51, 84, 81, 113, 93, 105, 123, 38, 90, 124, 95, 64, 122, 95, 4, 16, 37, 5, 69, 16, 80, 114, 100, 87, 121, 20, 7}));
        }
        return usage;
    }

    II11llll1I() {
    }

    II11llll1I(Object obj) {
        super(obj);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIl11Illl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIlIl1IIl1 build() throws NoSuchMethodException {
        if (lIIlI111II.I1111l111I(5237)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{90, 1, 22, 84, 46, 68, 121, 126, 87, 1, 3, 115, 122, 82}));
        }
        return new I1II111III(this.mFwkBuilder.build());
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIl11Illl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public II11llll1I setUsage(int i) {
        this.mFwkBuilder.setUsage(i);
        if (androidx.versionedparcelable.custom.entities.IllIIIIII1.Il1IIlI1II(63)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{6, 53, 5, 43, 12, 98, 79, 74, 74, 16, 99, 85, 91, 99, 124, 91, 15}));
        }
        return this;
    }
}
