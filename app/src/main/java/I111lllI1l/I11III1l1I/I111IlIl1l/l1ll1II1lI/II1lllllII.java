package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.graphics.drawable.Il1IIllIll;
import java.io.UTFDataFormatException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II1lllllII implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;

    II1lllllII(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
    }

    @Override // java.lang.Runnable
    public void run() throws UTFDataFormatException {
        if (Il1IIllIll.ll1I1lII11(I1I1lI1II1.a(new byte[]{103, 35, 20, 18, 14, 94, 102, 113, 10, 23, 94, 122, 82, 91, 6, 99, 58, 59, 13, 94, 4, 36, 73}), 370497445L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{112, 44, 22, 39, 87, 65, 84, 72, 109, 85, 119, 100, 89, 110, 6, 118, 50, 18, 40, 11, 89, 51, 64, 111, 85, 2}));
        }
        Il1lII1l1l il1lII1l1lRemove = this.this$1.this$0.mConnections.remove(this.val$callbacks.asBinder());
        if (il1lII1l1lRemove != null) {
            il1lII1l1lRemove.callbacks.asBinder().unlinkToDeath(il1lII1l1lRemove, 0);
        }
    }
}
