package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import androidx.constraintlayout.widget.l1IIll1I1l;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lIII1IlIlI extends llIIl11lIl {
    lIII1IlIlI(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.llIIl11lIl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIIlllI1l
    public boolean isTrustedForMediaControl(l1l1I1llII l1l1i1llii) throws CertificateNotYetValidException {
        if (l1IIll1I1l.l11I11I11l(I1I1lI1II1.a(new byte[]{15, 53, 36, 4, 22, 76, 6, 9, 109, 51, 0, 86, 98, 113, 126, 101, 59, 16, 51, 118, 121, 51, 85, 2, 98, 85, 68, 40, 19, 6, 94}))) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{4, 28, 24, 20, 33, 70, 2, 93, 77, 29, 85, 95, 98, 85, 85, 95, 48, 49}));
        }
        return hasMediaControlPermission(l1l1i1llii) || super.isTrustedForMediaControl(l1l1i1llii);
    }

    private boolean hasMediaControlPermission(l1l1I1llII l1l1i1llii) {
        return getContext().checkPermission(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 73, 1, 66, 93, 92, 74, 71, 92, 13, 15, 76, Byte.MAX_VALUE, 117, 37, 122, 116, 108, 117, 120, 43, 48, 119, 121, 55, 104, 39, 45, 43, 54, 103, 120, 124}), l1l1i1llii.getPid(), l1l1i1llii.getUid()) == 0;
    }
}
