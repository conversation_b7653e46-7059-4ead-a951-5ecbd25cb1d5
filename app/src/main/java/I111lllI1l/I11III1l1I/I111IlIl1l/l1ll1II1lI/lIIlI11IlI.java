package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lIIlI11IlI {
    public static boolean areSameOptions(Bundle bundle, Bundle bundle2) {
        if (bundle == bundle2) {
            return true;
        }
        if (bundle == null) {
            return bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1) == -1 && bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1) == -1;
        }
        if (bundle2 == null) {
            return bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1) == -1 && bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1) == -1;
        }
        boolean z = bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1) == bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1) && bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1) == bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1);
        if (Il1I1lllIl.Il1IIlI1II(7300)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{14, 33, 16}));
        }
        return z;
    }

    public static boolean hasDuplicatedItems(Bundle bundle, Bundle bundle2) {
        int i;
        int i2;
        int i3;
        int i4 = bundle == null ? -1 : bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1);
        int i5 = bundle2 == null ? -1 : bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1);
        int i6 = bundle == null ? -1 : bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1);
        int i7 = bundle2 == null ? -1 : bundle2.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1);
        int i8 = Integer.MAX_VALUE;
        if (i4 == -1 || i6 == -1) {
            i = Integer.MAX_VALUE;
            i2 = 0;
        } else {
            i2 = i4 * i6;
            i = (i6 + i2) - 1;
        }
        if (i5 == -1 || i7 == -1) {
            i3 = 0;
        } else {
            i3 = i5 * i7;
            i8 = (i7 + i3) - 1;
        }
        return i >= i3 && i8 >= i2;
    }

    private lIIlI11IlI() {
    }
}
