package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I1111IIl11;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import com.ironsource.mediationsdk.logger.IronSourceError;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateParsingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lll1IlllIl implements Il111lI1Il {
    private int mContentType;
    private int mFlags;
    private int mLegacyStream;
    private int mUsage;

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setFlags(int i) throws NoSuchAlgorithmException {
        lll1IlllIl flags = setFlags(i);
        if (lI1lllIII1.Ill1lIIlIl(561668442L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{94, 81, 48, 61, 13, 86, 124, 126, 99, 2, 103, 3, 69, 119, 89, 90, 45, 14, 11, 86, 68}));
        }
        return flags;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setLegacyStreamType(int i) {
        if (lIIII1l1lI.I1lllI1llI(6290)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{103, 85, 37, 11, 82, 112, 89, 86, 99, 37, 99, 5, 1, 124, 124, 111, 81, 11, 46, 91, 7, 10, 105}));
        }
        lll1IlllIl legacyStreamType = setLegacyStreamType(i);
        if (l1IIll1I1l.IlII1Illll(I1I1lI1II1.a(new byte[]{71, 29, 91, 12, 48, 113, 15, 86, 116, 82, 72, 99, 103, 8, 92, 123, 1, 2, 51, 95, 117, 82, 82, 91, 73, 6, 14, 43, 3, 90, 102}), lIIlI111II.ll1I1lII11)) {
            throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{122, 30, 50, 17, 15, 116, 14, 71, 116, 11, 64, 64, 116, 88, 90, 93, 24, 21, 18, 123, 84, 19, 122, 96, 125, 7, 98, 87}));
        }
        return legacyStreamType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setUsage(int i) throws InterruptedException, CertificateParsingException, CloneNotSupportedException {
        if (I1111IIl11.I11II1I1I1(I1I1lI1II1.a(new byte[]{92, 54, 41, 2, 24, 67, 115, 74, 82, 22, 126, 71, 126, 116, 92, 119, 35, 88, 37, 84, 126, 49}), 10383)) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{70, 55, 54, 19, 87, 67, 102, 94, 83, 19, 99, 117, 111, 83, 96, 70, 52, 20, 53, 93, 115, 43, 120, 7, 123}));
        }
        lll1IlllIl usage = setUsage(i);
        if (android.accounts.utils.lIIlI111II.I1I11l11l1(lIIlI111II.ll1I1lII11)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{88, 30, 44, 60, 81, 98, 116, 121, 104, 53, 119, 91, 67, 122, 101, 89, 85, 55, 49, 97, 124, 87, 86, 1, 81, 84, 4}));
        }
        return usage;
    }

    lll1IlllIl() {
        this.mUsage = 0;
        this.mContentType = 0;
        this.mFlags = 0;
        this.mLegacyStream = -1;
    }

    lll1IlllIl(I1IllIll1l i1IllIll1l) {
        this.mUsage = 0;
        this.mContentType = 0;
        this.mFlags = 0;
        this.mLegacyStream = -1;
        this.mUsage = i1IllIll1l.getUsage();
        this.mContentType = i1IllIll1l.getContentType();
        this.mFlags = i1IllIll1l.getFlags();
        this.mLegacyStream = i1IllIll1l.getRawLegacyStreamType();
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIlIl1IIl1 build() throws NoSuchAlgorithmException {
        if (llIlII1IlI.IllIlI1l1I(I1I1lI1II1.a(new byte[]{89, 34, 49, 63, 44, 99, 122, 70, 118, 37, 81, 98, 121, 105, 101, 101, 47, 38}), 267983356L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{14, 5, 26, 83, 53}));
        }
        return new ll11llI1Il(this.mContentType, this.mFlags, this.mUsage, this.mLegacyStream);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public lll1IlllIl setUsage(int i) throws InterruptedException {
        if (androidx.core.location.lIIlI111II.l111lI11I1(IronSourceError.ERROR_RV_ALL_SMASHES_SESSION_CAPPED)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{1, 62, 83, 55, 90, 0, 116, 91, 67, 30, 100, 95, 12, 105, 89, 93, 6, 2, 90, 88}));
        }
        switch (i) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
            case 15:
                this.mUsage = i;
                break;
            case 16:
                this.mUsage = 12;
                break;
            default:
                this.mUsage = 0;
                break;
        }
        if (l1lll111II.I1lllI1llI(8873)) {
            throw new ClassCastException(I1I1lI1II1.a(new byte[]{98, 52, 58, 13, 50, 92, 69, 98, 113, 8, 66, 87, 100, 88, 98, 4, 83, 41, 36, 3, 8, 82, 105, 98, 100, 66, 4}));
        }
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public lll1IlllIl setContentType(int i) {
        if (i == 0 || i == 1 || i == 2 || i == 3 || i == 4) {
            this.mContentType = i;
        } else {
            this.mContentType = 0;
        }
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public lll1IlllIl setFlags(int i) throws NoSuchAlgorithmException {
        this.mFlags = (i & 1023) | this.mFlags;
        if (Il1I1lllIl.l1Il11I1Il(I1I1lI1II1.a(new byte[]{77, 23, 4, 22, 36, 113, 111, 114, 87, 38, 9, 71, 2, 83, 126, 100, 3, 23, 82, 102, 6, 27, Byte.MAX_VALUE, 101, 68, 95, 124}), 213942874L)) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{116, 28, 52, 1, 24, 68, 15, 86, 64, 81, 86, 90, 7, 119, 88, 82, 85, 82, 14, 72, 113}));
        }
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public lll1IlllIl setLegacyStreamType(int i) {
        if (i == 10) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{100, 48, 48, 32, 35, 120, 104, 113, 122, 39, 117, 99, 102, 112, 118, 124, 46, 40, 54, 107, 16, 8, 64, 21, 93, 89, 67, 69, 5, 18, 91, 6, 80, 5, 1, 28, 66, 70, 67, 66, 92, 5, 93, 16, 65, 64, 68, 80, 66, 21, 10, 83, 68, 65, 68, 84, 64, 22, 66, 22, 1, 86, 23, 5, 88, 22, 66, 4, 23, 81, 94, 95, 25, 20, 92, 81, 76, 91, 85, 86, 9}));
        }
        this.mLegacyStream = i;
        return setInternalLegacyStreamType(i);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    private lll1IlllIl setInternalLegacyStreamType(int i) throws ReflectiveOperationException {
        switch (i) {
            case 0:
                this.mContentType = 1;
                break;
            case 1:
                this.mContentType = 4;
                break;
            case 2:
                this.mContentType = 4;
                break;
            case 3:
                this.mContentType = 2;
                break;
            case 4:
                this.mContentType = 4;
                break;
            case 5:
                this.mContentType = 4;
                break;
            case 6:
                this.mContentType = 1;
                this.mFlags |= 4;
                break;
            case 7:
                this.mFlags = 1 | this.mFlags;
                this.mContentType = 4;
                break;
            case 8:
                this.mContentType = 4;
                break;
            case 9:
                this.mContentType = 4;
                break;
            case 10:
                this.mContentType = 1;
                break;
            default:
                Log.e(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 116, 67, 68, 75, 13, 82, 69, 65, 92, 71, 118, 13, 12, 18, 83, 68}), I1I1lI1II1.a(new byte[]{126, 10, 20, 4, 14, 92, 83, 16, 74, 16, 66, 85, 84, 84, 20, 65, 27, 17, 7, 18}) + i + I1I1lI1II1.a(new byte[]{23, 2, 13, 23, 66, 116, 66, 84, 80, 11, 113, 68, 65, 75, 93, 87, 23, 21, 7, 65, 115, 14, 94, 69, 82, 66}));
                break;
        }
        this.mUsage = ll11llI1Il.usageForStreamType(i);
        if (android.media.content.lIIlI111II.llIIl11lIl(165044517L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{2, 35, 7, 49, 38, 97, 14, 3, 90, 87, 93, 0, 124, 120, 76, 81, 87, 10, 19}));
        }
        return this;
    }
}
