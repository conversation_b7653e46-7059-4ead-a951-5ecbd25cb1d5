package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.Il1IIllIll;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llIl1IlllI implements Runnable {
    final /* synthetic */ Il1lII1l1l this$1;

    llIl1IlllI(Il1lII1l1l il1lII1l1l) {
        this.this$1 = il1lII1l1l;
    }

    @Override // java.lang.Runnable
    public void run() {
        if (lIIlI111II.lI1lIIll11(839817824L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{124, 86, 53, 20, 87, 122, 124, 124, 94, 60, 5, 65, 6, 116, 13, 81}));
        }
        this.this$1.this$0.mConnections.remove(this.this$1.callbacks.asBinder());
        if (Il1IIllIll.I1lIllll1l(I1I1lI1II1.a(new byte[]{116, 93, 56, 51, 90, 102, 7, 66, 126, 5, 96, 91, 69, 76, 125, 114}))) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{80, 86, 41, 80, 56, 101, 112, 125, 84, 10, 97, 82, 109, 117}));
        }
    }
}
