package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import android.media.browse.MediaBrowser$MediaItem;
import android.os.Bundle;
import android.service.media.MediaBrowserService$Result;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.support.v4.media.session.I1lI1Il111;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I11lII1Il1 extends III1Il1II1 {
    final /* synthetic */ lI1IIIl1I1 this$1;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    I11lII1Il1(lI1IIIl1I1 li1iiil1i1, Context context) {
        super(li1iiil1i1, context);
        this.this$1 = li1iiil1i1;
    }

    @Override // android.service.media.MediaBrowserService
    public void onLoadChildren(String str, MediaBrowserService$Result<List<MediaBrowser$MediaItem>> mediaBrowserService$Result, Bundle bundle) throws InterruptedException, InstantiationException {
        if (lIIllIlIl1.Il1IIlI1II(438676376L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{90, 44, 44, 4, 5, 123, 110, 92, 15, 6, 88, 84, 119, 15, 98, 1, 21, 23, 7, 125, 73, 19, 71, 108, 4, 85, 78, 85}));
        }
        I1lI1Il111.ensureClassLoader(bundle);
        this.this$1.this$0.mCurConnection = this.this$1.this$0.mConnectionFromFwk;
        this.this$1.onLoadChildren(str, new lIIIIlIIl1<>(mediaBrowserService$Result), bundle);
        this.this$1.this$0.mCurConnection = null;
    }
}
