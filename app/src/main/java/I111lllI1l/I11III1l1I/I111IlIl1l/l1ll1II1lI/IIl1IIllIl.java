package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.content.II1I11IlI1;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;
import androidx.core.location.lI1lI11Ill;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIl1IIllIl implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ String val$id;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ IBinder val$token;

    IIl1IIllIl(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, IBinder iBinder, Bundle bundle) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$id = str;
        this.val$token = iBinder;
        this.val$options = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws CertPathValidatorException, ReflectiveOperationException {
        if (lI1lI11Ill.I1II1111ll(5282)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{94, 51, 22, 81, 42, 82, 65, 95, 113, 45, 96, 4, 86, 123, 5, 77, 32, 54, 9, 121, 122, 35, 70}));
        }
        Il1lII1l1l il1lII1l1l = this.this$1.this$0.mConnections.get(this.val$callbacks.asBinder());
        if (il1lII1l1l != null) {
            this.this$1.this$0.addSubscription(this.val$id, il1lII1l1l, this.val$token, this.val$options);
            return;
        }
        Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{86, 0, 6, 54, 23, 87, 68, 83, 75, 13, 64, 68, 92, 86, 90, 21, 4, 14, 16, 18, 83, 0, 95, 89, 81, 87, 84, 14, 68, 70, 95, 2, 67, 68, 11, 22, 12, 18, 67, 16, 75, 1, 87, 89, 70, 77, 81, 71, 7, 5, 66, 91, 84, 92}) + this.val$id);
        if (II1I11IlI1.I1II1111ll(404070581L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{115, 10, 81, 39, 17, 80, 121, 126, 104, 47, 3, 93}));
        }
    }
}
