package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.I1II1llI1I;
import android.accounts.utils.I1lllI11II;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.media.content.II1I11IlI1;
import android.os.Build$VERSION;
import android.os.Bundle;
import android.os.IBinder;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.support.v4.media.session.MediaSessionCompat$Token;
import android.support.v4.os.ResultReceiver;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.l1l1I111I1;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.CharConversionException;
import java.io.EOFException;
import java.io.FileDescriptor;
import java.io.NotActiveException;
import java.io.PrintWriter;
import java.net.UnknownServiceException;
import java.security.AccessControlException;
import java.security.InvalidParameterException;
import java.security.KeyStoreException;
import java.security.NoSuchProviderException;
import java.security.cert.CRLException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateParsingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.RejectedExecutionException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIl1I1II.I1Il1l1llI.IIlIllllII.IIIl1lIlll;

/* loaded from: classes.dex */
public abstract class lIIIIll1II extends Service {
    private static final float EPSILON = 1.0E-5f;
    public static final int RESULT_ERROR = -1;
    static final int RESULT_FLAG_ON_LOAD_ITEM_NOT_IMPLEMENTED = 2;
    static final int RESULT_FLAG_ON_SEARCH_NOT_IMPLEMENTED = 4;
    static final int RESULT_FLAG_OPTION_NOT_HANDLED = 1;
    public static final int RESULT_OK = 0;
    public static final int RESULT_PROGRESS_UPDATE = 1;
    Il1lII1l1l mCurConnection;
    private IIIlIl1I1l mImpl;
    MediaSessionCompat$Token mSession;
    static final String TAG = I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64});
    public static final String SERVICE_INTERFACE = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 44, 86, 81, 90, 87, 117, 23, 11, 69, 68, 6, 69, 55, 7, 23, 20, 92, 84, 85});
    public static final String KEY_MEDIA_ITEM = I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 94, 68, 92, 9});
    public static final String KEY_SEARCH_RESULTS = I1I1lI1II1.a(new byte[]{68, 1, 3, 23, 1, 93, 104, 66, 92, 23, 69, 92, 65, 74});
    static final boolean DEBUG = Log.isLoggable(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), 3);
    final Il1lII1l1l mConnectionFromFwk = new Il1lII1l1l(this, I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 71, 80, 17, 18, 11, 93, 94, 79, 126, 80, 87, 95, 86, 38, 11, 92, 67, 17, 88, 8, 14, 0, 16}), -1, -1, null, null);
    final ArrayList<Il1lII1l1l> mPendingConnections = new ArrayList<>();
    final IIIl1lIlll<IBinder, Il1lII1l1l> mConnections = new IIIl1lIlll<>();
    final IIll11II1I mHandler = new IIll11II1I(this);

    public abstract IlIllIll1I onGetRoot(String str, int i, Bundle bundle);

    public abstract void onLoadChildren(String str, IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> ilIlIlllI1);

    public void onSubscribe(String str, Bundle bundle) throws CertPathValidatorException {
        if (lIIlI111II.I1IlI11II1(5951)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{65, 54, 6, 9, 82, 12, 81, 120, 64, 80, 72, 115, 126, 64, 3, 96, 41, 13}));
        }
    }

    public void attachToBaseContext(Context context) {
        attachBaseContext(context);
    }

    @Override // android.app.Service
    public void onCreate() {
        super.onCreate();
        if (Build$VERSION.SDK_INT >= 28) {
            this.mImpl = new IIl1l11I1l(this);
        } else if (Build$VERSION.SDK_INT >= 26) {
            this.mImpl = new lI1IIIl1I1(this);
        } else {
            this.mImpl = new lIIl1I11II(this);
        }
        this.mImpl.onCreate();
    }

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        if (IIlI1Il1lI.l11I11I11l(227577576L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{114, 47, 54, 81, 87, 76, 4, 106, 123, 30, 74, 100, 102, 78, 80, 6, 49, 23, 39, 93, 4, 6, 125, 113, 120, 85}));
        }
        IBinder iBinderOnBind = this.mImpl.onBind(intent);
        if (androidx.core.location.lIIlI111II.lI11IlI1lI(813002724L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{96, 60, 56, 15, 33, 83, 4, 65, 116, 8, 6, 90, 65, 110, 98, 125, 7, 14, 87, 119, 92, 48, 96, 98, 81}));
        }
        return iBinderOnBind;
    }

    @Override // android.app.Service
    public void dump(FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
        if (lIIlI111II.I11II1111l(8100)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{90, 46, 80, 31}));
        }
        if (l1l1IllI11.I1lllI1llI(5897)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{126, 6, 56, 38, 43, 70, 67, 89, 94, 33, 8, 90, 3, 104, 3, 109, 58, 84, 59, 75, 8, 6, 82, 103, 120, 3, 93, 3, 81}));
        }
    }

    public void onLoadChildren(String str, IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> ilIlIlllI1, Bundle bundle) {
        ilIlIlllI1.setFlags(1);
        onLoadChildren(str, ilIlIlllI1);
    }

    public void onUnsubscribe(String str) throws ReflectiveOperationException {
        if (android.media.content.Il1llIl111.l11I11I11l(I1I1lI1II1.a(new byte[]{2, 45, 10, 49, 18, 120, 2, 6, 8, 87, 121, 98, 65, 109, 0, 84, 44, 84, 85}), 6985)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{97}));
        }
    }

    public void onLoadItem(String str, IlIlIlllI1<MediaBrowserCompat$MediaItem> ilIlIlllI1) throws BrokenBarrierException {
        ilIlIlllI1.setFlags(2);
        ilIlIlllI1.sendResult(null);
    }

    public void onSearch(String str, Bundle bundle, IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> ilIlIlllI1) throws BrokenBarrierException, EOFException {
        ilIlIlllI1.setFlags(4);
        ilIlIlllI1.sendResult(null);
        if (android.media.content.lIIlI111II.Il1lII1l1l(2979)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{78, 42, 50, 32, 55, 0, 125, 69, 99, 10, 94, 125, 101, 110, 81, 97, 90, 15}));
        }
    }

    public void onCustomAction(String str, Bundle bundle, IlIlIlllI1<Bundle> ilIlIlllI1) throws CharConversionException {
        ilIlIlllI1.sendError(null);
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{114, 14, 85, 81, 1, 94, 124, 113, 92, 37, 6, 93, 6, 88, 77, 82, 1, 86}), 322781349L)) {
            Log.e(I1I1lI1II1.a(new byte[]{4, 43, 7, 93, 39, 86, 99, 0, 104, 53, 70, 120, 68, 122, 76, 126, 51, 25, 22, 71}), I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 82, 81, 48, 80, 4, 99, 68, 80, 62, 115, 69, 0, 65, 4, 4, 52, 17, 33, 93, 123, 11, 64, 67, 73, 92, 97, 50}));
        }
    }

    public void setSessionToken(MediaSessionCompat$Token mediaSessionCompat$Token) {
        if (mediaSessionCompat$Token == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{100, 1, 17, 22, 11, 90, 89, 16, 77, 11, 91, 85, 91, 25, 89, 84, 27, 65, 12, 93, 68, 65, 81, 80, 19, 88, 66, 9, 8}));
        }
        if (this.mSession != null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 17, 80, 68, 67, 80, 11, 94, 16, 65, 86, 95, 80, 12, 65, 10, 83, 67, 65, 82, 89, 65, 83, 86, 1, 29, 18, 85, 6, 82, 10, 66, 22, 7, 65}));
        }
        this.mSession = mediaSessionCompat$Token;
        this.mImpl.setSessionToken(mediaSessionCompat$Token);
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.I1II1111ll(356224429L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{93, 20, 19, 0, 11, 108, 81, 85, 92, 12, 4, 102, 1, 79, 99, 91, 58, 87, 45, 102, 0, 81, 86, 12, 74, 1}));
        }
    }

    public MediaSessionCompat$Token getSessionToken() {
        return this.mSession;
    }

    public final Bundle getBrowserRootHints() {
        Bundle browserRootHints = this.mImpl.getBrowserRootHints();
        if (Il1l11I11I.IlIllIll1I(I1I1lI1II1.a(new byte[]{120, 2, 85, 40, 42, 102, 117, 98, 114, 9, 65, 0, 76, 10, 102, 96, 59, 84, 59, 87, 70, 45, 4, 81}))) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{94, 44, 58, 22, 18, 5, 102, 68, 105, 42, 93, 124, 124, 86, 119, 115}));
        }
        return browserRootHints;
    }

    public final llIllIlll1 getCurrentBrowserInfo() {
        return this.mImpl.getCurrentBrowserInfo();
    }

    public void notifyChildrenChanged(String str) {
        if (str == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{71, 5, 16, 0, 12, 65, 126, 84, 25, 7, 81, 94, 91, 86, 64, 21, 0, 4, 66, 92, 69, 13, 95, 21, 90, 88, 23, 11, 11, 70, 94, 5, 78, 39, 10, 12, 14, 81, 69, 85, 87, 39, 88, 81, 91, 94, 81, 81}));
        }
        this.mImpl.notifyChildrenChanged(str, null);
    }

    public void notifyChildrenChanged(String str, Bundle bundle) throws CRLException {
        if (str == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{71, 5, 16, 0, 12, 65, 126, 84, 25, 7, 81, 94, 91, 86, 64, 21, 0, 4, 66, 92, 69, 13, 95, 21, 90, 88, 23, 11, 11, 70, 94, 5, 78, 39, 10, 12, 14, 81, 69, 85, 87, 39, 88, 81, 91, 94, 81, 81}));
        }
        if (bundle == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{88, 20, 22, 12, 13, 91, 68, 16, 90, 5, 94, 94, 90, 77, 20, 87, 7, 65, 12, 71, 92, 13, 19, 92, 93, 22, 89, 10, 16, 91, 81, 26, 116, 12, 11, 9, 6, 71, 82, 94, 122, 12, 81, 94, 82, 92, 80}));
        }
        this.mImpl.notifyChildrenChanged(str, bundle);
        if (l1lI1I1l11.l11I11I11l(307311332L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 30, 54, 19, 83, 80, 79, 0, 110, 14, 85, 125, 83, 112, 117, 1, 19, 37, 5, 101, 4, 2, 88, 94, 120, 82, 110, 87}));
        }
    }

    public void notifyChildrenChanged(llIllIlll1 llillilll1, String str, Bundle bundle) throws CharConversionException, KeyStoreException {
        if (android.support.v4.graphics.drawable.lIIlI111II.IIll1l1lII(8070)) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{82, 13, 51, 52, 20, 119, 120, 103, 118, 16, 102, 71, 4, 105, 94, 91, 50, 13, 55, 0, 114, 80, 94, 89}));
        }
        if (llillilll1 == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{69, 1, 15, 10, 22, 80, 98, 67, 92, 22, 121, 94, 83, 86, 20, 86, 3, 15, 12, 93, 68, 65, 81, 80, 19, 88, 66, 9, 8, 18, 94, 13, 23, 10, 13, 17, 11, 83, 78, 115, 81, 13, 92, 84, 71, 92, 90, 118, 10, 0, 12, 85, 85, 5}));
        }
        if (str == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{71, 5, 16, 0, 12, 65, 126, 84, 25, 7, 81, 94, 91, 86, 64, 21, 0, 4, 66, 92, 69, 13, 95, 21, 90, 88, 23, 11, 11, 70, 94, 5, 78, 39, 10, 12, 14, 81, 69, 85, 87, 39, 88, 81, 91, 94, 81, 81}));
        }
        if (bundle == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{88, 20, 22, 12, 13, 91, 68, 16, 90, 5, 94, 94, 90, 77, 20, 87, 7, 65, 12, 71, 92, 13, 19, 92, 93, 22, 89, 10, 16, 91, 81, 26, 116, 12, 11, 9, 6, 71, 82, 94, 122, 12, 81, 94, 82, 92, 80}));
        }
        this.mImpl.notifyChildrenChanged(llillilll1, str, bundle);
        if (II1I11IlI1.IlIllIll1I(373967409L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{78, 33, 4, 93, 41, 96, 65, 5, 123, 37, 83, 113, 96, 84, 66, 94, 14, 2, 16, 104, 6, 21}));
        }
    }

    boolean isValidPackage(String str, int i) {
        if (str == null) {
            if (androidx.recyclerview.widget.content.adapter.II1lllllI1.llll111lI1(I1I1lI1II1.a(new byte[]{70, 83, 14, 17, 15, 86, 64, 6, 117, 7, 9, 89, 89, 13, 4, 113, 33, 27, 44, 4, 81, 55, 4, 6}), 1570061561L)) {
                throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{100, 23, 18, 6, 81, 96, Byte.MAX_VALUE, 74, 85, 22, 114}));
            }
            return false;
        }
        for (String str2 : getPackageManager().getPackagesForUid(i)) {
            if (str2.equals(str)) {
                return true;
            }
        }
        return false;
    }

    void addSubscription(String str, Il1lII1l1l il1lII1l1l, IBinder iBinder, Bundle bundle) throws IllegalAccessException, CertPathValidatorException {
        List<I1II1llI1I<IBinder, Bundle>> arrayList = il1lII1l1l.subscriptions.get(str);
        if (arrayList == null) {
            arrayList = new ArrayList<>();
        }
        for (I1II1llI1I<IBinder, Bundle> i1II1llI1I : arrayList) {
            if (iBinder == i1II1llI1I.first && lIIlI11IlI.areSameOptions(bundle, i1II1llI1I.second)) {
                if (I1lllI11II.IlIllIll1I(186092457L)) {
                    throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{85, 47, 23, 35, 33, 126, 86, 3, 111, 14, 85, 4, 112, 110, 85, 108, 3, 15, 36, 107, 98, 55, 70, 94, 7}));
                }
                return;
            }
        }
        arrayList.add(new I1II1llI1I<>(iBinder, bundle));
        il1lII1l1l.subscriptions.put(str, arrayList);
        performLoadChildren(str, il1lII1l1l, bundle, null);
        this.mCurConnection = il1lII1l1l;
        onSubscribe(str, bundle);
        this.mCurConnection = null;
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{5, 53, 20}))) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{93, 28, 82, 21}));
        }
    }

    boolean removeSubscription(String str, Il1lII1l1l il1lII1l1l, IBinder iBinder) throws CharConversionException, ReflectiveOperationException {
        if (lIlIII1I1l.lI11llll1I(I1I1lI1II1.a(new byte[]{121, 14, 86, 3, 33, 111, 113, 68, 83, 44, 2, 116, 4, 96, 70, 124, 17, 9, 20, 10}), 225677442L)) {
            throw new ClassCircularityError(I1I1lI1II1.a(new byte[]{83, 46, 5, 16, 0}));
        }
        boolean z = false;
        try {
            if (iBinder == null) {
                boolean z2 = il1lII1l1l.subscriptions.remove(str) != null;
                this.mCurConnection = il1lII1l1l;
                onUnsubscribe(str);
                this.mCurConnection = null;
                if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{98, 41, 18, 18, 83, 83, 65, 125, 86, 18, 115}), 193704397L)) {
                    throw new CharConversionException(I1I1lI1II1.a(new byte[]{94, 7, 8, 28, 90, 103}));
                }
                return z2;
            }
            List<I1II1llI1I<IBinder, Bundle>> list = il1lII1l1l.subscriptions.get(str);
            if (list != null) {
                Iterator<I1II1llI1I<IBinder, Bundle>> it = list.iterator();
                while (it.hasNext()) {
                    if (iBinder == it.next().first) {
                        it.remove();
                        z = true;
                    }
                }
                if (list.size() == 0) {
                    il1lII1l1l.subscriptions.remove(str);
                }
            }
            return z;
        } finally {
            this.mCurConnection = il1lII1l1l;
            onUnsubscribe(str);
            this.mCurConnection = null;
        }
    }

    void performLoadChildren(String str, Il1lII1l1l il1lII1l1l, Bundle bundle, Bundle bundle2) {
        l11lIIlllI l11liillli = new l11lIIlllI(this, str, il1lII1l1l, str, bundle, bundle2);
        this.mCurConnection = il1lII1l1l;
        if (bundle == null) {
            onLoadChildren(str, l11liillli);
        } else {
            onLoadChildren(str, l11liillli, bundle);
        }
        this.mCurConnection = null;
        if (!l11liillli.isDone()) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{88, 10, 46, 10, 3, 81, 116, 88, 80, 8, 84, 66, 80, 87, 20, 88, 23, 18, 22, 18, 83, 0, 95, 89, 19, 82, 82, 17, 5, 81, 95, 75, 30, 68, 13, 23, 66, 70, 82, 94, 93, 54, 85, 67, 64, 85, 64, 29, 75, 65, 0, 87, 86, 14, 65, 80, 19, 68, 82, 17, 17, 64, 89, 10, 89, 3, 66, 3, 13, 71, 23, 64, 88, 7, 91, 81, 82, 92, 9}) + il1lII1l1l.pkg + I1I1lI1II1.a(new byte[]{23, 13, 6, 88}) + str);
        }
        if (lIlIII1I1l.Ill1lIIlIl(2156)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{116, 48, 6, 46, 11, 94, 80, 95, 106, 33, 89, 81, 103, 109, 92, 86, 42, 49, 42, 11, 96, 11, 97, 93, 94, 117, 81, 47}));
        }
    }

    List<MediaBrowserCompat$MediaItem> applyOptions(List<MediaBrowserCompat$MediaItem> list, Bundle bundle) throws CertificateParsingException, NotActiveException {
        if (list == null) {
            if (lII1llllI1.l1l1l1IIlI(234534626L)) {
                throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{123, 60, 15, 42, 45, 65, 122, 93, 111, 83, 122, 95, 69, 78, 91, 71, 9, 15, 17, 80, 8, 48, Byte.MAX_VALUE, 64, 94, 95, 117, 61, 43, 122}));
            }
            return null;
        }
        int i = bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114}), -1);
        int i2 = bundle.getInt(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 53, 37, 117, 114, 60, 100, 45, 56, 32}), -1);
        if (i == -1 && i2 == -1) {
            return list;
        }
        int i3 = i2 * i;
        int size = i3 + i2;
        if (i < 0 || i2 < 1 || i3 >= list.size()) {
            return Collections.emptyList();
        }
        if (size > list.size()) {
            size = list.size();
        }
        List<MediaBrowserCompat$MediaItem> listSubList = list.subList(i3, size);
        if (llIlI11III.I1II1111ll(I1I1lI1II1.a(new byte[]{111, 23, 80, 8, 41, 3, 88, 118, 64, 42, 0, 64, 112, 126, 92, Byte.MAX_VALUE, 87, 81, 26, 7, 8, 7, 82, 91, 87, 120, 101}))) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{90, 39, 84, 93, 20, 122, 79, 104, 76, 42, 116, 8, 67, 88, 80, 84, 43, 45, 35, 71, 87, 36}));
        }
        return listSubList;
    }

    void performLoadItem(String str, Il1lII1l1l il1lII1l1l, ResultReceiver resultReceiver) throws BrokenBarrierException {
        Illl1IIlII illl1IIlII = new Illl1IIlII(this, str, resultReceiver);
        this.mCurConnection = il1lII1l1l;
        onLoadItem(str, illl1IIlII);
        this.mCurConnection = null;
        if (!illl1IIlII.isDone()) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{88, 10, 46, 10, 3, 81, 126, 68, 92, 9, 16, 93, 64, 74, 64, 21, 1, 0, 14, 94, 16, 5, 86, 65, 82, 85, 95, 77, 77, 18, 88, 17, 23, 23, 7, 11, 6, 103, 82, 67, 76, 8, 68, 24, 28, 25, 86, 80, 4, 14, 16, 87, 16, 19, 86, 65, 70, 68, 89, 12, 10, 85, 23, 5, 88, 22, 66, 12, 6, 8}) + str);
        }
    }

    void performSearch(String str, Bundle bundle, Il1lII1l1l il1lII1l1l, ResultReceiver resultReceiver) throws BrokenBarrierException, EOFException, NoSuchProviderException {
        if (llIl1lII1I.ll1I1lII11(I1I1lI1II1.a(new byte[]{92, 44, 14, 23, 37, 112, 113, 86, 85, 34, 90, 124, 77, 92, 0, 121, 10, 27, 40, 123, 5, 13, 4, 96}), 4998)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{66, 17, 47, 23, 59, 97, 92, 81, 123, 50, 99, 81, 98, 115, 67, 65, 39, 89, 24, 93, 101, 22, 89, 67}));
        }
        II1III111I ii1iii111i = new II1III111I(this, str, resultReceiver);
        this.mCurConnection = il1lII1l1l;
        onSearch(str, bundle, ii1iii111i);
        this.mCurConnection = null;
        if (!ii1iii111i.isDone()) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{88, 10, 49, 0, 3, 71, 84, 88, 25, 9, 69, 67, 65, 25, 87, 84, 14, 13, 66, 86, 85, 21, 82, 86, 91, 30, 30, 69, 11, 64, 23, 16, 82, 10, 6, 55, 7, 70, 66, 92, 77, 76, 25, 16, 87, 92, 82, 90, 16, 4, 66, 64, 85, 21, 70, 71, 93, 95, 89, 2, 68, 84, 88, 17, 23, 21, 23, 0, 16, 76, 10}) + str);
        }
        if (l11Il1lI11.IlII1Illll(7336)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{117, 3, 50, 44, 38, 116, 99, 89, 109, 55, 116}));
        }
    }

    void performCustomAction(String str, Bundle bundle, Il1lII1l1l il1lII1l1l, ResultReceiver resultReceiver) throws CharConversionException, UnknownServiceException {
        IIII1l1lll iIII1l1lll = new IIII1l1lll(this, str, resultReceiver);
        this.mCurConnection = il1lII1l1l;
        onCustomAction(str, bundle, iIII1l1lll);
        this.mCurConnection = null;
        if (!iIII1l1lll.isDone()) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{88, 10, 33, 16, 17, 65, 88, 93, 120, 7, 68, 89, 90, 87, 20, 88, 23, 18, 22, 18, 83, 0, 95, 89, 19, 82, 82, 17, 5, 81, 95, 75, 30, 68, 13, 23, 66, 70, 82, 94, 93, 54, 85, 67, 64, 85, 64, 29, 75, 65, 13, 64, 16, 18, 86, 91, 87, 115, 69, 23, 11, 64, 31, 74, 23, 6, 7, 3, 13, 71, 82, 16, 75, 1, 68, 69, 71, 87, 93, 91, 5, 65, 4, 93, 66, 65, 82, 86, 71, 95, 88, 11, 89}) + str + I1I1lI1II1.a(new byte[]{23, 1, 26, 17, 16, 84, 68, 13}) + bundle);
        }
        if (lI1lllIII1.Ill1lIIlIl(235383023L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{110, 28, 59, 34, 56, 13, 81, 5, 110, 93, 74, 2, 98, 11, 114, 112, 9, 40, 7, 106, 92, 51, 125, 115, 93, 88, 114, 6, 54, 68, 124}));
        }
    }
}
