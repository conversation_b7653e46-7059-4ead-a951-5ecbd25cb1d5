package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.content.II1I11IlI1;
import android.os.Bundle;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I1llIII1Il implements Runnable {
    final /* synthetic */ lIlII1Illl this$1;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ String val$parentId;
    final /* synthetic */ llIllIlll1 val$remoteUserInfo;

    I1llIII1Il(lIlII1Illl lilii1illl, llIllIlll1 llillilll1, String str, Bundle bundle) {
        this.this$1 = lilii1illl;
        this.val$remoteUserInfo = llillilll1;
        this.val$parentId = str;
        this.val$options = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws NoSuchFieldException {
        if (II1I11IlI1.I1lllI1llI(544)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{126, 6, 37, 31, 58, 113, 116, 100, 116, 45, 7, 92, 87, 11, 83, 7, 0, 14, 84, 112, 72, 11, 75}));
        }
        for (int i = 0; i < this.this$1.this$0.mConnections.size(); i++) {
            Il1lII1l1l il1lII1l1lValueAt = this.this$1.this$0.mConnections.valueAt(i);
            if (il1lII1l1lValueAt.browserInfo.equals(this.val$remoteUserInfo)) {
                this.this$1.notifyChildrenChangedOnHandler(il1lII1l1lValueAt, this.val$parentId, this.val$options);
                return;
            }
        }
    }
}
