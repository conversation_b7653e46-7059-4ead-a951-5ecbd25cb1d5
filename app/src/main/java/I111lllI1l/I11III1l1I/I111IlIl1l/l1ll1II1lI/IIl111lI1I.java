package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioManager;
import androidx.core.location.Il1l11I11I;
import java.io.NotSerializableException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class IIl111lI1I {
    static int getStreamMinVolume(AudioManager audioManager, int i) throws NotSerializableException {
        int streamMinVolume = audioManager.getStreamMinVolume(i);
        if (Il1l11I11I.I1lI11IIll(I1I1lI1II1.a(new byte[]{4, 5, 86, 42, 37, 111, 86, 1, 125, 6, 117, 94, 76, 0, 65, 114, 11, 85, 23, 106, 103}), 166672885L)) {
            throw new NotSerializableException(I1I1lI1II1.a(new byte[]{94, 32, 12, 11, 83, 100, 67, 88, 1, 3, 99, 115, 70, 80, 97, 122, 18, 80, 91, 89, 103, 25, 123, 120, 69, 110, 4, 34}));
        }
        return streamMinVolume;
    }

    private IIl111lI1I() {
    }
}
