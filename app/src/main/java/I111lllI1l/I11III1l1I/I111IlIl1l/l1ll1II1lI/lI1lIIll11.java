package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import android.media.browse.MediaBrowser$MediaItem;
import android.os.Bundle;
import android.service.media.MediaBrowserService;
import android.service.media.MediaBrowserService$BrowserRoot;
import android.service.media.MediaBrowserService$Result;
import android.support.v4.media.session.I1lI1Il111;
import androidx.core.location.I111I11Ill;
import androidx.interpolator.view.animation.IIIlIll111;
import java.net.SocketTimeoutException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lI1lIIll11 extends MediaBrowserService {
    final /* synthetic */ Il1llllI1I this$1;

    lI1lIIll11(Il1llllI1I il1llllI1I, Context context) {
        this.this$1 = il1llllI1I;
        attachBaseContext(context);
    }

    @Override // android.service.media.MediaBrowserService
    public MediaBrowserService$BrowserRoot onGetRoot(String str, int i, Bundle bundle) throws IllegalAccessException, InterruptedException, NoSuchAlgorithmException, SignatureException, KeyStoreException {
        if (I111I11Ill.l111IIlII1(I1I1lI1II1.a(new byte[]{112, 23, 13}), 344625125L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{80, 39, 80, 28, 54, 97, 71, 120, Byte.MAX_VALUE, 18, 71, 6, 79}));
        }
        I1lI1Il111.ensureClassLoader(bundle);
        IlIllIll1I ilIllIll1IOnGetRoot = this.this$1.onGetRoot(str, i, bundle == null ? null : new Bundle(bundle));
        if (ilIllIll1IOnGetRoot == null) {
            return null;
        }
        return new MediaBrowserService$BrowserRoot(ilIllIll1IOnGetRoot.mRootId, IlIllIll1I.access$100(ilIllIll1IOnGetRoot));
    }

    @Override // android.service.media.MediaBrowserService
    public void onLoadChildren(String str, MediaBrowserService$Result<List<MediaBrowser$MediaItem>> mediaBrowserService$Result) throws SocketTimeoutException {
        this.this$1.onLoadChildren(str, new lIIIIlIIl1<>(mediaBrowserService$Result));
        if (IIIlIll111.I111IlIl1I(276938664L)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{92, 2, 55, 29, 50, 79, 5, 126, 75, 61, 104, 86, 87, 118, 6, 12, 56, 8, 46, 104, 95, 27, 113}));
        }
    }
}
