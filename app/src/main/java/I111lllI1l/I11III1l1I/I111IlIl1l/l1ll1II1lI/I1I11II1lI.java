package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lIIIIII11I;
import android.os.Bundle;
import android.os.IBinder;
import android.support.v4.os.ResultReceiver;
import android.text.TextUtils;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I1Ill1lIII;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.EOFException;
import java.net.MalformedURLException;
import java.security.InvalidAlgorithmParameterException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class I1I11II1lI {
    final /* synthetic */ lIIIIll1II this$0;

    I1I11II1lI(lIIIIll1II liiiill1ii) {
        this.this$0 = liiiill1ii;
    }

    public void connect(String str, int i, int i2, Bundle bundle, IIll1IIlII iIll1IIlII) {
        if (Il11II1llI.Il1IIlI1II(255251908L)) {
            throw new MalformedURLException(I1I1lI1II1.a(new byte[]{79, 51, 51, 12, 17, 94, 6, 69, 95, 28, 8, 88, Byte.MAX_VALUE, 92, 78, 93, 23, 17, 17, 75, 73, 48, 71, 6, 87, 124}));
        }
        if (!this.this$0.isValidPackage(str, i2)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{103, 5, 1, 14, 3, 82, 82, 31, 76, 13, 84, 16, 88, 80, 71, 88, 3, 21, 1, 90, 10, 65, 70, 92, 87, 11}) + i2 + I1I1lI1II1.a(new byte[]{23, 20, 3, 6, 9, 84, 80, 85, 4}) + str);
        }
        this.this$0.mHandler.postOrRun(new l1Il1II11l(this, iIll1IIlII, str, i, i2, bundle));
    }

    public void disconnect(IIll1IIlII iIll1IIlII) {
        if (IIlI1Il1lI.llII1lIIlI(I1I1lI1II1.a(new byte[]{100, 45, 24, 43, 21, 97, 67, 104, 81, 41, 121, 125, 12, 94, 80, 66, 51, 45, 24, 118, 106, 17, 125, 100, 100, 15, 89, 85}))) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{91, 93, 44, 41, 44, 67, 94, 5, 11, 17, 91, 84, 86, 110, 78, 82, 50, 6, 24, 7, 116, 51, 95, 120, 80}));
        }
        this.this$0.mHandler.postOrRun(new II1lllllII(this, iIll1IIlII));
    }

    public void addSubscription(String str, IBinder iBinder, Bundle bundle, IIll1IIlII iIll1IIlII) {
        if (lIIlI111II.l1llI1llII(4404)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{78, 1, 49, 18}));
        }
        this.this$0.mHandler.postOrRun(new IIl1IIllIl(this, iIll1IIlII, str, iBinder, bundle));
    }

    public void removeSubscription(String str, IBinder iBinder, IIll1IIlII iIll1IIlII) {
        if (llIl1lII1I.I1lIllll1l(329315630L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{82, 6, 17, 1, 91}));
        }
        this.this$0.mHandler.postOrRun(new l11IIlI1l1(this, iIll1IIlII, str, iBinder));
    }

    public void getMediaItem(String str, ResultReceiver resultReceiver, IIll1IIlII iIll1IIlII) {
        if (TextUtils.isEmpty(str) || resultReceiver == null) {
            return;
        }
        this.this$0.mHandler.postOrRun(new II1lI11III(this, iIll1IIlII, str, resultReceiver));
        if (l1IIll1I1l.Il1IIlI1II(6157)) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{14, 3, 8, 42, 56, 12, 99, 7, 87, 20, 5, 105, 123, 82, 126, 94, 15, 59, 43, 10, 114, 2, 106, 88, 11, 69, 123}));
        }
    }

    public void registerCallbacks(IIll1IIlII iIll1IIlII, String str, int i, int i2, Bundle bundle) {
        if (androidx.versionedparcelable.custom.entities.IIlII1IIIl.IllIlI1l1I(I1I1lI1II1.a(new byte[]{15, 92, 39, 39, 0, 66, 67, 105, 116, 17}), 240243250L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{2, 7, 43, 7, 52, 64, 71, 120, 123, 45, 117, 74, 5, 125, 12, 2, 44, 47, 85, 6, 93}));
        }
        this.this$0.mHandler.postOrRun(new IIlII1IIIl(this, iIll1IIlII, i2, str, i, bundle));
    }

    public void unregisterCallbacks(IIll1IIlII iIll1IIlII) {
        if (lIIIIII11I.IlII1Illll(213164145L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{109, 23, 90, 33, 23, 125, 83, 122, 80, 15, 122, 67, Byte.MAX_VALUE}));
        }
        this.this$0.mHandler.postOrRun(new ll1I1l1l1l(this, iIll1IIlII));
    }

    public void search(String str, Bundle bundle, ResultReceiver resultReceiver, IIll1IIlII iIll1IIlII) {
        if (llIlII1IlI.I1lIllll1l(371045171L)) {
            Log.w(I1I1lI1II1.a(new byte[]{102, 52, 13, 84, 13, 103, 112, 93, 74, 60, 3, 114, 83, 104, 101, 84, 80, 45, 54, 70, 64, 50, 87, 121}), I1I1lI1II1.a(new byte[]{81, 46, 86, 11, 37, 82, 6, 120, 125, 33, 69}));
            return;
        }
        if (TextUtils.isEmpty(str) || resultReceiver == null) {
            if (l1lll111II.Il1IIlI1II(407633152L)) {
                throw new EOFException(I1I1lI1II1.a(new byte[]{81, 40, 50, 22, 85, 83, 97, 82, 108, 29, 94, 85, 116, 79, 88, 93, 91}));
            }
        } else {
            this.this$0.mHandler.postOrRun(new I111l1I1II(this, iIll1IIlII, str, bundle, resultReceiver));
            if (android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{92, 86, 0, 9, 43, 103, Byte.MAX_VALUE, 115, 124, 20, 68, 123, 91, 1, 98, 86, 84, 43, 51, 68, 73}), 237002413L)) {
                throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{93, 84, 40, 87, 54, 121, 82, 102, 117, 40, 94, 101, 80, 91, 112, 77}));
            }
        }
    }

    public void sendCustomAction(String str, Bundle bundle, ResultReceiver resultReceiver, IIll1IIlII iIll1IIlII) {
        if (I1Ill1lIII.l11I11I11l(201110220L)) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{1, 20, 26, 16, 55, 86, 125, 69, 108, 42, 105, 89, 80, 72, 81, 64, 14, 50, 54, 87, 124, 53, 64}));
        }
        if (TextUtils.isEmpty(str) || resultReceiver == null) {
            return;
        }
        this.this$0.mHandler.postOrRun(new lIIlllI1Il(this, iIll1IIlII, str, bundle, resultReceiver));
        if (ll1l11I1II.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{116, 42, 49, 33, 87, 5, 122, 6, 73, 0, 115, 71, 103, 74, 124, 4, 46}), 249772375L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{116, 7, 35, 45, 56, 116, 93, 123, 85, 8, 126, 67, 88, 8, 121, 13, 59, 5, 9, 119}));
        }
    }
}
