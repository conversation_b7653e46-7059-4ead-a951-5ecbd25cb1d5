package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import androidx.interpolator.view.animation.lIIlI111II;
import androidx.interpolator.view.animation.llIlII1IlI;
import java.security.NoSuchAlgorithmException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class llII1llll1 implements Runnable {
    final /* synthetic */ Il1llllI1I this$1;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ String val$parentId;
    final /* synthetic */ llIllIlll1 val$remoteUserInfo;

    llII1llll1(Il1llllI1I il1llllI1I, llIllIlll1 llillilll1, String str, Bundle bundle) {
        this.this$1 = il1llllI1I;
        this.val$remoteUserInfo = llillilll1;
        this.val$parentId = str;
        this.val$options = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws NoSuchAlgorithmException, ReflectiveOperationException {
        if (lIIlI111II.l111l1llIl(213578760L)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{103, 30, 81, 32, 3, 76, 89, 90, 124, 15, 104, 70, 120, 125, 4, 65, 15, 41, 42, 65, 67, 8, 113, 115, 123, 116, 126, 7, 48, 86}));
        }
        for (int i = 0; i < this.this$1.this$0.mConnections.size(); i++) {
            Il1lII1l1l il1lII1l1lValueAt = this.this$1.this$0.mConnections.valueAt(i);
            if (il1lII1l1lValueAt.browserInfo.equals(this.val$remoteUserInfo)) {
                this.this$1.notifyChildrenChangedForCompatOnHandler(il1lII1l1lValueAt, this.val$parentId, this.val$options);
            }
        }
        if (llIlII1IlI.I1II1111ll(175496842L)) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{126, 13, 48, 54, 45, 3, 100, 74, 93, 39, 106, 9, 71, 85, 100, 88, 6, 35, 82, 6, 82, 7, 120, 101, 120}));
        }
    }
}
