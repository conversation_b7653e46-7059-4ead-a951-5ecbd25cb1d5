package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.versionedparcelable.IIII1IIl1I;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lII1111lIl {
    public static ll11llI1Il read(IIII1IIl1I iIII1IIl1I) throws UnrecoverableEntryException {
        if (IlIIlI11I1.I1lllI1llI(253082524L)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{3, 61, 27, 44, 17, 108, 67, 91, 95, 37, 2, 84, 95, 65, 5, 84, 51, 88, 35, 101, 66, 7, 124, 97, 10}));
        }
        ll11llI1Il ll11lli1il = new ll11llI1Il();
        ll11lli1il.mUsage = iIII1IIl1I.readInt(ll11lli1il.mUsage, 1);
        ll11lli1il.mContentType = iIII1IIl1I.readInt(ll11lli1il.mContentType, 2);
        ll11lli1il.mFlags = iIII1IIl1I.readInt(ll11lli1il.mFlags, 3);
        ll11lli1il.mLegacyStream = iIII1IIl1I.readInt(ll11lli1il.mLegacyStream, 4);
        if (Il1IIllIll.I1lIllll1l(I1I1lI1II1.a(new byte[]{85, 12, 41, 84, 9, 114, 124, 82, 86, 13, 0, 98, 112, 112, 95, 86, 38, 6, 91}))) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{88, 51, 59, 50, 4, 6, 77, 70, 88, 17, 1, 95, 66, 82, 1, 125}));
        }
        return ll11lli1il;
    }

    public static void write(ll11llI1Il ll11lli1il, IIII1IIl1I iIII1IIl1I) {
        if (llIlI11III.I1lIllll1l(I1I1lI1II1.a(new byte[]{117, 32, 91, 17, 46, 98, 91, Byte.MAX_VALUE, 77, 11, 102, 87, 126, 65, 67, 108, 58, 41, 85, 5, 94, 12, 81, 92, 66, 92, 112}), 828725890L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{110, 15, 4, 15, 53, 92, 126, 121, 77, 80, 66, 116, 68, 15, 120, 84, 45, 37, 6, 122, 123}));
        }
        iIII1IIl1I.setSerializationFlags(false, false);
        iIII1IIl1I.writeInt(ll11lli1il.mUsage, 1);
        iIII1IIl1I.writeInt(ll11lli1il.mContentType, 2);
        iIII1IIl1I.writeInt(ll11lli1il.mFlags, 3);
        iIII1IIl1I.writeInt(ll11lli1il.mLegacyStream, 4);
        if (lIIlI111II.Il1lII1l1l(8652)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{65, 34, 56, 19, 49, 87, 97, 106, 12, 40, 116, 74, 92, 114, 126}));
        }
    }
}
