package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioAttributes;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class I1II111III extends IIll1l1111 {
    public I1II111III() {
    }

    I1II111III(AudioAttributes audioAttributes) {
        super(audioAttributes, -1);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIll1l1111, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getVolumeControlStream() throws CertPathValidatorException {
        if (IIlI1ll1ll.lll1111l11(I1I1lI1II1.a(new byte[]{83, 11, 16, 10, 56, 91, 5, 119, 72, 14, 97, 4, 65, 90, 69, 77, 27, 11, 26, 90, 69}), 326743330L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{90, 45, 36}));
        }
        return this.mAudioAttributes.getVolumeControlStream();
    }
}
