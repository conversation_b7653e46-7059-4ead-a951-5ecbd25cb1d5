package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lI1l1I1l1l;
import android.os.Bundle;
import android.support.v4.os.ResultReceiver;
import android.util.Log;
import androidx.core.location.I1Ill1lIII;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import java.io.EOFException;
import java.security.NoSuchProviderException;
import java.security.SignatureException;
import java.security.UnrecoverableEntryException;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.RejectedExecutionException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class I111l1I1II implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ Bundle val$extras;
    final /* synthetic */ String val$query;
    final /* synthetic */ ResultReceiver val$receiver;

    I111l1I1II(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, Bundle bundle, ResultReceiver resultReceiver) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$query = str;
        this.val$extras = bundle;
        this.val$receiver = resultReceiver;
    }

    @Override // java.lang.Runnable
    public void run() throws SignatureException, BrokenBarrierException, EOFException, NoSuchProviderException, UnrecoverableEntryException {
        if (IIlI1ll1ll.llll111lI1(I1I1lI1II1.a(new byte[]{5, 23, 0, 33, 23, 101, 92, 8, 73, 86, 117, 124, 97, 111, 99, 123, 39, 2, 22, 65, 85, 88, 0}))) {
            throw new SignatureException(I1I1lI1II1.a(new byte[]{4, 0, 18, 32, 27, 84, 125, 95, 111, 44, 81, 66, 103, 122, 81, 1, 44, 17, 42, 80, 106, 4, 112, 81, 95, 87, 14, 53}));
        }
        Il1lII1l1l il1lII1l1l = this.this$1.this$0.mConnections.get(this.val$callbacks.asBinder());
        if (il1lII1l1l == null) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{68, 1, 3, 23, 1, 93, 23, 86, 86, 22, 16, 83, 84, 85, 88, 87, 3, 2, 9, 18, 68, 9, 82, 65, 19, 95, 68, 11, 67, 70, 23, 17, 82, 3, 11, 22, 22, 80, 69, 85, 93, 68, 65, 69, 80, 75, 77, 8}) + this.val$query);
            if (I1Ill1lIII.Ill1lIIlIl(192896232L)) {
                throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{93, 8, 80, 84, 36}));
            }
        } else {
            this.this$1.this$0.performSearch(this.val$query, this.val$extras, il1lII1l1l, this.val$receiver);
            if (lI1l1I1l1l.IlII1Illll(I1I1lI1II1.a(new byte[]{65}), 293697574L)) {
                throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{79, 21, 26, 82, 56, 93, 91}));
            }
        }
    }
}
