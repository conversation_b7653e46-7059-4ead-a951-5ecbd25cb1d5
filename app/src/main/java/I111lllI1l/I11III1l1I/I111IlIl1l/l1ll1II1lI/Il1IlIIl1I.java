package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class Il1IlIIl1I {
    public static final int CLIENT_MSG_ADD_SUBSCRIPTION = 3;
    public static final int CLIENT_MSG_CONNECT = 1;
    public static final int CLIENT_MSG_DISCONNECT = 2;
    public static final int CLIENT_MSG_GET_MEDIA_ITEM = 5;
    public static final int CLIENT_MSG_REGISTER_CALLBACK_MESSENGER = 6;
    public static final int CLIENT_MSG_REMOVE_SUBSCRIPTION = 4;
    public static final int CLIENT_MSG_SEARCH = 8;
    public static final int CLIENT_MSG_SEND_CUSTOM_ACTION = 9;
    public static final int CLIENT_MSG_UNREGISTER_CALLBACK_MESSENGER = 7;
    public static final int CLIENT_VERSION_1 = 1;
    public static final int CLIENT_VERSION_CURRENT = 1;
    public static final int SERVICE_MSG_ON_CONNECT = 1;
    public static final int SERVICE_MSG_ON_CONNECT_FAILED = 2;
    public static final int SERVICE_MSG_ON_LOAD_CHILDREN = 3;
    public static final int SERVICE_VERSION_1 = 1;
    public static final int SERVICE_VERSION_2 = 2;
    public static final int SERVICE_VERSION_CURRENT = 2;
    public static final String DATA_CALLBACK_TOKEN = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 6, 81, 83, 94, 102, 64, 90, 9, 4, 12});
    public static final String DATA_CALLING_UID = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 76, 93, 81});
    public static final String DATA_CALLING_PID = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81});
    public static final String DATA_MEDIA_ITEM_ID = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5});
    public static final String DATA_MEDIA_ITEM_LIST = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 14, 8, 17, 70});
    public static final String DATA_MEDIA_SESSION_TOKEN = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 67, 80, 74, 71, 92, 13, 15, 61, 70, 95, 10, 86, 91});
    public static final String DATA_OPTIONS = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 90, 71, 68, 80, 11, 94, 67});
    public static final String DATA_NOTIFY_CHILDREN_CHANGED_OPTIONS = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 91, 88, 68, 80, 2, 73, 111, 86, 81, 93, 89, 6, 19, 7, 92, 111, 2, 91, 84, 93, 81, 82, 1, 59, 93, 71, 23, 94, 11, 12, 22});
    public static final String DATA_PACKAGE_NAME = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 69, 86, 83, 82, 5, 87, 85, 106, 87, 85, 88, 7});
    public static final String DATA_RESULT_RECEIVER = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 82, 67, 76, 8, 68, 111, 71, 92, 87, 80, 11, 23, 7, 64});
    public static final String DATA_ROOT_HINTS = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 88, 95, 77, 59, 88, 89, 91, 77, 71});
    public static final String DATA_SEARCH_EXTRAS = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 70, 82, 81, 75, 7, 88, 111, 80, 65, 64, 71, 3, 18});
    public static final String DATA_SEARCH_QUERY = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 70, 82, 81, 75, 7, 88, 111, 68, 76, 81, 71, 27});
    public static final String DATA_CUSTOM_ACTION = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 66, 67, 77, 11, 93, 111, 84, 90, 64, 92, 13, 15});
    public static final String DATA_CUSTOM_ACTION_EXTRAS = I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 66, 67, 77, 11, 93, 111, 84, 90, 64, 92, 13, 15, 61, 87, 72, 21, 65, 84, 64});
    public static final String EXTRA_CLIENT_VERSION = I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 92, 80, 1, 94, 68, 106, 79, 81, 71, 17, 8, 13, 92});
    public static final String EXTRA_CALLING_PID = I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 84, 81, 85, 8, 89, 94, 82, 102, 68, 92, 6});
    public static final String EXTRA_SERVICE_VERSION = I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 75, 18, 89, 83, 80, 102, 66, 80, 16, 18, 11, 93, 94});
    public static final String EXTRA_MESSENGER_BINDER = I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 90, 85, 74, 23, 85, 94, 82, 92, 70});
    public static final String EXTRA_SESSION_BINDER = I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 74, 23, 89, 95, 91, 102, 86, 92, 12, 5, 7, 64});

    private Il1IlIIl1I() {
    }
}
