package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.VolumeProvider;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IlIl1lllII extends VolumeProvider {
    final /* synthetic */ l1l1lI1lI1 this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IlIl1lllII(l1l1lI1lI1 l1l1li1li1, int i, int i2, int i3, String str) {
        super(i, i2, i3, str);
        this.this$0 = l1l1li1li1;
    }

    @Override // android.media.VolumeProvider
    public void onSetVolumeTo(int i) {
        this.this$0.onSetVolumeTo(i);
    }

    @Override // android.media.VolumeProvider
    public void onAdjustVolume(int i) throws CertificateNotYetValidException {
        if (I1I1IIIIl1.I1lllI1llI(I1I1lI1II1.a(new byte[]{122, 49, 15, 86, 83}), 166815314L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{69}));
        }
        this.this$0.onAdjustVolume(i);
        if (IllllI11lI.Il1IIlI1II(I1I1lI1II1.a(new byte[]{64, 87, 0, 84, 5, 84, 80, 72, 83}), 6563)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{69, 84, 56, 29, 82, 125, 83, 86, 67, 15, 2, 69, 87, 81, 78, 12, 80, 4, 91}));
        }
    }
}
