package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.content.II1I11IlI1;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class l1Il1II11l implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ int val$pid;
    final /* synthetic */ String val$pkg;
    final /* synthetic */ Bundle val$rootHints;
    final /* synthetic */ int val$uid;

    l1Il1II11l(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, int i, int i2, Bundle bundle) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$pkg = str;
        this.val$pid = i;
        this.val$uid = i2;
        this.val$rootHints = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws RemoteException {
        IBinder iBinderAsBinder = this.val$callbacks.asBinder();
        this.this$1.this$0.mConnections.remove(iBinderAsBinder);
        Il1lII1l1l il1lII1l1l = new Il1lII1l1l(this.this$1.this$0, this.val$pkg, this.val$pid, this.val$uid, this.val$rootHints, this.val$callbacks);
        this.this$1.this$0.mCurConnection = il1lII1l1l;
        il1lII1l1l.root = this.this$1.this$0.onGetRoot(this.val$pkg, this.val$uid, this.val$rootHints);
        this.this$1.this$0.mCurConnection = null;
        if (il1lII1l1l.root == null) {
            Log.i(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{121, 11, 66, 23, 13, 90, 67, 16, 95, 11, 66, 16, 86, 85, 93, 80, 12, 21, 66}) + this.val$pkg + I1I1lI1II1.a(new byte[]{23, 2, 16, 10, 15, 21, 68, 85, 75, 18, 89, 83, 80, 25}) + getClass().getName());
            try {
                this.val$callbacks.onConnectFailed();
            } catch (RemoteException unused) {
                Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{116, 5, 14, 9, 11, 91, 80, 16, 86, 10, 115, 95, 91, 87, 81, 86, 22, 39, 3, 91, 92, 4, 87, 29, 26, 22, 81, 4, 13, 94, 82, 7, 25, 68, 43, 2, 12, 90, 69, 89, 87, 3, 30, 16, 69, 82, 83, 8}) + this.val$pkg);
            }
        } else {
            try {
                this.this$1.this$0.mConnections.put(iBinderAsBinder, il1lII1l1l);
                iBinderAsBinder.linkToDeath(il1lII1l1l, 0);
                if (this.this$1.this$0.mSession != null) {
                    this.val$callbacks.onConnect(il1lII1l1l.root.getRootId(), this.this$1.this$0.mSession, il1lII1l1l.root.getExtras());
                }
            } catch (RemoteException unused2) {
                Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{116, 5, 14, 9, 11, 91, 80, 16, 86, 10, 115, 95, 91, 87, 81, 86, 22, 73, 75, 18, 86, 0, 90, 89, 86, 82, 25, 69, 32, 64, 88, 19, 71, 13, 12, 2, 66, 86, 91, 89, 92, 10, 68, 30, 21, 73, 95, 82, 95}) + this.val$pkg);
                this.this$1.this$0.mConnections.remove(iBinderAsBinder);
            }
        }
        if (II1I11IlI1.II1111I11I(I1I1lI1II1.a(new byte[]{81, 23, 17, 61, 54, 118, 109, 65, 92, 49, 69, 65, 98, 107, 126, 95, 24, 15, 82, 83, 68, 21, 113, 116, 4, 81, 64}), I1I1lI1II1.a(new byte[]{109, 83, 15, 9, 59, 113, 98, 93, 12, 82, 118, 92, 109, 91, 119, 94, 87}))) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{112, 33, 41, 39, 47, 109, 89, 87, 97, 33, 1, 7, 2, 8, 110, 100, 46, 15, 49, 93}));
        }
    }
}
