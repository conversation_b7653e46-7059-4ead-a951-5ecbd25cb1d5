package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.support.v4.media.session.MediaSessionCompat$Token;
import java.util.List;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public interface IIll1IIlII {
    IBinder asBinder();

    void onConnect(String str, MediaSessionCompat$Token mediaSessionCompat$Token, Bundle bundle) throws RemoteException;

    void onConnectFailed() throws RemoteException;

    void onLoadChildren(String str, List<MediaBrowserCompat$MediaItem> list, Bundle bundle, Bundle bundle2) throws RemoteException;
}
