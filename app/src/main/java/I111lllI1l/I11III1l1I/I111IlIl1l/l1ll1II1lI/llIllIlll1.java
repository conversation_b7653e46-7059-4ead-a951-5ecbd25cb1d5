package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.session.MediaSessionManager$RemoteUserInfo;
import android.os.Build$VERSION;
import android.text.TextUtils;
import android.util.Log;
import androidx.core.location.lI1lI11Ill;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.security.cert.CRLException;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class llIllIlll1 {
    public static final String LEGACY_CONTROLLER = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 71, 80, 17, 18, 11, 93, 94, 79, 126, 80, 87, 95, 86, 38, 11, 92, 67, 17, 88, 8, 14, 0, 16});
    public static final int UNKNOWN_PID = -1;
    public static final int UNKNOWN_UID = -1;
    l1l1I1llII mImpl;

    public llIllIlll1(String str, int i, int i2) {
        if (str == null) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{71, 5, 1, 14, 3, 82, 82, 16, 74, 12, 95, 69, 89, 93, 90, 18, 22, 65, 0, 87, 16, 15, 70, 89, 95}));
        }
        if (TextUtils.isEmpty(str)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{71, 5, 1, 14, 3, 82, 82, 126, 88, 9, 85, 16, 70, 81, 91, 64, 14, 5, 66, 80, 85, 65, 93, 90, 93, 83, 90, 21, 16, 75}));
        }
        if (Build$VERSION.SDK_INT >= 28) {
            this.mImpl = new I11ll1lIl1(str, i, i2);
        } else {
            this.mImpl = new IlIl1l1III(str, i, i2);
        }
    }

    public llIllIlll1(MediaSessionManager$RemoteUserInfo mediaSessionManager$RemoteUserInfo) throws CRLException {
        String packageName = I11ll1lIl1.getPackageName(mediaSessionManager$RemoteUserInfo);
        if (packageName == null) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{71, 5, 1, 14, 3, 82, 82, 16, 74, 12, 95, 69, 89, 93, 90, 18, 22, 65, 0, 87, 16, 15, 70, 89, 95}));
        }
        if (TextUtils.isEmpty(packageName)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{71, 5, 1, 14, 3, 82, 82, 126, 88, 9, 85, 16, 70, 81, 91, 64, 14, 5, 66, 80, 85, 65, 93, 90, 93, 83, 90, 21, 16, 75}));
        }
        this.mImpl = new I11ll1lIl1(mediaSessionManager$RemoteUserInfo);
    }

    public String getPackageName() {
        String packageName = this.mImpl.getPackageName();
        if (!I1I1IIIIl1.l11I11I11l(468631338L)) {
            return packageName;
        }
        Log.e(I1I1lI1II1.a(new byte[]{121, 12, 26, 42, 6, 112, 90, 86, 85, 49, 70, 83, 115, 94, 98, 6, 35, 84, 46, 0, 97, 3, 123, 71, 0, 122}), I1I1lI1II1.a(new byte[]{78, 48, 44, 33, 82, 108, 120, 84}));
        return null;
    }

    public int getPid() {
        int pid = this.mImpl.getPid();
        if (lIlIII1I1l.l11I11I11l(9164)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{109, 5, 27, 52, 51, 7, 0, 89, 83, 6, 84, 66, 90}));
        }
        return pid;
    }

    public int getUid() {
        if (!lI1lI11Ill.IlII1Illll(9277)) {
            return this.mImpl.getUid();
        }
        Log.i(I1I1lI1II1.a(new byte[]{68, 17, 83, 61, 87, 84, 78, 73, 86, 62, 64, 82, 95, 85, 69, 91, 11, 80, 52, 81, Byte.MAX_VALUE, 14, 82, 13, 103}), I1I1lI1II1.a(new byte[]{101, 17, 86, 16, 46, 124, 113, 106, 78, 45, 67, 66, 91, 125, 113, 89, 50, 20, 0, 107, 113, 85, 71, 121, 105}));
        return 0;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof llIllIlll1) {
            return this.mImpl.equals(((llIllIlll1) obj).mImpl);
        }
        return false;
    }

    public int hashCode() {
        return this.mImpl.hashCode();
    }
}
