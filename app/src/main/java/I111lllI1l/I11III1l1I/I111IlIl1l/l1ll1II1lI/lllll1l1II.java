package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.IBinder;
import android.support.v4.graphics.drawable.I111lIl11I;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lllll1l1II implements Runnable {
    final /* synthetic */ lIlII1Illl this$1;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ String val$parentId;

    lllll1l1II(lIlII1Illl lilii1illl, String str, Bundle bundle) {
        this.this$1 = lilii1illl;
        this.val$parentId = str;
        this.val$options = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws CloneNotSupportedException {
        if (I111lIl11I.I1lllI1llI(217464895L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{120, 13, 27, 14, 53}));
        }
        Iterator<IBinder> it = this.this$1.this$0.mConnections.keySet().iterator();
        while (it.hasNext()) {
            this.this$1.notifyChildrenChangedOnHandler(this.this$1.this$0.mConnections.get(it.next()), this.val$parentId, this.val$options);
        }
    }
}
