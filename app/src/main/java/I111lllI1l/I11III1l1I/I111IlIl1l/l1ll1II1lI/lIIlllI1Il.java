package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.content.lll1IIII11;
import android.os.Bundle;
import android.support.v4.os.ResultReceiver;
import android.util.Log;
import java.io.CharConversionException;
import java.net.UnknownServiceException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lIIlllI1Il implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ String val$action;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ Bundle val$extras;
    final /* synthetic */ ResultReceiver val$receiver;

    lIIlllI1Il(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, Bundle bundle, ResultReceiver resultReceiver) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$action = str;
        this.val$extras = bundle;
        this.val$receiver = resultReceiver;
    }

    @Override // java.lang.Runnable
    public void run() throws CharConversionException, UnknownServiceException {
        if (lll1IIII11.III11111Il(351801939L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{99}));
        }
        Il1lII1l1l il1lII1l1l = this.this$1.this$0.mConnections.get(this.val$callbacks.asBinder());
        if (il1lII1l1l == null) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{68, 1, 12, 1, 33, 64, 68, 68, 86, 9, 113, 83, 65, 80, 91, 91, 66, 7, 13, 64, 16, 2, 82, 89, 95, 84, 86, 6, 15, 18, 67, 11, 86, 16, 66, 12, 17, 91, 16, 68, 25, 22, 85, 87, 92, 74, 64, 80, 16, 4, 6, 18, 81, 2, 71, 92, 92, 88, 10}) + this.val$action + I1I1lI1II1.a(new byte[]{27, 68, 7, 29, 22, 71, 86, 67, 4}) + this.val$extras);
        } else {
            this.this$1.this$0.performCustomAction(this.val$action, this.val$extras, il1lII1l1l, this.val$receiver);
        }
    }
}
