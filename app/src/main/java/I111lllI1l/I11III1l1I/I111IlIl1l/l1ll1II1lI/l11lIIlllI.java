package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.RemoteException;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.util.Log;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.io.NotActiveException;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateParsingException;
import java.util.List;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class l11lIIlllI extends IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> {
    final /* synthetic */ lIIIIll1II this$0;
    final /* synthetic */ Il1lII1l1l val$connection;
    final /* synthetic */ Bundle val$notifyChildrenChangedOptions;
    final /* synthetic */ String val$parentId;
    final /* synthetic */ Bundle val$subscribeOptions;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    l11lIIlllI(lIIIIll1II liiiill1ii, Object obj, Il1lII1l1l il1lII1l1l, String str, Bundle bundle, Bundle bundle2) {
        super(obj);
        this.this$0 = liiiill1ii;
        this.val$connection = il1lII1l1l;
        this.val$parentId = str;
        this.val$subscribeOptions = bundle;
        this.val$notifyChildrenChangedOptions = bundle2;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    /* synthetic */ void onResultSent(List<MediaBrowserCompat$MediaItem> list) throws GeneralSecurityException, BrokenBarrierException, NotActiveException {
        if (androidx.constraintlayout.widget.Il1lII1l1l.l11I11I11l(262067256L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{82, 3, 1, 41, 24, 100, 109, 1, 84, 32, 98, 83, 101, 79, 98, 13, 51, 48, 55, 123, 9, 42, 70, 125, 121, 2, 6, 49, 44, 102}));
        }
        onResultSent(list);
        if (android.media.content.Il1llIl111.I111IlIl1I(7118)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{113, 21, 11, 3, 87, 69, 70, 126, 112, 13, 124, 3, 121}));
        }
    }

    void onResultSent(List<MediaBrowserCompat$MediaItem> list) throws CertificateParsingException, BrokenBarrierException, NotActiveException {
        if (androidx.constraintlayout.widget.l111Il1lI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{90, 30, 46, 33, 6, 89, 66, 0, 116}), 171975223L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{97, 13, 45, 15, 82, 118, 111, 96, 112, 42, 5, 125, 108, 87, 90, 122, 85, 13, 32}));
        }
        if (this.this$0.mConnections.get(this.val$connection.callbacks.asBinder()) != this.val$connection) {
            if (lIIIIll1II.DEBUG) {
                Log.d(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{121, 11, 22, 69, 17, 80, 89, 84, 80, 10, 87, 16, 90, 87, 120, 90, 3, 5, 33, 90, 89, 13, 87, 71, 86, 88, 23, 23, 1, 65, 66, 15, 67, 68, 4, 10, 16, 21, 84, 95, 87, 10, 85, 83, 65, 80, 91, 91, 66, 21, 10, 83, 68, 65, 91, 84, 64, 22, 85, 0, 1, 92, 23, 7, 94, 23, 1, 10, 12, 91, 82, 83, 77, 1, 84, 30, 21, 73, 95, 82, 95}) + this.val$connection.pkg + I1I1lI1II1.a(new byte[]{23, 13, 6, 88}) + this.val$parentId);
            }
            if (lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{68, 0, 41, 10, 23, 123, 118, 66, 9, 55}), lIIlI111II.ll1I1lII11)) {
                Log.i(I1I1lI1II1.a(new byte[]{93, 12, 87, 54, 80, 89, 103, 122, 109, 7, 100, 84}), I1I1lI1II1.a(new byte[]{100, 48, 52, 31, 20, 123, 69, 71, 122, 22, 98, 96, 90, 15, 69, 114}));
                return;
            }
            return;
        }
        if ((getFlags() & 1) != 0) {
            list = this.this$0.applyOptions(list, this.val$subscribeOptions);
        }
        try {
            this.val$connection.callbacks.onLoadChildren(this.val$parentId, list, this.val$subscribeOptions, this.val$notifyChildrenChangedOptions);
        } catch (RemoteException unused) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{116, 5, 14, 9, 11, 91, 80, 16, 86, 10, 124, 95, 84, 93, 119, 93, 11, 13, 6, 64, 85, 15, 27, 28, 19, 80, 86, 12, 8, 87, 83, 67, 81, 11, 16, 69, 11, 81, 10}) + this.val$parentId + I1I1lI1II1.a(new byte[]{23, 20, 3, 6, 9, 84, 80, 85, 4}) + this.val$connection.pkg);
        }
    }
}
