package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import Il1lIll1l1.l1IlIllI11.llIllI1l11.lI1lll1l1I.I1II1llI1I;
import android.accounts.utils.Ill11ll111;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Messenger;
import android.support.v4.media.session.MediaSessionCompat$Token;
import android.util.Log;
import androidx.core.location.I11II1l1lI;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.interpolator.view.animation.llIlII1IlI;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.util.List;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lIlII1Illl implements IIIlIl1I1l {
    private Messenger mMessenger;
    final /* synthetic */ lIIIIll1II this$0;

    lIlII1Illl(lIIIIll1II liiiill1ii) {
        this.this$0 = liiiill1ii;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void onCreate() throws CloneNotSupportedException {
        if (ll1l11I1II.l1l1l1IIlI(I1I1lI1II1.a(new byte[]{81, 1, 7, 10, 54, 89, 122, 103, 105, 81, 0, 126, 76, 85, 90, 90, 15, 5, 5, 7, 85}))) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{3, 15, 18}));
        }
        this.mMessenger = new Messenger(this.this$0.mHandler);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public IBinder onBind(Intent intent) throws BrokenBarrierException {
        if (Il11II1llI.I1lllI1llI(388264947L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{85, 38, 39, 49, 22, Byte.MAX_VALUE, 113, 102, 13, 13, 97, 65, 66}));
        }
        if (I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 44, 86, 81, 90, 87, 117, 23, 11, 69, 68, 6, 69, 55, 7, 23, 20, 92, 84, 85}).equals(intent.getAction())) {
            return this.mMessenger.getBinder();
        }
        if (Ill11ll111.I1lIllll1l(179220533L)) {
            throw new NoSuchMethodError(I1I1lI1II1.a(new byte[]{100, 34, 44, 53, 8, 80, 67, 117, 15, 21, 93, 88, 93, 112, 125, 126, 80, 0, 83, 10, 113, 51}));
        }
        return null;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void setSessionToken(MediaSessionCompat$Token mediaSessionCompat$Token) {
        if (llIlII1IlI.l1l1Il1I11(I1I1lI1II1.a(new byte[]{124, 33, 38, 17, 80, 124, 89, 4, 110, 55, Byte.MAX_VALUE, 7, 89, 112}), 173950384L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{14, 38, 16, 2, 90, 80, 4, 103, 64, 12, 8, 115, 82, 105, 110, 126, 17}));
        }
        this.this$0.mHandler.post(new l1111lIII1(this, mediaSessionCompat$Token));
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void notifyChildrenChanged(String str, Bundle bundle) {
        this.this$0.mHandler.post(new lllll1l1II(this, str, bundle));
        if (llIlI11III.l11I11I11l(2127)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{120, 86, 18, 49, 8, 68, 84, 93, 80, 50, 117, 84, 80, 1, 65, 89, 43}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void notifyChildrenChanged(llIllIlll1 llillilll1, String str, Bundle bundle) {
        this.this$0.mHandler.post(new I1llIII1Il(this, llillilll1, str, bundle));
    }

    void notifyChildrenChangedOnHandler(Il1lII1l1l il1lII1l1l, String str, Bundle bundle) {
        if (Ill11ll111.IlII1Illll(265152261L)) {
            throw new ArithmeticException(I1I1lI1II1.a(new byte[]{115, 92, 45, 51, 40, 118, 86, 95, 115, 52, 85, 4, 64}));
        }
        List<I1II1llI1I<IBinder, Bundle>> list = il1lII1l1l.subscriptions.get(str);
        if (list != null) {
            for (I1II1llI1I<IBinder, Bundle> i1II1llI1I : list) {
                if (lIIlI11IlI.hasDuplicatedItems(bundle, i1II1llI1I.second)) {
                    this.this$0.performLoadChildren(str, il1lII1l1l, i1II1llI1I.second, bundle);
                }
            }
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public Bundle getBrowserRootHints() throws NoSuchMethodException {
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 46, 10, 3, 81, 116, 88, 80, 8, 84, 66, 80, 87, 24, 21, 13, 15, 46, 93, 81, 5, 122, 65, 86, 91, 27, 69, 11, 92, 100, 6, 86, 22, 1, 13, 78, 21, 88, 66, 25, 11, 94, 115, 64, 74, 64, 90, 15, 32, 1, 70, 89, 14, 93, 21, 94, 83, 67, 13, 11, 86, 68}));
        }
        Bundle bundle = this.this$0.mCurConnection.rootHints == null ? null : new Bundle(this.this$0.mCurConnection.rootHints);
        if (I11II1l1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{109, 49, 54, 48, 58, 123}), 327108338L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{109, 19, 54, 45, 24, 2, 69, 95, 111, 30, 88, 98, 77, 124, 87, 66, 58, 84, 85, 67, 88, 34, 105, 98, 81, 122, 95, 44, 93}));
        }
        return bundle;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public llIllIlll1 getCurrentBrowserInfo() {
        if (llIlII1IlI.Ill1lIIlIl(7481)) {
            Log.i(I1I1lI1II1.a(new byte[]{85, 14, 86, 3, 81, 120, 114, 114, 92, 0, 96, 71, 65, 0, 120, 118, 0, 3, 26, 68, 70, 25}), I1I1lI1II1.a(new byte[]{95, 62, 18, 28, 50, 77, 89, 5, 82, 39}));
            return null;
        }
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 46, 10, 3, 81, 116, 88, 80, 8, 84, 66, 80, 87, 24, 21, 13, 15, 46, 93, 81, 5, 122, 65, 86, 91, 27, 69, 11, 92, 100, 6, 86, 22, 1, 13, 78, 21, 88, 66, 25, 11, 94, 115, 64, 74, 64, 90, 15, 32, 1, 70, 89, 14, 93, 21, 94, 83, 67, 13, 11, 86, 68}));
        }
        return this.this$0.mCurConnection.browserInfo;
    }
}
