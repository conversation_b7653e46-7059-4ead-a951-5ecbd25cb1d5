package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.VolumeProvider;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.security.AccessControlException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIllIlIl1l extends VolumeProvider {
    final /* synthetic */ l1l1lI1lI1 this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIllIlIl1l(l1l1lI1lI1 l1l1li1li1, int i, int i2, int i3) {
        super(i, i2, i3);
        this.this$0 = l1l1li1li1;
    }

    @Override // android.media.VolumeProvider
    public void onSetVolumeTo(int i) {
        this.this$0.onSetVolumeTo(i);
    }

    @Override // android.media.VolumeProvider
    public void onAdjustVolume(int i) {
        this.this$0.onAdjustVolume(i);
        if (lIlIII1I1l.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{77, 6, 50, 47, 20, 7, 120, 83, 91, 9, 117, 100, 103, 111, 65, 93, 52, 12, 56, 116, 84, 56, 91, 120, 64, 91}), 170821476L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{94, 19, 6, 12, 83, 123, 99, 90, 79, 11, 64, 91, 96, 85, 94}));
        }
    }
}
