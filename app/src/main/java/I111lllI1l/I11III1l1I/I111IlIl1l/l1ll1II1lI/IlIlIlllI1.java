package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lIIIIII11I;
import android.os.Bundle;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.lI1lI11Ill;
import androidx.core.location.llIl1lII1I;
import java.io.CharConversionException;
import java.io.UTFDataFormatException;
import java.security.KeyException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class IlIlIlllI1<T> {
    private final Object mDebug;
    private boolean mDetachCalled;
    private int mFlags;
    private boolean mSendErrorCalled;
    private boolean mSendResultCalled;

    IlIlIlllI1(Object obj) {
        this.mDebug = obj;
    }

    public void sendResult(T t) throws BrokenBarrierException {
        if (this.mSendResultCalled || this.mSendErrorCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{68, 1, 12, 1, 48, 80, 68, 69, 85, 16, 24, 25, 21, 90, 85, 89, 14, 4, 6, 18, 71, 9, 86, 91, 19, 83, 94, 17, 12, 87, 69, 67, 68, 1, 12, 1, 48, 80, 68, 69, 85, 16, 24, 25, 21, 86, 70, 21, 17, 4, 12, 86, 117, 19, 65, 90, 65, 30, 30, 69, 12, 83, 83, 67, 86, 8, 16, 0, 3, 81, 78, 16, 91, 1, 85, 94, 21, 90, 85, 89, 14, 4, 6, 18, 86, 14, 65, 15, 19}) + this.mDebug);
        }
        this.mSendResultCalled = true;
        onResultSent(t);
    }

    public void sendProgressUpdate(Bundle bundle) throws KeyException, UTFDataFormatException {
        if (this.mSendResultCalled || this.mSendErrorCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{68, 1, 12, 1, 50, 71, 88, 87, 75, 1, 67, 67, 96, 73, 80, 84, 22, 4, 74, 27, 16, 2, 82, 89, 95, 83, 83, 69, 19, 90, 82, 13, 23, 1, 11, 17, 10, 80, 69, 16, 74, 1, 94, 84, 103, 92, 71, 64, 14, 21, 74, 27, 16, 14, 65, 21, 64, 83, 89, 1, 33, 64, 69, 12, 69, 76, 75, 69, 10, 84, 83, 16, 88, 8, 66, 85, 84, 93, 77, 21, 0, 4, 7, 92, 16, 2, 82, 89, 95, 83, 83, 69, 2, 93, 69, 89, 23}) + this.mDebug);
        }
        checkExtraFields(bundle);
        onProgressUpdateSent(bundle);
        if (lI1lI11Ill.IlIllIll1I(I1I1lI1II1.a(new byte[]{90}), 779804211L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{98, 38, 40, 29, 10, 123, 68, 71, 96, 85, 117, 106, 112, 117, 87, 101, 51, 84, 50, 10, Byte.MAX_VALUE, 45, 75, 100, 64, 100, 118}));
        }
    }

    public void sendError(Bundle bundle) throws CharConversionException {
        if (lIIlI111II.lI1lIIll11(316501330L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{121, 38, 11, 83, 52, 91, 71, 2, 104, 82, 94, 98, 83, 125, 125, 86, 53, 41, 53, 71, 65, 81, 75, 124, 67, 92, 113, 21, 50, 101}));
        }
        if (this.mSendResultCalled || this.mSendErrorCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{68, 1, 12, 1, 39, 71, 69, 95, 75, 76, 25, 16, 86, 88, 88, 89, 7, 5, 66, 69, 88, 4, 93, 21, 86, 95, 67, 13, 1, 64, 23, 16, 82, 10, 6, 55, 7, 70, 66, 92, 77, 76, 25, 16, 90, 75, 20, 70, 7, 15, 6, 119, 66, 19, 92, 71, 27, 31, 23, 13, 5, 86, 23, 2, 91, 22, 7, 4, 6, 76, 23, 82, 92, 1, 94, 16, 86, 88, 88, 89, 7, 5, 66, 84, 95, 19, 9, 21}) + this.mDebug);
        }
        this.mSendErrorCalled = true;
        onErrorSent(bundle);
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{15, 32, 53, 20, 13, 82, 93, 106, 75}), 285164565L)) {
            throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{71, 15, 35, 87, 18}));
        }
    }

    public void detach() {
        if (this.mDetachCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{83, 1, 22, 4, 1, 93, 31, 25, 25, 7, 81, 92, 89, 92, 80, 21, 21, 9, 7, 92, 16, 5, 86, 65, 82, 85, 95, 77, 77, 18, 95, 2, 83, 68, 3, 9, 16, 80, 86, 84, 64, 68, 82, 85, 80, 87, 20, 86, 3, 13, 14, 87, 84, 65, 85, 90, 65, 12, 23}) + this.mDebug);
        }
        if (this.mSendResultCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{83, 1, 22, 4, 1, 93, 31, 25, 25, 7, 81, 92, 89, 92, 80, 21, 21, 9, 7, 92, 16, 18, 86, 91, 87, 100, 82, 22, 17, 94, 67, 75, 30, 68, 10, 4, 6, 21, 86, 92, 75, 1, 81, 84, 76, 25, 86, 80, 7, 15, 66, 81, 81, 13, 95, 80, 87, 22, 81, 10, 22, 8, 23}) + this.mDebug);
        }
        if (this.mSendErrorCalled) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{83, 1, 22, 4, 1, 93, 31, 25, 25, 7, 81, 92, 89, 92, 80, 21, 21, 9, 7, 92, 16, 18, 86, 91, 87, 115, 69, 23, 11, 64, 31, 74, 23, 12, 3, 1, 66, 84, 91, 66, 92, 5, 84, 73, 21, 91, 81, 80, 12, 65, 1, 83, 92, 13, 86, 81, 19, 80, 88, 23, 94, 18}) + this.mDebug);
        }
        this.mDetachCalled = true;
    }

    boolean isDone() {
        return this.mDetachCalled || this.mSendResultCalled || this.mSendErrorCalled;
    }

    void setFlags(int i) {
        this.mFlags = i;
    }

    int getFlags() {
        if (lIIIIII11I.III11111Il(I1I1lI1II1.a(new byte[]{118, 84, 53, 1, 27, 0, 2, 74, 77, 32, 96, 82, 92, 67, 125, 5, 5, 4, 80, 89, 68, 51, 99, 87, 125, 0, 1, 10, 34, 119}))) {
            throw new NoSuchFieldError(I1I1lI1II1.a(new byte[]{4, 13, 23}));
        }
        return this.mFlags;
    }

    void onResultSent(T t) throws BrokenBarrierException {
        if (llIl1lII1I.IlIIl111lI(I1I1lI1II1.a(new byte[]{70, 51, 38, 18, 12, 86, 7, 0, 116, 43, 6, 93, 94, 91, 6, 108, 19, 11, 18, 83, 113, 89, Byte.MAX_VALUE, 111, 64, 71, 84, 47}), 590355809L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{92, 84, 41, 18, 42, 123, 122, 6, 87, 43, 125, Byte.MAX_VALUE, 5, 65, 124, 118, 42, 24, 10, 75, 68, 56, 96}));
        }
    }

    void onProgressUpdateSent(Bundle bundle) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{126, 16, 66, 12, 17, 21, 89, 95, 77, 68, 67, 69, 69, 73, 91, 71, 22, 4, 6, 18, 68, 14, 19, 70, 86, 88, 83, 69, 5, 92, 23, 10, 89, 16, 7, 23, 11, 88, 23, 69, 73, 0, 81, 68, 80, 25, 82, 90, 16, 65}) + this.mDebug);
    }

    void onErrorSent(Bundle bundle) {
        throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{126, 16, 66, 12, 17, 21, 89, 95, 77, 68, 67, 69, 69, 73, 91, 71, 22, 4, 6, 18, 68, 14, 19, 70, 86, 88, 83, 69, 5, 92, 23, 6, 69, 22, 13, 23, 66, 83, 88, 66, 25}) + this.mDebug);
    }

    private void checkExtraFields(Bundle bundle) throws UTFDataFormatException {
        if (bundle == null) {
            return;
        }
        if (bundle.containsKey(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 33, 43, 101, 121, 47, 120, 37, 38, 58, 50, 103, 120, 119, 107, 33, 99, 99}))) {
            float f = bundle.getFloat(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 84, 1, 84, 89, 84, 23, 86, 71, 13, 22, 17, 87, 30, 4, 75, 65, 65, 87, 25, 33, 43, 101, 121, 47, 120, 37, 38, 58, 50, 103, 120, 119, 107, 33, 99, 99}));
            if (f < -1.0E-5f || f > 1.00001f) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 20, 84, 91, 69, 92, 68, 95, 86, 21, 77, 92, 80, 66, 36, 58, 102, 98, 32, 108, 113, 124, 97, 121, 41, 43, 115, 115, 60, 103, 54, 45, 34, 48, 112, 100, 99, 25, 2, 89, 85, 89, 93, 20, 88, 23, 18, 22, 18, 82, 4, 19, 84, 19, 80, 91, 10, 5, 70, 23, 13, 66, 9, 0, 0, 16, 21, 64, 89, 77, 12, 89, 94, 21, 98, 4, 27, 82, 77, 66, 3, 30, 81, 110}));
            }
        }
        if (IllllI11Il.I1lIllll1l(788200488L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{96, 18, 58, 39, 59, 120, 93, 118, 81, 48, 124, 82, 98, 105, 65, 82, 86, 7, 58, 74, 123, 24, 80, 100, 86, 123, 82}));
        }
    }
}
