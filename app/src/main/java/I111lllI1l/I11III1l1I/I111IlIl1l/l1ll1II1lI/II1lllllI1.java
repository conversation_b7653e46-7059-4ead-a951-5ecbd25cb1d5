package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import android.media.session.MediaSessionManager;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II1lllllI1 extends lIII1IlIlI {
    MediaSessionManager mObject;

    II1lllllI1(Context context) {
        super(context);
        this.mObject = (MediaSessionManager) context.getSystemService(I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 68, 85, 74, 23, 89, 95, 91}));
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.lIII1IlIlI, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.llIIl11lIl, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIIlllI1l
    public boolean isTrustedForMediaControl(l1l1I1llII l1l1i1llii) {
        return super.isTrustedForMediaControl(l1l1i1llii);
    }
}
