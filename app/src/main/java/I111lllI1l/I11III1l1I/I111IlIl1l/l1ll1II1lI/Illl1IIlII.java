package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.support.v4.os.ResultReceiver;
import androidx.core.location.IIlIIlIII1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.security.cert.CertificateEncodingException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class Illl1IIlII extends IlIlIlllI1<MediaBrowserCompat$MediaItem> {
    final /* synthetic */ lIIIIll1II this$0;
    final /* synthetic */ ResultReceiver val$receiver;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    Illl1IIlII(lIIIIll1II liiiill1ii, Object obj, ResultReceiver resultReceiver) {
        super(obj);
        this.this$0 = liiiill1ii;
        this.val$receiver = resultReceiver;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    /* synthetic */ void onResultSent(MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem) throws BrokenBarrierException, CertificateEncodingException {
        if (android.accounts.utils.IIIlIl1I1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{102, 21, 3, 44, 23, 87, 14, 71, 72, 16, 98, 90, 114, 80, 100, 87, 48, 39, 17, 0, 65, 20, 116, 111, 106, 122}), 4012)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{89, 52, 58, 82}));
        }
        onResultSent(mediaBrowserCompat$MediaItem);
    }

    void onResultSent(MediaBrowserCompat$MediaItem mediaBrowserCompat$MediaItem) throws BrokenBarrierException, CertificateEncodingException {
        if (IIlIIlIII1.l11I11I11l(4738)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{15, 28, 46, 29, 87, 123, 122, 121, 116, 47, 94, 95, 98, 88, 113, 82, 15, 25, 48, 90, 89, 86, 65, 82, 3, 94, 84}));
        }
        if ((getFlags() & 2) != 0) {
            this.val$receiver.send(-1, null);
            if (lII1llllI1.l11I11I11l(208831437L)) {
                throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{124, 42, 50, 21, 19, 84, 115, 90, 65, 5, 5, 1, 98, 116, 70, Byte.MAX_VALUE, 52, 27}));
            }
        } else {
            Bundle bundle = new Bundle();
            bundle.putParcelable(I1I1lI1II1.a(new byte[]{90, 1, 6, 12, 3, 106, 94, 68, 92, 9}), mediaBrowserCompat$MediaItem);
            this.val$receiver.send(0, bundle);
        }
    }
}
