package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import android.media.browse.MediaBrowser$MediaItem;
import android.service.media.MediaBrowserService$Result;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class III1Il1II1 extends lI1lIIll11 {
    final /* synthetic */ lIIl1I11II this$1;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    III1Il1II1(lIIl1I11II liil1i11ii, Context context) {
        super(liil1i11ii, context);
        this.this$1 = liil1i11ii;
    }

    @Override // android.service.media.MediaBrowserService
    public void onLoadItem(String str, MediaBrowserService$Result<MediaBrowser$MediaItem> mediaBrowserService$Result) {
        this.this$1.onLoadItem(str, new lIIIIlIIl1<>(mediaBrowserService$Result));
        if (Il1I1lllIl.II1111I11I(I1I1lI1II1.a(new byte[]{111, 40, 37, 87, 84, 0, 99, 104, 76, 18, 116, 69, 112, 114, 88, 108, 21, 9, 19, 3, 73, 57, 101}), 178256786L)) {
            throw new <PERSON>trapMethodError(I1I1lI1II1.a(new byte[]{95, 80, 0, 18, 37, 79, 0, 124, 10, 40, 87, 89, 111, 116, 7, 79, 3, 19, 91, 121, 126, 56, 74, 120}));
        }
    }
}
