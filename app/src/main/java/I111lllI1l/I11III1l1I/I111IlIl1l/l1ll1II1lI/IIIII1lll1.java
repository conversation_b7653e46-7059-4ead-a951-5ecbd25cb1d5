package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Context;
import android.os.Build$VERSION;
import android.util.Log;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.InvalidClassException;
import java.net.NoRouteToHostException;
import java.security.InvalidKeyException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class IIIII1lll1 {
    private static volatile IIIII1lll1 sSessionManager;
    IlIIlllI1l mImpl;
    static final String TAG = I1I1lI1II1.a(new byte[]{122, 1, 6, 12, 3, 102, 82, 67, 74, 13, 95, 94, 120, 88, 90, 84, 5, 4, 16});
    static final boolean DEBUG = Log.isLoggable(I1I1lI1II1.a(new byte[]{122, 1, 6, 12, 3, 102, 82, 67, 74, 13, 95, 94, 120, 88, 90, 84, 5, 4, 16}), 3);
    private static final Object sLock = new Object();

    public static IIIII1lll1 getSessionManager(Context context) throws InvalidKeyException {
        IIIII1lll1 iIIII1lll1;
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{123}), 222843593L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{102, 37, 85, 51, 4, 84, 123, 0, 126, 83, 99, 121, 124, 82, 100, 76, 10, 51, 46, Byte.MAX_VALUE, 120}));
        }
        if (context == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{84, 11, 12, 17, 7, 77, 67, 16, 90, 5, 94, 94, 90, 77, 20, 87, 7, 65, 12, 71, 92, 13}));
        }
        synchronized (sLock) {
            if (sSessionManager == null) {
                sSessionManager = new IIIII1lll1(context.getApplicationContext());
            }
            iIIII1lll1 = sSessionManager;
        }
        return iIIII1lll1;
    }

    private IIIII1lll1(Context context) {
        if (Build$VERSION.SDK_INT >= 28) {
            this.mImpl = new II1lllllI1(context);
        } else {
            this.mImpl = new lIII1IlIlI(context);
        }
    }

    public boolean isTrustedForMediaControl(llIllIlll1 llillilll1) throws NoRouteToHostException {
        if (androidx.versionedparcelable.custom.entities.IIlII1IIIl.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 29, 13, 7, 14, 89, 84, 6, 99, 38, 8, 8, 115, 119, 5, 86, 13, 85, 24, 116, 89, 36, 107, 7}), 505001117L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{84, 23, 56, 52, 37, 111, 94, 5, 112, 60, 67, 122, 87}));
        }
        if (llillilll1 == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{66, 23, 7, 23, 43, 91, 81, 95, 25, 23, 88, 95, 64, 85, 80, 21, 12, 14, 22, 18, 82, 4, 19, 91, 70, 90, 91}));
        }
        boolean zIsTrustedForMediaControl = this.mImpl.isTrustedForMediaControl(llillilll1.mImpl);
        if (llIlI11III.l11I11I11l(6288)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{70, 32, 1, 44, 1, 93}));
        }
        return zIsTrustedForMediaControl;
    }

    Context getContext() throws InvalidClassException {
        Context context = this.mImpl.getContext();
        if (IIIlIll111.IlII1Illll(186983704L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{77, 50, 80, 23}));
        }
        return context;
    }
}
