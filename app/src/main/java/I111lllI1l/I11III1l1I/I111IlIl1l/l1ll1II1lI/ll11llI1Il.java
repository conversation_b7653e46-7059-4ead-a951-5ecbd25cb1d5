package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lI1l1I1l1l;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.util.Log;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.IllIlllIII;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import com.mbridge.msdk.newreward.player.view.floatview.FloatWebTemplateView;
import java.io.SyncFailedException;
import java.io.UTFDataFormatException;
import java.net.PortUnreachableException;
import java.security.cert.CertificateException;
import java.util.Arrays;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class ll11llI1Il implements IIlIl1IIl1 {
    public int mContentType;
    public int mFlags;
    public int mLegacyStream;
    public int mUsage;

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public Object getAudioAttributes() {
        return null;
    }

    public ll11llI1Il() {
        this.mUsage = 0;
        this.mContentType = 0;
        this.mFlags = 0;
        this.mLegacyStream = -1;
    }

    ll11llI1Il(int i, int i2, int i3, int i4) {
        this.mContentType = i;
        this.mFlags = i2;
        this.mUsage = i3;
        this.mLegacyStream = i4;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getVolumeControlStream() throws PortUnreachableException, UTFDataFormatException {
        int volumeStreamType = I1IllIll1l.toVolumeStreamType(true, this.mFlags, this.mUsage);
        if (!Il1I1lllIl.IlIIl111lI(I1I1lI1II1.a(new byte[]{94, 12, 7, 20, 10, 118, 78, 120, 8, 62, 102, 97, 80, 15, Byte.MAX_VALUE, 80, 27, 56, 52, 107, 115, 21, 106, 88, 73, 114}), IronSourceConstants.RV_AD_UNIT_CAPPED)) {
            return volumeStreamType;
        }
        Log.i(I1I1lI1II1.a(new byte[]{66, 47, 17, 41, 3, 77, 92, 82, 124, 87, 4, 5}), I1I1lI1II1.a(new byte[]{116, 18, 36, 14, 83, 120, 70, 85, 75, 81, 81, 104, 83, 90, 70, 76, 10, 85, 35, 0, 92, 27, 101, 7, 103, 70, Byte.MAX_VALUE, 33, 34, 65}));
        return 0;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getLegacyStreamType() throws PortUnreachableException, ReflectiveOperationException, UTFDataFormatException {
        if (Il1I1lllIl.IIl1lIII11(I1I1lI1II1.a(new byte[]{96, 33, 20, 6, 26, 101, 69, 8, 120, 54, 1, 117, 98, 122, 95, 103, 9, 53, 33, 123, 100, 18}), I1I1lI1II1.a(new byte[]{103, 10, 42, 84, 5}))) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{86, 62, 80, 60, 91, 7, 115, 125, 9}));
        }
        int i = this.mLegacyStream;
        if (i != -1) {
            return i;
        }
        int volumeStreamType = I1IllIll1l.toVolumeStreamType(false, this.mFlags, this.mUsage);
        if (l1l1IllI11.III11111Il(570977864L)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{77, 92, 58, 92, 13, 80, 92, 114, 91, 84, 99, 126, 93, 96, 93, 91, 16, 84, 50, 104, 106, 51, 96, 122, 107, 94, 83, 52, 28, 71, 79}));
        }
        return volumeStreamType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getRawLegacyStreamType() throws CloneNotSupportedException {
        if (androidx.constraintlayout.widget.Il1lII1l1l.Il1IIlI1II(5645)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{89, 15, 47, 29, 40}));
        }
        int i = this.mLegacyStream;
        if (IllIlllIII.Il1IIlI1II(1514568006L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{103, 47, 82, 21, 26, 124, 3, 7, 86, 16, 87, 123, 99, 105, 82, 103, 1, 47, 50, 1, 92, 5, 87, 1, Byte.MAX_VALUE, 126}));
        }
        return i;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getContentType() {
        if (I11II1l1lI.l1ll11I11l(I1I1lI1II1.a(new byte[]{111, 5, 42, 20, 44, 111, 79, 74, 124, 37}), 216526853L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{102, 54, 58, 35, 85, 118, 92, 93}));
        }
        return this.mContentType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getUsage() {
        int i = this.mUsage;
        if (llIlI11III.l1l1l1IIlI(240243250L)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{102, 14, 12, 87, 50, Byte.MAX_VALUE, 111, 8, 118, 28, 88, 91, 99}));
        }
        return i;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getFlags() throws PortUnreachableException, ReflectiveOperationException, UTFDataFormatException {
        int i = this.mFlags;
        int legacyStreamType = getLegacyStreamType();
        if (legacyStreamType == 6) {
            i |= 4;
        } else if (legacyStreamType == 7) {
            i |= 1;
        }
        return i & FloatWebTemplateView.FLOAT_MINI_CARD;
    }

    public int hashCode() throws SyncFailedException {
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{122, 42, 36, 84, 38, 125, 114, 121, 72, 38, 91, 90, 116, 124, 67, 70, 22, 32, 44, 103, 93, 36}), 791395895L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{113, 3, 91, 46, 4, 71, 102, 87, 85, 11}));
        }
        int iHashCode = Arrays.hashCode(new Object[]{Integer.valueOf(this.mContentType), Integer.valueOf(this.mFlags), Integer.valueOf(this.mUsage), Integer.valueOf(this.mLegacyStream)});
        if (Ill11ll111.I11II1I1I1(I1I1lI1II1.a(new byte[]{125, 55, 7, 63, 59, 70, 126, 113, 95, 5, 122}))) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{94, 46, 10, 87, 36, 94, 67, 98, 85, 41, 118, 115, 6, 107, 12, 76, 22, 46, 19, 120, 96, 24, 86, 92, 11, 124, 1, 33}));
        }
        return iHashCode;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof ll11llI1Il)) {
            return false;
        }
        ll11llI1Il ll11lli1il = (ll11llI1Il) obj;
        return this.mContentType == ll11lli1il.getContentType() && this.mFlags == ll11lli1il.getFlags() && this.mUsage == ll11lli1il.getUsage() && this.mLegacyStream == ll11lli1il.mLegacyStream;
    }

    public String toString() {
        if (lIIII1l1lI.IlII1Illll(I1I1lI1II1.a(new byte[]{110, 9, 6, 93, 32, 79, 71, 8, 117, 81, 114, 92, 95, 82, 126, 118, 39, 49, 52, 0, 70, 19, 1, 83, 98, 125, 102, 1, 8}))) {
            throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{98, 0, 7, 7, 85, 90, 2, 6, 97, 42, 121, 69, 1, 116, 82, 121, 15, 82}));
        }
        StringBuilder sb = new StringBuilder(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 116, 67, 68, 75, 13, 82, 69, 65, 92, 71, 118, 13, 12, 18, 83, 68, 91}));
        if (this.mLegacyStream != -1) {
            sb.append(I1I1lI1II1.a(new byte[]{23, 23, 22, 23, 7, 84, 90, 13})).append(this.mLegacyStream);
            sb.append(I1I1lI1II1.a(new byte[]{23, 0, 7, 23, 11, 67, 82, 84}));
        }
        sb.append(I1I1lI1II1.a(new byte[]{23, 17, 17, 4, 5, 80, 10})).append(I1IllIll1l.usageToString(this.mUsage)).append(I1I1lI1II1.a(new byte[]{23, 7, 13, 11, 22, 80, 89, 68, 4})).append(this.mContentType).append(I1I1lI1II1.a(new byte[]{23, 2, 14, 4, 5, 70, 10, 0, 65})).append(Integer.toHexString(this.mFlags).toUpperCase());
        String string = sb.toString();
        if (IIlIIlIII1.I1lllI1llI(276546338L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{109, 54, 55, 84, 39, 77, 88, 5, 64, 39, 90, 115, Byte.MAX_VALUE, 93, 95, 98, 91, 56, 46, 85, 96, 25, 114, 91}));
        }
        return string;
    }

    static int usageForStreamType(int i) throws CertificateException {
        if (lI1l1I1l1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{121, 43, 80, 41, 90, 7, 93, 71, 126, 49, 5, 89, 81, 88}))) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{3, 48, 27, 87, 45, 121, 121, 66, 88, 22, 69, 119, 77}));
        }
        switch (i) {
            case 0:
                return 2;
            case 1:
            case 7:
                return 13;
            case 2:
                if (I111lIl11I.I1II1111ll(194605548L)) {
                    throw new NullPointerException(I1I1lI1II1.a(new byte[]{82, 60, 53, 48, 52, 97, 0, 86, 11, 47, 98, 71, 68}));
                }
                return 6;
            case 3:
                return 1;
            case 4:
                return 4;
            case 5:
                return 5;
            case 6:
                if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{4, 10, 90, 13, 10, 1, 120, 115, 88, 37, 88, 5, 123, 96, 93, 3, 48, 35, 13, 89, 82, 10, 84, 5, 98, 76, 5, 13, 30}), 229300298L)) {
                    throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{125, 12, 50, 47, 15, 83, 111, 116}));
                }
                return 2;
            case 8:
                return 3;
            case 9:
            default:
                return 0;
            case 10:
                return 11;
        }
    }
}
