package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lI1l1I1l1l;
import android.media.content.lIIlI111II;
import android.os.Bundle;
import android.support.v4.os.ResultReceiver;
import android.util.Log;
import androidx.core.location.I11II1l1lI;
import java.net.UnknownServiceException;
import java.security.DigestException;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIII1l1lll extends IlIlIlllI1<Bundle> {
    final /* synthetic */ lIIIIll1II this$0;
    final /* synthetic */ ResultReceiver val$receiver;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIII1l1lll(lIIIIll1II liiiill1ii, Object obj, ResultReceiver resultReceiver) {
        super(obj);
        this.this$0 = liiiill1ii;
        this.val$receiver = resultReceiver;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void onResultSent(Bundle bundle) throws CertificateEncodingException {
        if (I11II1l1lI.Ill1lIIlIl(9874)) {
            Log.w(I1I1lI1II1.a(new byte[]{89, 14, 47, 0, 0, 91, 15, 106, 64, 28, 98, 81, 97, 1, 96, 88, 40, 80, 44, 115, 70, 17, 106, 115, 114, 69}), I1I1lI1II1.a(new byte[]{78, 47, 26, 53, 35, 91, 7, 121, 14, 50, 8, 93, 101, 64, 69, 3, 5, 17, 13, 84, 81}));
        } else {
            this.val$receiver.send(0, bundle);
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    void onProgressUpdateSent(Bundle bundle) throws UnknownServiceException, CertificateEncodingException {
        this.val$receiver.send(1, bundle);
        if (lI1l1I1l1l.I111IlIl1I(I1I1lI1II1.a(new byte[]{14, 54, 22, 19, 24, 86, 96}))) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{99, 85, 22, 31, 33, 115, 116, 94, 107, 47, 70, 91, 114, 87, 80, 69, 22, 8, 42, 91, 0, 55, 119, 93, 89, 3, 90, 54, 49, 118}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    void onErrorSent(Bundle bundle) throws DigestException, CertificateEncodingException {
        if (lIIlI111II.lI1lIIll11(570236575L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{115, 20, 9, 8, 4, 100, 68, 101, 67, 33, 66, 114, 76, 91, 77, 98, 48}));
        }
        this.val$receiver.send(-1, bundle);
        if (I11II1l1lI.I1lllI1llI(328453708L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{110, 44, 26, 61, 56, 7, 0, 123, 95, 18, 99, 5, 93, 96}));
        }
    }
}
