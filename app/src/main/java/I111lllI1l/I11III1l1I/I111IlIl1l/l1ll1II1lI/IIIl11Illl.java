package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioAttributes;
import android.media.AudioAttributes$Builder;
import android.media.content.lIIlI111II;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.core.location.I1111IIl11;
import androidx.core.location.IIlIIlIII1;
import androidx.interpolator.view.animation.Il11II1llI;
import java.io.InvalidObjectException;
import java.io.SyncFailedException;
import java.security.InvalidKeyException;
import java.security.NoSuchProviderException;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIIl11Illl implements Il111lI1Il {
    final AudioAttributes$Builder mFwkBuilder;

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setFlags(int i) throws InstantiationException, InvalidKeyException, NoSuchProviderException {
        if (I1111IIl11.llII1lIIlI(I1I1lI1II1.a(new byte[]{122, 49, 3, 14, 17, 109, 89, 83, 117, 38, 123, 66, 97, 72, 3, 83, 81, 8, 41, 91, 3, 2, 85, 124, 7, 92, 3, 17, 41, 106, 6, 23}))) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{96, 60, 33, 84, 21, 119, 115, 105}));
        }
        IIIl11Illl flags = setFlags(i);
        if (androidx.constraintlayout.widget.l111Il1lI1.IlIllIll1I(415538999L)) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{67, 44, 82, 11}));
        }
        return flags;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setLegacyStreamType(int i) throws UnrecoverableEntryException {
        if (androidx.constraintlayout.widget.Il1lII1l1l.I111IlIl1I(4000)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{89, 47, 45, 22, 82, 96, 95, 92, 80, 62, 117, 104, 124, 97, 71, 100, 35, 4, 32, 96, 121, 11}));
        }
        return setLegacyStreamType(i);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public /* synthetic */ Il111lI1Il setUsage(int i) throws InstantiationException {
        if (IllllI11Il.IlIllIll1I(1399919432L)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{103}));
        }
        return setUsage(i);
    }

    IIIl11Illl() {
        this.mFwkBuilder = new AudioAttributes$Builder();
    }

    IIIl11Illl(Object obj) {
        this.mFwkBuilder = new AudioAttributes$Builder((AudioAttributes) obj);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIlIl1IIl1 build() throws InvalidObjectException {
        if (IIlIIlIII1.I1lllI1llI(235005201L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{123, 42, 18, 44, 37}));
        }
        return new IIll1l1111(this.mFwkBuilder.build());
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIIl11Illl setUsage(int i) throws SyncFailedException {
        if (lIIlI111II.lIl11IlI1l(9858)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{78, 51}));
        }
        if (i == 16) {
            i = 12;
        }
        this.mFwkBuilder.setUsage(i);
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIIl11Illl setContentType(int i) throws InterruptedException {
        this.mFwkBuilder.setContentType(i);
        if (Il11II1llI.l1l1l1IIlI(4477)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{1}));
        }
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIIl11Illl setFlags(int i) throws InstantiationException {
        this.mFwkBuilder.setFlags(i);
        if (android.support.v4.graphics.drawable.lIIlI111II.IIlI1Il1lI(2284)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{98, 53, 56, 19, 51, 112, 77, 123, 14, 10, 65, 2, 4, 97, 99, 3}));
        }
        return this;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il111lI1Il
    public IIIl11Illl setLegacyStreamType(int i) {
        this.mFwkBuilder.setLegacyStreamType(i);
        return this;
    }
}
