package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import android.util.Log;
import androidx.core.location.IllIlllIII;
import androidx.versionedparcelable.IIII1IIl1I;
import java.lang.reflect.InvocationTargetException;
import java.net.MalformedURLException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class IIl1l1IllI {
    public static I1IllIll1l read(IIII1IIl1I iIII1IIl1I) {
        if (IllIlllIII.IlII1Illll(658014564L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{116, 81, 16, 21, 54, 125, Byte.MAX_VALUE, 98, 99, 54, 73, 64, 96, 13, 92, 120, 22, 87, 24}));
        }
        I1IllIll1l i1IllIll1l = new I1IllIll1l();
        i1IllIll1l.mImpl = (IIlIl1IIl1) iIII1IIl1I.readVersionedParcelable(i1IllIll1l.mImpl, 1);
        if (!Ill11ll111.l111IIlII1(I1I1lI1II1.a(new byte[]{110, 22, 8, 38, 85, 118, 123, 123, 110, 85, 6, 101, 80}), I1I1lI1II1.a(new byte[]{99, 5, 49, 47, 8, Byte.MAX_VALUE, 122, 115, 86, 22, 117, 89, 83, 82, 66, 66, 17, 49, 53, 121, 94, 57, 80, 89, 113, 70, 77, 49}))) {
            return i1IllIll1l;
        }
        Log.d(I1I1lI1II1.a(new byte[]{71, 45, 48, 43, 15, 1, 6, 82, 10, 2, 4}), I1I1lI1II1.a(new byte[]{2, 92, 5, 19, 43, 92, 68, 91, 90, 34, 102}));
        return null;
    }

    public static void write(I1IllIll1l i1IllIll1l, IIII1IIl1I iIII1IIl1I) throws IllegalAccessException, MalformedURLException, IllegalArgumentException, InvocationTargetException {
        iIII1IIl1I.setSerializationFlags(false, false);
        iIII1IIl1I.writeVersionedParcelable(i1IllIll1l.mImpl, 1);
    }
}
