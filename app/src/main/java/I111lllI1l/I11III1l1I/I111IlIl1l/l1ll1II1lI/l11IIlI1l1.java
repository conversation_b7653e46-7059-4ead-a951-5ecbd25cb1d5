package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.IBinder;
import android.util.Log;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class l11IIlI1l1 implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ String val$id;
    final /* synthetic */ IBinder val$token;

    l11IIlI1l1(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, IBinder iBinder) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$id = str;
        this.val$token = iBinder;
    }

    @Override // java.lang.Runnable
    public void run() {
        Il1lII1l1l il1lII1l1l = this.this$1.this$0.mConnections.get(this.val$callbacks.asBinder());
        if (il1lII1l1l == null) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{69, 1, 15, 10, 20, 80, 100, 69, 91, 23, 83, 66, 92, 73, 64, 92, 13, 15, 66, 84, 95, 19, 19, 86, 82, 90, 91, 7, 5, 81, 92, 67, 67, 12, 3, 17, 66, 92, 68, 94, 30, 16, 16, 66, 80, 94, 93, 70, 22, 4, 16, 87, 84, 65, 90, 81, 14}) + this.val$id);
        } else {
            if (this.this$1.this$0.removeSubscription(this.val$id, il1lII1l1l, this.val$token)) {
                return;
            }
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{69, 1, 15, 10, 20, 80, 100, 69, 91, 23, 83, 66, 92, 73, 64, 92, 13, 15, 66, 81, 81, 13, 95, 80, 87, 22, 81, 10, 22, 18}) + this.val$id + I1I1lI1II1.a(new byte[]{23, 19, 10, 12, 1, 93, 23, 89, 74, 68, 94, 95, 65, 25, 71, 64, 0, 18, 1, 64, 89, 3, 86, 81}));
        }
    }
}
