package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.media.session.MediaSessionCompat$Token;
import androidx.constraintlayout.widget.lIIlI111II;
import java.security.NoSuchAlgorithmException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IlIII1l1ll implements Runnable {
    final /* synthetic */ Il1llllI1I this$1;
    final /* synthetic */ MediaSessionCompat$Token val$token;

    IlIII1l1ll(Il1llllI1I il1llllI1I, MediaSessionCompat$Token mediaSessionCompat$Token) {
        this.this$1 = il1llllI1I;
        this.val$token = mediaSessionCompat$Token;
    }

    @Override // java.lang.Runnable
    public void run() throws InterruptedException, NoSuchAlgorithmException {
        this.this$1.setSessionTokenOnHandler(this.val$token);
        if (lIIlI111II.I11II1111l(231689158L)) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{115, 52, 58, 36, 52, 84, 1, 71, 124, 87, 74, 122, 96, 124, 102, 4, 58, 10}));
        }
    }
}
