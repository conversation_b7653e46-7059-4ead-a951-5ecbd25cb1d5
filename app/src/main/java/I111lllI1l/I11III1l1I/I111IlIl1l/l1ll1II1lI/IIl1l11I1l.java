package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IIl1l11I1l extends lI1IIIl1I1 {
    final /* synthetic */ lIIIIll1II this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIl1l11I1l(lIIIIll1II liiiill1ii) {
        super(liiiill1ii);
        this.this$0 = liiiill1ii;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il1llllI1I, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public llIllIlll1 getCurrentBrowserInfo() {
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 37, 0, 22, 103, 88, 95, 77, 72, 16, 95, 91, 117, 91, 84, 6, 34, 10, 91, 92, 5, 65, 80, 93, 26, 23, 10, 10, 126, 88, 2, 83, 45, 22, 0, 15, 25, 23, 95, 87, 55, 85, 81, 71, 90, 92, 25, 66, 14, 16, 18, 95, 15, 112, 64, 64, 66, 88, 8, 37, 81, 67, 10, 88, 10, 66, 8, 7, 65, 95, 95, 93, 23}));
        }
        if (this.this$0.mCurConnection == this.this$0.mConnectionFromFwk) {
            return new llIllIlll1(this.mServiceFwk.getCurrentBrowserInfo());
        }
        llIllIlll1 llillilll1 = this.this$0.mCurConnection.browserInfo;
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{0, 62, 23, 36, 24, 122, Byte.MAX_VALUE, 126, 122, 16, 99, 119, 115, 120, 3}), 653851465L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{101, 80, 50, 31, 87, 119, 113, 96, 110, 12, 70}));
        }
        return llillilll1;
    }
}
