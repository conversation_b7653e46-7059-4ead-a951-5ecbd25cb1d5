package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.IBinder;
import java.io.UTFDataFormatException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class ll1I1l1l1l implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;

    ll1I1l1l1l(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
    }

    @Override // java.lang.Runnable
    public void run() throws UTFDataFormatException {
        IBinder iBinderAsBinder = this.val$callbacks.asBinder();
        Il1lII1l1l il1lII1l1lRemove = this.this$1.this$0.mConnections.remove(iBinderAsBinder);
        if (il1lII1l1lRemove != null) {
            iBinderAsBinder.unlinkToDeath(il1lII1l1lRemove, 0);
        }
        if (android.media.content.Il1llIl111.I1lllI1llI(4251)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{5, 30, 35, 13, 14, 102, 2, 64, 86, 20, 73, 91, 77, 13, 2, 84, 21, 86}));
        }
    }
}
