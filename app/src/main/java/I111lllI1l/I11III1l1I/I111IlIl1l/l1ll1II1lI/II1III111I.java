package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.Parcelable;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.support.v4.os.ResultReceiver;
import androidx.interpolator.view.animation.Il11II1llI;
import java.security.cert.CertificateEncodingException;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II1III111I extends IlIlIlllI1<List<MediaBrowserCompat$MediaItem>> {
    final /* synthetic */ lIIIIll1II this$0;
    final /* synthetic */ ResultReceiver val$receiver;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    II1III111I(lIIIIll1II liiiill1ii, Object obj, ResultReceiver resultReceiver) {
        super(obj);
        this.this$0 = liiiill1ii;
        this.val$receiver = resultReceiver;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IlIlIlllI1
    public void onResultSent(List<MediaBrowserCompat$MediaItem> list) throws CertificateEncodingException {
        if ((getFlags() & 4) != 0 || list == null) {
            this.val$receiver.send(-1, null);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putParcelableArray(I1I1lI1II1.a(new byte[]{68, 1, 3, 23, 1, 93, 104, 66, 92, 23, 69, 92, 65, 74}), (Parcelable[]) list.toArray(new MediaBrowserCompat$MediaItem[0]));
        this.val$receiver.send(0, bundle);
        if (Il11II1llI.l1l1l1IIlI(8798)) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 2, 49, 47, 5, 92, 114, 122, 122, 37, 89, 86, 7, 115, 108, 76, 24, 46, 56, 80, 69, 19, 105, 112, 102, 65, 15, 92, 8, 66, 85}));
        }
    }
}
