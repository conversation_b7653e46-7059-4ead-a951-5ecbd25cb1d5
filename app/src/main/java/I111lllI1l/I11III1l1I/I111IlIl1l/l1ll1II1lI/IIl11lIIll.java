package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lIIIIII11I;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.support.v4.media.MediaBrowserCompat$MediaItem;
import android.support.v4.media.session.MediaSessionCompat$Token;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.io.CharConversionException;
import java.net.NoRouteToHostException;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathBuilderException;
import java.util.ArrayList;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class IIl11lIIll implements IIll1IIlII {
    final Messenger mCallbacks;

    IIl11lIIll(Messenger messenger) {
        this.mCallbacks = messenger;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIll1IIlII
    public IBinder asBinder() {
        if (Ill11ll111.l1Il11I1Il(I1I1lI1II1.a(new byte[]{115, 5, 47, 10, 39, 1, 124, 88, 67, 22, 93, 83, 94, 9, 101, 126, 11, 87, 50, 3, 91, 34, 126, 71}), 283279407L)) {
            throw new UnsatisfiedLinkError(I1I1lI1II1.a(new byte[]{111}));
        }
        return this.mCallbacks.getBinder();
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIll1IIlII
    public void onConnect(String str, MediaSessionCompat$Token mediaSessionCompat$Token, Bundle bundle) throws CertPathBuilderException, RemoteException {
        if (lIIIIII11I.lll1111l11(I1I1lI1II1.a(new byte[]{7, 23, 56, 36, 12, 115, 64, 116, 124, 13, 93, 66, 64, 124, 112, 7, 20, 37, 17, 122, 103, 18, Byte.MAX_VALUE, 122, 86, 108, 81, 35, 12, 80, 2}), 261793903L)) {
            throw new CertPathBuilderException(I1I1lI1II1.a(new byte[]{116, 41, 0, 21, 45, 6, 96, 65, 8, 60, 88, 65, 67, 92, 124, 5, 59, 51, 91}));
        }
        if (bundle == null) {
            bundle = new Bundle();
        }
        bundle.putInt(I1I1lI1II1.a(new byte[]{82, 28, 22, 23, 3, 106, 68, 85, 75, 18, 89, 83, 80, 102, 66, 80, 16, 18, 11, 93, 94}), 2);
        Bundle bundle2 = new Bundle();
        bundle2.putString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5}), str);
        bundle2.putParcelable(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 67, 80, 74, 71, 92, 13, 15, 61, 70, 95, 10, 86, 91}), mediaSessionCompat$Token);
        bundle2.putBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 88, 95, 77, 59, 88, 89, 91, 77, 71}), bundle);
        sendRequest(1, bundle2);
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIll1IIlII
    public void onConnectFailed() throws RemoteException, NoSuchProviderException, NoRouteToHostException {
        if (android.accounts.utils.IIIlIl1I1l.l11I11I11l(1605940482L)) {
            throw new NoRouteToHostException(I1I1lI1II1.a(new byte[]{5, 48, 13, 55, 81, 89, 121, 114, 64, 14}));
        }
        sendRequest(2, null);
        if (androidx.constraintlayout.widget.I1IllIll1l.III11111Il(I1I1lI1II1.a(new byte[]{95, 47, 4, 83, 24}))) {
            throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{7, 9, 53, 22, 84, 83, 112, 105, 86, 60, 124, 72, 93, 95, 96, 114, 3, 87, 22, 5, 70}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIll1IIlII
    public void onLoadChildren(String str, List<MediaBrowserCompat$MediaItem> list, Bundle bundle, Bundle bundle2) throws CharConversionException, RemoteException {
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.lll1111l11(I1I1lI1II1.a(new byte[]{101, 40, 42, 4, 13, 87, 77, 4, 72, 80, 117, 121, 76, 11, 117, 12, 5, 13, 15, 83, 116, 14, 89, 69, 112, 6, 4, 31, 0, 84, 86, 26}), 215782226L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{68, 80, 55, 32, 39, 124, 66, 116, 10, 0, 90, 122, 1, 83, 91, 4, 1, 84, 4, 113, 88}));
        }
        Bundle bundle3 = new Bundle();
        bundle3.putString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5}), str);
        bundle3.putBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 90, 71, 68, 80, 11, 94, 67}), bundle);
        bundle3.putBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 91, 88, 68, 80, 2, 73, 111, 86, 81, 93, 89, 6, 19, 7, 92, 111, 2, 91, 84, 93, 81, 82, 1, 59, 93, 71, 23, 94, 11, 12, 22}), bundle2);
        if (list != null) {
            bundle3.putParcelableArrayList(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 14, 8, 17, 70}), list instanceof ArrayList ? (ArrayList) list : new ArrayList<>(list));
        }
        sendRequest(3, bundle3);
        if (l11Il1lI11.l11I11I11l(I1I1lI1II1.a(new byte[]{0, 60, 37, 49, 91, 82, 83, 7, 122, 86, 102}))) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{68, 21, 46, 21, 44, 69, 97, 5, 110, 13, 73, 91, 108, 76, 67, 0, 5, 13, 12, 99, 88, 46, 70, 123, 71, 121, 78, 3, 16, 121, 7}));
        }
    }

    private void sendRequest(int i, Bundle bundle) throws RemoteException {
        Message messageObtain = Message.obtain();
        messageObtain.what = i;
        messageObtain.arg1 = 2;
        messageObtain.setData(bundle);
        this.mCallbacks.send(messageObtain);
    }
}
