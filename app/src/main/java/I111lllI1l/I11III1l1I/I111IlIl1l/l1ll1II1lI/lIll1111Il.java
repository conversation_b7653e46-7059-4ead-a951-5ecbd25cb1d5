package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioAttributes;
import androidx.versionedparcelable.IIII1IIl1I;

/* loaded from: classes.dex */
public class lIll1111Il {
    public static I1II111III read(IIII1IIl1I iIII1IIl1I) {
        I1II111III i1ii111iii = new I1II111III();
        i1ii111iii.mAudioAttributes = (AudioAttributes) iIII1IIl1I.readParcelable(i1ii111iii.mAudioAttributes, 1);
        i1ii111iii.mLegacyStreamType = iIII1IIl1I.readInt(i1ii111iii.mLegacyStreamType, 2);
        return i1ii111iii;
    }

    public static void write(I1II111III i1ii111iii, IIII1IIl1I iIII1IIl1I) {
        iIII1IIl1I.setSerializationFlags(false, false);
        iIII1IIl1I.writeParcelable(i1ii111iii.mAudioAttributes, 1);
        iIII1IIl1I.writeInt(i1ii111iii.mLegacyStreamType, 2);
    }
}
