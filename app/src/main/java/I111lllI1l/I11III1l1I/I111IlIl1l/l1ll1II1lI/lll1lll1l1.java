package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioManager$OnAudioFocusChangeListener;
import android.os.Handler;
import android.os.Handler$Callback;
import android.os.Message;
import java.security.cert.CertificateNotYetValidException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public class lll1lll1l1 implements AudioManager$OnAudioFocusChangeListener, Handler$Callback {
    private static final int FOCUS_CHANGE = 2782386;
    private final Handler mHandler;
    private final AudioManager$OnAudioFocusChangeListener mListener;

    lll1lll1l1(AudioManager$OnAudioFocusChangeListener audioManager$OnAudioFocusChangeListener, Handler handler) {
        this.mListener = audioManager$OnAudioFocusChangeListener;
        this.mHandler = new Handler(handler.getLooper(), this);
    }

    @Override // android.media.AudioManager$OnAudioFocusChangeListener
    public void onAudioFocusChange(int i) {
        Handler handler = this.mHandler;
        handler.sendMessage(Message.obtain(handler, FOCUS_CHANGE, i, 0));
    }

    @Override // android.os.Handler$Callback
    public boolean handleMessage(Message message) throws CertificateNotYetValidException {
        if (message.what == FOCUS_CHANGE) {
            this.mListener.onAudioFocusChange(message.arg1);
            return true;
        }
        if (android.media.content.Il1llIl111.l1l1l1IIlI(1306244526L)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{2, 49, 32, 41, 81, 98, 123, 98, 10, 83, 66, 124, 67, 123, 109, 4, 45, 37, 16, 5, 73, 21, 81, 121}));
        }
        return false;
    }
}
