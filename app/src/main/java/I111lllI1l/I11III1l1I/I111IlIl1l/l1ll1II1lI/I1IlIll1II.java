package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import android.media.AudioManager$OnAudioFocusChangeListener;
import android.os.Handler;
import android.os.Looper;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import java.io.InterruptedIOException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1IlIll1II {
    private I1IllIll1l mAudioAttributesCompat;
    private Handler mFocusChangeHandler;
    private int mFocusGain;
    private AudioManager$OnAudioFocusChangeListener mOnAudioFocusChangeListener;
    private boolean mPauseOnDuck;

    private static boolean isValidFocusGain(int i) {
        if (!IlIIlI11I1.IlII1Illll(440215206L)) {
            return i == 1 || i == 2 || i == 3 || i == 4;
        }
        Log.e(I1I1lI1II1.a(new byte[]{65, 93, 17, 16, 85, 7, 122, 83, 88, 54, 86, 122, 81, 97, 96, 81, 20, 15, 37, 117, 3, 57, 94, 84, 87, 66}), I1I1lI1II1.a(new byte[]{98, 44, 3, 13, 35, 112, 100, 89, 87, 47, 115}));
        return false;
    }

    public I1IlIll1II(int i) {
        this.mAudioAttributesCompat = ll1ll1IlIl.FOCUS_DEFAULT_ATTR;
        setFocusGain(i);
    }

    public I1IlIll1II(ll1ll1IlIl ll1ll1ilil) {
        this.mAudioAttributesCompat = ll1ll1IlIl.FOCUS_DEFAULT_ATTR;
        if (ll1ll1ilil == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 115, 88, 83, 76, 23, 98, 85, 68, 76, 81, 70, 22, 34, 13, 95, 64, 0, 71, 21, 71, 89, 23, 6, 11, 66, 78, 67, 90, 17, 17, 17, 66, 91, 88, 68, 25, 6, 85, 16, 91, 76, 88, 89}));
        }
        this.mFocusGain = ll1ll1ilil.getFocusGain();
        this.mOnAudioFocusChangeListener = ll1ll1ilil.getOnAudioFocusChangeListener();
        this.mFocusChangeHandler = ll1ll1ilil.getFocusChangeHandler();
        this.mAudioAttributesCompat = ll1ll1ilil.getAudioAttributesCompat();
        this.mPauseOnDuck = ll1ll1ilil.willPauseWhenDucked();
    }

    public I1IlIll1II setFocusGain(int i) {
        if (!isValidFocusGain(i)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{126, 8, 14, 0, 5, 84, 91, 16, 88, 17, 84, 89, 90, 25, 82, 90, 1, 20, 17, 18, 87, 0, 90, 91, 19, 66, 78, 21, 1, 18}) + i);
        }
        this.mFocusGain = i;
        if (I111lIl11I.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{101, 42, 5, 34, 84, 1, 103, 93, 74, 21, Byte.MAX_VALUE, 4, 96}), 199353101L)) {
            throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{64, 53, 9}));
        }
        return this;
    }

    public I1IlIll1II setOnAudioFocusChangeListener(AudioManager$OnAudioFocusChangeListener audioManager$OnAudioFocusChangeListener) {
        return setOnAudioFocusChangeListener(audioManager$OnAudioFocusChangeListener, new Handler(Looper.getMainLooper()));
    }

    public I1IlIll1II setOnAudioFocusChangeListener(AudioManager$OnAudioFocusChangeListener audioManager$OnAudioFocusChangeListener, Handler handler) {
        if (audioManager$OnAudioFocusChangeListener == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{120, 10, 35, 16, 6, 92, 88, 118, 86, 7, 69, 67, 118, 81, 85, 91, 5, 4, 46, 91, 67, 21, 86, 91, 86, 68, 23, 8, 17, 65, 67, 67, 89, 11, 22, 69, 0, 80, 23, 94, 76, 8, 92}));
        }
        if (handler == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 5, 12, 1, 14, 80, 69, 16, 84, 17, 67, 68, 21, 87, 91, 65, 66, 3, 7, 18, 94, 20, 95, 89}));
        }
        this.mOnAudioFocusChangeListener = audioManager$OnAudioFocusChangeListener;
        this.mFocusChangeHandler = handler;
        return this;
    }

    public I1IlIll1II setAudioAttributes(I1IllIll1l i1IllIll1l) {
        if (i1IllIll1l == null) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{126, 8, 14, 0, 5, 84, 91, 16, 87, 17, 92, 92, 21, 120, 65, 81, 11, 14, 35, 70, 68, 19, 90, 87, 70, 66, 82, 22}));
        }
        this.mAudioAttributesCompat = i1IllIll1l;
        return this;
    }

    public I1IlIll1II setWillPauseWhenDucked(boolean z) {
        this.mPauseOnDuck = z;
        return this;
    }

    public ll1ll1IlIl build() throws InterruptedIOException {
        if (Ill11ll111.l11I11I11l(236810741L)) {
            throw new InterruptedIOException(I1I1lI1II1.a(new byte[]{4, 8, 44, 49, 51, 98, 118, 87, 104, 10, 98, 95, 96, 119, 120, 99, 53, 19, 4, 120, 84, 7, 74, 76}));
        }
        if (this.mOnAudioFocusChangeListener != null) {
            return new ll1ll1IlIl(this.mFocusGain, this.mOnAudioFocusChangeListener, this.mFocusChangeHandler, this.mAudioAttributesCompat, this.mPauseOnDuck);
        }
        throw new IllegalStateException(I1I1lI1II1.a(new byte[]{116, 5, 12, 66, 22, 21, 85, 69, 80, 8, 84, 16, 84, 87, 20, 116, 23, 5, 11, 93, 118, 14, 80, 64, 64, 100, 82, 20, 17, 87, 68, 23, 116, 11, 15, 21, 3, 65, 23, 89, 87, 23, 68, 81, 91, 90, 81, 21, 21, 8, 22, 90, 95, 20, 71, 21, 82, 22, 91, 12, 23, 70, 82, 13, 82, 22}));
    }
}
