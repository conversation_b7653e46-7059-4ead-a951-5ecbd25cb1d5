package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.AudioAttributes;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.versionedparcelable.IIII1IIl1I;
import java.util.concurrent.CancellationException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class Il1llIl111 {
    public static IIll1l1111 read(IIII1IIl1I iIII1IIl1I) {
        IIll1l1111 iIll1l1111 = new IIll1l1111();
        iIll1l1111.mAudioAttributes = (AudioAttributes) iIII1IIl1I.readParcelable(iIll1l1111.mAudioAttributes, 1);
        iIll1l1111.mLegacyStreamType = iIII1IIl1I.readInt(iIll1l1111.mLegacyStreamType, 2);
        return iIll1l1111;
    }

    public static void write(IIll1l1111 iIll1l1111, IIII1IIl1I iIII1IIl1I) {
        iIII1IIl1I.setSerializationFlags(false, false);
        iIII1IIl1I.writeParcelable(iIll1l1111.mAudioAttributes, 1);
        iIII1IIl1I.writeInt(iIll1l1111.mLegacyStreamType, 2);
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{88, 15, 17, 17, 80, 87, 109, 123, 72, 61, 95, 92, 71, 64, 70, 121, 85, 0, 11, Byte.MAX_VALUE, 104, 84, 91, 94}), 6990)) {
            throw new CancellationException(I1I1lI1II1.a(new byte[]{120, 13, 6, 47, 0, 93, 5, 102, 85, 40, 119, 101, Byte.MAX_VALUE, 82, 12, 3, 7, 48, 43, 120, 106, 0, 123, 101, 98, 65, 112, 80, 0, 92, 70}));
        }
    }
}
