package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.I1lllI11II;
import android.os.Bundle;
import android.os.Parcel;
import android.support.v4.graphics.drawable.l11Il111ll;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.io.UTFDataFormatException;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lI1IIIl1I1 extends lIIl1I11II {
    final /* synthetic */ lIIIIll1II this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lI1IIIl1I1(lIIIIll1II liiiill1ii) {
        super(liiiill1ii);
        this.this$0 = liiiill1ii;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.lIIl1I11II, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il1llllI1I, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void onCreate() {
        this.mServiceFwk = new I11lII1Il1(this, this.this$0);
        this.mServiceFwk.onCreate();
    }

    public void onLoadChildren(String str, lIIIIlIIl1<List<Parcel>> liiiiliil1, Bundle bundle) throws InterruptedException, InstantiationException {
        if (l11Il1lI11.IlIllIll1I(I1I1lI1II1.a(new byte[]{114, 81, 33, 6, 3, 0, 5, 3, 78, 7, 66, 117, 121, 114, 118}), 6341)) {
            throw new InstantiationException(I1I1lI1II1.a(new byte[]{95, 19, 5, 4, 12, 111}));
        }
        llIIlI1llI lliili1lli = new llIIlI1llI(this, str, liiiiliil1, bundle);
        lIIIIll1II liiiill1ii = this.this$0;
        liiiill1ii.mCurConnection = liiiill1ii.mConnectionFromFwk;
        this.this$0.onLoadChildren(str, lliili1lli, bundle);
        this.this$0.mCurConnection = null;
        if (androidx.recyclerview.widget.content.adapter.II1lllllI1.l1ll11I11l(I1I1lI1II1.a(new byte[]{117, 84, 59, 36, 46, 7, 93, 87, 120, 8, 87, 1, 99, 111, 94, 112, 44, 82, 15}), 328884104L)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{85, 49, 80, 40, 7, 2, 103, 126, 84, 7, 119, 95, 89, 96, 7, 114, 58, 12}));
        }
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il1llllI1I, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public Bundle getBrowserRootHints() {
        if (this.this$0.mCurConnection == null) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{99, 12, 11, 22, 66, 70, 95, 95, 76, 8, 84, 16, 87, 92, 20, 86, 3, 13, 14, 87, 84, 65, 90, 91, 64, 95, 83, 0, 68, 93, 81, 67, 88, 10, 37, 0, 22, 103, 88, 95, 77, 72, 16, 95, 91, 117, 91, 84, 6, 34, 10, 91, 92, 5, 65, 80, 93, 26, 23, 10, 10, 126, 88, 2, 83, 45, 22, 0, 15, 25, 23, 95, 87, 55, 85, 81, 71, 90, 92, 25, 66, 14, 16, 18, 95, 15, 112, 64, 64, 66, 88, 8, 37, 81, 67, 10, 88, 10, 66, 8, 7, 65, 95, 95, 93, 23}));
        }
        if (this.this$0.mCurConnection == this.this$0.mConnectionFromFwk) {
            return this.mServiceFwk.getBrowserRootHints();
        }
        Bundle bundle = this.this$0.mCurConnection.rootHints == null ? null : new Bundle(this.this$0.mCurConnection.rootHints);
        if (l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{123, 40, 7, 43, 46, 4, 97, 92, Byte.MAX_VALUE, 48, 126, 88, Byte.MAX_VALUE, 77}), 4282)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{85, 87}));
        }
        return bundle;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il1llllI1I
    void notifyChildrenChangedForFramework(String str, Bundle bundle) throws UTFDataFormatException {
        if (bundle != null) {
            this.mServiceFwk.notifyChildrenChanged(str, bundle);
        } else {
            super.notifyChildrenChangedForFramework(str, bundle);
        }
        if (I1lllI11II.Il1IIlI1II(3749)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{95, 39, 7, 21, 55, 88, 80, 97, 116, 22, 7, 86, Byte.MAX_VALUE, 76, 68, 76, 36, 23, 8, 80, 115, 43, 66}));
        }
    }
}
