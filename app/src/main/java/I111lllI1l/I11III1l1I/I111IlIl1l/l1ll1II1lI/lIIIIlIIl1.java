package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.browse.MediaBrowser$MediaItem;
import android.os.Parcel;
import android.service.media.MediaBrowserService$Result;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lIIIIlIIl1<T> {
    MediaBrowserService$Result mResultFwk;

    lIIIIlIIl1(MediaBrowserService$Result mediaBrowserService$Result) {
        this.mResultFwk = mediaBrowserService$Result;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public void sendResult(T t) throws NoSuchFieldException {
        if (t instanceof List) {
            this.mResultFwk.sendResult(parcelListToItemList((List) t));
        } else if (t instanceof Parcel) {
            Parcel parcel = (Parcel) t;
            parcel.setDataPosition(0);
            this.mResultFwk.sendResult(MediaBrowser$MediaItem.CREATOR.createFromParcel(parcel));
            parcel.recycle();
        } else {
            this.mResultFwk.sendResult(null);
        }
        if (androidx.versionedparcelable.custom.entities.IIlII1IIIl.lll1111l11(I1I1lI1II1.a(new byte[]{125, 14, 6, 55}), 2746)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{102, 85, 58, 93, 7, 111, 123, 65, Byte.MAX_VALUE, 13, 117, 117, 99, 91, 94, 113, 36, 25, 40, 1, 98, 41, 125, 122, 94, 121, 115, 39}));
        }
    }

    public void detach() throws UnsupportedEncodingException {
        if (IIll1llI1l.Il1IIlI1II(3265)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{70, 35, 52, 93, 44, 115, 0, 69, 15, 0, 70, 86, 122, 126, 91, 83, 22, 89, 45, 97, 103, 35, 124, 77, 0, 83, 14, 41, 50}));
        }
        this.mResultFwk.detach();
    }

    List<MediaBrowser$MediaItem> parcelListToItemList(List<Parcel> list) {
        if (list == null) {
            return null;
        }
        ArrayList arrayList = new ArrayList(list.size());
        for (Parcel parcel : list) {
            parcel.setDataPosition(0);
            arrayList.add((MediaBrowser$MediaItem) MediaBrowser$MediaItem.CREATOR.createFromParcel(parcel));
            parcel.recycle();
        }
        return arrayList;
    }
}
