package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.RemoteException;
import android.support.v4.media.session.MediaSessionCompat$Token;
import android.util.Log;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class l1111lIII1 implements Runnable {
    final /* synthetic */ lIlII1Illl this$1;
    final /* synthetic */ MediaSessionCompat$Token val$token;

    l1111lIII1(lIlII1Illl lilii1illl, MediaSessionCompat$Token mediaSessionCompat$Token) {
        this.this$1 = lilii1illl;
        this.val$token = mediaSessionCompat$Token;
    }

    @Override // java.lang.Runnable
    public void run() {
        if (android.media.content.Il1llIl111.Ill1lIIlIl(623677276L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{126, 32, 0, 43, 42, 124, 2, Byte.MAX_VALUE, 109, 44, 126, 85, 99, 80, 12, 0, 53, 21, 45}));
        }
        Iterator<Il1lII1l1l> it = this.this$1.this$0.mConnections.values().iterator();
        while (it.hasNext()) {
            Il1lII1l1l next = it.next();
            try {
                next.callbacks.onConnect(next.root.getRootId(), this.val$token, next.root.getExtras());
            } catch (RemoteException unused) {
                Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{116, 11, 12, 11, 7, 86, 67, 89, 86, 10, 16, 86, 90, 75, 20}) + next.pkg + I1I1lI1II1.a(new byte[]{23, 13, 17, 69, 12, 90, 23, 92, 86, 10, 87, 85, 71, 25, 66, 84, 14, 8, 6, 28}));
                it.remove();
            }
        }
    }
}
