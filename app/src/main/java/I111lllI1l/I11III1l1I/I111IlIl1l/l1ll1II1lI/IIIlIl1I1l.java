package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.support.v4.media.session.MediaSessionCompat$Token;

/* loaded from: classes.dex */
interface IIIlIl1I1l {
    Bundle getBrowserRootHints();

    llIllIlll1 getCurrentBrowserInfo();

    void notifyChildrenChanged(llIllIlll1 llillilll1, String str, Bundle bundle);

    void notifyChildrenChanged(String str, Bundle bundle);

    IBinder onBind(Intent intent);

    void onCreate();

    void setSessionToken(MediaSessionCompat$Token mediaSessionCompat$Token);
}
