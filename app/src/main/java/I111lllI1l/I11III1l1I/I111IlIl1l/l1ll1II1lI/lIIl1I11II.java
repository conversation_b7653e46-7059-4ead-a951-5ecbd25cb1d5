package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.Ill11ll111;
import android.os.Parcel;
import androidx.core.location.l1l1I111I1;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class lIIl1I11II extends Il1llllI1I {
    final /* synthetic */ lIIIIll1II this$0;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    lIIl1I11II(lIIIIll1II liiiill1ii) {
        super(liiiill1ii);
        this.this$0 = liiiill1ii;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.Il1llllI1I, I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIIlIl1I1l
    public void onCreate() {
        if (Ill11ll111.l111l1I1Il(I1I1lI1II1.a(new byte[]{94, 23, 80}), 386811732L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{65, 41, 1, 41, 44, 123, 64, 100, 14, 8, 105, 1, 100, 10, 89, 67, 22, 32, 10, 115, 7, 56, 75, 81, 84}));
        }
        this.mServiceFwk = new III1Il1II1(this, this.this$0);
        this.mServiceFwk.onCreate();
        if (l1l1I111I1.l11I11I11l(I1I1lI1II1.a(new byte[]{123, 48, 32, 22, 18, 114, 126, 121, 94, 93, 8}))) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{15, 40, 9, 9, 59, 5, 79, 100, 77, 82, 103, 117, 79, 11, 7, 113, 46}));
        }
    }

    public void onLoadItem(String str, lIIIIlIIl1<Parcel> liiiiliil1) {
        IIl1I11lIl iIl1I11lIl = new IIl1I11lIl(this, str, liiiiliil1);
        lIIIIll1II liiiill1ii = this.this$0;
        liiiill1ii.mCurConnection = liiiill1ii.mConnectionFromFwk;
        this.this$0.onLoadItem(str, iIl1I11lIl);
        this.this$0.mCurConnection = null;
        if (androidx.constraintlayout.widget.Il1lII1l1l.I111IlIl1I(2307)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{86, 81, 53, 46, 5, 2, 122, 114, 14, 2, 86, 83, 122, 84, 97, 81, 54, 48, 41, 85, 126, 5, 2}));
        }
    }
}
