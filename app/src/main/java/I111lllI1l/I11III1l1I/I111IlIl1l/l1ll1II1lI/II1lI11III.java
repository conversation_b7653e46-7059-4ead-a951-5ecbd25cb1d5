package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.support.v4.os.ResultReceiver;
import android.util.Log;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class II1lI11III implements Runnable {
    final /* synthetic */ I1I11II1lI this$1;
    final /* synthetic */ IIll1IIlII val$callbacks;
    final /* synthetic */ String val$mediaId;
    final /* synthetic */ ResultReceiver val$receiver;

    II1lI11III(I1I11II1lI i1I11II1lI, IIll1IIlII iIll1IIlII, String str, ResultReceiver resultReceiver) {
        this.this$1 = i1I11II1lI;
        this.val$callbacks = iIll1IIlII;
        this.val$mediaId = str;
        this.val$receiver = resultReceiver;
    }

    @Override // java.lang.Runnable
    public void run() throws BrokenBarrierException {
        Il1lII1l1l il1lII1l1l = this.this$1.this$0.mConnections.get(this.val$callbacks.asBinder());
        if (il1lII1l1l == null) {
            Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{80, 1, 22, 40, 7, 81, 94, 81, 112, 16, 85, 93, 21, 95, 91, 71, 66, 2, 3, 94, 92, 3, 82, 86, 88, 22, 67, 13, 5, 70, 23, 10, 68, 10, 69, 17, 66, 71, 82, 87, 80, 23, 68, 85, 71, 92, 80, 21, 11, 5, 95}) + this.val$mediaId);
        } else {
            this.this$1.this$0.performLoadItem(this.val$mediaId, il1lII1l1l, this.val$receiver);
        }
    }
}
