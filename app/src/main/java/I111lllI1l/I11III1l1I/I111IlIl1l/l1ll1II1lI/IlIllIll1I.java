package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.interpolator.view.animation.llIlII1IlI;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class IlIllIll1I {
    private final Bundle mExtras;
    private final String mRootId;
    public static final String EXTRA_RECENT = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 74, 1, 66, 70, 92, 90, 81, 27, 15, 4, 6, 91, 81, 79, 86, 77, 71, 68, 86, 75, 54, 119, 116, 38, 121, 48});
    public static final String EXTRA_OFFLINE = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 74, 1, 66, 70, 92, 90, 81, 27, 15, 4, 6, 91, 81, 79, 86, 77, 71, 68, 86, 75, 43, 116, 113, 47, 126, 42, 39});
    public static final String EXTRA_SUGGESTED = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 74, 1, 66, 70, 92, 90, 81, 27, 15, 4, 6, 91, 81, 79, 86, 77, 71, 68, 86, 75, 55, 103, 112, 36, 114, 55, 54, 32, 38});

    @Deprecated
    public static final String EXTRA_SUGGESTION_KEYWORDS = I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 74, 1, 66, 70, 92, 90, 81, 27, 15, 4, 6, 91, 81, 79, 86, 77, 71, 68, 86, 75, 55, 103, 112, 36, 114, 55, 54, 44, 45, 123, 104, 123, 124, 61, 103, Byte.MAX_VALUE, 103, 125, 103});

    static /* synthetic */ Bundle access$100(IlIllIll1I ilIllIll1I) throws BrokenBarrierException {
        Bundle bundle = ilIllIll1I.mExtras;
        if (lIIlI111II.l1l11llIl1(7903)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{66, 92, 83, 32, 44, 119, 92, 65, 97, 38, 91}));
        }
        return bundle;
    }

    public IlIllIll1I(String str, Bundle bundle) {
        if (str == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 16, 90, 88, 68, 25, 13, 84, 16, 92, 87, 20, 119, 16, 14, 21, 65, 85, 19, 97, 90, 92, 66, 23, 6, 5, 92, 89, 12, 67, 68, 0, 0, 66, 91, 66, 92, 85, 74, 16, 101, 70, 92, 20, 91, 23, 13, 14, 18, 86, 14, 65, 21, 113, 68, 88, 18, 23, 87, 69, 49, 88, 11, 22, 69, 11, 91, 68, 68, 92, 5, 84}));
        }
        this.mRootId = str;
        this.mExtras = bundle;
    }

    public String getRootId() {
        return this.mRootId;
    }

    public Bundle getExtras() {
        if (llIlII1IlI.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{15, 29, 45, 81, 56, 112, 85, 70, 114, 45, 95, 95, 93, 112, 66, 121, 9, 56, 0, 69, 87, 59, 4, 84, 4, 82, 90, 49}), 193811239L)) {
            throw new UnknownError(I1I1lI1II1.a(new byte[]{97, 33, 27, 11, 87, 123, 6, 105, 122, 82, 98}));
        }
        return this.mExtras;
    }
}
