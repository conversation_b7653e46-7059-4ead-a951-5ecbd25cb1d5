package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import IIII11Il1l.Il1IlIlI1l.II111IIl1l.lll111IlIl.I1Il1lllIl;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.support.v4.media.lIIllIIlll;
import android.support.v4.media.session.I1lI1Il111;
import android.support.v4.os.ResultReceiver;
import android.util.Log;
import androidx.interpolator.view.animation.IllllI11lI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: classes.dex */
public final class IIll11II1I extends Handler {
    private final I1I11II1lI mServiceBinderImpl;
    final /* synthetic */ lIIIIll1II this$0;

    IIll11II1I(lIIIIll1II liiiill1ii) {
        this.this$0 = liiiill1ii;
        this.mServiceBinderImpl = new I1I11II1lI(liiiill1ii);
    }

    @Override // android.os.Handler
    public void handleMessage(Message message) {
        if (lIIlI111II.I11II1111l(445687186L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{102, 41, 82, 41, 84, 13, Byte.MAX_VALUE, 67, 86, 28, 96, 95, 66}));
        }
        Bundle data = message.getData();
        switch (message.what) {
            case 1:
                Bundle bundle = data.getBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 88, 95, 77, 59, 88, 89, 91, 77, 71}));
                I1lI1Il111.ensureClassLoader(bundle);
                this.mServiceBinderImpl.connect(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 69, 86, 83, 82, 5, 87, 85, 106, 87, 85, 88, 7})), data.getInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81})), data.getInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 76, 93, 81})), bundle, new IIl11lIIll(message.replyTo));
                break;
            case 2:
                this.mServiceBinderImpl.disconnect(new IIl11lIIll(message.replyTo));
                break;
            case 3:
                Bundle bundle2 = data.getBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 90, 71, 68, 80, 11, 94, 67}));
                I1lI1Il111.ensureClassLoader(bundle2);
                this.mServiceBinderImpl.addSubscription(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5})), I1Il1lllIl.getBinder(data, I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 6, 81, 83, 94, 102, 64, 90, 9, 4, 12})), bundle2, new IIl11lIIll(message.replyTo));
                break;
            case 4:
                this.mServiceBinderImpl.removeSubscription(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5})), I1Il1lllIl.getBinder(data, I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 6, 81, 83, 94, 102, 64, 90, 9, 4, 12})), new IIl11lIIll(message.replyTo));
                break;
            case 5:
                this.mServiceBinderImpl.getMediaItem(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 88, 82, 84, 80, 5, 111, 89, 65, 92, 89, 106, 11, 5})), (ResultReceiver) data.getParcelable(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 82, 67, 76, 8, 68, 111, 71, 92, 87, 80, 11, 23, 7, 64})), new IIl11lIIll(message.replyTo));
                break;
            case 6:
                Bundle bundle3 = data.getBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 88, 95, 77, 59, 88, 89, 91, 77, 71}));
                I1lI1Il111.ensureClassLoader(bundle3);
                this.mServiceBinderImpl.registerCallbacks(new IIl11lIIll(message.replyTo), data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 69, 86, 83, 82, 5, 87, 85, 106, 87, 85, 88, 7})), data.getInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81})), data.getInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 76, 93, 81})), bundle3);
                break;
            case 7:
                this.mServiceBinderImpl.unregisterCallbacks(new IIl11lIIll(message.replyTo));
                break;
            case 8:
                Bundle bundle4 = data.getBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 70, 82, 81, 75, 7, 88, 111, 80, 65, 64, 71, 3, 18}));
                I1lI1Il111.ensureClassLoader(bundle4);
                this.mServiceBinderImpl.search(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 70, 82, 81, 75, 7, 88, 111, 68, 76, 81, 71, 27})), bundle4, (ResultReceiver) data.getParcelable(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 82, 67, 76, 8, 68, 111, 71, 92, 87, 80, 11, 23, 7, 64})), new IIl11lIIll(message.replyTo));
                break;
            case 9:
                Bundle bundle5 = data.getBundle(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 66, 67, 77, 11, 93, 111, 84, 90, 64, 92, 13, 15, 61, 87, 72, 21, 65, 84, 64}));
                I1lI1Il111.ensureClassLoader(bundle5);
                this.mServiceBinderImpl.sendCustomAction(data.getString(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 66, 67, 77, 11, 93, 111, 84, 90, 64, 92, 13, 15})), bundle5, (ResultReceiver) data.getParcelable(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 71, 82, 67, 76, 8, 68, 111, 71, 92, 87, 80, 11, 23, 7, 64})), new IIl11lIIll(message.replyTo));
                break;
            default:
                Log.w(I1I1lI1II1.a(new byte[]{122, 38, 49, 0, 16, 67, 94, 83, 92, 39, 95, 93, 69, 88, 64}), I1I1lI1II1.a(new byte[]{98, 10, 10, 4, 12, 81, 91, 85, 93, 68, 93, 85, 70, 74, 85, 82, 7, 91, 66}) + message + I1I1lI1II1.a(new byte[]{61, 68, 66, 54, 7, 71, 65, 89, 90, 1, 16, 70, 80, 75, 71, 92, 13, 15, 88, 18}) + 2 + I1I1lI1II1.a(new byte[]{61, 68, 66, 38, 14, 92, 82, 94, 77, 68, 70, 85, 71, 74, 93, 90, 12, 91, 66}) + message.arg1);
                break;
        }
        if (IllllI11lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{77, 1, 18, 63, 82, 122, 67, 119, 108, 30, 86, 95, 121, 87, 87, 80}), 1386660527L)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{70, 51, 23, 53, 41, Byte.MAX_VALUE, 117, 100, 77, 55, 106, 2, 125, 88, 71, 119, 27, 59, 7, 101, 89, 56, 120}));
        }
    }

    @Override // android.os.Handler
    public boolean sendMessageAtTime(Message message, long j) {
        Bundle data = message.getData();
        data.setClassLoader(lIIllIIlll.class.getClassLoader());
        data.putInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 76, 93, 81}), Binder.getCallingUid());
        int callingPid = Binder.getCallingPid();
        if (callingPid > 0) {
            data.putInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81}), callingPid);
        } else if (!data.containsKey(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81}))) {
            data.putInt(I1I1lI1II1.a(new byte[]{83, 5, 22, 4, 61, 86, 86, 92, 85, 13, 94, 87, 106, 73, 93, 81}), -1);
        }
        return super.sendMessageAtTime(message, j);
    }

    public void postOrRun(Runnable runnable) {
        if (Thread.currentThread() == getLooper().getThread()) {
            runnable.run();
        } else {
            post(runnable);
        }
    }
}
