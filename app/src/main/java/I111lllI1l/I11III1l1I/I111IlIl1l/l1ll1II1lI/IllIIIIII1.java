package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.os.Bundle;
import android.os.IBinder;
import android.support.v4.graphics.drawable.IllllI11Il;
import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class IllIIIIII1 implements Runnable {
    final /* synthetic */ Il1llllI1I this$1;
    final /* synthetic */ Bundle val$options;
    final /* synthetic */ String val$parentId;

    IllIIIIII1(Il1llllI1I il1llllI1I, String str, Bundle bundle) {
        this.this$1 = il1llllI1I;
        this.val$parentId = str;
        this.val$options = bundle;
    }

    @Override // java.lang.Runnable
    public void run() throws NoSuchAlgorithmException {
        if (IllllI11Il.Ill1lIIlIl(6882)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{64, 39, 22, 87}));
        }
        Iterator<IBinder> it = this.this$1.this$0.mConnections.keySet().iterator();
        while (it.hasNext()) {
            this.this$1.notifyChildrenChangedForCompatOnHandler(this.this$1.this$0.mConnections.get(it.next()), this.val$parentId, this.val$options);
        }
    }
}
