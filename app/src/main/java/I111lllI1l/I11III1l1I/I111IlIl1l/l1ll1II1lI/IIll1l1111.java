package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.accounts.utils.lIIIIII11I;
import android.media.AudioAttributes;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I1111IIl11;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.UTFDataFormatException;
import java.net.PortUnreachableException;
import java.security.AccessControlException;
import java.security.DigestException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class IIll1l1111 implements IIlIl1IIl1 {
    public AudioAttributes mAudioAttributes;
    public int mLegacyStreamType;

    public IIll1l1111() {
        this.mLegacyStreamType = -1;
    }

    IIll1l1111(AudioAttributes audioAttributes) {
        this(audioAttributes, -1);
    }

    IIll1l1111(AudioAttributes audioAttributes, int i) {
        this.mAudioAttributes = audioAttributes;
        this.mLegacyStreamType = i;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public Object getAudioAttributes() {
        AudioAttributes audioAttributes = this.mAudioAttributes;
        if (IIll1llI1l.Il1IIlI1II(7231)) {
            throw new StringIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{89, 80}));
        }
        return audioAttributes;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getVolumeControlStream() {
        if (l1IIll1I1l.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{117, 6, 10, 28, 59, 89, 84, 113, 92, 30, 100, 82, 119, 79, 80, 116, 6, 87, 8, 4, 6, 39, 89, 99, 112, 0}), 2813)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{118, 32, 23, 81, 40, 65, 80, 126, 85, 23, 66, 8, 93, 126, 82, 68, 27, 50, 46, 97, 96, 81, 75, 82, 90, 123, 115, 14, 30}));
        }
        return I1IllIll1l.toVolumeStreamType(true, getFlags(), getUsage());
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getLegacyStreamType() throws DigestException, PortUnreachableException, UTFDataFormatException {
        int i = this.mLegacyStreamType;
        if (i != -1) {
            return i;
        }
        int volumeStreamType = I1IllIll1l.toVolumeStreamType(false, getFlags(), getUsage());
        if (lIIIIII11I.I1II1111ll(513562960L)) {
            throw new DigestException(I1I1lI1II1.a(new byte[]{91, 84, 83, 11}));
        }
        return volumeStreamType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getRawLegacyStreamType() {
        return this.mLegacyStreamType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getContentType() {
        int contentType = this.mAudioAttributes.getContentType();
        if (lIlIII1I1l.IllIlI1l1I(I1I1lI1II1.a(new byte[]{82, 85, 40, 28, 86, 67, 0, 65, 115, 87}), 231140493L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{90, 22, 13, 53, 54, 66, 83, 93, 72, 9, 90, 91, 100, 74, 77, 79, 86, 8, 86, 102, 72, 21, 122, 124, 85, 83, 98, 16, 87, 7, 92}));
        }
        return contentType;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getUsage() throws NoSuchMethodException {
        int usage = this.mAudioAttributes.getUsage();
        if (I1111IIl11.I1lIllll1l(625453502L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{96}));
        }
        return usage;
    }

    @Override // I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI.IIlIl1IIl1
    public int getFlags() {
        return this.mAudioAttributes.getFlags();
    }

    public int hashCode() {
        return this.mAudioAttributes.hashCode();
    }

    public boolean equals(Object obj) {
        if (obj instanceof IIll1l1111) {
            return this.mAudioAttributes.equals(((IIll1l1111) obj).mAudioAttributes);
        }
        return false;
    }

    public String toString() {
        String str = I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 116, 67, 68, 75, 13, 82, 69, 65, 92, 71, 118, 13, 12, 18, 83, 68, 91, 19, 84, 70, 82, 94, 10, 5, 70, 67, 17, 94, 6, 23, 17, 7, 70, 10}) + this.mAudioAttributes;
        if (lII1llllI1.III111l111(I1I1lI1II1.a(new byte[]{89, 28, 15, 87, 41}), I1I1lI1II1.a(new byte[]{102, 42, 7, 8, 45, 100, 83, 65, 115, 62, 66, 125, 71, 87, 86, 109, 27, 86, 53, 98, 85, 23, 90, 100}))) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{113, 53, 21, 49, 82, 89, 66, 95, 78, 93, 119, 119}));
        }
        return str;
    }
}
