package I111lllI1l.I11III1l1I.I111IlIl1l.l1ll1II1lI;

import android.media.VolumeProvider;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.I111lIl11I;
import android.util.Log;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import java.io.StreamCorruptedException;
import java.security.cert.CertPathValidatorException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public abstract class l1l1lI1lI1 {
    public static final int VOLUME_CONTROL_ABSOLUTE = 2;
    public static final int VOLUME_CONTROL_FIXED = 0;
    public static final int VOLUME_CONTROL_RELATIVE = 1;
    private Il1IIlI1lI mCallback;
    private final String mControlId;
    private final int mControlType;
    private int mCurrentVolume;
    private final int mMaxVolume;
    private VolumeProvider mVolumeProviderFwk;

    public void onAdjustVolume(int i) {
        if (Il11II1llI.I111IlIl1I(lIIlI111II.l1ll11I11l)) {
            throw new StreamCorruptedException(I1I1lI1II1.a(new byte[]{93, 83, 49, 83, 51, 116, 66, 1, 123, 50, 94, 85, 124, 96, 108, 98, 39, 24, 87, 117, 88, 8, 125}));
        }
    }

    public l1l1lI1lI1(int i, int i2, int i3) {
        this(i, i2, i3, null);
    }

    public l1l1lI1lI1(int i, int i2, int i3, String str) {
        this.mControlType = i;
        this.mMaxVolume = i2;
        this.mCurrentVolume = i3;
        this.mControlId = str;
    }

    public final int getCurrentVolume() {
        return this.mCurrentVolume;
    }

    public final int getVolumeControl() {
        int i = this.mControlType;
        if (!l11Il1lI11.IlIllIll1I(I1I1lI1II1.a(new byte[]{15, 48, 51, 1, 9, 126, 100, 74, 117, 15, 6, 104, 90, 72, Byte.MAX_VALUE, 4, 18, 7, 53, Byte.MAX_VALUE}), 8381)) {
            return i;
        }
        Log.i(I1I1lI1II1.a(new byte[]{114, 32, 14, 50, 42, 90, 118, 105, 83, 15, 70, 102, 81, 74, 4, 3, 37, 11, 48, 87, 67, 4, 69, 97, 118, 89, 81, 16, 3}), I1I1lI1II1.a(new byte[]{125, 29, 21, 12, 4, 125, 83, 102, 74, 32, 5, 101, 13, 74, 93, 65, 83, 32, 38, 115, 118, 42, 97, 65, 64, 113, 7, 7, 20}));
        return 0;
    }

    public final int getMaxVolume() {
        return this.mMaxVolume;
    }

    public final void setCurrentVolume(int i) {
        if (lIIlI111II.l111I1ll1l(1225)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{93, 30, 49, 47, 4, 12, 65, 8, 116, 60, 89, 122, 88, 105, 96, 120, 55, 7, 50, 88, 95, 16, 81, 88, 82}));
        }
        this.mCurrentVolume = i;
        llllI1l1II.setCurrentVolume((VolumeProvider) getVolumeProvider(), i);
        Il1IIlI1lI il1IIlI1lI = this.mCallback;
        if (il1IIlI1lI != null) {
            il1IIlI1lI.onVolumeChanged(this);
        }
    }

    public final String getVolumeControlId() throws CertPathValidatorException {
        String str = this.mControlId;
        if (androidx.constraintlayout.widget.l111Il1lI1.l1l1Il1I11(I1I1lI1II1.a(new byte[]{83, 44, 26, 43, 44, 81, 86, 88, 65}), I1I1lI1II1.a(new byte[]{2, 46, 27}))) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{82, 35, 20, 1, 4, 7, 88, 95, 118, 54, 106, 126, 13, 110, 13, 92, 22, 20, 6, 80, 101, 23, 94, 3, 107, 103, 81, 28, 29, 71, 0}));
        }
        return str;
    }

    public void onSetVolumeTo(int i) {
        if (I111lIl11I.IlIIl111lI(I1I1lI1II1.a(new byte[]{116, 60, 12, 7, 27, 99, 90, 71, 87, 39, 120, 3, 91, 124, 108, 7, 0, 7, 1, 104, 3, 44, 97, 81, 120, 89, 82, 93}))) {
            Log.w(I1I1lI1II1.a(new byte[]{65, 32, 49, 49, 24, 102, 109, 102, 85, 86, 102, 113, 66, 84, 112, 68, 11, 53, 42, 121, 88, 13, 81, 111, 100, 89, 80, 8, 29, 101, 84}), I1I1lI1II1.a(new byte[]{94, 0, 22, 16, 37, 87, 6, 124, 84, 16, 113, 0, 97, 93, 12, 118, 1, 10, 4, 80, 70, 57, 69, 122, 91, 126, 71, 12, 87, 84, 69, 39}));
        }
    }

    public void setCallback(Il1IIlI1lI il1IIlI1lI) {
        this.mCallback = il1IIlI1lI;
    }

    public Object getVolumeProvider() {
        if (this.mVolumeProviderFwk == null) {
            if (Build$VERSION.SDK_INT >= 30) {
                this.mVolumeProviderFwk = new IlIl1lllII(this, this.mControlType, this.mMaxVolume, this.mCurrentVolume, this.mControlId);
            } else {
                this.mVolumeProviderFwk = new IIllIlIl1l(this, this.mControlType, this.mMaxVolume, this.mCurrentVolume);
            }
        }
        return this.mVolumeProviderFwk;
    }
}
