package I11IllI1l1.Il11l1l11I.llIIl1l1l1.IlIII1IIl1;

import android.support.v4.graphics.drawable.lIIlI111II;
import androidx.core.location.I111I11Ill;
import java.io.File;
import java.security.AccessControlException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class llllIllIII {
    private int LD;
    private File[] Lxb;

    public llllIllIII(File[] fileArr, int i) {
        this.Lxb = fileArr;
        this.LD = i;
    }

    public File[] Lxb() {
        File[] fileArr = this.Lxb;
        if (I111I11Ill.IlII1Illll(217464895L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{118, 38, 15, 32, 46, 77, 86}));
        }
        return fileArr;
    }

    public int LD() {
        int i = this.LD;
        if (lIIlI111II.IIlI1Il1lI(5569)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{99, 8, 81, 49, 91, 103, 111, 93, 104, 40, 93, 8, 69, 111, 117, 101, 33}));
        }
        return i;
    }
}
