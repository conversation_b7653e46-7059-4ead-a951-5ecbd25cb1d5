package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III1lllIII;
import androidx.constraintlayout.widget.l111Il1lI1;
import java.io.NotSerializableException;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
public class IIlI1II1ll implements Runnable {
    private III1lllIII mRuntimeExtras;
    private llIIll1Il1 mWorkManagerImpl;
    private String mWorkSpecId;

    public IIlI1II1ll(llIIll1Il1 lliill1il1, String str, III1lllIII iII1lllIII) {
        this.mWorkManagerImpl = lliill1il1;
        this.mWorkSpecId = str;
        this.mRuntimeExtras = iII1lllIII;
    }

    @Override // java.lang.Runnable
    public void run() throws NotSerializableException {
        this.mWorkManagerImpl.getProcessor().startWork(this.mWorkSpecId, this.mRuntimeExtras);
        if (l111Il1lI1.IllIlI1l1I("cK1qa1G6ld", 397578385L)) {
            throw new NotSerializableException("AWcrQw7653zPZhzX1rw5FJjrsUP");
        }
    }
}
