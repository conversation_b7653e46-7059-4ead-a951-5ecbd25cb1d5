package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;

/* loaded from: classes.dex */
class ll11llI1Il implements Runnable {
    final /* synthetic */ IIllIl1ll1 this$0;
    final /* synthetic */ III1l1lIlI val$foregroundFuture;

    ll11llI1Il(IIllIl1ll1 iIllIl1ll1, III1l1lIlI iII1l1lIlI) {
        this.this$0 = iIllIl1ll1;
        this.val$foregroundFuture = iII1l1lIlI;
    }

    @Override // java.lang.Runnable
    public void run() throws SignatureException, NoSuchAlgorithmException {
        this.val$foregroundFuture.setFuture(this.this$0.mWorker.getForegroundInfoAsync());
    }
}
