package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/* loaded from: classes.dex */
class I1lIlIIIl1 implements ThreadFactory {
    private int mThreadsCreated = 0;
    final /* synthetic */ ll1lIlI11I this$0;

    I1lIlIIIl1(ll1lIlI11I ll1lili11i) {
        this.this$0 = ll1lili11i;
    }

    @Override // java.util.concurrent.ThreadFactory
    public Thread newThread(Runnable runnable) {
        Thread threadNewThread = Executors.defaultThreadFactory().newThread(runnable);
        threadNewThread.setName("WorkManager-WorkTimer-thread-" + this.mThreadsCreated);
        this.mThreadsCreated++;
        if (l1lI1I1l11.I1lllI1llI(161301770L)) {
            throw new IllegalArgumentException("Uq1ltljnl0ur0xGuFFviEQ9NYvsX");
        }
        return threadNewThread;
    }
}
