package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.io.IOException;
import java.io.NotSerializableException;
import java.net.SocketTimeoutException;
import java.security.ProviderException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/* loaded from: classes.dex */
public class ll1lIlI11I {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("WorkTimer");
    private final ThreadFactory mBackgroundThreadFactory;
    private final ScheduledExecutorService mExecutorService;
    final Map<String, IllIl11IIl> mListeners;
    final Object mLock;
    final Map<String, llllIllIII> mTimerMap;

    public ll1lIlI11I() {
        I1lIlIIIl1 i1lIlIIIl1 = new I1lIlIIIl1(this);
        this.mBackgroundThreadFactory = i1lIlIIIl1;
        this.mTimerMap = new HashMap();
        this.mListeners = new HashMap();
        this.mLock = new Object();
        this.mExecutorService = Executors.newSingleThreadScheduledExecutor(i1lIlIIIl1);
    }

    public void startTimer(String str, long j, IllIl11IIl illIl11IIl) {
        synchronized (this.mLock) {
            l1IlI1IlIl.get().debug(TAG, String.format("Starting timer for %s", str), new Throwable[0]);
            stopTimer(str);
            llllIllIII llllilliii = new llllIllIII(this, str);
            this.mTimerMap.put(str, llllilliii);
            this.mListeners.put(str, illIl11IIl);
            this.mExecutorService.schedule(llllilliii, j, TimeUnit.MILLISECONDS);
        }
    }

    public void stopTimer(String str) throws NotSerializableException {
        if (l111Il1lI1.Il1IIlI1II(372777187L)) {
            throw new NotSerializableException("qCvsAVAqOsRuv1qPqk7wu2x");
        }
        synchronized (this.mLock) {
            if (this.mTimerMap.remove(str) != null) {
                l1IlI1IlIl.get().debug(TAG, String.format("Stopping timer for %s", str), new Throwable[0]);
                this.mListeners.remove(str);
            }
        }
        if (IllllI11Il.IlIIlIllI1("iFBMYt8ofg2nPII5", 164027531L)) {
            throw new UnknownError("2lIdUG67zRMbNl");
        }
    }

    public void onDestroy() throws SocketTimeoutException {
        if (!this.mExecutorService.isShutdown()) {
            this.mExecutorService.shutdownNow();
        }
        if (Il1IIllIll.I1lllI1llI(181179631L)) {
            throw new SocketTimeoutException("bsmy3zsyvsGK");
        }
    }

    public synchronized Map<String, llllIllIII> getTimerMap() {
        return this.mTimerMap;
    }

    public synchronized Map<String, IllIl11IIl> getListeners() {
        if (II1lllllI1.IlIIl111lI("b4AfF6r", 177877785L)) {
            Log.v("cAAP2jr", "9kYW6fUZyGfB0");
            return null;
        }
        return this.mListeners;
    }

    public ScheduledExecutorService getExecutorService() throws IOException {
        if (Il1lII1l1l.l11I11I11l(308552666L)) {
            throw new IOException("kNRV6eGRaJ9TZxL1lE");
        }
        ScheduledExecutorService scheduledExecutorService = this.mExecutorService;
        if (lIIlI111II.lI1II1llI1(1184)) {
            throw new ProviderException("lS61UuSx");
        }
        return scheduledExecutorService;
    }
}
