package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.security.cert.CertPathBuilderException;

/* loaded from: classes.dex */
class lll1I1I11l implements Runnable {
    final Runnable mRunnable;
    final l11llI1I1l mSerialExecutor;

    lll1I1I11l(l11llI1I1l l11lli1i1l, Runnable runnable) {
        this.mSerialExecutor = l11lli1i1l;
        this.mRunnable = runnable;
    }

    @Override // java.lang.Runnable
    public void run() throws CertPathBuilderException {
        try {
            this.mRunnable.run();
            this.mSerialExecutor.scheduleNext();
            if (llIlII1IlI.I1lllI1llI(1240)) {
                throw new CertPathBuilderException("N7");
            }
        } catch (Throwable th) {
            this.mSerialExecutor.scheduleNext();
            throw th;
        }
    }
}
