package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.IlIlIIll11;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.app.ActivityManager;
import android.app.ActivityManager$RunningAppProcessInfo;
import android.app.Application;
import android.content.Context;
import android.os.Build$VERSION;
import android.os.Process;
import android.text.TextUtils;
import java.lang.reflect.Method;
import java.util.List;

/* loaded from: classes.dex */
public class IlllIIlIII {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("ProcessUtils");

    private IlllIIlIII() {
    }

    public static boolean isDefaultProcess(Context context, IlIlIIll11 ilIlIIll11) {
        String processName = getProcessName(context);
        if (!TextUtils.isEmpty(ilIlIIll11.getDefaultProcessName())) {
            return TextUtils.equals(processName, ilIlIIll11.getDefaultProcessName());
        }
        return TextUtils.equals(processName, context.getApplicationInfo().processName);
    }

    public static String getProcessName(Context context) {
        List<ActivityManager$RunningAppProcessInfo> runningAppProcesses;
        if (Build$VERSION.SDK_INT >= 28) {
            return Application.getProcessName();
        }
        try {
            Method declaredMethod = Class.forName("android.app.ActivityThread", false, IlllIIlIII.class.getClassLoader()).getDeclaredMethod("currentProcessName", new Class[0]);
            declaredMethod.setAccessible(true);
            Object objInvoke = declaredMethod.invoke(null, new Object[0]);
            if (objInvoke instanceof String) {
                return (String) objInvoke;
            }
        } catch (Throwable th) {
            l1IlI1IlIl.get().debug(TAG, "Unable to check ActivityThread for processName", th);
        }
        int iMyPid = Process.myPid();
        ActivityManager activityManager = (ActivityManager) context.getSystemService("activity");
        if (activityManager != null && (runningAppProcesses = activityManager.getRunningAppProcesses()) != null && !runningAppProcesses.isEmpty()) {
            for (ActivityManager$RunningAppProcessInfo activityManager$RunningAppProcessInfo : runningAppProcesses) {
                if (activityManager$RunningAppProcessInfo.pid == iMyPid) {
                    return activityManager$RunningAppProcessInfo.processName;
                }
            }
        }
        return null;
    }
}
