package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import android.support.v4.graphics.drawable.IllllI11Il;

/* loaded from: classes.dex */
class lIl1llIlll implements Runnable {
    final /* synthetic */ IlIIlIl1II this$0;
    final /* synthetic */ Object val$input;

    lIl1llIlll(IlIIlIl1II ilIIlIl1II, Object obj) {
        this.this$0 = ilIIlIl1II;
        this.val$input = obj;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v4, types: [Out, java.lang.Object] */
    @Override // java.lang.Runnable
    public void run() throws ReflectiveOperationException {
        if (IllllI11Il.IlIIl111lI("QjyIhpdgjLh52GoOi0wJa6QxzLj9")) {
            throw new ReflectiveOperationException("SnNRswak67SuIsRIjLO");
        }
        synchronized (this.this$0.val$lock) {
            ?? Apply = this.this$0.val$mappingMethod.apply(this.val$input);
            if (this.this$0.mCurrentOutput == 0 && Apply != 0) {
                this.this$0.mCurrentOutput = Apply;
                this.this$0.val$outputLiveData.postValue(Apply);
            } else if (this.this$0.mCurrentOutput != 0 && !this.this$0.mCurrentOutput.equals(Apply)) {
                this.this$0.mCurrentOutput = Apply;
                this.this$0.val$outputLiveData.postValue(Apply);
            }
        }
    }
}
