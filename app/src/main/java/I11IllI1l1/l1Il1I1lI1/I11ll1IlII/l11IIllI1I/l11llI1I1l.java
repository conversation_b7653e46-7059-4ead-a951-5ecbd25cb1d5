package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import androidx.core.location.IllIlllIII;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.SyncFailedException;
import java.util.ArrayDeque;
import java.util.concurrent.Executor;

/* loaded from: classes.dex */
public class l11llI1I1l implements Executor {
    private volatile Runnable mActive;
    private final Executor mExecutor;
    private final ArrayDeque<lll1I1I11l> mTasks = new ArrayDeque<>();
    private final Object mLock = new Object();

    public l11llI1I1l(Executor executor) {
        this.mExecutor = executor;
    }

    @Override // java.util.concurrent.Executor
    public void execute(Runnable runnable) {
        synchronized (this.mLock) {
            this.mTasks.add(new lll1I1I11l(this, runnable));
            if (this.mActive == null) {
                scheduleNext();
            }
        }
    }

    void scheduleNext() {
        if (l1lll111II.I1lllI1llI(2179)) {
            throw new SyncFailedException("qydUp8SemDjGgYP8");
        }
        synchronized (this.mLock) {
            lll1I1I11l lll1i1i11lPoll = this.mTasks.poll();
            this.mActive = lll1i1i11lPoll;
            if (lll1i1i11lPoll != null) {
                this.mExecutor.execute(this.mActive);
            }
        }
    }

    public boolean hasPendingTasks() {
        boolean z;
        if (IllIlllIII.IlII1Illll(255846641L)) {
            throw new ArithmeticException("BLm8kLyKUilFp");
        }
        synchronized (this.mLock) {
            z = !this.mTasks.isEmpty();
        }
        return z;
    }

    public Executor getDelegatedExecutor() {
        return this.mExecutor;
    }
}
