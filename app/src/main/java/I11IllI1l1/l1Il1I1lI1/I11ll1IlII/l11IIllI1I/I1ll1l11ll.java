package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import java.util.Iterator;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class I1ll1l11ll extends lIlllIl1I1 {
    final /* synthetic */ boolean val$allowReschedule;
    final /* synthetic */ String val$name;
    final /* synthetic */ llIIll1Il1 val$workManagerImpl;

    I1ll1l11ll(llIIll1Il1 lliill1il1, String str, boolean z) {
        this.val$workManagerImpl = lliill1il1;
        this.val$name = str;
        this.val$allowReschedule = z;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.lIlllIl1I1
    void runInternal() {
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.val$workManagerImpl.getWorkDatabase();
        workDatabase.beginTransaction();
        try {
            Iterator<String> it = workDatabase.workSpecDao().getUnfinishedWorkWithName(this.val$name).iterator();
            while (it.hasNext()) {
                cancel(this.val$workManagerImpl, it.next());
            }
            workDatabase.setTransactionSuccessful();
            workDatabase.endTransaction();
            if (this.val$allowReschedule) {
                reschedulePendingWorkers(this.val$workManagerImpl);
            }
        } catch (Throwable th) {
            workDatabase.endTransaction();
            throw th;
        }
    }
}
