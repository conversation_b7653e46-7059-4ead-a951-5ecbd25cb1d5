package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import IllIlIllll.Il11l1l11I.l1I1l11Il1.lllII11ll1.l111I1I1Il;
import android.support.v4.graphics.drawable.Il1IIllIll;
import lI111IIIII.Il111lll1I.I1lIl1lIlI.I1lIl1lIII.Il1ll1IIll;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.Il1lIl1IIl;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIlIII1I1l;

/* JADX INFO: Add missing generic type declarations: [In] */
/* loaded from: classes.dex */
class IlIIlIl1II<In> implements lIlIII1I1l<In> {
    Out mCurrentOutput = null;
    final /* synthetic */ Object val$lock;
    final /* synthetic */ l111I1I1Il val$mappingMethod;
    final /* synthetic */ Il1lIl1IIl val$outputLiveData;
    final /* synthetic */ Il1ll1IIll val$workTaskExecutor;

    IlIIlIl1II(Il1ll1IIll il1ll1IIll, Object obj, l111I1I1Il l111i1i1il, Il1lIl1IIl il1lIl1IIl) {
        this.val$workTaskExecutor = il1ll1IIll;
        this.val$lock = obj;
        this.val$mappingMethod = l111i1i1il;
        this.val$outputLiveData = il1lIl1IIl;
    }

    @Override // llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.lIlIII1I1l
    public void onChanged(In in) {
        if (Il1IIllIll.IIll1I11lI("VRAGeqGdM39WU1z4WE7")) {
            throw new IllegalAccessError("xxYRF1");
        }
        this.val$workTaskExecutor.executeOnBackgroundThread(new lIl1llIlll(this, in));
    }
}
