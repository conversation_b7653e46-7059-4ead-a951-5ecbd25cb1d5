package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.content.ComponentName;
import android.content.Context;
import androidx.interpolator.view.animation.lIIlI111II;
import java.security.cert.CertificateException;

/* loaded from: classes.dex */
public class lII11llIIl {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("PackageManagerHelper");

    private lII11llIIl() {
    }

    public static void setComponentEnabled(Context context, Class<?> cls, boolean z) {
        try {
            context.getPackageManager().setComponentEnabledSetting(new ComponentName(context, cls.getName()), z ? 1 : 2, 1);
            l1IlI1IlIl l1ili1ilil = l1IlI1IlIl.get();
            String str = TAG;
            Object[] objArr = new Object[2];
            objArr[0] = cls.getName();
            objArr[1] = z ? "enabled" : "disabled";
            l1ili1ilil.debug(str, String.format("%s %s", objArr), new Throwable[0]);
        } catch (Exception e) {
            l1IlI1IlIl l1ili1ilil2 = l1IlI1IlIl.get();
            String str2 = TAG;
            Object[] objArr2 = new Object[2];
            objArr2[0] = cls.getName();
            objArr2[1] = z ? "enabled" : "disabled";
            l1ili1ilil2.debug(str2, String.format("%s could not be %s", objArr2), e);
        }
    }

    public static boolean isComponentExplicitlyEnabled(Context context, Class<?> cls) throws CertificateException {
        boolean zIsComponentExplicitlyEnabled = isComponentExplicitlyEnabled(context, cls.getName());
        if (lIIlI111II.IIll1l1lII(4932)) {
            throw new CertificateException("BTnqOJmjU2Wi9bgHBL9ghrFUAot63");
        }
        return zIsComponentExplicitlyEnabled;
    }

    public static boolean isComponentExplicitlyEnabled(Context context, String str) {
        return context.getPackageManager().getComponentEnabledSetting(new ComponentName(context, str)) == 1;
    }
}
