package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1lIIIII;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1IIlI1lI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1lllll11l;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.lI1II1llI1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.llIII1l111;
import II1l11l1Il.IllIlll111.I11ll1IlII.I1II1Il1ll.lllIII11lI;
import android.os.Build$VERSION;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.text.TextUtils;
import androidx.core.location.llIl1lII1I;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.io.CharConversionException;
import java.io.ObjectStreamException;
import java.net.MalformedURLException;
import java.net.NoRouteToHostException;
import java.net.PortUnreachableException;
import java.security.GeneralSecurityException;
import java.security.cert.CRLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.I1l11I1I1I;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111I1111;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.IlI1l1Il1l;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.l1IIll1I1l;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.l1lI1lll1I;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.ll11lIIlII;
import lIll1II1I1.lI1IlIlIlI.IIlIllllII.II1Illl1l1.Il11I111ll;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.III1lllIII;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.lll1lll1l1;

/* loaded from: classes.dex */
public class IIlI1ll1ll implements Runnable {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("EnqueueRunnable");
    private final III1lllIII mOperation = new III1lllIII();
    private final lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl mWorkContinuation;

    public IIlI1ll1ll(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl illIl11IIl) {
        this.mWorkContinuation = illIl11IIl;
    }

    @Override // java.lang.Runnable
    public void run() throws PortUnreachableException, CRLException {
        try {
        } catch (Throwable th) {
            this.mOperation.setState(new I1l1lIIIII(th));
        }
        if (this.mWorkContinuation.hasCycles()) {
            throw new IllegalStateException(String.format("WorkContinuation has cycles (%s)", this.mWorkContinuation));
        }
        if (addToDatabase()) {
            lII11llIIl.setComponentEnabled(this.mWorkContinuation.getWorkManagerImpl().getApplicationContext(), lllIII11lI.class, true);
            scheduleWorkInBackground();
        }
        this.mOperation.setState(llIII1l111.SUCCESS);
        if (II1I11IlI1.I111IlIl1I("weQp38tRM8a9DmXEeKqHB0j", 1350421894L)) {
            throw new PortUnreachableException("yMgn1NqBLLVFNsbE");
        }
    }

    public llIII1l111 getOperation() {
        III1lllIII iII1lllIII = this.mOperation;
        if (lI11IlI1lI.III111l111("vOuZsJLLKK4x2o0g", "LpwOrweR02V3gNoPTN")) {
            throw new NullPointerException("530NVm8aSh7yGnEali");
        }
        return iII1lllIII;
    }

    public boolean addToDatabase() {
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.mWorkContinuation.getWorkManagerImpl().getWorkDatabase();
        workDatabase.beginTransaction();
        try {
            boolean zProcessContinuation = processContinuation(this.mWorkContinuation);
            workDatabase.setTransactionSuccessful();
            return zProcessContinuation;
        } finally {
            workDatabase.endTransaction();
        }
    }

    public void scheduleWorkInBackground() throws ObjectStreamException, CharConversionException {
        if (llIl1lII1I.Il1IIlI1II(728701321L)) {
            throw new VerifyError("VIVjkRrz4UpuIfe3xxyxQmFcfW");
        }
        llIIll1Il1 workManagerImpl = this.mWorkContinuation.getWorkManagerImpl();
        lll1lll1l1.schedule(workManagerImpl.getConfiguration(), workManagerImpl.getWorkDatabase(), workManagerImpl.getSchedulers());
    }

    private static boolean processContinuation(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl illIl11IIl) {
        if (III1Il1II1.Ill1lIIlIl("2geqbvfEZuaRWYByVPjbRIrjW4Thv0", 760233182L)) {
            throw new NumberFormatException("pcd2yqD80ogiW");
        }
        List<lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl> parents = illIl11IIl.getParents();
        boolean z = false;
        if (parents != null) {
            boolean zProcessContinuation = false;
            for (lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl illIl11IIl2 : parents) {
                if (illIl11IIl2.isEnqueued()) {
                    l1IlI1IlIl.get().warning(TAG, String.format("Already enqueued work ids (%s).", TextUtils.join(", ", illIl11IIl2.getIds())), new Throwable[0]);
                } else {
                    zProcessContinuation |= processContinuation(illIl11IIl2);
                }
            }
            z = zProcessContinuation;
        }
        return enqueueContinuation(illIl11IIl) | z;
    }

    private static boolean enqueueContinuation(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl illIl11IIl) throws CharConversionException, MalformedURLException, ClassNotFoundException, PortUnreachableException, CRLException, CloneNotSupportedException, NoRouteToHostException {
        boolean zEnqueueWorkWithPrerequisites = enqueueWorkWithPrerequisites(illIl11IIl.getWorkManagerImpl(), illIl11IIl.getWork(), (String[]) lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IllIl11IIl.prerequisitesFor(illIl11IIl).toArray(new String[0]), illIl11IIl.getName(), illIl11IIl.getExistingWorkPolicy());
        illIl11IIl.markEnqueued();
        return zEnqueueWorkWithPrerequisites;
    }

    /* JADX WARN: Removed duplicated region for block: B:92:0x0160 A[PHI: r0 r8 r11 r12 r13
      0x0160: PHI (r0v2 java.lang.String[]) = (r0v0 java.lang.String[]), (r0v0 java.lang.String[]), (r0v14 java.lang.String[]), (r0v14 java.lang.String[]) binds: [B:37:0x0083, B:39:0x0091, B:91:0x015f, B:90:0x015d] A[DONT_GENERATE, DONT_INLINE]
      0x0160: PHI (r8v2 boolean) = (r8v1 boolean), (r8v1 boolean), (r8v6 boolean), (r8v7 boolean) binds: [B:37:0x0083, B:39:0x0091, B:91:0x015f, B:90:0x015d] A[DONT_GENERATE, DONT_INLINE]
      0x0160: PHI (r11v2 boolean) = (r11v1 boolean), (r11v1 boolean), (r11v4 boolean), (r11v4 boolean) binds: [B:37:0x0083, B:39:0x0091, B:91:0x015f, B:90:0x015d] A[DONT_GENERATE, DONT_INLINE]
      0x0160: PHI (r12v2 boolean) = (r12v1 boolean), (r12v1 boolean), (r12v5 boolean), (r12v5 boolean) binds: [B:37:0x0083, B:39:0x0091, B:91:0x015f, B:90:0x015d] A[DONT_GENERATE, DONT_INLINE]
      0x0160: PHI (r13v2 boolean) = (r13v1 boolean), (r13v1 boolean), (r13v5 boolean), (r13v5 boolean) binds: [B:37:0x0083, B:39:0x0091, B:91:0x015f, B:90:0x015d] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static boolean enqueueWorkWithPrerequisites(llIIll1Il1 lliill1il1, List<? extends lI1II1llI1> list, String[] strArr, String str, l1lllll11l l1lllll11lVar) throws CharConversionException, MalformedURLException, PortUnreachableException, CRLException, CloneNotSupportedException {
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4;
        String[] strArr2 = strArr;
        if (android.accounts.utils.I1lllI11II.Ill1lIIlIl(17)) {
            throw new MalformedURLException("C3RRRzNcHA2Z1uGGciZc");
        }
        long jCurrentTimeMillis = System.currentTimeMillis();
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = lliill1il1.getWorkDatabase();
        boolean z5 = strArr2 != null && strArr2.length > 0;
        if (z5) {
            z = true;
            z2 = false;
            z3 = false;
            for (String str2 : strArr2) {
                Il111lI1Il workSpec = workDatabase.workSpecDao().getWorkSpec(str2);
                if (workSpec == null) {
                    l1IlI1IlIl.get().error(TAG, String.format("Prerequisite %s doesn't exist; not enqueuing", str2), new Throwable[0]);
                    if (lIIlI111II.I1lll11llI(1422590190L)) {
                        throw new NegativeArraySizeException("VDoeNmdGZeY");
                    }
                    return false;
                }
                Il1llllI1I il1llllI1I = workSpec.state;
                z &= il1llllI1I == Il1llllI1I.SUCCEEDED;
                if (il1llllI1I == Il1llllI1I.FAILED) {
                    z3 = true;
                } else if (il1llllI1I == Il1llllI1I.CANCELLED) {
                    z2 = true;
                }
            }
        } else {
            z = true;
            z2 = false;
            z3 = false;
        }
        boolean z6 = !TextUtils.isEmpty(str);
        if (z6 && !z5) {
            List<IlI1l1Il1l> workSpecIdAndStatesForName = workDatabase.workSpecDao().getWorkSpecIdAndStatesForName(str);
            if (workSpecIdAndStatesForName.isEmpty()) {
                z4 = false;
            } else if (l1lllll11lVar == l1lllll11l.APPEND || l1lllll11lVar == l1lllll11l.APPEND_OR_REPLACE) {
                I1l11I1I1I i1l11I1I1IDependencyDao = workDatabase.dependencyDao();
                List arrayList = new ArrayList();
                for (IlI1l1Il1l ilI1l1Il1l : workSpecIdAndStatesForName) {
                    if (!i1l11I1I1IDependencyDao.hasDependents(ilI1l1Il1l.id)) {
                        boolean z7 = (ilI1l1Il1l.state == Il1llllI1I.SUCCEEDED) & z;
                        if (ilI1l1Il1l.state == Il1llllI1I.FAILED) {
                            z3 = true;
                        } else if (ilI1l1Il1l.state == Il1llllI1I.CANCELLED) {
                            z2 = true;
                        }
                        arrayList.add(ilI1l1Il1l.id);
                        z = z7;
                    }
                }
                if (l1lllll11lVar == l1lllll11l.APPEND_OR_REPLACE && (z2 || z3)) {
                    Il111I1111 il111I1111WorkSpecDao = workDatabase.workSpecDao();
                    Iterator<IlI1l1Il1l> it = il111I1111WorkSpecDao.getWorkSpecIdAndStatesForName(str).iterator();
                    while (it.hasNext()) {
                        il111I1111WorkSpecDao.delete(it.next().id);
                    }
                    arrayList = Collections.emptyList();
                    z2 = false;
                    z3 = false;
                }
                strArr2 = (String[]) arrayList.toArray(strArr2);
                z5 = strArr2.length > 0;
                z4 = false;
            } else {
                if (l1lllll11lVar == l1lllll11l.KEEP) {
                    for (IlI1l1Il1l ilI1l1Il1l2 : workSpecIdAndStatesForName) {
                        if (ilI1l1Il1l2.state == Il1llllI1I.ENQUEUED || ilI1l1Il1l2.state == Il1llllI1I.RUNNING) {
                            return false;
                        }
                    }
                }
                lIlllIl1I1.forName(str, lliill1il1, false).run();
                Il111I1111 il111I1111WorkSpecDao2 = workDatabase.workSpecDao();
                Iterator<IlI1l1Il1l> it2 = workSpecIdAndStatesForName.iterator();
                while (it2.hasNext()) {
                    il111I1111WorkSpecDao2.delete(it2.next().id);
                }
                z4 = true;
            }
        }
        for (lI1II1llI1 li1ii1lli1 : list) {
            Il111lI1Il workSpec2 = li1ii1lli1.getWorkSpec();
            if (!z5 || z) {
                if (workSpec2.isPeriodic()) {
                    workSpec2.periodStartTime = 0L;
                } else {
                    workSpec2.periodStartTime = jCurrentTimeMillis;
                }
            } else if (z3) {
                workSpec2.state = Il1llllI1I.FAILED;
            } else if (z2) {
                workSpec2.state = Il1llllI1I.CANCELLED;
            } else {
                workSpec2.state = Il1llllI1I.BLOCKED;
            }
            if (Build$VERSION.SDK_INT <= 25) {
                tryDelegateConstrainedWorkSpec(workSpec2);
            }
            if (workSpec2.state == Il1llllI1I.ENQUEUED) {
                z4 = true;
            }
            workDatabase.workSpecDao().insertWorkSpec(workSpec2);
            if (z5) {
                int length = strArr2.length;
                int i = 0;
                while (i < length) {
                    workDatabase.dependencyDao().insertDependency(new ll11lIIlII(li1ii1lli1.getStringId(), strArr2[i]));
                    i++;
                    strArr2 = strArr2;
                    z4 = z4;
                }
            }
            String[] strArr3 = strArr2;
            boolean z8 = z4;
            Iterator<String> it3 = li1ii1lli1.getTags().iterator();
            while (it3.hasNext()) {
                workDatabase.workTagDao().insert(new l1lI1lll1I(it3.next(), li1ii1lli1.getStringId()));
            }
            if (z6) {
                workDatabase.workNameDao().insert(new l1IIll1I1l(str, li1ii1lli1.getStringId()));
            }
            strArr2 = strArr3;
            z4 = z8;
        }
        return z4;
    }

    private static void tryDelegateConstrainedWorkSpec(Il111lI1Il il111lI1Il) throws CharConversionException, CloneNotSupportedException {
        I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.IllllI11lI illllI11lI = il111lI1Il.constraints;
        String str = il111lI1Il.workerClassName;
        if (str.equals(Il11I111ll.class.getName())) {
            return;
        }
        if (illllI11lI.requiresBatteryNotLow() || illllI11lI.requiresStorageNotLow()) {
            Il1IIlI1lI il1IIlI1lI = new Il1IIlI1lI();
            il1IIlI1lI.putAll(il111lI1Il.input).putString(Il11I111ll.ARGUMENT_CLASS_NAME, str);
            il111lI1Il.workerClassName = Il11I111ll.class.getName();
            il111lI1Il.input = il1IIlI1lI.build();
        }
    }

    private static boolean usesScheduler(llIIll1Il1 lliill1il1, String str) throws GeneralSecurityException, NoSuchFieldException, ClassNotFoundException {
        try {
            Class<?> cls = Class.forName(str);
            Iterator<lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.ll11llI1Il> it = lliill1il1.getSchedulers().iterator();
            while (it.hasNext()) {
                if (cls.isAssignableFrom(it.next().getClass())) {
                    if (II1I11IlI1.IlII1Illll("KAhTA2M3ojYA5MxSMTzGK6baGtrc5", 424884857L)) {
                        throw new GeneralSecurityException("IqvQxeni69Auja6b");
                    }
                    return true;
                }
            }
            return false;
        } catch (ClassNotFoundException unused) {
            if (androidx.core.location.IIlIIlIII1.I1lllI1llI(484394509L)) {
                throw new NoSuchFieldException("XEAXwHwFIDowsvAZJI8dP3vEzz");
            }
            return false;
        }
    }
}
