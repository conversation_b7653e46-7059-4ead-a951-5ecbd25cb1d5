package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III111l1l1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import java.util.List;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class I1lllI11II extends I1llIl1lIl<List<l111l1lIIl>> {
    final /* synthetic */ III111l1l1 val$querySpec;
    final /* synthetic */ llIIll1Il1 val$workManager;

    I1lllI11II(llIIll1Il1 lliill1il1, III111l1l1 iII111l1l1) {
        this.val$workManager = lliill1il1;
        this.val$querySpec = iII111l1l1;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public List<l111l1lIIl> runInternal() {
        return Il111lI1Il.WORK_INFO_MAPPER.apply(this.val$workManager.getWorkDatabase().rawWorkInfoDao().getWorkInfoPojos(IIl1IIllIl.workQueryToRawQuery(this.val$querySpec)));
    }
}
