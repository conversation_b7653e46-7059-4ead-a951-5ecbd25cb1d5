package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I11IlIIll1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.io.InvalidClassException;
import java.security.UnrecoverableKeyException;
import java.util.UUID;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.lll1llll1I;

/* loaded from: classes.dex */
class lllll1l1II implements Runnable {
    final /* synthetic */ l1l1IllI11 this$0;
    final /* synthetic */ I11IlIIll1 val$data;
    final /* synthetic */ III1l1lIlI val$future;
    final /* synthetic */ UUID val$id;

    lllll1l1II(l1l1IllI11 l1l1illi11, UUID uuid, I11IlIIll1 i11IlIIll1, III1l1lIlI iII1l1lIlI) {
        this.this$0 = l1l1illi11;
        this.val$id = uuid;
        this.val$data = i11IlIIll1;
        this.val$future = iII1l1lIlI;
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x00a9 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:19:0x00aa  */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void run() throws InvalidClassException, UnrecoverableKeyException {
        Il111lI1Il workSpec;
        if (IIlI1Il1lI.Ill1lIIlIl(3043)) {
            throw new InvalidClassException("BXN6btpOm");
        }
        String string = this.val$id.toString();
        l1IlI1IlIl.get().debug(l1l1IllI11.TAG, String.format("Updating progress for %s (%s)", this.val$id, this.val$data), new Throwable[0]);
        this.this$0.mWorkDatabase.beginTransaction();
        try {
            workSpec = this.this$0.mWorkDatabase.workSpecDao().getWorkSpec(string);
        } finally {
            try {
                this.this$0.mWorkDatabase.endTransaction();
                if (!IIlII1IIIl.I1lllI1llI(1951)) {
                }
            } catch (Throwable th) {
            }
        }
        if (workSpec == null) {
            throw new IllegalStateException("Calls to setProgressAsync() must complete before a ListenableWorker signals completion of work by returning an instance of Result.");
        }
        if (workSpec.state == Il1llllI1I.RUNNING) {
            this.this$0.mWorkDatabase.workProgressDao().insert(new lll1llll1I(string, this.val$data));
        } else {
            l1IlI1IlIl.get().warning(l1l1IllI11.TAG, String.format("Ignoring setProgressAsync(...). WorkSpec (%s) is not in a RUNNING state.", string), new Throwable[0]);
        }
        this.val$future.set(null);
        this.this$0.mWorkDatabase.setTransactionSuccessful();
        this.this$0.mWorkDatabase.endTransaction();
        if (!IIlII1IIIl.I1lllI1llI(1951)) {
            throw new UnrecoverableKeyException("NcQj0V7L1m808ffCiamo4Oz6");
        }
    }
}
