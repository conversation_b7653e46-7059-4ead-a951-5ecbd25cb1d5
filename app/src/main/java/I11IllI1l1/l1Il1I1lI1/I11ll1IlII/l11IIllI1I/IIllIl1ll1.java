package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1ll1I11;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1l1I1llII;
import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import II11I1lll1.l1Il1I1l1I.ll1IlllIII.I1lI1llIll.II1lllllI1;
import android.content.Context;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lI111IIIII.Il111lll1I.I1lIl1lIlI.I1lIl1lIII.Il1ll1IIll;

/* loaded from: classes.dex */
public class IIllIl1ll1 implements Runnable {
    static final String TAG = l1IlI1IlIl.tagWithPrefix("WorkForegroundRunnable");
    final Context mContext;
    final I1l1ll1I11 mForegroundUpdater;
    final III1l1lIlI<Void> mFuture = III1l1lIlI.create();
    final Il1ll1IIll mTaskExecutor;
    final Il111lI1Il mWorkSpec;
    final l1l1I1llII mWorker;

    public IIllIl1ll1(Context context, Il111lI1Il il111lI1Il, l1l1I1llII l1l1i1llii, I1l1ll1I11 i1l1ll1I11, Il1ll1IIll il1ll1IIll) {
        this.mContext = context;
        this.mWorkSpec = il111lI1Il;
        this.mWorker = l1l1i1llii;
        this.mForegroundUpdater = i1l1ll1I11;
        this.mTaskExecutor = il1ll1IIll;
    }

    public IlIIlIllI1<Void> getFuture() {
        return this.mFuture;
    }

    @Override // java.lang.Runnable
    public void run() {
        if (!this.mWorkSpec.expedited || II1lllllI1.isAtLeastS()) {
            this.mFuture.set(null);
            return;
        }
        III1l1lIlI iII1l1lIlICreate = III1l1lIlI.create();
        this.mTaskExecutor.getMainThreadExecutor().execute(new ll11llI1Il(this, iII1l1lIlICreate));
        iII1l1lIlICreate.addListener(new I111IlIl1I(this, iII1l1lIlICreate), this.mTaskExecutor.getMainThreadExecutor());
    }
}
