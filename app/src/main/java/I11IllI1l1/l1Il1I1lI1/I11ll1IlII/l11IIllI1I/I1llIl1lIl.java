package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III111l1l1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import android.media.content.IIl1l1IllI;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import androidx.core.location.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import com.ironsource.mediationsdk.utils.IronSourceConstants;
import java.io.SyncFailedException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CancellationException;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
public abstract class I1llIl1lIl<T> implements Runnable {
    private final III1l1lIlI<T> mFuture = III1l1lIlI.create();

    abstract T runInternal();

    @Override // java.lang.Runnable
    public void run() {
        if (lIlIII1I1l.l11I11I11l(IronSourceConstants.RV_INSTANCE_AVAILABILITY_TRUE)) {
            throw new IllegalMonitorStateException("S2W4mmiEoxEO35MPg");
        }
        try {
            this.mFuture.set(runInternal());
        } catch (Throwable th) {
            this.mFuture.setException(th);
        }
    }

    public IlIIlIllI1<T> getFuture() {
        return this.mFuture;
    }

    public static I1llIl1lIl<List<l111l1lIIl>> forStringIds(llIIll1Il1 lliill1il1, List<String> list) {
        IllI1lIIIl illI1lIIIl = new IllI1lIIIl(lliill1il1, list);
        if (androidx.recyclerview.widget.content.adapter.l1l1IllI11.III111l111("0LUWgmgER", "RVJWMlTBZq8Zgrg1")) {
            throw new CancellationException("5S67Xsk4gfdtWGPgc9XMEnqvGQgKOH");
        }
        return illI1lIIIl;
    }

    public static I1llIl1lIl<l111l1lIIl> forUUID(llIIll1Il1 lliill1il1, UUID uuid) {
        return new I1llllIlII(lliill1il1, uuid);
    }

    public static I1llIl1lIl<List<l111l1lIIl>> forTag(llIIll1Il1 lliill1il1, String str) {
        if (lIIlI111II.IIll1l1lII(3261)) {
            throw new IllegalArgumentException("wj");
        }
        I1Illl11II i1Illl11II = new I1Illl11II(lliill1il1, str);
        if (lIIllIlIl1.Il1IIlI1II(813755852L)) {
            throw new SyncFailedException("Ziqybu5eZ");
        }
        return i1Illl11II;
    }

    public static I1llIl1lIl<List<l111l1lIIl>> forUniqueWork(llIIll1Il1 lliill1il1, String str) {
        return new llll1I1III(lliill1il1, str);
    }

    public static I1llIl1lIl<List<l111l1lIIl>> forWorkQuerySpec(llIIll1Il1 lliill1il1, III111l1l1 iII111l1l1) {
        I1lllI11II i1lllI11II = new I1lllI11II(lliill1il1, iII111l1l1);
        if (IIl1l1IllI.Il1IIlI1II("ntMNA7geKDlTM2jiwIgVX0q", 366784735L)) {
            throw new ExceptionInInitializerError("WkIhki4v4kQDAc0O2q1bwl3OSSZ");
        }
        return i1lllI11II;
    }
}
