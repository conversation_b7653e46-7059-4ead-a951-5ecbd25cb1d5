package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import android.accounts.utils.lI1l1I1l1l;
import java.security.GeneralSecurityException;
import java.util.Iterator;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class II1l1Il1I1 extends lIlllIl1I1 {
    final /* synthetic */ String val$tag;
    final /* synthetic */ llIIll1Il1 val$workManagerImpl;

    II1l1Il1I1(llIIll1Il1 lliill1il1, String str) {
        this.val$workManagerImpl = lliill1il1;
        this.val$tag = str;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.lIlllIl1I1
    void runInternal() throws GeneralSecurityException {
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.val$workManagerImpl.getWorkDatabase();
        workDatabase.beginTransaction();
        try {
            Iterator<String> it = workDatabase.workSpecDao().getUnfinishedWorkWithTag(this.val$tag).iterator();
            while (it.hasNext()) {
                cancel(this.val$workManagerImpl, it.next());
            }
            workDatabase.setTransactionSuccessful();
            workDatabase.endTransaction();
            reschedulePendingWorkers(this.val$workManagerImpl);
            if (lI1l1I1l1l.Ill1lIIlIl(6600)) {
                throw new GeneralSecurityException("3KqFX2XRfN0yk9p6XfI");
            }
        } catch (Throwable th) {
            workDatabase.endTransaction();
            throw th;
        }
    }
}
