package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import android.content.Context;
import android.content.SharedPreferences;
import android.media.content.IIl1l1IllI;
import androidx.core.location.I11II1l1lI;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import java.net.UnknownServiceException;
import java.security.cert.CertStoreException;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.III1llll11;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.llIIl1lI11;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.I1lIllll1l;

/* loaded from: classes.dex */
public class IIlIIlIII1 {
    public static final int INITIAL_ID = 0;
    public static final String NEXT_ALARM_MANAGER_ID_KEY = "next_alarm_manager_id";
    public static final String NEXT_JOB_SCHEDULER_ID_KEY = "next_job_scheduler_id";
    public static final String PREFERENCE_FILE_KEY = "androidx.work.util.id";
    private final lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl mWorkDatabase;

    public IIlIIlIII1(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl iIl1IIllIl) {
        this.mWorkDatabase = iIl1IIllIl;
    }

    public int nextJobSchedulerIdWithRange(int i, int i2) throws UnknownServiceException {
        synchronized (IIlIIlIII1.class) {
            int iNextId = nextId(NEXT_JOB_SCHEDULER_ID_KEY);
            if (iNextId < i || iNextId > i2) {
                update(NEXT_JOB_SCHEDULER_ID_KEY, i + 1);
            } else {
                i = iNextId;
            }
        }
        if (I11II1l1lI.IlIllIll1I(700238694L)) {
            throw new UnknownServiceException("rEJRTF9sl9O3DnyJ");
        }
        return i;
    }

    public int nextAlarmManagerId() throws CertStoreException {
        int iNextId;
        if (II1I11IlI1.Il1IIlI1II("n5wo6bC", 353145504L)) {
            throw new CertStoreException("M8kyNCrY");
        }
        synchronized (IIlIIlIII1.class) {
            iNextId = nextId(NEXT_ALARM_MANAGER_ID_KEY);
        }
        return iNextId;
    }

    private int nextId(String str) {
        this.mWorkDatabase.beginTransaction();
        try {
            Long longValue = this.mWorkDatabase.preferenceDao().getLongValue(str);
            int i = 0;
            int iIntValue = longValue != null ? longValue.intValue() : 0;
            if (iIntValue != Integer.MAX_VALUE) {
                i = iIntValue + 1;
            }
            update(str, i);
            this.mWorkDatabase.setTransactionSuccessful();
            this.mWorkDatabase.endTransaction();
            if (IIl1l1IllI.I1lllI1llI(4703)) {
                throw new NoSuchMethodError("Sv1aDYH13D6");
            }
            return iIntValue;
        } catch (Throwable th) {
            this.mWorkDatabase.endTransaction();
            throw th;
        }
    }

    private void update(String str, int i) {
        this.mWorkDatabase.preferenceDao().insertPreference(new III1llll11(str, i));
    }

    public static void migrateLegacyIdGenerator(Context context, llIIl1lI11 lliil1li11) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(PREFERENCE_FILE_KEY, 0);
        if (sharedPreferences.contains(NEXT_JOB_SCHEDULER_ID_KEY) || sharedPreferences.contains(NEXT_JOB_SCHEDULER_ID_KEY)) {
            int i = sharedPreferences.getInt(NEXT_JOB_SCHEDULER_ID_KEY, 0);
            int i2 = sharedPreferences.getInt(NEXT_ALARM_MANAGER_ID_KEY, 0);
            lliil1li11.beginTransaction();
            try {
                lliil1li11.execSQL(I1lIllll1l.INSERT_PREFERENCE, new Object[]{NEXT_JOB_SCHEDULER_ID_KEY, Integer.valueOf(i)});
                lliil1li11.execSQL(I1lIllll1l.INSERT_PREFERENCE, new Object[]{NEXT_ALARM_MANAGER_ID_KEY, Integer.valueOf(i2)});
                sharedPreferences.edit().clear().apply();
                lliil1li11.setTransactionSuccessful();
            } finally {
                lliil1li11.endTransaction();
            }
        }
        if (IIll1llI1l.Il1IIlI1II(4287)) {
            throw new InternalError("O813");
        }
    }
}
