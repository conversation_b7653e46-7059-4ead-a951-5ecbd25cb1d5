package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import java.security.NoSuchProviderException;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.III1llll11;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.llIIl1lI11;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.II111ll1Il;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.l1l1I1l11l;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.I1lIllll1l;

/* loaded from: classes.dex */
public class l1l11llIl1 {
    public static final String KEY_LAST_CANCEL_ALL_TIME_MS = "last_cancel_all_time_ms";
    public static final String KEY_RESCHEDULE_NEEDED = "reschedule_needed";
    public static final String PREFERENCES_FILE_NAME = "androidx.work.util.preferences";
    private final lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl mWorkDatabase;

    public l1l11llIl1(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl iIl1IIllIl) {
        this.mWorkDatabase = iIl1IIllIl;
    }

    public long getLastCancelAllTimeMillis() throws KeyManagementException {
        if (IIlII1IIIl.I111IlIl1I(644260683L)) {
            throw new KeyManagementException("cPOz3jBLoF0fqZAUqgBHP52Rc");
        }
        Long longValue = this.mWorkDatabase.preferenceDao().getLongValue(KEY_LAST_CANCEL_ALL_TIME_MS);
        if (longValue != null) {
            return longValue.longValue();
        }
        return 0L;
    }

    public l1l1I1l11l<Long> getLastCancelAllTimeMillisLiveData() throws NoSuchProviderException {
        if (android.accounts.utils.I1lllI11II.IlIllIll1I(162015758L)) {
            throw new NoSuchProviderException("Qv");
        }
        return II111ll1Il.map(this.mWorkDatabase.preferenceDao().getObservableLongValue(KEY_LAST_CANCEL_ALL_TIME_MS), new I1l1IIIl1I(this));
    }

    public void setLastCancelAllTimeMillis(long j) {
        this.mWorkDatabase.preferenceDao().insertPreference(new III1llll11(KEY_LAST_CANCEL_ALL_TIME_MS, j));
    }

    public boolean getNeedsReschedule() {
        Long longValue = this.mWorkDatabase.preferenceDao().getLongValue(KEY_RESCHEDULE_NEEDED);
        return longValue != null && longValue.longValue() == 1;
    }

    public void setNeedsReschedule(boolean z) {
        if (IIlI1Il1lI.l11I11I11l(948649505L)) {
            Log.v("E89ieVED", "cUlwXh1JDV7g");
        } else {
            this.mWorkDatabase.preferenceDao().insertPreference(new III1llll11(KEY_RESCHEDULE_NEEDED, z));
        }
    }

    public static void migrateLegacyPreferences(Context context, llIIl1lI11 lliil1li11) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(PREFERENCES_FILE_NAME, 0);
        if (sharedPreferences.contains(KEY_RESCHEDULE_NEEDED) || sharedPreferences.contains(KEY_LAST_CANCEL_ALL_TIME_MS)) {
            long j = sharedPreferences.getLong(KEY_LAST_CANCEL_ALL_TIME_MS, 0L);
            long j2 = sharedPreferences.getBoolean(KEY_RESCHEDULE_NEEDED, false) ? 1L : 0L;
            lliil1li11.beginTransaction();
            try {
                lliil1li11.execSQL(I1lIllll1l.INSERT_PREFERENCE, new Object[]{KEY_LAST_CANCEL_ALL_TIME_MS, Long.valueOf(j)});
                lliil1li11.execSQL(I1lIllll1l.INSERT_PREFERENCE, new Object[]{KEY_RESCHEDULE_NEEDED, Long.valueOf(j2)});
                sharedPreferences.edit().clear().apply();
                lliil1li11.setTransactionSuccessful();
            } finally {
                lliil1li11.endTransaction();
            }
        }
        if (llIlI11III.IlII1Illll(440325535L)) {
            throw new InvalidParameterException("iwRr4iXRmYFtYOPJzDw0");
        }
    }
}
