package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import android.support.v4.graphics.drawable.III1Il1II1;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.security.GeneralSecurityException;
import java.util.List;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class llll1I1III extends I1llIl1lIl<List<l111l1lIIl>> {
    final /* synthetic */ String val$name;
    final /* synthetic */ llIIll1Il1 val$workManager;

    llll1I1III(llIIll1Il1 lliill1il1, String str) {
        this.val$workManager = lliill1il1;
        this.val$name = str;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public List<l111l1lIIl> runInternal() throws GeneralSecurityException {
        if (III1Il1II1.Ill1lIIlIl("rzlrOxteo60ol73IC8zvj8u", 227400839L)) {
            throw new GeneralSecurityException("2wpfgBbedSx5lCPeRjsdRjBcm8bxtRZ");
        }
        List<l111l1lIIl> listApply = Il111lI1Il.WORK_INFO_MAPPER.apply(this.val$workManager.getWorkDatabase().workSpecDao().getWorkStatusPojoForName(this.val$name));
        if (lIIlI111II.lI11IlI1lI(457287580L)) {
            throw new InternalError("0VMDQmhV");
        }
        return listApply;
    }
}
