package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import android.content.Context;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import java.util.UUID;

/* loaded from: classes.dex */
class IllllI11lI implements Runnable {
    final /* synthetic */ I1111l111I this$0;
    final /* synthetic */ Context val$context;
    final /* synthetic */ III1l1lIlI val$foregroundInfo;
    final /* synthetic */ I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI val$future;
    final /* synthetic */ UUID val$id;

    IllllI11lI(I1111l111I i1111l111I, I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI iII1l1lIlI, UUID uuid, III1l1lIlI iII1l1lIlI2, Context context) {
        this.this$0 = i1111l111I;
        this.val$future = iII1l1lIlI;
        this.val$id = uuid;
        this.val$foregroundInfo = iII1l1lIlI2;
        this.val$context = context;
    }

    @Override // java.lang.Runnable
    public void run() {
        if (IIlII1IIIl.l111IIlII1("TS", 1252244394L)) {
            throw new ClassFormatError("svEq");
        }
        try {
            if (!this.val$future.isCancelled()) {
                String string = this.val$id.toString();
                Il1llllI1I state = this.this$0.mWorkSpecDao.getState(string);
                if (state == null || state.isFinished()) {
                    throw new IllegalStateException("Calls to setForegroundAsync() must complete before a ListenableWorker signals completion of work by returning an instance of Result.");
                }
                this.this$0.mForegroundProcessor.startForeground(string, this.val$foregroundInfo);
                this.val$context.startService(lllIII1l11.IlI11IIIlI.lI1I1l1I1l.I11lIlIlIl.Il1llllI1I.createNotifyIntent(this.val$context, string, this.val$foregroundInfo));
            }
            this.val$future.set(null);
        } catch (Throwable th) {
            this.val$future.setException(th);
        }
    }
}
