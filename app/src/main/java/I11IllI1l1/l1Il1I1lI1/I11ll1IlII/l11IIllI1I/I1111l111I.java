package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1ll1I11;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import android.content.Context;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.io.SyncFailedException;
import java.util.UUID;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111I1111;
import lI111IIIII.Il111lll1I.I1lIl1lIlI.I1lIl1lIII.Il1ll1IIll;
import lllIII1l11.IlI11IIIlI.lI1I1l1I1l.I11lIlIlIl.IIlI1Il1lI;

/* loaded from: classes.dex */
public class I1111l111I implements I1l1ll1I11 {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("WMFgUpdater");
    final IIlI1Il1lI mForegroundProcessor;
    private final Il1ll1IIll mTaskExecutor;
    final Il111I1111 mWorkSpecDao;

    public I1111l111I(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl iIl1IIllIl, IIlI1Il1lI iIlI1Il1lI, Il1ll1IIll il1ll1IIll) {
        this.mForegroundProcessor = iIlI1Il1lI;
        this.mTaskExecutor = il1ll1IIll;
        this.mWorkSpecDao = iIl1IIllIl.workSpecDao();
    }

    @Override // I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1ll1I11
    public IlIIlIllI1<Void> setForegroundAsync(Context context, UUID uuid, III1l1lIlI iII1l1lIlI) throws SyncFailedException {
        I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI iII1l1lIlICreate = I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI.create();
        this.mTaskExecutor.executeOnBackgroundThread(new IllllI11lI(this, iII1l1lIlICreate, uuid, iII1l1lIlI, context));
        if (lIIlI111II.II1lllllII(511320979L)) {
            throw new SyncFailedException("MQ");
        }
        return iII1l1lIlICreate;
    }
}
