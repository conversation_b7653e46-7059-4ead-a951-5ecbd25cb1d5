package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import IllIlIllll.Il11l1l11I.l1I1l11Il1.lllII11ll1.l111I1I1Il;
import lI111IIIII.Il111lll1I.I1lIl1lIlI.I1lIl1lIII.Il1ll1IIll;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.Il1lIl1IIl;
import llIlIllII1.llIIl1l1l1.l1Il1I1l1I.lI11ll11I1.l1l1I1l11l;

/* loaded from: classes.dex */
public class III11l1II1 {
    public static <In, Out> l1l1I1l11l<Out> dedupedMappedLiveDataFor(l1l1I1l11l<In> l1l1i1l11l, l111I1I1Il<In, Out> l111i1i1il, Il1ll1IIll il1ll1IIll) {
        Object obj = new Object();
        Il1lIl1IIl il1lIl1IIl = new Il1lIl1IIl();
        il1lIl1IIl.addSource(l1l1i1l11l, new IlIIlIl1II(il1ll1IIll, obj, l111i1i1il, il1lIl1IIl));
        return il1lIl1IIl;
    }

    private III11l1II1() {
    }
}
