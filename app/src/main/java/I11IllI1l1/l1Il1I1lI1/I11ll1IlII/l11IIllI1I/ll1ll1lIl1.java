package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.content.Context;
import android.os.PowerManager;
import android.os.PowerManager$WakeLock;
import java.util.HashMap;
import java.util.WeakHashMap;

/* loaded from: classes.dex */
public class ll1ll1lIl1 {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("WakeLocks");
    private static final WeakHashMap<PowerManager$WakeLock, String> sWakeLocks = new WeakHashMap<>();

    public static PowerManager$WakeLock newWakeLock(Context context, String str) {
        String str2 = "WorkManager: " + str;
        PowerManager$WakeLock powerManager$WakeLockNewWakeLock = ((PowerManager) context.getApplicationContext().getSystemService("power")).newWakeLock(1, str2);
        WeakHashMap<PowerManager$WakeLock, String> weakHashMap = sWakeLocks;
        synchronized (weakHashMap) {
            weakHashMap.put(powerManager$WakeLockNewWakeLock, str2);
        }
        return powerManager$WakeLockNewWakeLock;
    }

    public static void checkWakeLocks() {
        HashMap map = new HashMap();
        WeakHashMap<PowerManager$WakeLock, String> weakHashMap = sWakeLocks;
        synchronized (weakHashMap) {
            map.putAll(weakHashMap);
        }
        for (PowerManager$WakeLock powerManager$WakeLock : map.keySet()) {
            if (powerManager$WakeLock != null && powerManager$WakeLock.isHeld()) {
                l1IlI1IlIl.get().warning(TAG, String.format("WakeLock held for %s", map.get(powerManager$WakeLock)), new Throwable[0]);
            }
        }
    }

    private ll1ll1lIl1() {
    }
}
