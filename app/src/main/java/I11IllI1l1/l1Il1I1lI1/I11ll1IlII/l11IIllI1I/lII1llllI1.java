package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import java.util.Iterator;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class lII1llllI1 extends lIlllIl1I1 {
    final /* synthetic */ llIIll1Il1 val$workManagerImpl;

    lII1llllI1(llIIll1Il1 lliill1il1) {
        this.val$workManagerImpl = lliill1il1;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.lIlllIl1I1
    void runInternal() {
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.val$workManagerImpl.getWorkDatabase();
        workDatabase.beginTransaction();
        try {
            Iterator<String> it = workDatabase.workSpecDao().getAllUnfinishedWork().iterator();
            while (it.hasNext()) {
                cancel(this.val$workManagerImpl, it.next());
            }
            new l1l11llIl1(this.val$workManagerImpl.getWorkDatabase()).setLastCancelAllTimeMillis(System.currentTimeMillis());
            workDatabase.setTransactionSuccessful();
        } finally {
            workDatabase.endTransaction();
        }
    }
}
