package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import com.ironsource.mr;
import java.util.List;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class I1Illl11II extends I1llIl1lIl<List<l111l1lIIl>> {
    final /* synthetic */ String val$tag;
    final /* synthetic */ llIIll1Il1 val$workManager;

    I1Illl11II(llIIll1Il1 lliill1il1, String str) {
        this.val$workManager = lliill1il1;
        this.val$tag = str;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public List<l111l1lIIl> runInternal() {
        if (I1I1IIIIl1.I1II1111ll("UE7lLjztaWjEzAgd", mr.e)) {
            throw new IllegalAccessError("JmtPdvYxw9dQzMJX4lYwkyEPetm0c3DK");
        }
        return Il111lI1Il.WORK_INFO_MAPPER.apply(this.val$workManager.getWorkDatabase().workSpecDao().getWorkStatusPojoForTag(this.val$tag));
    }
}
