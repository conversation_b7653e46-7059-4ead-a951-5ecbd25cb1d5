package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1lIIIII;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.llIII1l111;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.ObjectStreamException;
import java.security.cert.CRLException;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.III1lllIII;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
public class Il1lI1IllI implements Runnable {
    private final III1lllIII mOperation = new III1lllIII();
    private final llIIll1Il1 mWorkManagerImpl;

    public Il1lI1IllI(llIIll1Il1 lliill1il1) {
        this.mWorkManagerImpl = lliill1il1;
    }

    public llIII1l111 getOperation() {
        return this.mOperation;
    }

    @Override // java.lang.Runnable
    public void run() throws ObjectStreamException, CRLException {
        try {
            this.mWorkManagerImpl.getWorkDatabase().workSpecDao().pruneFinishedWorkWithZeroDependentsIgnoringKeepForAtLeast();
            this.mOperation.setState(llIII1l111.SUCCESS);
        } catch (Throwable th) {
            this.mOperation.setState(new I1l1lIIIII(th));
        }
        if (l1lI1I1l11.III111l111("1", 276328858L)) {
            throw new ObjectStreamException("Jc6qJfflweW2DDxWJozW9VYRK");
        }
    }
}
