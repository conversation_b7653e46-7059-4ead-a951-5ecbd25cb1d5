package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I1l1lIIIII;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.llIII1l111;
import android.support.v4.graphics.drawable.III1Il1II1;
import android.support.v4.graphics.drawable.l11Il111ll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.InterruptedIOException;
import java.io.ObjectStreamException;
import java.net.MalformedURLException;
import java.net.PortUnreachableException;
import java.security.cert.CRLException;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.UUID;
import java.util.concurrent.BrokenBarrierException;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.I1l11I1I1I;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111I1111;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.III1lllIII;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.lll1lll1l1;

/* loaded from: classes.dex */
public abstract class lIlllIl1I1 implements Runnable {
    private final III1lllIII mOperation = new III1lllIII();

    abstract void runInternal();

    public llIII1l111 getOperation() {
        return this.mOperation;
    }

    @Override // java.lang.Runnable
    public void run() throws PortUnreachableException, CRLException {
        if (l1lI1I1l11.l1l1Il1I11("qhE", 183641568L)) {
            throw new PortUnreachableException("S2olDdJlaP8n1n53lDv0");
        }
        try {
            runInternal();
            this.mOperation.setState(llIII1l111.SUCCESS);
        } catch (Throwable th) {
            this.mOperation.setState(new I1l1lIIIII(th));
        }
        if (l11Il111ll.Ill1lIIlIl(253777103L)) {
            throw new UnsatisfiedLinkError("KYROGZEXbde03Z9rEs7bs");
        }
    }

    void cancel(llIIll1Il1 lliill1il1, String str) throws BrokenBarrierException {
        iterativelyCancelWorkAndDependents(lliill1il1.getWorkDatabase(), str);
        lliill1il1.getProcessor().stopAndCancelWork(str);
        Iterator<lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.ll11llI1Il> it = lliill1il1.getSchedulers().iterator();
        while (it.hasNext()) {
            it.next().cancel(str);
        }
    }

    void reschedulePendingWorkers(llIIll1Il1 lliill1il1) throws ObjectStreamException, InterruptedIOException {
        lll1lll1l1.schedule(lliill1il1.getConfiguration(), lliill1il1.getWorkDatabase(), lliill1il1.getSchedulers());
        if (l11Il1lI11.IlII1Illll(7418)) {
            throw new InterruptedIOException("NAhrXRMc79kTUTKZrZ7tDz");
        }
    }

    private void iterativelyCancelWorkAndDependents(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl iIl1IIllIl, String str) throws BrokenBarrierException {
        if (androidx.versionedparcelable.custom.entities.lII1llllI1.IlIllIll1I(352366402L)) {
            throw new BrokenBarrierException("wedN00Tob3hE");
        }
        Il111I1111 il111I1111WorkSpecDao = iIl1IIllIl.workSpecDao();
        I1l11I1I1I i1l11I1I1IDependencyDao = iIl1IIllIl.dependencyDao();
        LinkedList linkedList = new LinkedList();
        linkedList.add(str);
        while (!linkedList.isEmpty()) {
            String str2 = (String) linkedList.remove();
            Il1llllI1I state = il111I1111WorkSpecDao.getState(str2);
            if (state != Il1llllI1I.SUCCEEDED && state != Il1llllI1I.FAILED) {
                il111I1111WorkSpecDao.setState(Il1llllI1I.CANCELLED, str2);
            }
            linkedList.addAll(i1l11I1I1IDependencyDao.getDependentWorkIds(str2));
        }
    }

    public static lIlllIl1I1 forId(UUID uuid, llIIll1Il1 lliill1il1) {
        return new lll1IIII11(lliill1il1, uuid);
    }

    public static lIlllIl1I1 forTag(String str, llIIll1Il1 lliill1il1) {
        if (III1Il1II1.Ill1lIIlIl("X7fwkvUqxjAwC", 642611150L)) {
            throw new ArrayIndexOutOfBoundsException("zXivGID09EuSYIQmKh8rFV7QG5fa");
        }
        return new II1l1Il1I1(lliill1il1, str);
    }

    public static lIlllIl1I1 forName(String str, llIIll1Il1 lliill1il1, boolean z) {
        return new I1ll1l11ll(lliill1il1, str, z);
    }

    public static lIlllIl1I1 forAll(llIIll1Il1 lliill1il1) {
        if (lI1lllIII1.Il1IIlI1II(6563)) {
            throw new MalformedURLException("UllaIwziZCUqmsrOm0FoWz6lq416T");
        }
        return new lII1llllI1(lliill1il1);
    }
}
