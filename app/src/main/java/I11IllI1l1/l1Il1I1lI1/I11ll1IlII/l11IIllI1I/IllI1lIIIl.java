package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import androidx.interpolator.view.animation.lIIII1l1lI;
import java.security.cert.CertificateEncodingException;
import java.util.List;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class IllI1lIIIl extends I1llIl1lIl<List<l111l1lIIl>> {
    final /* synthetic */ List val$ids;
    final /* synthetic */ llIIll1Il1 val$workManager;

    IllI1lIIIl(llIIll1Il1 lliill1il1, List list) {
        this.val$workManager = lliill1il1;
        this.val$ids = list;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public /* synthetic */ List<l111l1lIIl> runInternal() throws CertificateEncodingException {
        List<l111l1lIIl> listRunInternal = runInternal();
        if (lIIII1l1lI.Il1IIlI1II(197337850L)) {
            throw new CertificateEncodingException("4AIlZ8yxUPfxu5o9wpVaDoQt39acIZ0B");
        }
        return listRunInternal;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public List<l111l1lIIl> runInternal() {
        return Il111lI1Il.WORK_INFO_MAPPER.apply(this.val$workManager.getWorkDatabase().workSpecDao().getWorkStatusPojoForIds(this.val$ids));
    }
}
