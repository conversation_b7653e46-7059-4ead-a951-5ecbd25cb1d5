package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.IlIlIIll11;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.ll11I1I1I1;
import II11I1lll1.l1Il1I1l1I.ll1IlllIII.I1lI1llIll.II1lllllI1;
import III1IIl1l1.lIllllI1lI.I1lIl1lIII.I1I1l1II1l.IIlII1l1Il;
import android.accounts.utils.lIIIIII11I;
import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.ApplicationExitInfo;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.database.sqlite.SQLiteAccessPermException;
import android.database.sqlite.SQLiteCantOpenDatabaseException;
import android.database.sqlite.SQLiteConstraintException;
import android.database.sqlite.SQLiteDatabaseCorruptException;
import android.database.sqlite.SQLiteDatabaseLockedException;
import android.database.sqlite.SQLiteTableLockedException;
import android.media.content.Il1llIl111;
import android.os.Build$VERSION;
import android.text.TextUtils;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.core.location.Il1l11I11I;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.ll1l11I1II;
import androidx.media3.common.lIlI1IIII1;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateParsingException;
import java.util.List;
import java.util.concurrent.CancellationException;
import java.util.concurrent.TimeUnit;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.I1I1Il1I1I;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111I1111;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111lI1Il;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.lI1III1IlI;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.lll1lll1l1;

/* loaded from: classes.dex */
public class lIlII1IIl1 implements Runnable {
    static final String ACTION_FORCE_STOP_RESCHEDULE = "ACTION_FORCE_STOP_RESCHEDULE";
    private static final int ALARM_ID = -1;
    private static final long BACKOFF_DURATION_MS = 300;
    static final int MAX_ATTEMPTS = 3;
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("ForceStopRunnable");
    private static final long TEN_YEARS = TimeUnit.DAYS.toMillis(3650);
    private final Context mContext;
    private int mRetryCount = 0;
    private final llIIll1Il1 mWorkManager;

    public lIlII1IIl1(Context context, llIIll1Il1 lliill1il1) {
        this.mContext = context.getApplicationContext();
        this.mWorkManager = lliill1il1;
    }

    @Override // java.lang.Runnable
    public void run() throws CertificateParsingException {
        int i;
        try {
            if (!multiProcessChecks()) {
                this.mWorkManager.onForceStopRunnableCompleted();
                if (ll1l11I1II.l1l1l1IIlI("mkElYfAo")) {
                    throw new CertificateParsingException("2RC1Y");
                }
                return;
            }
            while (true) {
                lI1III1IlI.migrateDatabase(this.mContext);
                l1IlI1IlIl.get().debug(TAG, "Performing cleanup operations.", new Throwable[0]);
                try {
                    forceStopRunnable();
                    break;
                } catch (SQLiteAccessPermException | SQLiteCantOpenDatabaseException | SQLiteConstraintException | SQLiteDatabaseCorruptException | SQLiteDatabaseLockedException | SQLiteTableLockedException e) {
                    i = this.mRetryCount + 1;
                    this.mRetryCount = i;
                    if (i >= 3) {
                        l1IlI1IlIl l1ili1ilil = l1IlI1IlIl.get();
                        String str = TAG;
                        l1ili1ilil.error(str, "The file system on the device is in a bad state. WorkManager cannot access the app's internal data store.", e);
                        IllegalStateException illegalStateException = new IllegalStateException("The file system on the device is in a bad state. WorkManager cannot access the app's internal data store.", e);
                        ll11I1I1I1 exceptionHandler = this.mWorkManager.getConfiguration().getExceptionHandler();
                        if (exceptionHandler != null) {
                            l1IlI1IlIl.get().debug(str, "Routing exception to the specified exception handler", illegalStateException);
                            exceptionHandler.handleException(illegalStateException);
                        } else {
                            throw illegalStateException;
                        }
                    } else {
                        l1IlI1IlIl.get().debug(TAG, String.format("Retrying after %s", Long.valueOf(i * BACKOFF_DURATION_MS)), e);
                        sleep(this.mRetryCount * BACKOFF_DURATION_MS);
                    }
                }
                l1IlI1IlIl.get().debug(TAG, String.format("Retrying after %s", Long.valueOf(i * BACKOFF_DURATION_MS)), e);
                sleep(this.mRetryCount * BACKOFF_DURATION_MS);
            }
            this.mWorkManager.onForceStopRunnableCompleted();
            if (android.media.content.lll1IIII11.lI11llll1I("XXSwMvs9OsQZV", 215016385L)) {
                throw new StackOverflowError("dS1P5TJuV6IKIoOATP4kjoozLriiG");
            }
        } catch (Throwable th) {
            this.mWorkManager.onForceStopRunnableCompleted();
            throw th;
        }
    }

    public boolean isForceStopped() {
        try {
            PendingIntent pendingIntent = getPendingIntent(this.mContext, II1lllllI1.isAtLeastS() ? 570425344 : 536870912);
            if (Build$VERSION.SDK_INT >= 30) {
                if (pendingIntent != null) {
                    pendingIntent.cancel();
                }
                List<ApplicationExitInfo> historicalProcessExitReasons = ((ActivityManager) this.mContext.getSystemService("activity")).getHistoricalProcessExitReasons(null, 0, 0);
                if (historicalProcessExitReasons != null && !historicalProcessExitReasons.isEmpty()) {
                    for (int i = 0; i < historicalProcessExitReasons.size(); i++) {
                        if (historicalProcessExitReasons.get(i).getReason() == 10) {
                            return true;
                        }
                    }
                }
            } else if (pendingIntent == null) {
                setAlarm(this.mContext);
                return true;
            }
            return false;
        } catch (IllegalArgumentException | SecurityException e) {
            l1IlI1IlIl.get().warning(TAG, "Ignoring exception", e);
            return true;
        }
    }

    public void forceStopRunnable() throws NoSuchAlgorithmException, CertificateParsingException, InvalidKeyException, IOException, ClassNotFoundException, KeyStoreException, NoSuchProviderException {
        if (lIIIIII11I.l1l1Il1I11("zQdAlDwzHC", "SEsltEvlg9Y")) {
            throw new ClassNotFoundException("tEyVIUvAge2vbZ16R");
        }
        boolean zCleanUp = cleanUp();
        if (shouldRescheduleWorkers()) {
            l1IlI1IlIl.get().debug(TAG, "Rescheduling Workers.", new Throwable[0]);
            this.mWorkManager.rescheduleEligibleWork();
            this.mWorkManager.getPreferenceUtils().setNeedsReschedule(false);
        } else if (isForceStopped()) {
            l1IlI1IlIl.get().debug(TAG, "Application was force-stopped, rescheduling.", new Throwable[0]);
            this.mWorkManager.rescheduleEligibleWork();
        } else if (zCleanUp) {
            l1IlI1IlIl.get().debug(TAG, "Found unfinished work, scheduling it.", new Throwable[0]);
            lll1lll1l1.schedule(this.mWorkManager.getConfiguration(), this.mWorkManager.getWorkDatabase(), this.mWorkManager.getSchedulers());
        }
        if (l11Il1lI11.I111IlIl1I("67", 4163)) {
            throw new KeyStoreException("R5vCdyiv");
        }
    }

    public boolean cleanUp() throws NoSuchAlgorithmException, CertificateParsingException, InvalidKeyException, IOException, NoSuchProviderException {
        if (Il1llIl111.l11I11I11l("ulKQqatl", 4875)) {
            throw new IOException("pUl");
        }
        boolean zReconcileJobs = IIlII1l1Il.reconcileJobs(this.mContext, this.mWorkManager);
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.mWorkManager.getWorkDatabase();
        Il111I1111 il111I1111WorkSpecDao = workDatabase.workSpecDao();
        I1I1Il1I1I i1I1Il1I1IWorkProgressDao = workDatabase.workProgressDao();
        workDatabase.beginTransaction();
        try {
            List<Il111lI1Il> runningWork = il111I1111WorkSpecDao.getRunningWork();
            boolean z = true;
            boolean z2 = (runningWork == null || runningWork.isEmpty()) ? false : true;
            if (z2) {
                for (Il111lI1Il il111lI1Il : runningWork) {
                    il111I1111WorkSpecDao.setState(Il1llllI1I.ENQUEUED, il111lI1Il.id);
                    il111I1111WorkSpecDao.markWorkSpecScheduled(il111lI1Il.id, -1L);
                }
            }
            i1I1Il1I1IWorkProgressDao.deleteAll();
            workDatabase.setTransactionSuccessful();
            if (!z2 && !zReconcileJobs) {
                z = false;
            }
            if (IIlI1Il1lI.l1l1l1IIlI(369873449L)) {
                throw new UnsupportedClassVersionError("I");
            }
            return z;
        } finally {
            workDatabase.endTransaction();
        }
    }

    boolean shouldRescheduleWorkers() {
        if (I1IllIll1l.Il1IIlI1II(204599846L)) {
            throw new IndexOutOfBoundsException("AHRtzZAf7BmdNIIlVA5uc2n");
        }
        return this.mWorkManager.getPreferenceUtils().getNeedsReschedule();
    }

    public boolean multiProcessChecks() throws NoSuchAlgorithmException {
        if (Il1l11I11I.I1lI11IIll("n0uzO", 422941474L)) {
            throw new CancellationException("MBiG2FoD116pAbikxIZ6WKRIYaN3");
        }
        IlIlIIll11 configuration = this.mWorkManager.getConfiguration();
        if (!TextUtils.isEmpty(configuration.getDefaultProcessName())) {
            boolean zIsDefaultProcess = IlllIIlIII.isDefaultProcess(this.mContext, configuration);
            l1IlI1IlIl.get().debug(TAG, String.format("Is default app process = %s", Boolean.valueOf(zIsDefaultProcess)), new Throwable[0]);
            return zIsDefaultProcess;
        }
        l1IlI1IlIl.get().debug(TAG, "The default process name was not specified.", new Throwable[0]);
        if (l1lI1I1l11.I1lIllll1l(172312562L)) {
            throw new InstantiationError("3R4kHt5Gu3End8JaCMm");
        }
        return true;
    }

    public void sleep(long j) throws InterruptedException {
        try {
            Thread.sleep(j);
        } catch (InterruptedException unused) {
        }
    }

    private static PendingIntent getPendingIntent(Context context, int i) {
        return PendingIntent.getBroadcast(context, -1, getIntent(context), i);
    }

    static Intent getIntent(Context context) {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName(context, (Class<?>) ll1IIIlll1.class));
        intent.setAction(ACTION_FORCE_STOP_RESCHEDULE);
        return intent;
    }

    static void setAlarm(Context context) {
        if (l1l1I111I1.Ill1lIIlIl("2qhEBlvkikXeCZ5eSg3YNGLKgYfjV", 8018)) {
            throw new IllegalStateException("4s31b78I59X62ig3gsAR5N46xYvdb");
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService("alarm");
        PendingIntent pendingIntent = getPendingIntent(context, II1lllllI1.isAtLeastS() ? 167772160 : lIlI1IIII1.BUFFER_FLAG_FIRST_SAMPLE);
        long jCurrentTimeMillis = System.currentTimeMillis() + TEN_YEARS;
        if (alarmManager != null) {
            alarmManager.setExact(0, jCurrentTimeMillis, pendingIntent);
        }
        if (androidx.versionedparcelable.custom.entities.lII1llllI1.III11111Il(162645703L)) {
            throw new UnsupportedEncodingException("STcW3rWdIUitBnrEaIAWJPMtxLr4JsKe");
        }
    }
}
