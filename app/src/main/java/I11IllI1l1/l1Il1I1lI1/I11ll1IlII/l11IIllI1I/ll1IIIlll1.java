package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/* loaded from: classes.dex */
public class ll1IIIlll1 extends BroadcastReceiver {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("ForceStopRunnable$Rcvr");

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        if (intent == null || !"ACTION_FORCE_STOP_RESCHEDULE".equals(intent.getAction())) {
            return;
        }
        l1IlI1IlIl.get().verbose(TAG, "Rescheduling alarm that keeps track of force-stops.", new Throwable[0]);
        lIlII1IIl1.setAlarm(context);
    }
}
