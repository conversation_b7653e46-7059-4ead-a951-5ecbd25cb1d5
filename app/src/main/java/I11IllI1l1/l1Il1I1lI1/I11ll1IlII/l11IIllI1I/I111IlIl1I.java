package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import androidx.constraintlayout.widget.l111Il1lI1;

/* loaded from: classes.dex */
class I111IlIl1I implements Runnable {
    final /* synthetic */ IIllIl1ll1 this$0;
    final /* synthetic */ III1l1lIlI val$foregroundFuture;

    I111IlIl1I(IIllIl1ll1 iIllIl1ll1, III1l1lIlI iII1l1lIlI) {
        this.this$0 = iIllIl1ll1;
        this.val$foregroundFuture = iII1l1lIlI;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // java.lang.Runnable
    public void run() {
        I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III1l1lIlI iII1l1lIlI;
        try {
            iII1l1lIlI = (I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III1l1lIlI) this.val$foregroundFuture.get();
        } catch (Throwable th) {
            this.this$0.mFuture.setException(th);
        }
        if (iII1l1lIlI == null) {
            throw new IllegalStateException(String.format("Worker was marked important (%s) but did not provide ForegroundInfo", this.this$0.mWorkSpec.workerClassName));
        }
        l1IlI1IlIl.get().debug(IIllIl1ll1.TAG, String.format("Updating notification for %s", this.this$0.mWorkSpec.workerClassName), new Throwable[0]);
        this.this$0.mWorker.setRunInForeground(true);
        this.this$0.mFuture.setFuture(this.this$0.mForegroundUpdater.setForegroundAsync(this.this$0.mContext, this.this$0.mWorker.getId(), iII1l1lIlI));
        if (l111Il1lI1.llII1lIIlI("vWabFc77LPUyCNHzqNff1")) {
            throw new NullPointerException("AIVU697cvjB8Qm1G9rDYDoLECWOloY");
        }
    }
}
