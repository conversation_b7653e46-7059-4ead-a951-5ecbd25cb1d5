package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import java.security.KeyException;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il111I1111;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.l11I1Il1ll;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
public class llIllIlll1 implements Runnable {
    private static final String TAG = l1IlI1IlIl.tagWithPrefix("StopWorkRunnable");
    private final boolean mStopInForeground;
    private final llIIll1Il1 mWorkManagerImpl;
    private final String mWorkSpecId;

    public llIllIlll1(llIIll1Il1 lliill1il1, String str, boolean z) {
        this.mWorkManagerImpl = lliill1il1;
        this.mWorkSpecId = str;
        this.mStopInForeground = z;
    }

    @Override // java.lang.Runnable
    public void run() throws KeyException {
        boolean zStopWork;
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.mWorkManagerImpl.getWorkDatabase();
        l11I1Il1ll processor = this.mWorkManagerImpl.getProcessor();
        Il111I1111 il111I1111WorkSpecDao = workDatabase.workSpecDao();
        workDatabase.beginTransaction();
        try {
            boolean zIsEnqueuedInForeground = processor.isEnqueuedInForeground(this.mWorkSpecId);
            if (this.mStopInForeground) {
                zStopWork = this.mWorkManagerImpl.getProcessor().stopForegroundWork(this.mWorkSpecId);
            } else {
                if (!zIsEnqueuedInForeground && il111I1111WorkSpecDao.getState(this.mWorkSpecId) == Il1llllI1I.RUNNING) {
                    il111I1111WorkSpecDao.setState(Il1llllI1I.ENQUEUED, this.mWorkSpecId);
                }
                zStopWork = this.mWorkManagerImpl.getProcessor().stopWork(this.mWorkSpecId);
            }
            l1IlI1IlIl.get().debug(TAG, String.format("StopWorkRunnable for %s; Processor.stopWork = %s", this.mWorkSpecId, Boolean.valueOf(zStopWork)), new Throwable[0]);
            workDatabase.setTransactionSuccessful();
        } finally {
            workDatabase.endTransaction();
        }
    }
}
