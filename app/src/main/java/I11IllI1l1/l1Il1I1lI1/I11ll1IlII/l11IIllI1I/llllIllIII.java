package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.I111lIl11I;
import java.io.NotSerializableException;

/* loaded from: classes.dex */
public class llllIllIII implements Runnable {
    static final String TAG = "WrkTimerRunnable";
    private final String mWorkSpecId;
    private final ll1lIlI11I mWorkTimer;

    llllIllIII(ll1lIlI11I ll1lili11i, String str) {
        this.mWorkTimer = ll1lili11i;
        this.mWorkSpecId = str;
    }

    @Override // java.lang.Runnable
    public void run() throws NotSerializableException {
        if (I111lIl11I.lIIIIlIIl1("MHmPQrCN", 205341150L)) {
            throw new NotSerializableException("bDZ");
        }
        synchronized (this.mWorkTimer.mLock) {
            if (this.mWorkTimer.mTimerMap.remove(this.mWorkSpecId) != null) {
                IllIl11IIl illIl11IIlRemove = this.mWorkTimer.mListeners.remove(this.mWorkSpecId);
                if (illIl11IIlRemove != null) {
                    illIl11IIlRemove.onTimeLimitExceeded(this.mWorkSpecId);
                }
            } else {
                l1IlI1IlIl.get().debug(TAG, String.format("Timer with %s is already marked as complete.", this.mWorkSpecId), new Throwable[0]);
            }
        }
        if (lIIlI111II.l111lI11I1(8952)) {
            throw new NoSuchMethodError("D5NyDInzbLYNRHmEt9p3LroKsu");
        }
    }
}
