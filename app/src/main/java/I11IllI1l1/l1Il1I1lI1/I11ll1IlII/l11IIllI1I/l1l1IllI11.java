package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I11II1II1I.lIlll111II.l11II1IIlI.I1Il111I11.III1l1lIlI;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.I11IlIIll1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l1IlI1IlIl;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.lII1111lIl;
import I1l1IlII1I.IIlIlI11ll.lIllllI1lI.Il11lI1Ill.IlIIlIllI1;
import android.content.Context;
import java.util.UUID;
import lI111IIIII.Il111lll1I.I1lIl1lIlI.I1lIl1lIII.Il1ll1IIll;

/* loaded from: classes.dex */
public class l1l1IllI11 implements lII1111lIl {
    static final String TAG = l1IlI1IlIl.tagWithPrefix("WorkProgressUpdater");
    final Il1ll1IIll mTaskExecutor;
    final lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl mWorkDatabase;

    public l1l1IllI11(lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl iIl1IIllIl, Il1ll1IIll il1ll1IIll) {
        this.mWorkDatabase = iIl1IIllIl;
        this.mTaskExecutor = il1ll1IIll;
    }

    @Override // I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.lII1111lIl
    public IlIIlIllI1<Void> updateProgress(Context context, UUID uuid, I11IlIIll1 i11IlIIll1) {
        III1l1lIlI iII1l1lIlICreate = III1l1lIlI.create();
        this.mTaskExecutor.executeOnBackgroundThread(new lllll1l1II(this, uuid, i11IlIIll1, iII1l1lIlICreate));
        return iII1l1lIlICreate;
    }
}
