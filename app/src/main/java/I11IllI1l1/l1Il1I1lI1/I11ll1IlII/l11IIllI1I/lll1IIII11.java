package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import java.util.UUID;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class lll1IIII11 extends lIlllIl1I1 {
    final /* synthetic */ UUID val$id;
    final /* synthetic */ llIIll1Il1 val$workManagerImpl;

    lll1IIII11(llIIll1Il1 lliill1il1, UUID uuid) {
        this.val$workManagerImpl = lliill1il1;
        this.val$id = uuid;
    }

    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.lIlllIl1I1
    void runInternal() {
        lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.IIl1IIllIl workDatabase = this.val$workManagerImpl.getWorkDatabase();
        workDatabase.beginTransaction();
        try {
            cancel(this.val$workManagerImpl, this.val$id.toString());
            workDatabase.setTransactionSuccessful();
            workDatabase.endTransaction();
            reschedulePendingWorkers(this.val$workManagerImpl);
        } catch (Throwable th) {
            workDatabase.endTransaction();
            throw th;
        }
    }
}
