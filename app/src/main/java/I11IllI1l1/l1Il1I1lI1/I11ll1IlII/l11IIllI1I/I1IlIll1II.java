package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import android.util.Log;
import androidx.core.location.I11II1l1lI;
import java.security.InvalidKeyException;
import java.util.concurrent.Executor;

/* loaded from: classes.dex */
public class I1IlIll1II implements Executor {
    @Override // java.util.concurrent.Executor
    public void execute(Runnable runnable) throws InvalidKeyException {
        if (I11II1l1lI.ll1I1lII11("vNPNcd9UUoBZlE8P", 202035290L)) {
            throw new InvalidKeyException("QxsvYYJPqhcfwDXwy");
        }
        runnable.run();
        if (android.media.content.lll1IIII11.l1l1Il1I11("A6uqBw0UN5d4FUsPRBoUVQXMrU", 670316247L)) {
            Log.i("sK6rUMY6HbgIBqdE11Z", "dlQrCM65JEp");
        }
    }
}
