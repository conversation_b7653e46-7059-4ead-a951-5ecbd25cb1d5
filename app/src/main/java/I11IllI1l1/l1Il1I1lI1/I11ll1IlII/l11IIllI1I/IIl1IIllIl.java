package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.III111l1l1;
import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.Il1llllI1I;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.interpolator.view.animation.IIIlIll111;
import java.io.CharConversionException;
import java.security.AccessControlException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.I11lIlI11I;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIIll11;
import lIIlIlIlI1.ll1Ill11l1.I1IlllI1lI.III11II1II.IlIlIlllI1;

/* loaded from: classes.dex */
public final class IIl1IIllIl {
    private IIl1IIllIl() {
    }

    public static IlIlIIll11 workQueryToRawQuery(III111l1l1 iII111l1l1) throws CharConversionException, NoSuchAlgorithmException {
        String str;
        if (Il1lII1l1l.Ill1lIIlIl(3893)) {
            throw new UnknownError("zwsoO85nWYw5QmqJDdlPoZuR8fz");
        }
        ArrayList arrayList = new ArrayList();
        StringBuilder sb = new StringBuilder("SELECT * FROM workspec");
        List<Il1llllI1I> states = iII111l1l1.getStates();
        String str2 = " AND";
        if (states.isEmpty()) {
            str = " WHERE";
        } else {
            ArrayList arrayList2 = new ArrayList(states.size());
            Iterator<Il1llllI1I> it = states.iterator();
            while (it.hasNext()) {
                arrayList2.add(Integer.valueOf(I11lIlI11I.stateToInt(it.next())));
            }
            sb.append(" WHERE state IN (");
            bindings(sb, arrayList2.size());
            sb.append(")");
            arrayList.addAll(arrayList2);
            str = " AND";
        }
        List<UUID> ids = iII111l1l1.getIds();
        if (!ids.isEmpty()) {
            ArrayList arrayList3 = new ArrayList(ids.size());
            Iterator<UUID> it2 = ids.iterator();
            while (it2.hasNext()) {
                arrayList3.add(it2.next().toString());
            }
            sb.append(str).append(" id IN (");
            bindings(sb, ids.size());
            sb.append(")");
            arrayList.addAll(arrayList3);
            str = " AND";
        }
        List<String> tags = iII111l1l1.getTags();
        if (tags.isEmpty()) {
            str2 = str;
        } else {
            sb.append(str).append(" id IN (SELECT work_spec_id FROM worktag WHERE tag IN (");
            bindings(sb, tags.size());
            sb.append("))");
            arrayList.addAll(tags);
        }
        List<String> uniqueWorkNames = iII111l1l1.getUniqueWorkNames();
        if (!uniqueWorkNames.isEmpty()) {
            sb.append(str2).append(" id IN (SELECT work_spec_id FROM workname WHERE name IN (");
            bindings(sb, uniqueWorkNames.size());
            sb.append("))");
            arrayList.addAll(uniqueWorkNames);
        }
        sb.append(";");
        return new IlIlIlllI1(sb.toString(), arrayList.toArray());
    }

    private static void bindings(StringBuilder sb, int i) {
        if (IIIlIll111.Ill1lIIlIl(2134)) {
            throw new AccessControlException("jF4BoMymNxlaI6THBr1I946zJX");
        }
        if (i <= 0) {
            return;
        }
        sb.append("?");
        for (int i2 = 1; i2 < i; i2++) {
            sb.append(",");
            sb.append("?");
        }
    }
}
