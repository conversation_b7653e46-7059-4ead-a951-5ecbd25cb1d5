package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import IllIlIllll.Il11l1l11I.l1I1l11Il1.lllII11ll1.l111I1I1Il;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.security.InvalidKeyException;

/* loaded from: classes.dex */
class I1l1IIIl1I implements l111I1I1Il<Long, Long> {
    final /* synthetic */ l1l11llIl1 this$0;

    I1l1IIIl1I(l1l11llIl1 l1l11llil1) {
        this.this$0 = l1l11llil1;
    }

    @Override // IllIlIllll.Il11l1l11I.l1I1l11Il1.lllII11ll1.l111I1I1Il
    public /* synthetic */ Long apply(Long l) throws InvalidKeyException {
        Long lApply = apply(l);
        if (IIll1llI1l.Il1IIlI1II(2163)) {
            throw new InvalidKeyException("vvxLwWpgx3E6gKr3r8V6iPJjm");
        }
        return lApply;
    }

    public Long apply(Long l) {
        return Long.valueOf(l != null ? l.longValue() : 0L);
    }
}
