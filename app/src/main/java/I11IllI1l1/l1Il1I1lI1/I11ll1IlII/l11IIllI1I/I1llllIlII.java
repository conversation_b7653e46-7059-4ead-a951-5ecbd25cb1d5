package I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I;

import I1I1l1lI11.II1lll11ll.lll11I1lIl.IIlI1llIIl.l111l1lIIl;
import java.util.UUID;
import l111I1lll1.l1I1I11111.lIl1llI11l.I11IIII1l1.Il1I1lI1ll;
import lll1IIlllI.l1l1lIlI1l.I11llI1I1I.lIIlIIIl1l.llIIll1Il1;

/* loaded from: classes.dex */
class I1llllIlII extends I1llIl1lIl<l111l1lIIl> {
    final /* synthetic */ UUID val$id;
    final /* synthetic */ llIIll1Il1 val$workManager;

    I1llllIlII(llIIll1Il1 lliill1il1, UUID uuid) {
        this.val$workManager = lliill1il1;
        this.val$id = uuid;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Override // I11IllI1l1.l1Il1I1lI1.I11ll1IlII.l11IIllI1I.I1llIl1lIl
    public l111l1lIIl runInternal() {
        Il1I1lI1ll workStatusPojoForId = this.val$workManager.getWorkDatabase().workSpecDao().getWorkStatusPojoForId(this.val$id.toString());
        if (workStatusPojoForId != null) {
            return workStatusPojoForId.toWorkInfo();
        }
        return null;
    }
}
