package I1111lIIl1.l1Il11l1Il.II1llllllI.llIl1IlIll;

import androidx.core.location.Il1l11I11I;
import androidx.interpolator.view.animation.Il11II1llI;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class lIIIIll1II {
    public static Throwable a(lII11II111 lii11ii111) throws ReflectiveOperationException {
        if (Il1l11I11I.IlIIlIllI1(I1I1lI1II1.a(new byte[]{7, 60, 23, 6, 10, 5, 122, 114, 111, 2, 104, 99, 76, 94, 87, 93, 27, 23, 50, 87, 125, 14}), I1I1lI1II1.a(new byte[]{110, 22, 10, 33, 45, 115, 97, 122, 80, 30, 86, 121, 65, 112, 114, 108, 52, 10, 4, 74, 126, 44, 116, 97, 0, 84, 77, 23, 87, 103, 110}))) {
            throw new ReflectiveOperationException(I1I1lI1II1.a(new byte[]{69, 3, 0, 44, 22, 109, 96, 91, 90, 21, 90, 118}));
        }
        Throwable thTryInternalFastPathGetFailure = lii11ii111.tryInternalFastPathGetFailure();
        if (Il11II1llI.I111IlIl1I(4467)) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{111, 32, 3, 84, 6, 12, 4, 83, 74, 44, 9, 68, 114, 92, 68, 86, 36, 50, 80, 6, 74, 6, 97, 87, 96, 103, 124, 82}));
        }
        return thTryInternalFastPathGetFailure;
    }
}
