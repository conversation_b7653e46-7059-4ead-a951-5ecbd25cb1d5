package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import android.util.Log;
import androidx.interpolator.view.animation.IllllI11lI;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Locale;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class llllllllI1 extends ThreadLocal<DateFormat> {
    llllllllI1() {
    }

    @Override // java.lang.ThreadLocal
    public /* synthetic */ DateFormat initialValue() {
        DateFormat dateFormatA = a();
        if (!IllllI11lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{77, 32, 0, 9, 47, 81, 95, 6, 92, 10, 104, 86, 113, 97, 125, 125, 35, 34, 58, 83, 104, 47, 121, 89, 89, 66}), 180250219L)) {
            return dateFormatA;
        }
        Log.e(I1I1lI1II1.a(new byte[]{124, 1, 15, 82, 10, 7, 95, 7, 0, 84, 6, 4, 89, 104, 6, 88, 90, 42, 14, 64, 91, 84, 107, 5, 89, 64}), I1I1lI1II1.a(new byte[]{122, 13, 27, 17, 46, 115, 4, 82, 0, 54, 89, 126, 121, 116, 96, 103, 23}));
        return null;
    }

    protected DateFormat a() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 16, 116, 41, 125, 16, 76, 64, 77, 76, 66, 41, 42, 8, 93, 12, 9, 70, 64, 22, 16, 34, 41, 102, 16}), Locale.US);
        simpleDateFormat.setLenient(false);
        simpleDateFormat.setTimeZone(llIIlI1llI.e);
        return simpleDateFormat;
    }
}
