package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1l1lIllI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II1l11I1Il;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l;
import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.I1Il1I11I1;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.l11Ill1II1;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.llllII11Il;
import android.util.Log;
import androidx.core.location.lIIlI111II;
import java.io.IOException;
import java.net.ProtocolException;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class Il111lllll implements llI1l1111l {
    private final boolean b;

    public Il111lllll(boolean z) {
        this.b = z;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l
    public lIlII1IIl1 intercept(ll1ll1IlIl ll1ll1ilil) throws IOException {
        boolean z;
        II1l11I1Il iI1l11I1IlA;
        lIlII1IIl1 lilii1iil1C;
        Intrinsics.checkNotNullParameter(ll1ll1ilil, I1I1lI1II1.a(new byte[]{84, 12, 3, 12, 12}));
        ll1l11I1II ll1l11i1ii = (ll1l11I1II) ll1ll1ilil;
        llllII11Il llllii11ilD = ll1l11i1ii.d();
        Intrinsics.a(llllii11ilD);
        ll1I1I1lI1 ll1i1i1li1E = ll1l11i1ii.e();
        I1l1lIllI1 a = ll1i1i1li1E.getA();
        long jCurrentTimeMillis = System.currentTimeMillis();
        llllii11ilD.a(ll1i1i1li1E);
        if (!lll1lIlIlI.c(ll1i1i1li1E.getF()) || a == null) {
            llllii11ilD.m();
            z = true;
            iI1l11I1IlA = null;
        } else {
            if (j.a(I1I1lI1II1.a(new byte[]{6, 84, 82, 72, 1, 90, 89, 68, 80, 10, 69, 85}), ll1i1i1li1E.a(I1I1lI1II1.a(new byte[]{114, 28, 18, 0, 1, 65})), true)) {
                llllii11ilD.g();
                iI1l11I1IlA = llllii11ilD.a(true);
                llllii11ilD.i();
                z = false;
            } else {
                z = true;
                iI1l11I1IlA = null;
            }
            if (iI1l11I1IlA == null) {
                if (a.isDuplex()) {
                    llllii11ilD.g();
                    a.writeTo(l11Ill1II1.a(llllii11ilD.a(ll1i1i1li1E, true)));
                } else {
                    I1Il1I11I1 i1Il1I11I1A = l11Ill1II1.a(llllii11ilD.a(ll1i1i1li1E, false));
                    a.writeTo(i1Il1I11I1A);
                    i1Il1I11I1A.close();
                }
            } else {
                llllii11ilD.m();
                if (!llllii11ilD.e().e()) {
                    llllii11ilD.j();
                }
            }
        }
        if (a == null || !a.isDuplex()) {
            llllii11ilD.h();
        }
        if (iI1l11I1IlA == null) {
            iI1l11I1IlA = llllii11ilD.a(false);
            Intrinsics.a(iI1l11I1IlA);
            if (z) {
                llllii11ilD.i();
                z = false;
            }
        }
        lIlII1IIl1 lilii1iil1C2 = iI1l11I1IlA.b(ll1i1i1li1E).b(llllii11ilD.e().getH()).c(jCurrentTimeMillis).d(System.currentTimeMillis()).c();
        int d = lilii1iil1C2.getD();
        if (d == 100) {
            II1l11I1Il iI1l11I1IlA2 = llllii11ilD.a(false);
            Intrinsics.a(iI1l11I1IlA2);
            if (z) {
                llllii11ilD.i();
            }
            lilii1iil1C2 = iI1l11I1IlA2.b(ll1i1i1li1E).b(llllii11ilD.e().getH()).c(jCurrentTimeMillis).d(System.currentTimeMillis()).c();
            d = lilii1iil1C2.getD();
        }
        llllii11ilD.a(lilii1iil1C2);
        if (this.b && d == 101) {
            lilii1iil1C = lilii1iil1C2.o().b(llIIlI1llI.c).c();
        } else {
            lilii1iil1C = lilii1iil1C2.o().b(llllii11ilD.b(lilii1iil1C2)).c();
        }
        if (j.a(I1I1lI1II1.a(new byte[]{84, 8, 13, 22, 7}), lilii1iil1C.a().a(I1I1lI1II1.a(new byte[]{116, 11, 12, 11, 7, 86, 67, 89, 86, 10})), true) || j.a(I1I1lI1II1.a(new byte[]{84, 8, 13, 22, 7}), lIlII1IIl1.a$default(lilii1iil1C, I1I1lI1II1.a(new byte[]{116, 11, 12, 11, 7, 86, 67, 89, 86, 10}), null, 2, null), true)) {
            llllii11ilD.j();
        }
        if (d == 204 || d == 205) {
            l11IIl1I11 a2 = lilii1iil1C.getA();
            if ((a2 == null ? -1L : a2.contentLength()) > 0) {
                StringBuilder sbAppend = new StringBuilder().append(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 48, 54, 53, 66})).append(d).append(I1I1lI1II1.a(new byte[]{23, 12, 3, 1, 66, 91, 88, 94, 20, 30, 85, 66, 90, 25, 119, 90, 12, 21, 7, 92, 68, 76, Byte.MAX_VALUE, 80, 93, 81, 67, 13, 94, 18}));
                l11IIl1I11 a3 = lilii1iil1C.getA();
                throw new ProtocolException(sbAppend.append(a3 != null ? Long.valueOf(a3.contentLength()) : null).toString());
            }
        }
        if (!lIIlI111II.llII1ll111(737882774L)) {
            return lilii1iil1C;
        }
        Log.w(I1I1lI1II1.a(new byte[]{1, 17, 32, 63, 56, 67, 83}), I1I1lI1II1.a(new byte[]{71, 15, 83, 28, 22, 93, 118, 97, 104, 11, 102, 66, 5, 85, 80}));
        return null;
    }
}
