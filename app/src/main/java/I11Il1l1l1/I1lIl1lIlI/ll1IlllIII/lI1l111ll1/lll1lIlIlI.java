package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import android.util.Log;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.llIl1lII1I;
import java.security.cert.CertificateNotYetValidException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\b\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u000b\u0010\fJ\u0015\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0005\u0010\u0006J\u0015\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0007\u0010\u0006J\u0015\u0010\b\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\b\u0010\u0006J\u0015\u0010\t\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\u0006J\u0015\u0010\n\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\n\u0010\u0006"}, d2 = {"LI11Il1l1l1/I1lIl1lIlI/ll1IlllIII/lI1l111ll1/lll1lIlIlI;", "", "", "p0", "", "a", "(Ljava/lang/String;)Z", "c", "e", "d", "b", "<init>", "()V"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public final class lll1lIlIlI {
    public static final lll1lIlIlI INSTANCE = new lll1lIlIlI();

    private lll1lIlIlI() {
    }

    public final boolean a(String p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{90, 1, 22, 13, 13, 81}));
        boolean z = Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 43, 49, 49})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 37, 54, 38, 42})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 49, 54})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{115, 33, 46, 32, 54, 112})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{122, 43, 52, 32}));
        if (!l111Il1lI1.I11II1I1I1(I1I1lI1II1.a(new byte[]{98, 60, 1, 14, 81, 5, 77, 121, 93, 12, 104}), 1154)) {
            return z;
        }
        Log.e(I1I1lI1II1.a(new byte[]{88, 84, 11, 12, 55, 79, 2, 66, 12, 54, 1, 68, 126}), I1I1lI1II1.a(new byte[]{126, 53, 90, 7, 58, 98, 82, 82, 125, 38, 96}));
        return false;
    }

    public static final boolean b(String p0) throws CertificateNotYetValidException {
        if (llIl1lII1I.Ill1lIIlIl(3435)) {
            throw new CertificateNotYetValidException(I1I1lI1II1.a(new byte[]{79, 86, 82, 6, 41}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{90, 1, 22, 13, 13, 81}));
        return Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 43, 49, 49})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 49, 54})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 37, 54, 38, 42})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 54, 45, 53, 50, 116, 99, 115, 113})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{101, 33, 50, 42, 48, 97}));
    }

    public static final boolean c(String p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{90, 1, 22, 13, 13, 81}));
        return (Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{112, 33, 54})) || Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 33, 35, 33}))) ? false : true;
    }

    public final boolean d(String p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{90, 1, 22, 13, 13, 81}));
        return Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 54, 45, 53, 36, 124, 121, 116}));
    }

    public final boolean e(String p0) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{90, 1, 22, 13, 13, 81}));
        return !Intrinsics.a((Object) p0, (Object) I1I1lI1II1.a(new byte[]{103, 54, 45, 53, 36, 124, 121, 116}));
    }
}
