package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II111ll1I1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II111lllll;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIllI1lIlI;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1lIlIlI;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llIlI11III;
import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.lIIllIIlll;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.ll1l1Illl1;
import android.accounts.utils.Ill11ll111;
import android.media.content.lIIlI111II;
import android.util.Log;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.core.location.I1111IIl11;
import androidx.versionedparcelable.custom.entities.II1I11IlI1;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.EOFException;
import java.io.ObjectStreamException;
import java.security.DigestException;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.cert.CertificateExpiredException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import kotlin.collections.ak;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll1II11II1.IIlIllllII.l1IlIllI11.lI1IIIl11I.II1I1lIllI;

/* loaded from: classes.dex */
public final class I1IllIll1l {
    private static final lIIllIIlll a = lIIllIIlll.Companion.a(I1I1lI1II1.a(new byte[]{21, 56}));
    private static final lIIllIIlll b = lIIllIIlll.Companion.a(I1I1lI1II1.a(new byte[]{62, 68, 78, 88}));

    public static final List<llI1lIlIlI> a(llIlI11III llili11iii, String str) throws NoSuchAlgorithmException, DigestException, NoSuchProviderException, CertificateExpiredException {
        Intrinsics.checkNotNullParameter(llili11iii, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{95, 1, 3, 1, 7, 71, 121, 81, 84, 1}));
        ArrayList arrayList = new ArrayList();
        int iA = llili11iii.a();
        int i = 0;
        while (i < iA) {
            int i2 = i + 1;
            if (j.a(str, llili11iii.a(i), true)) {
                try {
                    a(new ll1l1Illl1().a(llili11iii.b(i)), arrayList);
                } catch (EOFException e) {
                    II1I1lIllI.b.a().a(I1I1lI1II1.a(new byte[]{98, 10, 3, 7, 14, 80, 23, 68, 86, 68, 64, 81, 71, 74, 81, 21, 1, 9, 3, 94, 92, 4, 93, 82, 86}), 5, e);
                }
            }
            i = i2;
        }
        return arrayList;
    }

    /* JADX WARN: Code restructure failed: missing block: B:73:0x00c7, code lost:
    
        continue;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x00c7, code lost:
    
        continue;
     */
    /* JADX WARN: Removed duplicated region for block: B:34:0x00d2  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    private static final void a(ll1l1Illl1 ll1l1illl1, List<llI1lIlIlI> list) throws NoSuchAlgorithmException, DigestException, EOFException, NoSuchProviderException {
        String strC;
        int iA;
        if (androidx.interpolator.view.animation.ll1l11I1II.I1II1111ll(I1I1lI1II1.a(new byte[]{103, 42, 9, 83, 46, 82, 95, 97, 74, 82, 89, 90, Byte.MAX_VALUE, 95, 66, 101, 40, 82, 3, 101, 115, 22, 107, 79, 125, 121, 114, 45, 13, 115, 7}))) {
            throw new NoSuchAlgorithmException(I1I1lI1II1.a(new byte[]{6, 35, 55, 61, 9, 64, 68, 66, 97, 19, 87, 72, 13, 115, 98, 65, 33}));
        }
        while (true) {
            String strC2 = null;
            while (true) {
                if (strC2 == null) {
                    a(ll1l1illl1);
                    strC2 = c(ll1l1illl1);
                    if (strC2 == null) {
                        return;
                    }
                }
                boolean zA = a(ll1l1illl1);
                strC = c(ll1l1illl1);
                if (strC == null) {
                    if (ll1l1illl1.g()) {
                        list.add(new llI1lIlIlI(strC2, ak.b()));
                        if (lII1llllI1.l111l1I1Il(I1I1lI1II1.a(new byte[]{98, 86, 85, 31, 83, 112, 71, 117, 10, 52, 90, 118, 122, 119, 103, 123, 56, 53, 18, 97, 86, 51, 3, 0, 66, 102, 92, 40, 32, 72}), 181347944L)) {
                            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{70, 52, 49, 45, 83, 120, Byte.MAX_VALUE, 104, 99, 10, 83, 66, 82, 0}));
                        }
                        return;
                    }
                    return;
                }
                iA = llIIlI1llI.a(ll1l1illl1, (byte) 61);
                boolean zA2 = a(ll1l1illl1);
                if (zA || (!zA2 && !ll1l1illl1.g())) {
                    LinkedHashMap linkedHashMap = new LinkedHashMap();
                    int iA2 = iA + llIIlI1llI.a(ll1l1illl1, (byte) 61);
                    while (true) {
                        if (strC == null) {
                            strC = c(ll1l1illl1);
                            if (a(ll1l1illl1)) {
                                break;
                            }
                            iA2 = llIIlI1llI.a(ll1l1illl1, (byte) 61);
                            if (iA2 != 0) {
                                break;
                            }
                            if (iA2 > 1 || a(ll1l1illl1)) {
                                return;
                            }
                            String strB = a(ll1l1illl1, (byte) 34) ? b(ll1l1illl1) : c(ll1l1illl1);
                            if (strB == null) {
                                if (lIIlI111II.I1Ill1lIII(163447658L)) {
                                    throw new UnknownError(I1I1lI1II1.a(new byte[]{92}));
                                }
                                return;
                            } else if (((String) linkedHashMap.put(strC, strB)) != null) {
                                if (II1I11IlI1.I1lllI1llI(2019)) {
                                    throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{71, 21, 87, 82, 39, 96, 99, 104, 79, 42, 99, 9, 94}));
                                }
                                return;
                            } else if (!a(ll1l1illl1) && !ll1l1illl1.g()) {
                                return;
                            } else {
                                strC = null;
                            }
                        } else if (iA2 != 0) {
                        }
                    }
                    list.add(new llI1lIlIlI(strC2, linkedHashMap));
                    strC2 = strC;
                }
            }
            Map mapSingletonMap = Collections.singletonMap(null, Intrinsics.a(strC, (Object) j.a((CharSequence) I1I1lI1II1.a(new byte[]{10}), iA)));
            Intrinsics.checkNotNullExpressionValue(mapSingletonMap, I1I1lI1II1.a(new byte[]{68, 13, 12, 2, 14, 80, 67, 95, 87, 41, 81, 64, 9, 106, 64, 71, 11, 15, 5, 30, 16, 50, 71, 71, -47, -74, -111, 0, 15, 18, 28, 67, 21, 89, 64, 75, 16, 80, 71, 85, 88, 16, 24, 85, 68, 122, 91, 64, 12, 21, 75, 27}));
            list.add(new llI1lIlIlI(strC2, mapSingletonMap));
        }
    }

    private static final boolean a(ll1l1Illl1 ll1l1illl1) throws NoSuchAlgorithmException, EOFException {
        if (androidx.versionedparcelable.custom.entities.lIIlI111II.l11I1Ill11(272)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{113, 53, 58, 2, 33, 92, 95, 97, 92, 55, 97, 6, 79, 78, 7, 97, 11, 36, 36, 67, 93, 45, 88, 109, 4, 110, 78, 32, 82, 106}));
        }
        boolean z = false;
        while (!ll1l1illl1.g()) {
            byte bD = ll1l1illl1.d(0L);
            boolean z2 = true;
            if (bD != 44) {
                if (bD != 32 && bD != 9) {
                    z2 = false;
                }
                if (!z2) {
                    break;
                }
                ll1l1illl1.k();
            } else {
                ll1l1illl1.k();
                z = true;
            }
        }
        return z;
    }

    private static final boolean a(ll1l1Illl1 ll1l1illl1, byte b2) {
        return !ll1l1illl1.g() && ll1l1illl1.d(0L) == b2;
    }

    private static final String b(ll1l1Illl1 ll1l1illl1) throws NoSuchAlgorithmException, DigestException, EOFException, InvalidAlgorithmParameterException {
        if (android.support.v4.graphics.drawable.lIIlI111II.lIll1IIl11(1472672997L)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{114, 40, 26, 51, 87, 13, 92, 124, 84, 41}));
        }
        if (!(ll1l1illl1.k() == 34)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{113, 5, 11, 9, 7, 81, 23, 66, 92, 21, 69, 89, 71, 92, 89, 80, 12, 21, 76}).toString());
        }
        ll1l1Illl1 ll1l1illl12 = new ll1l1Illl1();
        while (true) {
            long jC = ll1l1illl1.c(a);
            if (jC == -1) {
                return null;
            }
            if (ll1l1illl1.d(jC) == 34) {
                ll1l1illl12.a(ll1l1illl1, jC);
                ll1l1illl1.k();
                return ll1l1illl12.s();
            }
            if (ll1l1illl1.a() == jC + 1) {
                if (I1111IIl11.l11I11I11l(431988907L)) {
                    throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{98}));
                }
                return null;
            }
            ll1l1illl12.a(ll1l1illl1, jC);
            ll1l1illl1.k();
            ll1l1illl12.a(ll1l1illl1, 1L);
        }
    }

    private static final String c(ll1l1Illl1 ll1l1illl1) throws NoSuchAlgorithmException, DigestException {
        if (Ill11ll111.III111l111(I1I1lI1II1.a(new byte[]{6, 38, 19, 47, 37, 76, 0, 90, 122, 0, 97, 106, 92, 86, 1, 102, 24}), 1318993157L)) {
            Log.i(I1I1lI1II1.a(new byte[]{93, 14, 59, 82, 52, 86, 113, 126, 75, 5, 7, 99, 109, 105, 68, 100, 40}), I1I1lI1II1.a(new byte[]{109, 14, 10, 4, 35, 68, 121, 83, 109}));
            return null;
        }
        long jC = ll1l1illl1.c(b);
        if (jC == -1) {
            jC = ll1l1illl1.a();
        }
        if (jC != 0) {
            return ll1l1illl1.f(jC);
        }
        return null;
    }

    public static final void a(lIllI1lIlI lilli1lili, II111lllll iI111lllll, llIlI11III llili11iii) {
        Intrinsics.checkNotNullParameter(lilli1lili, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        Intrinsics.checkNotNullParameter(iI111lllll, I1I1lI1II1.a(new byte[]{66, 22, 14}));
        Intrinsics.checkNotNullParameter(llili11iii, I1I1lI1II1.a(new byte[]{95, 1, 3, 1, 7, 71, 68}));
        if (lilli1lili == lIllI1lIlI.b) {
            if (androidx.core.location.lIIlI111II.lIll1IIl11(164889618L)) {
                throw new IllegalThreadStateException(I1I1lI1II1.a(new byte[]{117, 3, 58, 81, 20, 65, 5, 113, 96, 39, 74, 118, 86, 117, 88, 71, 4, 56, 1, 80, 84, 40, 114, 84, 125, 2}));
            }
            return;
        }
        List<II111ll1I1> listA = II111ll1I1.a.a(iI111lllll, llili11iii);
        if (listA.isEmpty()) {
            return;
        }
        lilli1lili.a(iI111lllll, listA);
        if (l111Il1lI1.Il1IIlI1II(164673248L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{126, 18, 14, 31, 59, 7, 77, 126, 96, 28, 64, 82, 0, 78}));
        }
    }

    public static final boolean a(lIlII1IIl1 lilii1iil1) throws ObjectStreamException {
        Intrinsics.checkNotNullParameter(lilii1iil1, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        if (Intrinsics.a((Object) lilii1iil1.a().getF(), (Object) I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 33, 35, 33}))) {
            return false;
        }
        int d = lilii1iil1.getD();
        if ((d >= 100 && d < 200) || d == 204 || d == 304) {
            return llIIlI1llI.a(lilii1iil1) != -1 || j.a(I1I1lI1II1.a(new byte[]{84, 12, 23, 11, 9, 80, 83}), lIlII1IIl1.a$default(lilii1iil1, I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 83, 82, 66, 20, 33, 94, 83, 90, 93, 93, 91, 5}), null, 2, null), true);
        }
        if (androidx.constraintlayout.widget.lIIlI111II.lIll1IIl11(1116909653L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{122}));
        }
        return true;
    }
}
