package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import java.io.NotActiveException;
import java.text.DateFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import kotlin.Unit;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class llIIII1IlI {
    private static final llllllllI1 a = new llllllllI1();
    private static final String[] b;
    private static final DateFormat[] c;

    static {
        String[] strArr = {I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 16, 116, 41, 125, 16, 76, 64, 77, 76, 66, 41, 42, 8, 93, 12, 9, 70, 64, 22, 77, 31, 30}), I1I1lI1II1.a(new byte[]{114, 33, 39, 32, 78, 21, 83, 84, 20, 41, 125, 125, 24, 64, 77, 21, 42, 41, 88, 95, 93, 91, 64, 70, 19, 76, 77, 31}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 47, 120, 122, 16, 93, 68, 120, 120, 15, 84, 89, 15, 17, 18, 66, 75, 73, 24, 74}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 29, 116, 41, 125, 29, 76, 64, 77, 76, 66, 41, 42, 8, 93, 12, 9, 70, 64, 22, 77}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 29, 116, 41, 125, 29, 76, 64, 77, 76, 66, 41, 42, 31, 93, 12, 30, 70, 64, 22, 77}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 16, 116, 41, 125, 16, 76, 64, 20, 125, 42, 91, 15, 95, 10, 18, 64, 21, 73}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 6, 81, 26, 125, 116, 41, 29, 73, 76, 64, 77, 21, 42, 41, 88, 95, 93, 91, 64, 70, 19, 76}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 6, 81, 23, 125, 116, 41, 16, 73, 76, 64, 77, 21, 42, 41, 88, 95, 93, 91, 64, 70, 19, 76}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 6, 81, 26, 125, 116, 41, 29, 73, 76, 64, 77, 21, 42, 41, 79, 95, 93, 76, 64, 70, 19, 76}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 6, 81, 26, 125, 116, 41, 29, 73, 76, 25, 124, 125, 88, 12, 15, 8, 67, 18, 19, 79}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 6, 81, 23, 125, 116, 41, 16, 73, 76, 25, 124, 125, 88, 12, 15, 8, 67, 18, 19, 79}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 6, 81, 26, 125, 116, 41, 29, 73, 76, 25, 124, 125, 88, 12, 15, 8, 67, 18, 19, 79}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 6, 81, 26, 125, 116, 41, 29, 73, 76, 64, 77, 21, 42, 41, 88, 95, 93, 91, 64, 70, 19, 76}), I1I1lI1II1.a(new byte[]{114, 33, 39, 73, 66, 81, 83, 29, 116, 41, 29, 73, 76, 64, 77, 21, 42, 41, 88, 95, 93, 91, 64, 70, 19, 76}), I1I1lI1II1.a(new byte[]{114, 33, 39, 69, 47, 120, 122, 16, 93, 68, 73, 73, 76, 64, 20, 125, 42, 91, 15, 95, 10, 18, 64, 21, 73})};
        b = strArr;
        c = new DateFormat[strArr.length];
    }

    public static final Date a(String str) throws NotActiveException {
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        if (str.length() == 0) {
            return null;
        }
        ParsePosition parsePosition = new ParsePosition(0);
        Date date = a.get().parse(str, parsePosition);
        if (parsePosition.getIndex() == str.length()) {
            return date;
        }
        String[] strArr = b;
        synchronized (strArr) {
            int length = strArr.length;
            int i = 0;
            while (i < length) {
                int i2 = i + 1;
                DateFormat[] dateFormatArr = c;
                SimpleDateFormat simpleDateFormat = dateFormatArr[i];
                if (simpleDateFormat == null) {
                    SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat(b[i], Locale.US);
                    simpleDateFormat2.setTimeZone(llIIlI1llI.e);
                    simpleDateFormat = simpleDateFormat2;
                    dateFormatArr[i] = simpleDateFormat;
                }
                parsePosition.setIndex(0);
                Date date2 = simpleDateFormat.parse(str, parsePosition);
                if (parsePosition.getIndex() != 0) {
                    if (androidx.interpolator.view.animation.ll1l11I1II.I1lllI1llI(212006623L)) {
                        throw new NotActiveException(I1I1lI1II1.a(new byte[]{120, 50, 90, 19, 9, 101, 1, 106, 91}));
                    }
                    return date2;
                }
                i = i2;
            }
            Unit unit = Unit.INSTANCE;
            return null;
        }
    }

    public static final String a(Date date) {
        Intrinsics.checkNotNullParameter(date, I1I1lI1II1.a(new byte[]{11, 16, 10, 12, 17, 11}));
        String str = a.get().format(date);
        Intrinsics.checkNotNullExpressionValue(str, I1I1lI1II1.a(new byte[]{100, 48, 35, 43, 38, 116, 101, 116, 102, 32, 113, 100, 112, 102, 114, 122, 48, 44, 35, 102, 30, 6, 86, 65, 27, 31, 25, 3, 11, 64, 90, 2, 67, 76, 22, 13, 11, 70, 30}));
        return str;
    }
}
