package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II111lllll;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import java.net.Proxy$Type;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u000e\u0010\u000fJ\u001f\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0007¢\u0006\u0004\b\u0007\u0010\bJ\u001f\u0010\n\u001a\u00020\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0002¢\u0006\u0004\b\n\u0010\u000bJ\u0017\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\fH\u0007¢\u0006\u0004\b\u0007\u0010\r"}, d2 = {"LI11Il1l1l1/I1lIl1lIlI/ll1IlllIII/lI1l111ll1/l1IIll1I1l;", "", "LII1I1Il1ll/l1ll1lI1l1/l11I1lIIl1/lIllllI1lI/ll1I1I1lI1;", "p0", "Ljava/net/Proxy$Type;", "p1", "", "a", "(LII1I1Il1ll/l1ll1lI1l1/l11I1lIIl1/lIllllI1lI/ll1I1I1lI1;Ljava/net/Proxy$Type;)Ljava/lang/String;", "", "b", "(LII1I1Il1ll/l1ll1lI1l1/l11I1lIIl1/lIllllI1lI/ll1I1I1lI1;Ljava/net/Proxy$Type;)Z", "LII1I1Il1ll/l1ll1lI1l1/l11I1lIIl1/lIllllI1lI/II111lllll;", "(LII1I1Il1ll/l1ll1lI1l1/l11I1lIIl1/lIllllI1lI/II111lllll;)Ljava/lang/String;", "<init>", "()V"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public final class l1IIll1I1l {
    public static final l1IIll1I1l INSTANCE = new l1IIll1I1l();

    private l1IIll1I1l() {
    }

    public final String a(ll1I1I1lI1 p0, Proxy$Type p1) {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67}));
        Intrinsics.checkNotNullParameter(p1, I1I1lI1II1.a(new byte[]{71, 22, 13, 29, 27, 97, 78, 64, 92}));
        StringBuilder sb = new StringBuilder();
        sb.append(p0.getF());
        sb.append(' ');
        l1IIll1I1l l1iill1i1l = INSTANCE;
        if (l1iill1i1l.b(p0, p1)) {
            sb.append(p0.a());
        } else {
            sb.append(l1iill1i1l.a(p0.a()));
        }
        sb.append(I1I1lI1II1.a(new byte[]{23, 44, 54, 49, 50, 26, 6, 30, 8}));
        String string = sb.toString();
        Intrinsics.checkNotNullExpressionValue(string, I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 117, 69, 80, 8, 84, 85, 71, 17, 29, 27, 3, 17, 18, 94, 73, 73, 81, 64, 90, 90, 83, 0, 22, 115, 84, 23, 94, 11, 12, 76, 76, 65, 88, 99, 77, 22, 89, 94, 82, 17, 29}));
        return string;
    }

    private final boolean b(ll1I1I1lI1 p0, Proxy$Type p1) {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{100, 44, 9, 80, 87, 81}), 590355808L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{3, 38, 85, 45, 39, 67, 66, 87, 107, 38, 97, 85, 88, 116, 94, 76, 52}));
        }
        return !p0.f() && p1 == Proxy$Type.HTTP;
    }

    public final String a(II111lllll p0) throws InvalidKeyException, InvalidAlgorithmParameterException {
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{66, 22, 14}));
        String strJ = p0.j();
        String strL = p0.l();
        return strL != null ? strJ + '?' + ((Object) strL) : strJ;
    }
}
