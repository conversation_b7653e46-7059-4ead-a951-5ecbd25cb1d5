package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II1l11I1Il;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.IIlIllIl1l;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.lll11lI1ll;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.II1IIl1I1I;
import java.io.IOException;
import kotlin.Metadata;

@Metadata(d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u0000 \u001c2\u00020\u0001:\u0001\u001cJ\b\u0010\u0006\u001a\u00020\u0007H&J\u0018\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH&J\b\u0010\u000e\u001a\u00020\u0007H&J\b\u0010\u000f\u001a\u00020\u0007H&J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H&J\u0012\u0010\u0014\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0016\u001a\u00020\u0017H&J\u0010\u0010\u0018\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\u0013H&J\b\u0010\u0019\u001a\u00020\u001aH&J\u0010\u0010\u001b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000bH&R\u0012\u0010\u0002\u001a\u00020\u0003X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005¨\u0006\u001d"}, d2 = {"Lokhttp3/internal/http/ExchangeCodec;", "", "connection", "Lokhttp3/internal/connection/RealConnection;", "getConnection", "()Lokhttp3/internal/connection/RealConnection;", "cancel", "", "createRequestBody", "Lokio/Sink;", "request", "Lokhttp3/Request;", "contentLength", "", "finishRequest", "flushRequest", "openResponseBodySource", "Lokio/Source;", "response", "Lokhttp3/Response;", "readResponseHeaders", "Lokhttp3/Response$Builder;", "expectContinue", "", "reportedContentLength", "trailers", "Lokhttp3/Headers;", "writeRequestHeaders", "Companion", "okhttp"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public interface Il1IllIl1I {
    public static final lIIllIlIl1 a = lIIllIlIl1.a;

    long a(lIlII1IIl1 lilii1iil1) throws IOException;

    II1l11I1Il a(boolean z) throws IOException;

    IIlIllIl1l a(ll1I1I1lI1 ll1i1i1li1, long j) throws IOException;

    II1IIl1I1I a();

    void a(ll1I1I1lI1 ll1i1i1li1) throws IOException;

    lll11lI1ll b(lIlII1IIl1 lilii1iil1) throws IOException;

    void b() throws IOException;

    void c() throws IOException;

    void d();
}
