package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1IIl111I1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1l1lIllI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II111ll1I1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II1l11I1Il;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II1lllllII;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIllI1lIlI;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l;
import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.l11Ill1II1;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.lII111lllI;
import android.support.v4.graphics.drawable.IllllI11Il;
import android.support.v4.graphics.drawable.lI1lllIII1;
import androidx.core.location.l1l1I111I1;
import androidx.versionedparcelable.custom.entities.lIlIl1Il11;
import com.ironsource.llI1l11IlI;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.AccessControlException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertPathValidatorException;
import java.util.List;
import kotlin.collections.q;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class lIII1lII11 implements llI1l1111l {
    private final lIllI1lIlI b;

    public lIII1lII11(lIllI1lIlI lilli1lili) {
        Intrinsics.checkNotNullParameter(lilli1lili, I1I1lI1II1.a(new byte[]{84, 11, 13, 14, 11, 80, 125, 81, 75}));
        this.b = lilli1lili;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l
    public lIlII1IIl1 intercept(ll1ll1IlIl ll1ll1ilil) throws UnrecoverableKeyException, IOException, CertPathValidatorException {
        l11IIl1I11 a;
        if (lI1lllIII1.l11I11I11l(I1I1lI1II1.a(new byte[]{124, 0, 17, 15, 16, 77, 66, 91, 109, 41, 0}), 457049658L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{71, 38, 36, 22, 15, 2, 0, 91, 74, 55, 119, 67}));
        }
        Intrinsics.checkNotNullParameter(ll1ll1ilil, I1I1lI1II1.a(new byte[]{84, 12, 3, 12, 12}));
        ll1I1I1lI1 ll1i1i1li1A = ll1ll1ilil.a();
        I1IIl111I1 i1IIl111I1G = ll1i1i1li1A.g();
        I1l1lIllI1 a2 = ll1i1i1li1A.getA();
        if (a2 != null) {
            II1lllllII iI1lllllIIContentType = a2.contentType();
            if (iI1lllllIIContentType != null) {
                i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 109, 29, 64, 85}), iI1lllllIIContentType.getC());
            }
            long jContentLength = a2.contentLength();
            if (jContentLength != -1) {
                i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 117, 1, 94, 87, 65, 81}), String.valueOf(jContentLength));
                i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 83, 82, 66, 20, 33, 94, 83, 90, 93, 93, 91, 5}));
            } else {
                i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 83, 82, 66, 20, 33, 94, 83, 90, 93, 93, 91, 5}), I1I1lI1II1.a(new byte[]{84, 12, 23, 11, 9, 80, 83}));
                i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 117, 1, 94, 87, 65, 81}));
            }
        }
        boolean z = false;
        if (ll1i1i1li1A.a(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 11, 17, 17})) == null) {
            i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 11, 17, 17}), llIIlI1llI.a(ll1i1i1li1A.a(), false, 1, (Object) null));
        }
        if (ll1i1i1li1A.a(I1I1lI1II1.a(new byte[]{116, 11, 12, 11, 7, 86, 67, 89, 86, 10})) == null) {
            i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{116, 11, 12, 11, 7, 86, 67, 89, 86, 10}), I1I1lI1II1.a(new byte[]{124, 1, 7, 21, 79, 116, 91, 89, 79, 1}));
        }
        if (ll1i1i1li1A.a(I1I1lI1II1.a(new byte[]{118, 7, 1, 0, 18, 65, 26, 117, 87, 7, 95, 84, 92, 87, 83})) == null && ll1i1i1li1A.a(I1I1lI1II1.a(new byte[]{101, 5, 12, 2, 7})) == null) {
            i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{118, 7, 1, 0, 18, 65, 26, 117, 87, 7, 95, 84, 92, 87, 83}), I1I1lI1II1.a(new byte[]{80, 30, 11, 21}));
            z = true;
        }
        List<II111ll1I1> listA = this.b.a(ll1i1i1li1A.a());
        if (!listA.isEmpty()) {
            i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{116, 11, 13, 14, 11, 80}), a(listA));
        }
        if (ll1i1i1li1A.a(I1I1lI1II1.a(new byte[]{98, 23, 7, 23, 79, 116, 80, 85, 87, 16})) == null) {
            i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{98, 23, 7, 23, 79, 116, 80, 85, 87, 16}), I1I1lI1II1.a(new byte[]{88, 15, 10, 17, 22, 69, 24, 4, 23, 85, 0, 30, 5}));
        }
        lIlII1IIl1 lilii1iil1A = ll1ll1ilil.a(i1IIl111I1G.c());
        I1IllIll1l.a(this.b, ll1i1i1li1A.a(), lilii1iil1A.f());
        II1l11I1Il iI1l11I1IlB = lilii1iil1A.o().b(ll1i1i1li1A);
        if (z && j.a(I1I1lI1II1.a(new byte[]{80, 30, 11, 21}), lIlII1IIl1.a$default(lilii1iil1A, I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 124, 10, 83, 95, 81, 80, 90, 82}), null, 2, null), true) && I1IllIll1l.a(lilii1iil1A) && (a = lilii1iil1A.getA()) != null) {
            lII111lllI lii111llli = new lII111lllI(a.source());
            iI1l11I1IlB.a(lilii1iil1A.f().b().b(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 124, 10, 83, 95, 81, 80, 90, 82})).b(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 117, 1, 94, 87, 65, 81})).b());
            iI1l11I1IlB.b(new lIII1IlIlI(lIlII1IIl1.a$default(lilii1iil1A, I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 109, 29, 64, 85}), null, 2, null), -1L, l11Ill1II1.a(lii111llli)));
        }
        lIlII1IIl1 lilii1iil1C = iI1l11I1IlB.c();
        if (IllllI11Il.ll1I1lII11(I1I1lI1II1.a(new byte[]{111, 3, 45, 43, 6, 94}))) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{117, 23, 90, 80, 52, 80, 124, 0, 97, 20}));
        }
        return lilii1iil1C;
    }

    private final String a(List<II111ll1I1> list) throws UnsupportedEncodingException {
        if (lIlIl1Il11.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{95, 5, 41, 50}), 167371523L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{91, 17, 43, 38, 8, 126, 125, 5, 105, 50, 93, 92, 97, 82, 85, 122, 27, 23, 55, 113, 92, 59, 89, 88, 68, 76, 90, 13, 5, 2}));
        }
        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (Object obj : list) {
            int i2 = i + 1;
            if (i < 0) {
                q.c();
            }
            II111ll1I1 iI111ll1I1 = (II111ll1I1) obj;
            if (i > 0) {
                sb.append(I1I1lI1II1.a(new byte[]{12, 68}));
            }
            sb.append(iI111ll1I1.getB()).append(llI1l11IlI.T).append(iI111ll1I1.b());
            i = i2;
        }
        String string = sb.toString();
        Intrinsics.checkNotNullExpressionValue(string, I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 117, 69, 80, 8, 84, 85, 71, 17, 29, 27, 3, 17, 18, 94, 73, 73, 81, 64, 90, 90, 83, 0, 22, 115, 84, 23, 94, 11, 12, 76, 76, 65, 88, 99, 77, 22, 89, 94, 82, 17, 29}));
        if (l1l1I111I1.I111IlIl1I(283188086L)) {
            throw new UnsupportedEncodingException(I1I1lI1II1.a(new byte[]{122, 61, 53, 4, 22, 66}));
        }
        return string;
    }
}
