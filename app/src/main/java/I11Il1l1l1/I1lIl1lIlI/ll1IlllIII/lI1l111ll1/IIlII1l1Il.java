package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1l11lIlIl;
import androidx.versionedparcelable.custom.entities.lIIlI111II;
import java.io.IOException;
import java.net.ProtocolException;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.j;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0007\u0010\bJ\u0017\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0007¢\u0006\u0004\b\u0005\u0010\u0006"}, d2 = {"LI11Il1l1l1/I1lIl1lIlI/ll1IlllIII/lI1l111ll1/IIlII1l1Il;", "", "", "p0", "LI11Il1l1l1/I1lIl1lIlI/ll1IlllIII/lI1l111ll1/IIII1111ll;", "a", "(Ljava/lang/String;)LI11Il1l1l1/I1lIl1lIlI/ll1IlllIII/lI1l111ll1/IIII1111ll;", "<init>", "()V"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public final class IIlII1l1Il {
    public /* synthetic */ IIlII1l1Il(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    private IIlII1l1Il() {
    }

    public final IIII1111ll a(String p0) throws NumberFormatException, IOException {
        I1l11lIlIl i1l11lIlIl;
        int i;
        String strSubstring;
        if (lIIlI111II.IIlI1Il1lI(523821969L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{86, 35, 11, 19, 21, 4, 98, 121, 124, 7, 113, 124, 65, 96, 4, Byte.MAX_VALUE, 91, 56, 1, 97, 116, 48, 89}));
        }
        Intrinsics.checkNotNullParameter(p0, I1I1lI1II1.a(new byte[]{68, 16, 3, 17, 23, 70, 123, 89, 87, 1}));
        if (j.a(p0, I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 48, 54, 53, 77, 4, 25}), false, 2, (Object) null)) {
            if (p0.length() < 9 || p0.charAt(8) != ' ') {
                throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
            }
            int iCharAt = p0.charAt(7) - '0';
            if (iCharAt == 0) {
                i1l11lIlIl = I1l11lIlIl.HTTP_1_0;
            } else {
                if (iCharAt != 1) {
                    throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
                }
                i1l11lIlIl = I1l11lIlIl.HTTP_1_1;
            }
            i = 9;
        } else {
            if (!j.a(p0, I1I1lI1II1.a(new byte[]{126, 39, 59, 69}), false, 2, (Object) null)) {
                throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
            }
            i1l11lIlIl = I1l11lIlIl.HTTP_1_0;
            i = 4;
        }
        int i2 = i + 3;
        if (p0.length() < i2) {
            throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
        }
        try {
            String strSubstring2 = p0.substring(i, i2);
            Intrinsics.checkNotNullExpressionValue(strSubstring2, I1I1lI1II1.a(new byte[]{67, 12, 11, 22, 66, 84, 68, 16, 83, 5, 70, 81, 27, 85, 85, 91, 5, 79, 49, 70, 66, 8, 93, 82, -47, -74, -111, 12, 10, 85, 31, 16, 67, 5, 16, 17, 43, 91, 83, 85, 65, 72, 16, 85, 91, 93, 125, 91, 6, 4, 26, 27}));
            int i3 = Integer.parseInt(strSubstring2);
            if (p0.length() <= i2) {
                strSubstring = "";
            } else {
                if (p0.charAt(i2) != ' ') {
                    throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
                }
                strSubstring = p0.substring(i + 4);
                Intrinsics.checkNotNullExpressionValue(strSubstring, I1I1lI1II1.a(new byte[]{67, 12, 11, 22, 66, 84, 68, 16, 83, 5, 70, 81, 27, 85, 85, 91, 5, 79, 49, 70, 66, 8, 93, 82, 26, 24, 68, 16, 6, 65, 67, 17, 94, 10, 5, 77, 17, 65, 86, 66, 77, 45, 94, 84, 80, 65, 29}));
            }
            IIII1111ll iIII1111ll = new IIII1111ll(i1l11lIlIl, i3, strSubstring);
            if (androidx.constraintlayout.widget.lIIlI111II.llllI1l1II(169912851L)) {
                throw new UnsupportedClassVersionError(I1I1lI1II1.a(new byte[]{92, 42, 58, 28, 56, 98, 117, 114, 110, 49, 71, 2, 115, 82, 71, 120, 7}));
            }
            return iIII1111ll;
        } catch (NumberFormatException unused) {
            throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{98, 10, 7, 29, 18, 80, 84, 68, 92, 0, 16, 67, 65, 88, 64, 64, 17, 65, 14, 91, 94, 4, 9, 21}), (Object) p0));
        }
    }
}
