package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1l11lIlIl;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\u0018\u0000 \n2\u00020\u0001:\u0001\nB\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007¢\u0006\u0002\u0010\bJ\b\u0010\t\u001a\u00020\u0007H\u0016R\u0010\u0010\u0004\u001a\u00020\u00058\u0006X\u0087\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u00020\u00078\u0006X\u0087\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004¢\u0006\u0002\n\u0000¨\u0006\u000b"}, d2 = {"Lokhttp3/internal/http/StatusLine;", "", "protocol", "Lokhttp3/Protocol;", "code", "", "message", "", "(Lokhttp3/Protocol;ILjava/lang/String;)V", "toString", "Companion", "okhttp"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public final class IIII1111ll {
    public static final IIlII1l1Il a = new IIlII1l1Il(null);
    public final I1l11lIlIl b;
    public final int c;
    public final String d;

    public IIII1111ll(I1l11lIlIl i1l11lIlIl, int i, String str) {
        Intrinsics.checkNotNullParameter(i1l11lIlIl, I1I1lI1II1.a(new byte[]{71, 22, 13, 17, 13, 86, 88, 92}));
        Intrinsics.checkNotNullParameter(str, I1I1lI1II1.a(new byte[]{90, 1, 17, 22, 3, 82, 82}));
        this.b = i1l11lIlIl;
        this.c = i;
        this.d = str;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        if (this.b == I1l11lIlIl.HTTP_1_0) {
            sb.append(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 48, 54, 53, 77, 4, 25, 0}));
        } else {
            sb.append(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 48, 54, 53, 77, 4, 25, 1}));
        }
        sb.append(' ').append(this.c);
        sb.append(' ').append(this.d);
        String string = sb.toString();
        Intrinsics.checkNotNullExpressionValue(string, I1I1lI1II1.a(new byte[]{100, 16, 16, 12, 12, 82, 117, 69, 80, 8, 84, 85, 71, 17, 29, 27, 3, 17, 18, 94, 73, 73, 81, 64, 90, 90, 83, 0, 22, 115, 84, 23, 94, 11, 12, 76, 76, 65, 88, 99, 77, 22, 89, 94, 82, 17, 29}));
        return string;
    }
}
