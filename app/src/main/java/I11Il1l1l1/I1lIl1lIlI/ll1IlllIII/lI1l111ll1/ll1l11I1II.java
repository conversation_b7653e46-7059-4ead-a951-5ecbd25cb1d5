package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l1111lIII1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.IlI11Ill11;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.llllII11Il;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.interpolator.view.animation.IIIlIll111;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.lII1llllI1;
import java.io.IOException;
import java.io.ObjectStreamException;
import java.security.cert.CertPathValidatorException;
import java.util.List;
import java.util.concurrent.RejectedExecutionException;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class ll1l11I1II implements ll1ll1IlIl {
    private final IlI11Ill11 a;
    private final List<llI1l1111l> b;
    private final int c;
    private final llllII11Il d;
    private final ll1I1I1lI1 e;
    private final int f;
    private final int g;
    private final int h;
    private int i;

    /* JADX WARN: Multi-variable type inference failed */
    public ll1l11I1II(IlI11Ill11 ilI11Ill11, List<? extends llI1l1111l> list, int i, llllII11Il llllii11il, ll1I1I1lI1 ll1i1i1li1, int i2, int i3, int i4) {
        Intrinsics.checkNotNullParameter(ilI11Ill11, I1I1lI1II1.a(new byte[]{84, 5, 14, 9}));
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{94, 10, 22, 0, 16, 86, 82, 64, 77, 11, 66, 67}));
        Intrinsics.checkNotNullParameter(ll1i1i1li1, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67}));
        this.a = ilI11Ill11;
        this.b = list;
        this.c = i;
        this.d = llllii11il;
        this.e = ll1i1i1li1;
        this.f = i2;
        this.g = i3;
        this.h = i4;
    }

    public final IlI11Ill11 c() {
        return this.a;
    }

    public final llllII11Il d() {
        return this.d;
    }

    public final ll1I1I1lI1 e() {
        return this.e;
    }

    public final int f() {
        return this.f;
    }

    public final int g() {
        return this.g;
    }

    public final int h() {
        int i = this.h;
        if (IIIlIll111.IlII1Illll(161926164L)) {
            throw new RejectedExecutionException(I1I1lI1II1.a(new byte[]{103, 14, 21, 82, 86, 70, 6, 90, 97, 14, 99, 125, 77, 124, 85, 80, 0, 47, 20, 113, 0, 87, 125, 67, 68, 1, 85, 23, 18, 2, 15, 32}));
        }
        return i;
    }

    public static /* synthetic */ ll1l11I1II a(ll1l11I1II ll1l11i1ii, int i, llllII11Il llllii11il, ll1I1I1lI1 ll1i1i1li1, int i2, int i3, int i4, int i5, Object obj) throws NoSuchFieldException {
        if (lII1llllI1.I1II1111ll(235603980L)) {
            throw new NoSuchFieldException(I1I1lI1II1.a(new byte[]{2, 52, 24, 49, 16, 118, 5, 68}));
        }
        if ((i5 & 1) != 0) {
            i = ll1l11i1ii.c;
        }
        if ((i5 & 2) != 0) {
            llllii11il = ll1l11i1ii.d;
        }
        llllII11Il llllii11il2 = llllii11il;
        if ((i5 & 4) != 0) {
            ll1i1i1li1 = ll1l11i1ii.e;
        }
        ll1I1I1lI1 ll1i1i1li12 = ll1i1i1li1;
        if ((i5 & 8) != 0) {
            i2 = ll1l11i1ii.f;
        }
        int i6 = i2;
        if ((i5 & 16) != 0) {
            i3 = ll1l11i1ii.g;
        }
        int i7 = i3;
        if ((i5 & 32) != 0) {
            i4 = ll1l11i1ii.h;
        }
        return ll1l11i1ii.a(i, llllii11il2, ll1i1i1li12, i6, i7, i4);
    }

    public final ll1l11I1II a(int i, llllII11Il llllii11il, ll1I1I1lI1 ll1i1i1li1, int i2, int i3, int i4) {
        Intrinsics.checkNotNullParameter(ll1i1i1li1, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67}));
        return new ll1l11I1II(this.a, this.b, i, llllii11il, ll1i1i1li1, i2, i3, i4);
    }

    public int i() throws ObjectStreamException {
        if (lIIlI111II.l1l11llIl1(173025298L)) {
            throw new ObjectStreamException(I1I1lI1II1.a(new byte[]{15, 45, 1, 35, 36, 68, 95, 89, 91, 86, 8, 81, 89, 109, 92, 119, 11, 16, 6, 86, 101, 14, 112, 90, 89, 102, 64, 33, 80, 91, 5, 19}));
        }
        return this.g;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl
    public l1111lIII1 b() throws IllegalAccessException {
        if (l1lI1I1l11.I1II1111ll(233505965L)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{80, 8, 12, 12, 90, 84, 110, 99, 81}));
        }
        return this.a;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl
    public ll1I1I1lI1 a() throws CertPathValidatorException {
        ll1I1I1lI1 ll1i1i1li1 = this.e;
        if (androidx.constraintlayout.widget.l1IIll1I1l.l11I11I11l(I1I1lI1II1.a(new byte[]{101}))) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{2, 33, 21, 92, 49, 65, Byte.MAX_VALUE, 89, 107, 5, 2, 2, 84, 79, 100, 6, 91, 13}));
        }
        return ll1i1i1li1;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl
    public lIlII1IIl1 a(ll1I1I1lI1 ll1i1i1li1) throws NoSuchFieldException, IOException {
        Intrinsics.checkNotNullParameter(ll1i1i1li1, I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67}));
        if (!(this.c < this.b.size())) {
            throw new IllegalStateException(I1I1lI1II1.a(new byte[]{116, 12, 7, 6, 9, 21, 81, 81, 80, 8, 85, 84, 27}).toString());
        }
        this.i++;
        llllII11Il llllii11il = this.d;
        if (llllii11il != null) {
            if (!llllii11il.c().a(ll1i1i1li1.a())) {
                throw new IllegalStateException((I1I1lI1II1.a(new byte[]{89, 1, 22, 18, 13, 71, 92, 16, 80, 10, 68, 85, 71, 90, 81, 69, 22, 14, 16, 18}) + this.b.get(this.c - 1) + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 69, 85, 77, 5, 89, 94, 21, 77, 92, 80, 66, 18, 3, 95, 85, 65, 91, 90, 64, 66, 23, 4, 10, 86, 23, 19, 88, 22, 22})).toString());
            }
            if (!(this.i == 1)) {
                throw new IllegalStateException((I1I1lI1II1.a(new byte[]{89, 1, 22, 18, 13, 71, 92, 16, 80, 10, 68, 85, 71, 90, 81, 69, 22, 14, 16, 18}) + this.b.get(this.c - 1) + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 84, 81, 85, 8, 16, 64, 71, 86, 87, 80, 7, 5, 74, 27, 16, 4, 75, 84, 80, 66, 91, 28, 68, 93, 89, 0, 82})).toString());
            }
        }
        ll1l11I1II ll1l11i1iiA = a(this, this.c + 1, null, ll1i1i1li1, 0, 0, 0, 58, null);
        llI1l1111l lli1l1111l = this.b.get(this.c);
        lIlII1IIl1 lilii1iil1Intercept = lli1l1111l.intercept(ll1l11i1iiA);
        if (lilii1iil1Intercept == null) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{94, 10, 22, 0, 16, 86, 82, 64, 77, 11, 66, 16}) + lli1l1111l + I1I1lI1II1.a(new byte[]{23, 22, 7, 17, 23, 71, 89, 85, 93, 68, 94, 69, 89, 85}));
        }
        if (this.d != null) {
            if (!(this.c + 1 >= this.b.size() || ll1l11i1iiA.i == 1)) {
                throw new IllegalStateException((I1I1lI1II1.a(new byte[]{89, 1, 22, 18, 13, 71, 92, 16, 80, 10, 68, 85, 71, 90, 81, 69, 22, 14, 16, 18}) + lli1l1111l + I1I1lI1II1.a(new byte[]{23, 9, 23, 22, 22, 21, 84, 81, 85, 8, 16, 64, 71, 86, 87, 80, 7, 5, 74, 27, 16, 4, 75, 84, 80, 66, 91, 28, 68, 93, 89, 0, 82})).toString());
            }
        }
        if (lilii1iil1Intercept.getA() != null) {
            return lilii1iil1Intercept;
        }
        throw new IllegalStateException((I1I1lI1II1.a(new byte[]{94, 10, 22, 0, 16, 86, 82, 64, 77, 11, 66, 16}) + lli1l1111l + I1I1lI1II1.a(new byte[]{23, 22, 7, 17, 23, 71, 89, 85, 93, 68, 81, 16, 71, 92, 71, 69, 13, 15, 17, 87, 16, 22, 90, 65, 91, 22, 89, 10, 68, 80, 88, 7, 78})).toString());
    }
}
