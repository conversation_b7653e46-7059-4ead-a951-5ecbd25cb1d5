package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II1lllllII;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11;
import IIlII1Il11.l11lI1l1ll.llI111llII.ll1lI1IlII.III1l1I11I;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.InvalidClassException;
import java.io.InvalidObjectException;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class lIII1IlIlI extends l11IIl1I11 {
    private final String a;
    private final long b;
    private final III1l1I11I c;

    public lIII1IlIlI(String str, long j, III1l1I11I iII1l1I11I) {
        Intrinsics.checkNotNullParameter(iII1l1I11I, I1I1lI1II1.a(new byte[]{68, 11, 23, 23, 1, 80}));
        this.a = str;
        this.b = j;
        this.c = iII1l1I11I;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11
    public long contentLength() throws InvalidClassException {
        long j = this.b;
        if (llIlII1IlI.l111l1I1Il(I1I1lI1II1.a(new byte[]{65, 18, 54, 80, 27, 70, 97, 7, 13, 23, 5, 89, 118, 78, 1, 97, 51, 23, 45, 68, 119, 36, 6}), 226229575L)) {
            throw new InvalidClassException(I1I1lI1II1.a(new byte[]{96, 2, 81, 92}));
        }
        return j;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11
    public II1lllllII contentType() throws InvalidObjectException {
        String str = this.a;
        II1lllllII iI1lllllIIB = str == null ? null : II1lllllII.a.b(str);
        if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{98, 82, 45, 45, 12, 5, 97, 82, 67, 9, 115, 7, 112, 12, 89, 6, 5, 45, 7, 89, 116, 55, 85, 86, 80, 112, 121, 41, 85, 71, 64}), 219502609L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 46, 81, 87, 54, 64, 65, 116, 117, 8, 67, 106, 80, 13, 121, 67, 5, 22, 37, 123, 98, 24, 4, 13, 105, 6, 101, 63, 15, 65, 118}));
        }
        return iI1lllllIIB;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11
    public III1l1I11I source() {
        return this.c;
    }
}
