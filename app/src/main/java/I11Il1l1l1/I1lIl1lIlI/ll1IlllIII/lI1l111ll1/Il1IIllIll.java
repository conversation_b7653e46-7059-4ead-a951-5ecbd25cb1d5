package I11Il1l1l1.I1lIl1lIlI.ll1IlllIII.lI1l111ll1;

import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1II1lII11;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1IIl111I1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.I1l1lIllI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.II111lllll;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.l11IIl1I11;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lI1I1I1I1I;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.lIlII1IIl1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1I1I1lI1;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.ll1ll1IlIl;
import II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l;
import III1Ill11I.I1lllllII1.Il111lll1I.I11IIl1ll1.llIIlI1llI;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.II1IIl1I1I;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.IlI11Ill11;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.l1l1I1l11l;
import Ill1IlIlI1.IlIlIlI11I.ll1IlllIII.lI1l111ll1.llllII11Il;
import android.accounts.utils.I1lllI11II;
import android.accounts.utils.lI1l1I1l1l;
import android.accounts.utils.lIIIIII11I;
import android.media.content.II1I11IlI1;
import android.media.content.Il1llIl111;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.util.Log;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.lI1lI11Ill;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.IllllI11lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.II1lllllI1;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.PortUnreachableException;
import java.net.ProtocolException;
import java.net.Proxy$Type;
import java.net.SocketTimeoutException;
import java.net.UnknownServiceException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import java.security.NoSuchProviderException;
import java.security.cert.CertPathBuilderException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateParsingException;
import java.util.Collection;
import java.util.List;
import javax.net.ssl.SSLHandshakeException;
import javax.net.ssl.SSLPeerUnverifiedException;
import kotlin.Metadata;
import kotlin.collections.q;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Regex;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIII11lI.I11ll1IlII.I1I1l1l1I1.l1l1lIlI1l.I1lll11llI;

@Metadata(d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\u0018\u0000 \u001e2\u00020\u0001:\u0001\u001eB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003¢\u0006\u0002\u0010\u0004J\u001a\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0002J\u001c\u0010\u000b\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u0002J\u0010\u0010\u000e\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016J\u0018\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0012H\u0002J(\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u00062\u0006\u0010\u0015\u001a\u00020\u0012H\u0002J\u0018\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0019\u001a\u00020\u0006H\u0002J\u0018\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001cH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u001f"}, d2 = {"Lokhttp3/internal/http/RetryAndFollowUpInterceptor;", "Lokhttp3/Interceptor;", "client", "Lokhttp3/OkHttpClient;", "(Lokhttp3/OkHttpClient;)V", "buildRedirectRequest", "Lokhttp3/Request;", "userResponse", "Lokhttp3/Response;", "method", "", "followUpRequest", "exchange", "Lokhttp3/internal/connection/Exchange;", "intercept", "chain", "Lokhttp3/Interceptor$Chain;", "isRecoverable", "", "e", "Ljava/io/IOException;", "requestSendStarted", "recover", "call", "Lokhttp3/internal/connection/RealCall;", "userRequest", "requestIsOneShot", "retryAfter", "", "defaultDelay", "Companion", "okhttp"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: classes.dex */
public final class Il1IIllIll implements llI1l1111l {
    public static final I11Ill1111 b = new I11Ill1111(null);
    private final I1II1lII11 c;

    public Il1IIllIll(I1II1lII11 i1II1lII11) {
        Intrinsics.checkNotNullParameter(i1II1lII11, I1I1lI1II1.a(new byte[]{84, 8, 11, 0, 12, 65}));
        this.c = i1II1lII11;
    }

    @Override // II1I1Il1ll.l1ll1lI1l1.l11I1lIIl1.lIllllI1lI.llI1l1111l
    public lIlII1IIl1 intercept(ll1ll1IlIl ll1ll1ilil) throws InterruptedException, IOException {
        llllII11Il llllii11ilJ;
        if (lIIlI111II.II1lllllII(400022514L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{98, 51, 13, 34}));
        }
        Intrinsics.checkNotNullParameter(ll1ll1ilil, I1I1lI1II1.a(new byte[]{84, 12, 3, 12, 12}));
        ll1l11I1II ll1l11i1ii = (ll1l11I1II) ll1ll1ilil;
        ll1I1I1lI1 ll1i1i1li1E = ll1l11i1ii.e();
        IlI11Ill11 ilI11Ill11C = ll1l11i1ii.c();
        List listB = q.b();
        lIlII1IIl1 lilii1iil1 = null;
        int i = 0;
        ll1I1I1lI1 ll1i1i1li1A = ll1i1i1li1E;
        boolean z = true;
        while (true) {
            ilI11Ill11C.a(ll1i1i1li1A, z);
            try {
                if (ilI11Ill11C.d()) {
                    throw new IOException(I1I1lI1II1.a(new byte[]{116, 5, 12, 6, 7, 89, 82, 84}));
                }
                try {
                    lIlII1IIl1 lilii1iil1A = ll1l11i1ii.a(ll1i1i1li1A);
                    if (lilii1iil1 != null) {
                        lilii1iil1A = lilii1iil1A.o().f(lilii1iil1.o().b((l11IIl1I11) null).c()).c();
                    }
                    lilii1iil1 = lilii1iil1A;
                    llllii11ilJ = ilI11Ill11C.j();
                    ll1i1i1li1A = a(lilii1iil1, llllii11ilJ);
                } catch (l1l1I1l11l e) {
                    if (!a(e.getLastConnectException(), ilI11Ill11C, ll1i1i1li1A, false)) {
                        throw llIIlI1llI.a(e.getFirstConnectException(), (List<? extends Exception>) listB);
                    }
                    listB = q.a((Collection<? extends IOException>) listB, e.getFirstConnectException());
                    ilI11Ill11C.a(true);
                    z = false;
                } catch (IOException e2) {
                    if (!a(e2, ilI11Ill11C, ll1i1i1li1A, !(e2 instanceof I1lll11llI))) {
                        throw llIIlI1llI.a(e2, (List<? extends Exception>) listB);
                    }
                    listB = q.a((Collection<? extends IOException>) listB, e2);
                    ilI11Ill11C.a(true);
                    z = false;
                }
                if (ll1i1i1li1A == null) {
                    if (llllii11ilJ != null && llllii11ilJ.d()) {
                        ilI11Ill11C.n();
                    }
                    ilI11Ill11C.a(false);
                    if (androidx.recyclerview.widget.content.adapter.lIIlI111II.l111IIlII1(6823)) {
                        throw new InterruptedException(I1I1lI1II1.a(new byte[]{95, 30, 21, 9, 81, 69, 97, 117, 111, 84, 118, 102, 125, 12, 98, 0, 12, 16, 19, 87, 101, 39, 65, 108, 75, 96, 95}));
                    }
                    return lilii1iil1;
                }
                I1l1lIllI1 a = ll1i1i1li1A.getA();
                if (a != null && a.isOneShot()) {
                    ilI11Ill11C.a(false);
                    if (!android.media.content.lIIlI111II.ll1I111ll1(6770)) {
                        return lilii1iil1;
                    }
                    Log.d(I1I1lI1II1.a(new byte[]{78, 61, 55, 10, 42, 80}), I1I1lI1II1.a(new byte[]{124, 48, 33, 7, 8, 84, 103, 84, 114, 21, 103, 65, 64, 67, 123}));
                    return null;
                }
                l11IIl1I11 a2 = lilii1iil1.getA();
                if (a2 != null) {
                    llIIlI1llI.a(a2);
                }
                i++;
                if (i > 20) {
                    throw new ProtocolException(Intrinsics.a(I1I1lI1II1.a(new byte[]{99, 11, 13, 69, 15, 84, 89, 73, 25, 2, 95, 92, 89, 86, 67, 24, 23, 17, 66, 64, 85, 16, 70, 80, 64, 66, 68, 95, 68}), (Object) Integer.valueOf(i)));
                }
                ilI11Ill11C.a(true);
                z = true;
            } catch (Throwable th) {
                ilI11Ill11C.a(true);
                throw th;
            }
        }
    }

    private final boolean a(IOException iOException, IlI11Ill11 ilI11Ill11, ll1I1I1lI1 ll1i1i1li1, boolean z) throws IllegalAccessException, NoSuchProviderException {
        if (IllllI11lI.I1lllI1llI(7841)) {
            throw new IllegalAccessException(I1I1lI1II1.a(new byte[]{118, 30, 91, 42, 37, 119, 97, 72, 95, 35, 92, 117, 108, 119, 5, 90, 87, 21, 86, 89, 81, 57, 101, 91, 86, 98, 68, 47, 34, 4}));
        }
        if (!this.c.f()) {
            return false;
        }
        if (z && a(iOException, ll1i1i1li1)) {
            if (l1lll111II.Il1IIlI1II(279760969L)) {
                throw new NoSuchProviderException(I1I1lI1II1.a(new byte[]{82, 51, 43, 55, 20, 7, 91, 118, 72}));
            }
            return false;
        }
        if (!a(iOException, z)) {
            if (Il1llIl111.I1lllI1llI(4303)) {
                throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{70, 16, 46, 31, 35, 1, 101, 81, 116, 83, 126, 7, 113, 76, 124, 4, 12, 35, 32, 7, 71, 49, 112, 90, 101, 88, 77, 53, 41, 99, 90, 27}));
            }
            return false;
        }
        if (ilI11Ill11.o()) {
            return true;
        }
        if (lI1lI11Ill.Il1IIlI1II(278651686L)) {
            Log.w(I1I1lI1II1.a(new byte[]{113, 12, 42, 6, 53, 98, 114, 104, 99, 8, 5, 104, 88, 124, 86, 95, 9, 36, 80, 68, 2, 42, 102, 108, 64, 15, Byte.MAX_VALUE, 60, 17, 93, 94}), I1I1lI1II1.a(new byte[]{121, 83, 4, 4, 21, 79, 111}));
        }
        return false;
    }

    private final boolean a(IOException iOException, ll1I1I1lI1 ll1i1i1li1) throws InterruptedException {
        if (IIll1llI1l.Il1IIlI1II(3229)) {
            throw new InterruptedException(I1I1lI1II1.a(new byte[]{102, 5, 9, 44, 49, 76, 4, 114, 11, 80, 90, 84, 82, 120, 119, 113, 17, 27, 47, 94, 66, 45, 85, 88, 101, 124, 115, 0, 44}));
        }
        I1l1lIllI1 a = ll1i1i1li1.getA();
        return (a != null && a.isOneShot()) || (iOException instanceof FileNotFoundException);
    }

    private final boolean a(IOException iOException, boolean z) throws GeneralSecurityException {
        if (Il1lII1l1l.I1lllI1llI(392954347L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{79}));
        }
        if (iOException instanceof ProtocolException) {
            return false;
        }
        if (iOException instanceof InterruptedIOException) {
            boolean z2 = (iOException instanceof SocketTimeoutException) && !z;
            if (androidx.constraintlayout.widget.I1IllIll1l.lll1111l11(I1I1lI1II1.a(new byte[]{92, 8, 27, 34, 47, 7, 68, 95, 72, 41, 98, 125, 95, 92}), 1053296694L)) {
                throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{88, 29, 91, 2, 40, 126, 103, 123, 0, 49, 100, 121}));
            }
            return z2;
        }
        if ((iOException instanceof SSLHandshakeException) && (iOException.getCause() instanceof CertificateException)) {
            if (android.accounts.utils.lIIlI111II.Il11lIlI1I(6682)) {
                Log.w(I1I1lI1II1.a(new byte[]{98, 8, 83, 42, 18, 84, 92, 1, 87, 17, 105, 0, 90, 118, 88, 94}), I1I1lI1II1.a(new byte[]{111, 20, 4, 43, 22, 79, 71, 72, 9, 84, 93, 72, 109}));
            }
            return false;
        }
        if (iOException instanceof SSLPeerUnverifiedException) {
            if (I1lllI11II.l1Il11I1Il(I1I1lI1II1.a(new byte[]{103, 84, 51, 8, 23}), 396233397L)) {
                throw new NumberFormatException(I1I1lI1II1.a(new byte[]{5, 47, 22, 53, 51, 125, 94, 103, 79, 84, 104, 82}));
            }
            return false;
        }
        if (lIIII1l1lI.I1lllI1llI(4221)) {
            throw new NumberFormatException(I1I1lI1II1.a(new byte[]{118, 49, 43, 53}));
        }
        return true;
    }

    private final ll1I1I1lI1 a(lIlII1IIl1 lilii1iil1, llllII11Il llllii11il) throws CertPathBuilderException, IOException, KeyManagementException, CertificateException, InvalidAlgorithmParameterException {
        II1IIl1I1I iI1IIl1I1IE;
        if (android.support.v4.graphics.drawable.lIIllIlIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{118, 15, 51, 82, 50, 121, 6, 66, 113, 20, 69, 83, 111, 67, 123}), 5306)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{82, 16, 55, 47, 3, 69, Byte.MAX_VALUE, 96}));
        }
        lI1I1I1I1I li1i1i1i1iI = (llllii11il == null || (iI1IIl1I1IE = llllii11il.e()) == null) ? null : iI1IIl1I1IE.i();
        int d = lilii1iil1.getD();
        String f = lilii1iil1.a().getF();
        if (d != 307 && d != 308) {
            if (d == 401) {
                ll1I1I1lI1 ll1i1i1li1A = this.c.getH().a(li1i1i1i1iI, lilii1iil1);
                if (II1lllllI1.I1lllI1llI(285581743L)) {
                    throw new KeyManagementException(I1I1lI1II1.a(new byte[]{4, 10, 58, 7, 81, 91, 118, 70, 126, 15, 116, 101, 92, 65, 64, 94, 51, 14, 26, 122, 5, 35, 124, 82, 122, 120, 94, 42, 0}));
                }
                return ll1i1i1li1A;
            }
            if (d == 421) {
                I1l1lIllI1 a = lilii1iil1.a().getA();
                if (a != null && a.isOneShot()) {
                    if (II1I11IlI1.l1Il11I1Il(I1I1lI1II1.a(new byte[]{84, 2, 39, 51}), 5208)) {
                        throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{93, 41, 14, 47, 51, 69, 96, 6, 0, 7, 69, 9, 99, 107, 122, 115, 0, 5, 55, 116, 116, 10, 102, 115, 96}));
                    }
                    return null;
                }
                if (llllii11il == null || !llllii11il.f()) {
                    if (lI1l1I1l1l.l11I11I11l(9917)) {
                        throw new NullPointerException(I1I1lI1II1.a(new byte[]{91, 80, 44, 7, 4, 100, 82, 66, 85}));
                    }
                    return null;
                }
                llllii11il.e().g();
                ll1I1I1lI1 ll1i1i1li1A2 = lilii1iil1.a();
                if (I1I1IIIIl1.I1lllI1llI(I1I1lI1II1.a(new byte[]{97, 6, 7, 2, 32, 90, 81, 97, 1, 17}), 330244151L)) {
                    throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{64, 87}));
                }
                return ll1i1i1li1A2;
            }
            if (d == 503) {
                lIlII1IIl1 lilii1iil1J = lilii1iil1.j();
                if (lilii1iil1J != null && lilii1iil1J.getD() == 503) {
                    return null;
                }
                if (a(lilii1iil1, Integer.MAX_VALUE) == 0) {
                    return lilii1iil1.a();
                }
                if (l1l1IllI11.l111l1I1Il(I1I1lI1II1.a(new byte[]{112, 42, 52, 28, 27, 3, 4, 86, 80, 53, 99, 99, 66, 123, 122, 7, 24, 47}), 280609948L)) {
                    throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{99, 92, 46, 21, 40, 119, 97, 5, 114, 55, 83, 4, 1, 67, 2, 13}));
                }
                return null;
            }
            if (d == 407) {
                Intrinsics.a(li1i1i1i1iI);
                if (li1i1i1i1iI.b().type() != Proxy$Type.HTTP) {
                    throw new ProtocolException(I1I1lI1II1.a(new byte[]{101, 1, 1, 0, 11, 67, 82, 84, 25, 44, 100, 100, 101, 102, 100, 103, 45, 57, 59, 109, 113, 52, 103, 125, 19, 30, 3, 85, 83, 27, 23, 0, 88, 0, 7, 69, 21, 93, 94, 92, 92, 68, 94, 95, 65, 25, 65, 70, 11, 15, 5, 18, 64, 19, 92, 77, 74}));
                }
                ll1I1I1lI1 ll1i1i1li1A3 = this.c.getP().a(li1i1i1i1iI, lilii1iil1);
                if (l11Il1lI11.IlII1Illll(8575)) {
                    throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{95, 85, 11, 83, 55, 125, 97, 2, 114, 0, 9, 69, 79, 122, 117, 126, 7, 7, 46, 86, 8, 54, 3, 108, 101, 120, 97, 34}));
                }
                return ll1i1i1li1A3;
            }
            if (d == 408) {
                if (!this.c.f()) {
                    return null;
                }
                I1l1lIllI1 a2 = lilii1iil1.a().getA();
                if (a2 != null && a2.isOneShot()) {
                    return null;
                }
                lIlII1IIl1 lilii1iil1J2 = lilii1iil1.j();
                if (lilii1iil1J2 == null || lilii1iil1J2.getD() != 408) {
                    if (a(lilii1iil1, 0) > 0) {
                        return null;
                    }
                    return lilii1iil1.a();
                }
                if (lIIII1l1lI.I1lllI1llI(2708)) {
                    throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{91, 6, 35, 48, 45, 13, 126, 92, 107, 41, 1, 82, Byte.MAX_VALUE}));
                }
                return null;
            }
            switch (d) {
                case 300:
                case 301:
                case 302:
                case 303:
                    break;
                default:
                    return null;
            }
        }
        ll1I1I1lI1 ll1i1i1li1A4 = a(lilii1iil1, f);
        if (IlIIlI11I1.I1II1111ll(I1I1lI1II1.a(new byte[]{125, 50, 27, 33, 27, 86, 122, 0, 77, 43, 115, 118, 1, 77, 4, 65, 44, 9, 45, 74, 123}), 162856774L)) {
            throw new ClassFormatError(I1I1lI1II1.a(new byte[]{115, 22, 20, 36, 38, 3, 84, 95, 94, 5, 87, 125, 71, 96, 78, 70, 7, 21, 8, 99, 106}));
        }
        return ll1i1i1li1A4;
    }

    private final ll1I1I1lI1 a(lIlII1IIl1 lilii1iil1, String str) throws CertPathBuilderException, CertificateParsingException, IOException, KeyManagementException {
        if (l1l1IllI11.ll1I1lII11(I1I1lI1II1.a(new byte[]{0, 92, 18, 86, 56, 80, 89, 115, 105, 11}))) {
            throw new CertificateParsingException(I1I1lI1II1.a(new byte[]{84, 87, 44, 45, 48, 112, 111, 95, 97, 15, 88, 125, 116, Byte.MAX_VALUE, 1, 69, 17, 49, 33, 126, 118, 18, 7, 92, 89, 89, 97}));
        }
        if (!this.c.h()) {
            return null;
        }
        String strA$default = lIlII1IIl1.a$default(lilii1iil1, I1I1lI1II1.a(new byte[]{123, 11, 1, 4, 22, 92, 88, 94}), null, 2, null);
        if (strA$default == null) {
            if (android.support.v4.graphics.drawable.lIIllIlIl1.Il1IIlI1II(1356678676L)) {
                throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{70, 48, 32, 53, 10, 64, 122, 117, 67, 80, 87, 2, 102, 67, 126, 96, 52, 53, 53, 98, 8, 23, 105, 7, 4, 116, 100, 28, 11, 95, 7}));
            }
            return null;
        }
        II111lllll iI111lllllA = lilii1iil1.a().a().a(strA$default);
        if (iI111lllllA == null) {
            if (lIIIIII11I.I1lI11IIll(I1I1lI1II1.a(new byte[]{78, 42, 54, 85, 54, 95, 109, 1, 90, 46, 123, 69, 108, 10, 125, 111, 33, 41, 56, 96, 1, 43, 7}), 1112665538L)) {
                throw new KeyManagementException(I1I1lI1II1.a(new byte[]{102, 22, 55, 14, 51, 66, 125, 6, 114, 32, 116, 64, 113, Byte.MAX_VALUE, 108, 79, 24, 44, 37, 10, 86, 9, 3, 114, 105, 103, 98, 87, 38, 126}));
            }
            return null;
        }
        if (!Intrinsics.a((Object) iI111lllllA.getO(), (Object) lilii1iil1.a().a().getO()) && !this.c.i()) {
            if (lIIlI111II.I1111l111I(9976)) {
                Log.d(I1I1lI1II1.a(new byte[]{100, 13, 24, 44, 22, 80, 3, 96, Byte.MAX_VALUE, 81, 95, 98, 4, 108}), I1I1lI1II1.a(new byte[]{2, 82, 24, 48, 22, 66, 65, 3, 85, 80, 91, 101, 79, 125, 5, 116, 33, 35, 87, 90, 3, 24, 68, 7, 100, 100, 91, 10, 41}));
            }
            return null;
        }
        I1IIl111I1 i1IIl111I1G = lilii1iil1.a().g();
        if (lll1lIlIlI.c(str)) {
            int d = lilii1iil1.getD();
            boolean z = lll1lIlIlI.INSTANCE.d(str) || d == 308 || d == 307;
            if (!lll1lIlIlI.INSTANCE.e(str) || d == 308 || d == 307) {
                i1IIl111I1G.a(str, z ? lilii1iil1.a().getA() : null);
            } else {
                i1IIl111I1G.a(I1I1lI1II1.a(new byte[]{112, 33, 54}), (I1l1lIllI1) null);
            }
            if (!z) {
                i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{99, 22, 3, 11, 17, 83, 82, 66, 20, 33, 94, 83, 90, 93, 93, 91, 5}));
                i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 117, 1, 94, 87, 65, 81}));
                i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{116, 11, 12, 17, 7, 91, 67, 29, 109, 29, 64, 85}));
            }
        }
        if (!llIIlI1llI.a(lilii1iil1.a().a(), iI111lllllA)) {
            i1IIl111I1G.c(I1I1lI1II1.a(new byte[]{118, 17, 22, 13, 13, 71, 94, 74, 88, 16, 89, 95, 91}));
        }
        return i1IIl111I1G.b(iI111lllllA).c();
    }

    private final int a(lIlII1IIl1 lilii1iil1, int i) throws NumberFormatException, ClassNotFoundException {
        if (IllIIIIII1.Il1IIlI1II(4657)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{120, 83, 48}));
        }
        String strA$default = lIlII1IIl1.a$default(lilii1iil1, I1I1lI1II1.a(new byte[]{101, 1, 22, 23, 27, 24, 118, 86, 77, 1, 66}), null, 2, null);
        if (strA$default == null) {
            return i;
        }
        if (!new Regex(I1I1lI1II1.a(new byte[]{107, 0, 73})).matches(strA$default)) {
            return Integer.MAX_VALUE;
        }
        Integer numValueOf = Integer.valueOf(strA$default);
        Intrinsics.checkNotNullExpressionValue(numValueOf, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7, 122, 81, 24, 81, 1, 81, 84, 80, 75, 29}));
        int iIntValue = numValueOf.intValue();
        if (android.media.content.lIIllIlIl1.llll111lI1(I1I1lI1II1.a(new byte[]{70, 6, 37, 13, 46, 89, 14, 113, 65, 10, 90, 87, 101, 84, 97, 123, 56, 19, 48, 7, 9, 8, 74}))) {
            throw new ClassNotFoundException(I1I1lI1II1.a(new byte[]{67, 81, 16, 3, 12, 2, 100, 105, 82, 46, 88, 117, 100, 72, 82, 70}));
        }
        return iIntValue;
    }
}
