package lII111IIll.lI1l111ll1.ll1IlllIII.ll1Ill11l1;

import IIl1IIlI1I.I1l11II11l.l1lI1lll1l.II1l1ll1ll.l1IlI1IlIl;
import android.accounts.Account;
import android.accounts.AccountManager;
import android.accounts.utils.Ill11ll111;
import android.content.ContentResolver;
import android.content.PeriodicSync;
import android.os.Bundle;
import android.text.TextUtils;
import androidx.core.location.I11II1l1lI;
import java.io.FileNotFoundException;
import java.net.UnknownServiceException;
import java.security.KeyStoreException;
import java.util.List;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lI111IIIII.l1l11I11II.llIllI1l11.lIl1I1111l.I1llIII1Il;
import lI1I1lIIIl.I1l11II11l.IIIII1I11I.IIIllIIIIl.I1llI1ll1l;
import lI1I1lIIIl.I1l11II11l.IIIII1I11I.IIIllIIIIl.lIIl1I11II;
import lI1I1lIIIl.I1l11II11l.IIIII1I11I.IIIllIIIIl.llIll11l11;

/* loaded from: classes6.dex */
public class l11ll11I11 {
    private static Account a;
    private static String b;
    private static String c;
    private static String d;
    private static String e;

    public static void a(lIIl1I11II liil1i11ii) throws UnknownServiceException {
        if (!llIll11l11.a()) {
            b(liil1i11ii);
        } else {
            c(liil1i11ii);
        }
        if (Ill11ll111.l1l1l1IIlI(235730429L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{99, 0, 20, 51, 10, 91, 125, 114, 97, 93, 69, 105, 88, 105, 70, 102, 24, 24, 6, 92, 68, 24, 89, 95, 67}));
        }
    }

    private static void b(lIIl1I11II liil1i11ii) {
        if (TextUtils.equals(liil1i11ii.c, liil1i11ii.f)) {
            try {
                b = liil1i11ii.a.getString(I1llI1ll1l.account_label);
                c = liil1i11ii.a.getString(I1llI1ll1l.account_type);
                d = liil1i11ii.a.getString(I1llI1ll1l.account_provider);
                e = liil1i11ii.a.getString(I1llI1ll1l.account_provider1);
                a = new Account(b, c);
                AccountManager accountManager = .get(liil1i11ii.a);
                int i = 0;
                if (liil1i11ii.a()) {
                    if (.getAccountsByType(c).length <= 0) {
                        accountManager.addAccountExplicitly(a, null, Bundle.EMPTY);
                        ContentResolver.setIsSyncable(a, d, 1);
                        ContentResolver.setSyncAutomatically(a, d, true);
                        ContentResolver.setMasterSyncAutomatically(true);
                    }
                    a();
                    if (!ContentResolver.isSyncPending(a, d)) {
                        a(true);
                    }
                    List<PeriodicSync> periodicSyncs = ContentResolver.getPeriodicSyncs(a, d);
                    if (periodicSyncs != null && periodicSyncs.size() > 0) {
                        i = 1;
                    }
                    if (i == 0) {
                        ContentResolver.addPeriodicSync(a, d, Bundle.EMPTY, d(liil1i11ii));
                        return;
                    }
                    return;
                }
                Account[] accountsByType = accountManager.getAccountsByType(c);
                while (i < accountsByType.length) {
                    accountManager.removeAccountExplicitly(accountsByType[i]);
                    i++;
                }
            } catch (Exception unused) {
            }
        }
    }

    private static void c(lIIl1I11II liil1i11ii) {
        if (TextUtils.equals(liil1i11ii.c, liil1i11ii.f)) {
            try {
                b = liil1i11ii.a.getString(I1llI1ll1l.account_label);
                c = liil1i11ii.a.getString(I1llI1ll1l.account_type);
                d = liil1i11ii.a.getString(I1llI1ll1l.account_provider);
                e = liil1i11ii.a.getString(I1llI1ll1l.account_provider1);
                a = new Account(b, c);
                AccountManager accountManager = AccountManager.get(liil1i11ii.a);
                if (liil1i11ii.a()) {
                    if (accountManager.getAccountsByType(c).length <= 0) {
                        accountManager.addAccountExplicitly(a, null, Bundle.EMPTY);
                        ContentResolver.setIsSyncable(a, d, 1);
                        ContentResolver.setSyncAutomatically(a, d, true);
                        ContentResolver.setMasterSyncAutomatically(true);
                    }
                    ContentResolver.removePeriodicSync(a, d, Bundle.EMPTY);
                    ContentResolver.addPeriodicSync(a, d, Bundle.EMPTY, d(liil1i11ii));
                    a(true);
                    return;
                }
                for (Account account : accountManager.getAccountsByType(c)) {
                    accountManager.removeAccountExplicitly(account);
                }
            } catch (Exception unused) {
            }
        }
    }

    public static void a(boolean z) {
        if (Ill11ll111.llll111lI1(I1I1lI1II1.a(new byte[]{92, 50, 47, 20, 21, 126, 3, 119, 87, 33, 102, 96, 120, 86, 119, 120, 13, 36, 20, 102, 124, 85, 64, 80}))) {
            throw new LinkageError(I1I1lI1II1.a(new byte[]{66, 10, 45, 10, 36, 115, 126, 94, 112, 15, 7, 106, 66, 75, 103, 87, 41, 46, 8, 90, 103, 27, 71, 68, 107}));
        }
        if (llIll11l11.a()) {
            try {
                Bundle bundle = new Bundle();
                bundle.putBoolean(I1I1lI1II1.a(new byte[]{81, 11, 16, 6, 7}), true);
                bundle.putBoolean(I1I1lI1II1.a(new byte[]{82, 28, 18, 0, 6, 92, 67, 85, 93}), true);
                ContentResolver.requestSync(a, d, bundle);
                l1IlI1IlIl.a(I1I1lI1II1.a(new byte[]{117, 13, 0}), I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 99, 64, 10, 83, 114}));
            } catch (Exception e2) {
                l1IlI1IlIl.a(I1I1lI1II1.a(new byte[]{117, 13, 0}), I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 99, 64, 10, 83, 114, 21, 92, 70, 71, 13, 19, 88}), e2);
            }
        } else {
            try {
                Bundle bundle2 = new Bundle();
                bundle2.putBoolean(I1I1lI1II1.a(new byte[]{81, 11, 16, 6, 7}), true);
                if (z) {
                    bundle2.putBoolean(I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 11, 71, 82, 111, 90, 12, 81, 66, 82, 80, 90, 82}), true);
                }
                ContentResolver.requestSync(a, d, bundle2);
                l1IlI1IlIl.a(I1I1lI1II1.a(new byte[]{117, 13, 0}), I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 99, 64, 10, 83, 113}));
            } catch (Exception e3) {
                l1IlI1IlIl.a(I1I1lI1II1.a(new byte[]{117, 13, 0}), I1I1lI1II1.a(new byte[]{69, 1, 19, 16, 7, 70, 67, 99, 64, 10, 83, 113, 21, 92, 70, 71, 13, 19, 88}), e3);
            }
        }
        if (I11II1l1lI.Ill1lIIlIl(1265)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{99, 80, 82, 16, 41, 7, 96, 82, 107, 44, 84, 67, 118, 0, 81, 116, 33, 82, 50, 90, 118, 19, 103, 113}));
        }
    }

    public static void a() throws FileNotFoundException {
        l1IlI1IlIl.a(I1I1lI1II1.a(new byte[]{117, 13, 0}), I1I1lI1II1.a(new byte[]{68, 1, 22, 44, 17, 102, 78, 94, 90, 5, 82, 92, 80}));
        ContentResolver.setIsSyncable(a, e, -1);
    }

    private static int d(lIIl1I11II liil1i11ii) throws KeyStoreException {
        if (Ill11ll111.llll111lI1(I1I1lI1II1.a(new byte[]{69, 50, 22, 31, 16, 118, 95, 69, 88, 2, 73, 73, 88, 114, 91, 111, 26, 85, 59, Byte.MAX_VALUE, 83, 51, 93, 116, 70, 15, 6, 11, 93}))) {
            throw new KeyStoreException(I1I1lI1II1.a(new byte[]{100, 18, 20, 84, 9, 121, 85, 91, 104, 82, 106, 0, 93, 8, 120, 91, 0}));
        }
        int i = liil1i11ii.e * 60;
        return i < 900 ? I1llIII1Il.TYPE_INT : i;
    }
}
