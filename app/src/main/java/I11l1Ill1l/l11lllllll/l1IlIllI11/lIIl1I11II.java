package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.content.Context;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.security.UnrecoverableEntryException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lIIl1I11II {
    private static lIIl1I11II b = new lIIl1I11II();
    private Context a;

    private lIIl1I11II() {
    }

    public static lIIl1I11II b() {
        lIIl1I11II liil1i11ii = b;
        if (llIlII1IlI.llll111lI1(I1I1lI1II1.a(new byte[]{5, 50, 13, 15, 32, 66, 103, 68, 125, 43, 87, 72, 67, 77, 12, 83, 56, 56, 35, 70}))) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{77, 17, 24, 21, 39, 118, 120, 8, 82, 86, 3, 82, 94, 76, 96, 95, 4, 50, 11, 86, 84, 45}));
        }
        return liil1i11ii;
    }

    public Context a() throws UnrecoverableEntryException {
        Context context = this.a;
        if (l11Il1lI11.IlII1Illll(5646)) {
            throw new UnrecoverableEntryException(I1I1lI1II1.a(new byte[]{2, 13, 53, 51, 54, 97, 93, 90, 97, 82, 113, 2, 122, 114, 108, 118, 86, 37, 8, 92, 66, 20, 71, 66, 112}));
        }
        return context;
    }

    public void a(Context context) {
        this.a = context != null ? context.getApplicationContext() : null;
    }
}
