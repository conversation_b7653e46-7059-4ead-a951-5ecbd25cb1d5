package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.webkit.WebView;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertPathValidatorException;

/* loaded from: classes.dex */
class I11lI11III implements Runnable {
    final /* synthetic */ WebView a;
    final /* synthetic */ String b;
    final /* synthetic */ l1ll11l111 c;

    I11lI11III(l1ll11l111 l1ll11l111Var, WebView webView, String str) {
        this.c = l1ll11l111Var;
        this.a = webView;
        this.b = str;
    }

    @Override // java.lang.Runnable
    public void run() throws CertPathValidatorException, InvalidAlgorithmParameterException {
        this.c.c(this.a, this.b);
    }
}
