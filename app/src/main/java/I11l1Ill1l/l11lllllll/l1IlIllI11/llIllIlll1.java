package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class llIllIlll1 {
    private static llIllIlll1 c = new llIllIlll1();
    private final ArrayList<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> a = new ArrayList<>();
    private final ArrayList<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> b = new ArrayList<>();

    private llIllIlll1() {
    }

    public static llIllIlll1 c() throws CertificateException {
        if (android.support.v4.graphics.drawable.l11Il111ll.l11I11I11l(I1I1lI1II1.a(new byte[]{71, 38, 59, 54}), 4663)) {
            throw new CertificateException(I1I1lI1II1.a(new byte[]{82, 30, 46, 19, 38, 123, 123, 82, 79, 54, 89, 119, 0, Byte.MAX_VALUE, 67, 76, 26, 27, 36}));
        }
        return c;
    }

    public Collection<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> a() throws CertPathValidatorException {
        Collection<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> collectionUnmodifiableCollection = Collections.unmodifiableCollection(this.b);
        if (android.support.v4.graphics.drawable.l11Il111ll.IlII1Illll(266116160L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{86, 19, 84, 82}));
        }
        return collectionUnmodifiableCollection;
    }

    public void a(lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111 liliii1111) {
        this.a.add(liliii1111);
        if (IllllI11Il.lll1111l11(I1I1lI1II1.a(new byte[]{78, 19, 48, 40}))) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{110, 61, 52, 23, 9, 92, 83, 122, 105, 21, 72, 82}));
        }
    }

    public Collection<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> b() {
        return Collections.unmodifiableCollection(this.a);
    }

    public void b(lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111 liliii1111) {
        boolean zD = d();
        this.a.remove(liliii1111);
        this.b.remove(liliii1111);
        if (zD && !d()) {
            III1Il1II1.c().e();
        }
        if (IIlI1Il1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{95, 62, 46, 82, 85, 87, 96, 1, 112, 22, 74, 84, 118, 77, 96, 119, 11, 43}), 233257951L)) {
            throw new StackOverflowError(I1I1lI1II1.a(new byte[]{112, 93, 49, 82, 42, 91, 3, 90, 11, 0, 99, 92, 120, 120, 82, 88, 44, 55, 80, 67, 82, 59, 125, 4, 6, 71, 99, 31, 92}));
        }
    }

    public void c(lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111 liliii1111) {
        boolean zD = d();
        this.b.add(liliii1111);
        if (zD) {
            return;
        }
        III1Il1II1.c().d();
    }

    public boolean d() {
        return this.b.size() > 0;
    }
}
