package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.content.Context;
import android.os.Handler;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.IllllI11Il;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.I1Ill1lIII;
import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.interpolator.view.animation.lIIII1l1lI;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.io.CharConversionException;
import java.io.IOException;
import java.io.InvalidObjectException;
import java.io.UTFDataFormatException;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertStoreException;
import java.security.cert.CertificateExpiredException;
import java.util.Iterator;
import java.util.concurrent.BrokenBarrierException;
import l1IIllI111.I1I1llI11l.lIllIl111I.lIl1l1lIll.l1Ill1llI1;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class III1Il1II1 implements I1llI1ll1l, IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.llIllIlll1 {
    private static III1Il1II1 f;
    private float a = 0.0f;
    private final IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.IllIIll11l b;
    private final IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.l11Il111ll c;
    private IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.l1lllIll1I d;
    private llIllIlll1 e;

    public III1Il1II1(IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.IllIIll11l illIIll11l, IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.l11Il111ll l11il111ll) {
        this.b = illIIll11l;
        this.c = l11il111ll;
    }

    private llIllIlll1 a() throws UTFDataFormatException {
        if (lIIII1l1lI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{80, 86, 4, 51, 20, 76, 116, 67, 109, 87, 103, 122, 67, 67, 4, 91, 13, 54, 58, 87, 126, 15, 7, 109, 10, 80, 5, 49, 47, 91, 65, 25}), 10677)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 21, 44, 3, 52, 76, 89}));
        }
        if (this.e == null) {
            this.e = llIllIlll1.c();
        }
        llIllIlll1 llillilll1 = this.e;
        if (IlIIlI11I1.I1lllI1llI(161479677L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{102, 13, 43, 46, 3, 12, 125, 67, 92, 29, 71, 122, 95, 78, 64, 116, 12, 83, 90, 97, 5, 49, 81, 70, 98}));
        }
        return llillilll1;
    }

    public static III1Il1II1 c() {
        if (llIlI11III.Il1IIlI1II(2722)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{71, 12, 53, 21, 58, 65, 0, 103, 67, 83}));
        }
        if (f == null) {
            f = new III1Il1II1(new IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.IllIIll11l(), new IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.l11Il111ll());
        }
        III1Il1II1 iII1Il1II1 = f;
        if (I1Ill1lIII.IlII1Illll(407538596L)) {
            throw new InternalError(I1I1lI1II1.a(new byte[]{96, 87, 80, 3, 52, 7, 94, 82, 15, 42, 99, 103, 96, 111, 122, 122, 49}));
        }
        return iII1Il1II1;
    }

    @Override // IlIl1llIIl.IIlIllllII.lI1lIll11I.llll111lll.llIllIlll1
    public void a(float f2) throws InvalidObjectException, BrokenBarrierException, CertPathValidatorException, UnknownHostException, CloneNotSupportedException, InvalidAlgorithmParameterException {
        this.a = f2;
        Iterator<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> it = a().a().iterator();
        while (it.hasNext()) {
            it.next().getAdSessionStatePublisher().a(f2);
        }
        if (lI11IlI1lI.IlIIl111lI(I1I1lI1II1.a(new byte[]{123, 48, 82, 53, 0, 112, 4, 100, 110, 13, 118, 81, 68, 67, 118, 79, 85, 47, 55, 1, 81, 81, 121, 111, 2, 119, 88, 13, 19, 126, 121, 38}), 10486)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{71, 34, 81, 84, 84, 90, 101, 0, 119, 37, 66, 104, 122, 104, 108, 111, 40, 12, 87, 91, 6, 21, 112, 6, 74, 111, 82}));
        }
    }

    public void a(Context context) throws InterruptedException, CertificateExpiredException {
        if (l1IIll1I1l.IlII1Illll(I1I1lI1II1.a(new byte[]{85, 13, 36, 4, 16, 123, 116, 6, 106, 37, 122, 92, 0, 93, 81, 13, 19, 15, 14, 80, 115, 23, 120, 92, 112, 81, 85, 51, 45, 83, Byte.MAX_VALUE}), 7552)) {
            throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{111, 52, 21, 22, 16, 111, 111, 5, 109, 49, 94, 68, 92, 122, 126, 124, 18, 57, 41, 113, 9}));
        }
        this.d = this.b.a(new Handler(), context, this.c.a(), this);
    }

    @Override // I11l1Ill1l.l11lllllll.l1IlIllI11.I1llI1ll1l
    public void a(boolean z) throws CertStoreException {
        if (IIlII1IIIl.I111IlIl1I(336312957L)) {
            throw new CertStoreException(I1I1lI1II1.a(new byte[]{97, 86, 7, 29, 14, 95, 86, 115, 82, 11, 100, 113, 112, 125, 89, 71, 6, 10, 24, 71, 69, 49}));
        }
        if (z) {
            l1Ill1llI1.getInstance().h();
        } else {
            l1Ill1llI1.getInstance().g();
        }
    }

    public float b() {
        float f2 = this.a;
        if (IllllI11Il.II1111I11I(I1I1lI1II1.a(new byte[]{97, 50, 86, 8, 37, 68, 6, 92, 85, 33, 116, 0, 1, 106, 64, 123, 47, 18, 32, 81, Byte.MAX_VALUE, 53, 86, 76, 125, 14, 93, 40, 12, 96}), I1I1lI1II1.a(new byte[]{15, 62, 85, 3, 50, 81, 5, 9, 123, 80, 3, 84, 94, 86, 89, 120, 19, 4}))) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{113, 21, 27, 0, 15, 76, 77, 116, 76, 93, 81, 84, 65, 11, 76, 90, 80, 19, 18, 119, 101, 12, 7, 99, 123, 87, 4}));
        }
        return f2;
    }

    public void d() {
        if (l1lI1I1l11.ll1I1lII11(I1I1lI1II1.a(new byte[]{77, 54, 58, 20, 9, 103, 84, 95, 119, 30, 84, 72, 118, 97, 98, 103, 51, 48, 9, 94, Byte.MAX_VALUE, 53, 86, 109, 105, 114, 14}), 7614)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{71, 52, 45, 35, 48, 2}));
        }
        l11Il111ll.g().a(this);
        l11Il111ll.g().e();
        l1Ill1llI1.getInstance().h();
        this.d.c();
        if (I11II1l1lI.lll1111l11(I1I1lI1II1.a(new byte[]{69, 85, 20, 49, 16, 79, 80, 72, 108, 41, 68, 101, 121, 0, 12, 4, 8, 8, 47, 119, 64, 32, 121, 126, 68, 64, 96, 7, 21, 1, 89}), 262694212L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{2, 37, 23, 93, 54, 121, 94, 119, 74, 2, 94, 118, 109, 64, 114, 111, 23, 8, 41, 72, 116}));
        }
    }

    public void e() throws CharConversionException {
        l1Ill1llI1.getInstance().j();
        l11Il111ll.g().f();
        this.d.d();
        if (Il1I1lllIl.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{92, 43, 46, 42, 43, 114, 2, 102, 15, 8, 103, 104, 126, 79, 126, 0}), 828335167L)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{88}));
        }
    }
}
