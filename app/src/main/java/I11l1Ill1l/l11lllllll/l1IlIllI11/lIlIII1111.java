package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.content.Context;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import java.io.InvalidObjectException;
import java.io.UTFDataFormatException;
import java.net.BindException;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.ProviderException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateEncodingException;
import java.util.Date;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class lIlIII1111 implements I1llI1ll1l {
    private static lIlIII1111 f = new lIlIII1111(new l1lllIll1I());
    protected I11lI1I1I1.Il1I11IlII.l111ll1lll.II1I1I1llI.llIIII1IlI a = new I11lI1I1I1.Il1I11IlII.l111ll1lll.II1I1I1llI.llIIII1IlI();
    private Date b;
    private boolean c;
    private l1lllIll1I d;
    private boolean e;

    private lIlIII1111(l1lllIll1I l1lllill1i) {
        this.d = l1lllill1i;
    }

    public static lIlIII1111 a() {
        if (lIIlI111II.I1IIl11lll(740682500L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{109, 30, 8, 45, 59, 96, 69, 71, 13, 52, 68, 4, Byte.MAX_VALUE, 95, 122, 79, 44, 55, 22, 7, 93, 19, 120}));
        }
        return f;
    }

    private void c() throws InvalidObjectException, CertPathValidatorException, UnknownHostException, InvalidAlgorithmParameterException {
        if (!this.c || this.b == null) {
            return;
        }
        Iterator<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> it = llIllIlll1.c().a().iterator();
        while (it.hasNext()) {
            it.next().getAdSessionStatePublisher().a(b());
        }
    }

    public void a(Context context) throws UTFDataFormatException, CertificateEncodingException {
        if (I1I1IIIIl1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{95, 9, 3, 82, 56, 76, 78, 101, 94, 28, 95, 114, 89}), 6166)) {
            throw new ProviderException(I1I1lI1II1.a(new byte[]{110, 20, 52, 86, 11, 1, 116, 94, 84, 30, 87, 83, 123, 73, 112, 12, 51, 55}));
        }
        if (this.c) {
            return;
        }
        this.d.a(context);
        this.d.a(this);
        this.d.e();
        this.e = this.d.c();
        this.c = true;
    }

    @Override // I11l1Ill1l.l11lllllll.l1IlIllI11.I1llI1ll1l
    public void a(boolean z) throws InvalidObjectException, CertPathValidatorException, UnknownHostException, InvalidAlgorithmParameterException {
        if (!this.e && z) {
            d();
        }
        this.e = z;
    }

    public Date b() throws BindException {
        Date date = this.b;
        Date date2 = date != null ? (Date) date.clone() : null;
        if (android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{116, 37, 24, 42, 81, 118, 122, 91, 86, 33, 81, 97, 109, 0, 108, 77, 3, 12, 36, 65, 123, 48, 4, 111}), 1047019601L)) {
            throw new BindException(I1I1lI1II1.a(new byte[]{99, 48, 26, 82, 6, 120, 0, 101, 83, 82, 64, 97, 64, 0, 121, 90, 87, 88, 56, 102, 66, 6, 105, 90, 102, 3}));
        }
        return date2;
    }

    public void d() throws InvalidObjectException, CertPathValidatorException, UnknownHostException, InvalidAlgorithmParameterException {
        Date dateA = this.a.a();
        Date date = this.b;
        if (date == null || dateA.after(date)) {
            this.b = dateA;
            c();
        }
    }
}
