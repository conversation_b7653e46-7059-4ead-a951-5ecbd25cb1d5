package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.media.content.lIIllIlIl1;
import android.view.View;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.core.location.Il1l11I11I;
import androidx.interpolator.view.animation.lI11IlI1lI;
import java.io.IOException;
import java.net.UnknownHostException;
import java.net.UnknownServiceException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIII1IlIlI;

/* loaded from: classes.dex */
public class IllIIll11l {
    private final lI11IIlll1.l1I1l11Il1.IIIlI11I1I.IlIlIlI11I.lIlIII1111 a;
    private final String b;
    private final lIII1IlIlI c;
    private final String d;

    public IllIIll11l(View view, lIII1IlIlI liii1ilili, String str) {
        this.a = new lI11IIlll1.l1I1l11Il1.IIIlI11I1I.IlIlIlI11I.lIlIII1111(view);
        this.b = view.getClass().getCanonicalName();
        this.c = liii1ilili;
        this.d = str;
    }

    public String a() throws IOException {
        if (lI11IlI1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{114, 60, 17, 38, 40, 114, 92, Byte.MAX_VALUE, 78, 46, 2, 86, 113, 106, 90, 112, 59}), 603330663L)) {
            throw new IOException(I1I1lI1II1.a(new byte[]{98, 45, 14, 14, 16, 77, 7, 85, 116, 9, 69, 105, 83, 105, 82, 122, 84, 44, 3, 0, 123, 56, 126, 123, 117, 88, 116, 40}));
        }
        return this.d;
    }

    public lIII1IlIlI b() throws UnknownHostException, UnknownServiceException {
        if (lIIlI111II.l1l11llIl1(271545423L)) {
            throw new UnknownServiceException(I1I1lI1II1.a(new byte[]{81, 18, 12, 48, 59, 101, 116, 9, 75, 22, 64, 3, 119, 107, 115, Byte.MAX_VALUE, 5, 49, 18, 83, 69, 44, 95, 13, 80, 15, 68, 60, 60}));
        }
        lIII1IlIlI liii1ilili = this.c;
        if (lIIllIlIl1.IlII1Illll(162600545L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{91, 93, 10, 38, 23, 92, 79, 84, 118, 86, 81, 73, 115, 10, 92, 103, 59, 46, 6, 104}));
        }
        return liii1ilili;
    }

    public lI11IIlll1.l1I1l11Il1.IIIlI11I1I.IlIlIlI11I.lIlIII1111 c() {
        lI11IIlll1.l1I1l11Il1.IIIlI11I1I.IlIlIlI11I.lIlIII1111 liliii1111 = this.a;
        if (Il1l11I11I.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{0, 29, 35, 52, 43}), 470892224L)) {
            throw new ArrayIndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{95, 23, 10, 50, 26, 13, 90}));
        }
        return liliii1111;
    }

    public String d() throws NoSuchMethodException {
        if (I1IllIll1l.l11I11I11l(721519051L)) {
            throw new NoSuchMethodException(I1I1lI1II1.a(new byte[]{111, 30, 5, 15, 36, 103, 99, 4, 125, 28, 113, 85, 112, 96, 6, 114, 17, 40}));
        }
        return this.b;
    }
}
