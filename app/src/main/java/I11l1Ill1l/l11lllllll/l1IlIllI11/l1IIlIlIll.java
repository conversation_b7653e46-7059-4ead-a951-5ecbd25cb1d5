package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import androidx.versionedparcelable.custom.entities.IllIIIIII1;
import java.io.InvalidObjectException;
import java.io.NotActiveException;
import java.security.GeneralSecurityException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
class l1IIlIlIll extends BroadcastReceiver {
    final /* synthetic */ IIllIlIl1l a;

    l1IIlIlIll(IIllIlIl1l iIllIlIl1l) {
        this.a = iIllIlIl1l;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x005e A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:14:0x005f  */
    @Override // android.content.BroadcastReceiver
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void onReceive(Context context, Intent intent) throws GeneralSecurityException, InvalidObjectException, NotActiveException, CloneNotSupportedException {
        IIllIlIl1l iIllIlIl1l;
        boolean zA;
        boolean z;
        if (IllIIIIII1.Il1IIlI1II(2647)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{64, 16, 52, 21, 86, 0, 125, 103, 15, 19, 120, 73, 116, 8, 117, 97, 35, 35, 4, 99, 98, 86, 88, 65, 118, 5, 7, 60, 10, 88, 2}));
        }
        if (!intent.getAction().equals(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 80, 10, 68, 85, 91, 77, 26, 84, 1, 21, 11, 93, 94, 79, 96, 118, 97, 115, 114, 43, 59, 125, 113, 37}))) {
            if (intent.getAction().equals(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 80, 10, 68, 85, 91, 77, 26, 84, 1, 21, 11, 93, 94, 79, 96, 118, 97, 115, 114, 43, 59, 125, 121}))) {
                iIllIlIl1l = this.a;
                zA = IIllIlIl1l.a(iIllIlIl1l);
                z = false;
            }
            if (!android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{121, 37, 18, 45, 35, 115, Byte.MAX_VALUE, 125, 107, 23, 103, 101, 3, 122, 83, 69, 84, 36, 39, 119, 106}), 705865363L)) {
                throw new UnsupportedOperationException(I1I1lI1II1.a(new byte[]{102, 52, 91}));
            }
            return;
        }
        iIllIlIl1l = this.a;
        zA = IIllIlIl1l.a(iIllIlIl1l);
        z = true;
        iIllIlIl1l.a(z, zA);
        IIllIlIl1l.a(this.a, z);
        if (!android.support.v4.graphics.drawable.III1Il1II1.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{121, 37, 18, 45, 35, 115, Byte.MAX_VALUE, 125, 107, 23, 103, 101, 3, 122, 83, 69, 84, 36, 39, 119, 106}), 705865363L)) {
        }
    }
}
