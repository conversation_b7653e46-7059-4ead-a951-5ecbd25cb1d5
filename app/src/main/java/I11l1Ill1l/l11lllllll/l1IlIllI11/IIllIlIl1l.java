package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.accounts.utils.lIIIIII11I;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.IntentFilter;
import androidx.constraintlayout.widget.IIlI1Il1lI;
import androidx.constraintlayout.widget.Il1lII1l1l;
import androidx.core.location.IIlIIlIII1;
import androidx.core.location.IllIlllIII;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.io.InvalidObjectException;
import java.lang.ref.WeakReference;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.cert.CertPathValidatorException;
import java.security.cert.CertificateExpiredException;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class IIllIlIl1l {
    private static IIllIlIl1l d = new IIllIlIl1l();
    private WeakReference<Context> a;
    private boolean b = false;
    private boolean c = false;

    static /* synthetic */ boolean a(IIllIlIl1l iIllIlIl1l) throws CertPathValidatorException, CloneNotSupportedException {
        if (android.support.v4.graphics.drawable.l11Il111ll.IlII1Illll(271797476L)) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{118, 38, 14, 28}));
        }
        boolean z = iIllIlIl1l.c;
        if (Il1lII1l1l.I1lllI1llI(394277129L)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{97, 85, 23, 52, 54, 81, 83, 89, 67, 19, 73, 67, 116, 67, 87, 95, 1, 47, 53, 116, 8, 49, 102, 94, 6, 122}));
        }
        return z;
    }

    static /* synthetic */ boolean a(IIllIlIl1l iIllIlIl1l, boolean z) throws GeneralSecurityException {
        if (IIlI1Il1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{97, 35, 91, 86, 52, 12, 122, 118, 67, 38, Byte.MAX_VALUE}), 174711504L)) {
            throw new GeneralSecurityException(I1I1lI1II1.a(new byte[]{98, 55, 82, 86, 3, 77, 91, 88, 107, 13, 103, 97, 108, 99}));
        }
        iIllIlIl1l.b = z;
        return z;
    }

    public static IIllIlIl1l b() {
        return d;
    }

    public void a() throws InvalidObjectException, InvalidKeyException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (lIIIIII11I.IllIlI1l1I(I1I1lI1II1.a(new byte[]{86, 30, 46, 7}), 1350421899L)) {
            throw new ExceptionInInitializerError(I1I1lI1II1.a(new byte[]{112, 80}));
        }
        Context context = this.a.get();
        if (context == null) {
            if (l1l1IllI11.lI11llll1I(I1I1lI1II1.a(new byte[]{113, 54, 80, 46, 46, 1, 101, 69, 73, 46, 7, 69, 82, 122, 65, 100, 32, 10, 53}), 228703318L)) {
                throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{80, 19, 40, 84, 11}));
            }
            return;
        }
        boolean zIsDeviceLocked = ((KeyguardManager) context.getSystemService(I1I1lI1II1.a(new byte[]{92, 1, 27, 2, 23, 84, 69, 84}))).isDeviceLocked();
        a(this.b, zIsDeviceLocked);
        this.c = zIsDeviceLocked;
        if (l11Il1lI11.I1II1111ll(7955)) {
            throw new IllegalAccessError(I1I1lI1II1.a(new byte[]{95, 87, 58, 43, 51, 82, 4, 3, 93, 32, 126, 125, 12, 75, Byte.MAX_VALUE, 125, 22, 81, 84, 94, 88, 6}));
        }
    }

    public void a(Context context) throws CertificateExpiredException {
        if (context == null) {
            if (IIlIIlIII1.I1lllI1llI(324210904L)) {
                throw new CertificateExpiredException(I1I1lI1II1.a(new byte[]{102, 60, 1, 39, 32, 101, 78, 8, 92, 3, 1, 66, 93}));
            }
        } else {
            this.a = new WeakReference<>(context);
            IntentFilter intentFilter = new IntentFilter(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 80, 10, 68, 85, 91, 77, 26, 84, 1, 21, 11, 93, 94, 79, 96, 118, 97, 115, 114, 43, 59, 125, 113, 37}));
            intentFilter.addAction(I1I1lI1II1.a(new byte[]{86, 10, 6, 23, 13, 92, 83, 30, 80, 10, 68, 85, 91, 77, 26, 84, 1, 21, 11, 93, 94, 79, 96, 118, 97, 115, 114, 43, 59, 125, 121}));
            context.registerReceiver(new l1IIlIlIll(this), intentFilter);
        }
    }

    public void a(boolean z, boolean z2) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if ((z2 || z) == (this.c || this.b)) {
            if (IllIlllIII.I1lllI1llI(I1I1lI1II1.a(new byte[]{115, 37, 21, 81, 0, 3, 88, 73, 79, 5, 92, 126, 89, 95, Byte.MAX_VALUE, 108, 84, 47, 21, 88, 70, 19, 6, 118, 0}), 8550)) {
                throw new VerifyError(I1I1lI1II1.a(new byte[]{126, 16, 53, 63, 44, 109, 95, 70, 122, 81, 94, 93, 90, 119, 65, 83, 53, 45, 26, 120}));
            }
        } else {
            Iterator<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> it = llIllIlll1.c().b().iterator();
            while (it.hasNext()) {
                it.next().getAdSessionStatePublisher().b(z2 || z);
            }
        }
    }
}
