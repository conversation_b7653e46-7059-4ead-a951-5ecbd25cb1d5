package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.accounts.utils.I1lllI11II;
import android.accounts.utils.Ill11ll111;
import android.accounts.utils.lIIIIII11I;
import android.media.content.II1I11IlI1;
import android.media.content.IIl1l1IllI;
import android.os.Handler;
import android.os.Looper;
import android.support.v4.graphics.drawable.Il1IIllIll;
import android.support.v4.graphics.drawable.lI1lllIII1;
import android.text.TextUtils;
import android.webkit.WebView;
import androidx.core.location.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.l11Il1lI11;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.versionedparcelable.custom.entities.IIlII1IIIl;
import androidx.versionedparcelable.custom.entities.l1lI1I1l11;
import androidx.versionedparcelable.custom.entities.l1lll111II;
import java.io.InvalidObjectException;
import java.io.SyncFailedException;
import java.net.UnknownHostException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidParameterException;
import java.security.KeyManagementException;
import java.security.cert.CRLException;
import java.security.cert.CertPathValidatorException;
import java.util.concurrent.BrokenBarrierException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.llIll1IlI1;
import org.json.JSONObject;

/* loaded from: classes.dex */
public class l1ll11l111 {
    private static l1ll11l111 a = new l1ll11l111();

    private l1ll11l111() {
    }

    public static final l1ll11l111 a() {
        return a;
    }

    public void a(WebView webView, String str) throws SyncFailedException, InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (Il11II1llI.IlIllIll1I(lIIlI111II.l1l1Il1I11)) {
            throw new SyncFailedException(I1I1lI1II1.a(new byte[]{121, 52, 47, 85, 54, 121, 3, 103, 105, 1, 94, 66, 88, 15, 82, 96, 12, 59, 7, 125, 114, 27}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{81, 13, 12, 12, 17, 93, 100, 85, 74, 23, 89, 95, 91}), str);
    }

    public void a(WebView webView, String str, float f) throws InvalidObjectException, CertPathValidatorException, UnknownHostException, CloneNotSupportedException, InvalidAlgorithmParameterException {
        if (Ill11ll111.lIIIIlIIl1(I1I1lI1II1.a(new byte[]{2, 86, 51, 32, 1, 95, 112, 117, 125, 61, 90, 117, 88, 78, 91, 114, 18, 19, 12, 68, 104, 34, 100, 7, 75, 5, 116, 87}), 197470933L)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{1, 85, 7, 86, 22, 3, 122, 67, 125, 12, 64, 82, 64, 73, 64, 69, 4, 37, 84, 1, 95, 39, 10}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{68, 1, 22, 33, 7, 67, 94, 83, 92, 50, 95, 92, 64, 84, 81}), Float.valueOf(f), str);
        if (IIlI1ll1ll.lI11llll1I(I1I1lI1II1.a(new byte[]{94, 61, 49, 23}), I1I1lI1II1.a(new byte[]{100, 18, 46, 55, 6, 4, 3, 66, Byte.MAX_VALUE, 37, 81, 96, 87, 72, 92, 68, 48, 80, 9, 7, 8, 10, 118, 124, 68, 122}))) {
            throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{64, 23, 24, 63, 48}));
        }
    }

    public void a(WebView webView, String str, String str2) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{68, 1, 22, 33, 7, 67, 94, 83, 92, 40, 95, 83, 94, 106, 64, 84, 22, 4}), str2);
    }

    public void a(WebView webView, String str, String str2, JSONObject jSONObject) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (l1lll111II.Il1IIlI1II(162150336L)) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{79, 61, 81, 63, 42, 97, 115, 70, 107, 6, 105, 88, 112, 122, 69, 66, 52, 17, 22, 6, Byte.MAX_VALUE, 51, 123, 115, 86, 102, 122}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{71, 17, 0, 9, 11, 70, 95, 125, 92, 0, 89, 81, 112, 79, 81, 91, 22}), str2, jSONObject, str);
    }

    public void a(WebView webView, String str, llIll1IlI1 llill1ili1, String str2) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{82, 22, 16, 10, 16}), llill1ili1.toString(), str2, str);
    }

    public void a(WebView webView, String str, JSONObject jSONObject) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (I1lllI11II.l111l1I1Il(I1I1lI1II1.a(new byte[]{68, 9, 8, 17, 91, 83, 6, 81, 88, 45, 124, 73, 13, 107, 112, 77, 87, 0, 18, 70, 2, 48, 89, 94, 86, 89, 103}), I1I1lI1II1.a(new byte[]{109, 20, 27, 43, 3, 100, 4, 119, 81, 14, 92, 73, 93, 64, 119, 87, 38, 17, 52, 93, 104, 14}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{83, 48, 19, 44, 56, 102, 84, 94, 80, 6}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{94, 10, 11, 17}), jSONObject, str);
    }

    public void a(WebView webView, String str, JSONObject jSONObject, JSONObject jSONObject2, JSONObject jSONObject3) throws InvalidObjectException, BrokenBarrierException, CertPathValidatorException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{68, 16, 3, 23, 22, 102, 82, 67, 74, 13, 95, 94}), str, jSONObject, jSONObject2, jSONObject3);
        if (lIlIII1I1l.IlIllIll1I(164827740L)) {
            throw new BrokenBarrierException(I1I1lI1II1.a(new byte[]{3, 80, 47, 38, 38, 64, 103, 70, 72, 15, 74, 4, 64, 109, 94, 66, 42, 11, 43, 80, 94, 6, 6, 81, 113, 99, 78}));
        }
    }

    void a(WebView webView, String str, Object... objArr) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (webView != null) {
            StringBuilder sb = new StringBuilder(128);
            sb.append(I1I1lI1II1.a(new byte[]{94, 2, 74, 18, 11, 91, 83, 95, 78, 74, 95, 93, 92, 93, 118, 71, 11, 5, 5, 87, 17, 92, 14, 64, 93, 82, 82, 3, 13, 92, 82, 7, 30, 31, 13, 8, 11, 81, 117, 66, 80, 0, 87, 85, 27}));
            sb.append(str);
            sb.append(I1I1lI1II1.a(new byte[]{31}));
            a(sb, objArr);
            sb.append(I1I1lI1II1.a(new byte[]{30, 25}));
            a(webView, sb);
        } else {
            I11lI1I1I1.Il1I11IlII.l111ll1lll.II1I1I1llI.l1lllIll1I.a(I1I1lI1II1.a(new byte[]{99, 12, 7, 69, 53, 80, 85, 102, 80, 1, 71, 16, 92, 74, 20, 91, 23, 13, 14, 18, 86, 14, 65, 21}) + str);
        }
        if (l1lI1I1l11.l1l1l1IIlI(274129956L)) {
            throw new InvalidObjectException(I1I1lI1II1.a(new byte[]{113, 82, 52, 7, 51, 101, 71, 89, 88, 34, 81, 94, 67, 85, 65}));
        }
    }

    void a(WebView webView, StringBuilder sb) throws CertPathValidatorException, InvalidAlgorithmParameterException {
        String string = sb.toString();
        Handler handler = webView.getHandler();
        if (handler == null || Looper.myLooper() == handler.getLooper()) {
            c(webView, string);
        } else {
            handler.post(new I11lI11III(this, webView, string));
        }
    }

    public void a(WebView webView, JSONObject jSONObject) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        if (Il1IIllIll.I1lllI1llI(529824756L)) {
            throw new InstantiationError(I1I1lI1II1.a(new byte[]{94, 37, 40, 29, 53, 12, 99, 123, 78, 18, 105, 3, 89, 8, 101}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{68, 1, 22, 41, 3, 70, 67, 113, 90, 16, 89, 70, 92, 77, 77}), jSONObject);
    }

    void a(StringBuilder sb, Object[] objArr) {
        String string;
        if (II1I11IlI1.llII1lIIlI(426949912L)) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{103, 23, 12, 28, 22, 1, 122, 103, 0, 3, 97, 85, 0, 107, 112, 2, 10, 88, 54, 80, 87, 52, 87, 120, 10, 113, 93, 86, 50}));
        }
        if (objArr == null || objArr.length <= 0) {
            return;
        }
        for (Object obj : objArr) {
            if (obj == null) {
                string = I1I1lI1II1.a(new byte[]{89, 17, 14, 9});
            } else {
                if (obj instanceof String) {
                    string = obj.toString();
                    if (!string.startsWith(I1I1lI1II1.a(new byte[]{76}))) {
                        sb.append('\"').append(string).append('\"');
                    }
                } else {
                    sb.append(obj);
                }
                sb.append(I1I1lI1II1.a(new byte[]{27}));
            }
            sb.append(string);
            sb.append(I1I1lI1II1.a(new byte[]{27}));
        }
        sb.setLength(sb.length() - 1);
    }

    public void b(WebView webView, String str) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{71, 17, 0, 9, 11, 70, 95, 121, 84, 20, 66, 85, 70, 74, 93, 90, 12, 36, 20, 87, 94, 21}), str);
    }

    public void b(WebView webView, String str, String str2) throws InvalidObjectException, CertPathValidatorException, CRLException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{68, 1, 22, 43, 3, 65, 94, 70, 92, 50, 89, 85, 66, 113, 93, 80, 16, 0, 16, 81, 88, 24}), str2, str);
        if (IIl1l1IllI.Ill1lIIlIl(5794)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{78, 15, 80, 82, 43, 108, 111, 6, 125, 60, 95, 98, 76, 91, 114, 113, 41, 45, 27, Byte.MAX_VALUE, 5}));
        }
    }

    public void b(WebView webView, String str, JSONObject jSONObject) throws InvalidObjectException, CertPathValidatorException, InvalidAlgorithmParameterException {
        a(webView, I1I1lI1II1.a(new byte[]{71, 17, 0, 9, 11, 70, 95, 124, 86, 5, 84, 85, 81, 124, 66, 80, 12, 21}), jSONObject, str);
    }

    public void c(WebView webView, String str, String str2) throws InvalidObjectException, CertPathValidatorException, KeyManagementException, InvalidAlgorithmParameterException {
        if (androidx.constraintlayout.widget.lIIlI111II.II1lllllII(232455529L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{15, 6, 80, 11, 1, 98, 66, 2, 116, 18, 83, 81, 7, 119, 64, 0, 35, 20, 49, 122, 105, 57, 4, 2}));
        }
        a(webView, I1I1lI1II1.a(new byte[]{68, 1, 22, 54, 22, 84, 67, 85}), str2, str);
        if (lIIIIII11I.I1lllI1llI(354499372L)) {
            throw new AbstractMethodError(I1I1lI1II1.a(new byte[]{80, 35, 81, 52, 53, 91, 65}));
        }
    }

    public boolean c(WebView webView, String str) throws CertPathValidatorException, InvalidAlgorithmParameterException {
        if (IIlII1IIIl.l1Il11I1Il(I1I1lI1II1.a(new byte[]{91, 51, 81, 82}), 10966)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{118, 39, 55, 63, 91, 87, 125, 9, 10, 93, 81, 114, 100, 91, 0, 13, 6, 11, 80, 85, 9, 82, 74, 111, 64, 96, 91}));
        }
        if (webView == null || TextUtils.isEmpty(str)) {
            return false;
        }
        try {
            webView.evaluateJavascript(str, null);
        } catch (IllegalStateException unused) {
            webView.loadUrl(I1I1lI1II1.a(new byte[]{93, 5, 20, 4, 17, 86, 69, 89, 73, 16, 10, 16}) + str);
        }
        if (lI1lllIII1.Il1IIlI1II(8185)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{99, 84, 81, 3, 17, Byte.MAX_VALUE, 112, 85, 14, 86, 71, 126, 101, 120, 7, 100, 35, 17, 38, 123, 117, 17, 68, 3, 7, 87, 103, 34, 34, 119, 82, 57}));
        }
        return true;
    }

    public void d(WebView webView, String str, String str2) throws CertPathValidatorException, InvalidAlgorithmParameterException {
        if (l11Il1lI11.Il1IIlI1II(I1I1lI1II1.a(new byte[]{2, 81, 24, 45, 5, 114, 67, 9, 80, 46, 88, 122, 69, 110, 7, 80}))) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{125, 55, 19, 39}));
        }
        if (str == null || TextUtils.isEmpty(str2)) {
            return;
        }
        c(webView, I1I1lI1II1.a(new byte[]{31, 2, 23, 11, 1, 65, 94, 95, 87, 76, 25, 16, 78, 77, 92, 92, 17, 79, 13, 95, 89, 5, 101, 80, 65, 95, 81, 12, 7, 83, 67, 10, 88, 10, 50, 23, 13, 69, 82, 66, 77, 13, 85, 67, 21, 4, 20, 65, 10, 8, 17, 28, 95, 12, 90, 81, 101, 83, 69, 12, 2, 91, 84, 2, 67, 13, 13, 11, 50, 71, 88, 64, 92, 22, 68, 89, 80, 74, 20, 73, 30, 65, 25, 79, 11, 46, 81, 95, 86, 85, 67, 75, 0, 87, 81, 10, 89, 1, 50, 23, 13, 69, 82, 66, 77, 29, 24, 68, 93, 80, 71, 27, 13, 12, 11, 86, 102, 4, 65, 92, 85, 95, 84, 4, 16, 91, 88, 13, 103, 22, 13, 21, 7, 71, 67, 89, 92, 23, 28, 16, 18, 80, 90, 95, 7, 2, 22, 91, 95, 15, 122, 81, 20, 26, 23, 30, 3, 87, 67, 89, 23, 2, 23, 11, 1, 65, 94, 95, 87, 76, 25, 16, 78, 79, 85, 71, 66, 2, 23, 64, 66, 4, 93, 65, 96, 85, 69, 12, 20, 70, 23, 94, 23, 0, 13, 6, 23, 88, 82, 94, 77, 68, 22, 22, 21, 93, 91, 86, 23, 12, 7, 92, 68, 79, 80, 64, 65, 68, 82, 11, 16, 97, 84, 17, 94, 20, 22, 94, 16, 80, 67, 69, 75, 10, 16, 83, 64, 75, 70, 80, 12, 21, 49, 81, 66, 8, 67, 65, 19, 16, 17, 69, 7, 71, 69, 17, 82, 10, 22, 54, 1, 71, 94, 64, 77, 74, 87, 85, 65, 120, 64, 65, 16, 8, 0, 71, 68, 4, 27, 18, 87, 87, 67, 4, 73, 91, 89, 9, 82, 7, 22, 12, 13, 91, 26, 89, 93, 67, 25, 11, 72, 21, 20, 86, 13, 15, 4, 91, 87, 20, 65, 84, 81, 90, 82, 95, 68, 70, 69, 22, 82, 25, 75, 94, 20, 84, 69, 16, 74, 7, 66, 89, 69, 77, 20, 8, 66, 5, 13, 81, 69, 12, 86, 91, 71, 24, 84, 23, 1, 83, 67, 6, 114, 8, 7, 8, 7, 91, 67, 24, 30, 23, 83, 66, 92, 73, 64, 18, 75, 90, 17, 81, 66, 8, 67, 65, 29, 69, 82, 17, 37, 70, 67, 17, 94, 6, 23, 17, 7, 29, 21, 68, 64, 20, 85, 18, 25, 27, 64, 80, 26, 21, 77, 88, 81, 23, 82, 70, 80, 68, 94, 21, 16, 16, 30, 88, 68, 7, 16, 12, 18, 65, 25, 67, 92, 16, 113, 68, 65, 75, 93, 87, 23, 21, 7, 26, 18, 18, 65, 86, 17, 26, 21, 64, 55, 113, 101, 42, 103, 48, 61, 54, 48, 118, 18, 18, 16, 95, 67, 83, 71, 80, 68, 65, 76, 18, 7, 70, 113, 21, 71, 71, 90, 84, 66, 17, 1, 26, 21, 7, 86, 16, 3, 72, 11, 91, 93, 85, 90, 16, 89, 95, 91, 20, 93, 81, 64, 77, 64, 23, 121, 47, 121, 112, 112, 98, 126, 42, 42, 109, 126, 39, 18, 70, 75, 94, 6, 90, 84, 69, 84, 1, 94, 68, 27, 91, 91, 81, 27, 79, 3, 66, 64, 4, 93, 81, 112, 94, 94, 9, 0, 26, 68, 0, 69, 13, 18, 17, 75, 14, 74, 25, 17, 77, 11}).replace(I1I1lI1II1.a(new byte[]{18, 55, 33, 55, 43, 101, 99, 111, 106, 54, 115, 21}), str).replace(I1I1lI1II1.a(new byte[]{18, 45, 44, 47, 39, 118, 99, 121, 118, 42, 111, 121, 113, 28}), str2));
    }
}
