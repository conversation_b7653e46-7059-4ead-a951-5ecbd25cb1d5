package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.util.Log;
import android.view.View;
import androidx.constraintlayout.widget.I1IllIll1l;
import androidx.constraintlayout.widget.l1IIll1I1l;
import androidx.core.location.lI1lI11Ill;
import androidx.versionedparcelable.custom.entities.llIlI11III;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIII1IlIlI;

/* loaded from: classes.dex */
public class llIIII1IlI {
    private static final Pattern b = Pattern.compile(I1I1lI1II1.a(new byte[]{105, 63, 3, 72, 24, 116, 26, 106, 9, 73, 9, 16, 104, 18, 16}));
    private final List<IllIIll11l> a = new ArrayList();

    private void a(View view) {
        if (view == null) {
            throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{113, 22, 11, 0, 12, 81, 91, 73, 118, 6, 67, 68, 71, 76, 87, 65, 11, 14, 12, 18, 89, 18, 19, 91, 70, 90, 91}));
        }
    }

    private void a(String str) throws UnknownHostException {
        if (l1IIll1I1l.I1lllI1llI(I1I1lI1II1.a(new byte[]{95, 42, 32, 54, 55, 87, 95, 84, 109, 81, 99, 0, 98, 118, 12, 113, 15, 13, 11, 2, 68, 22, 66}), 9529)) {
            throw new UnknownHostException(I1I1lI1II1.a(new byte[]{95, 21, 47, 29, 83, 94, 71, 102, 91, 54, 94, Byte.MAX_VALUE, 77, 72, 88, 13, 13, 24, 44, 121, 114, 24, 112, 82, 7, 99, 81, 39, 48, 68}));
        }
        if (str != null) {
            if (str.length() > 50) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{113, 22, 11, 0, 12, 81, 91, 73, 118, 6, 67, 68, 71, 76, 87, 65, 11, 14, 12, 18, 88, 0, 64, 21, 87, 83, 67, 4, 13, 94, 82, 7, 23, 22, 7, 4, 17, 90, 89, 16, 86, 18, 85, 66, 21, 12, 4, 21, 1, 9, 3, 64, 81, 2, 71, 80, 65, 69, 23, 12, 10, 18, 91, 6, 89, 3, 22, 13}));
            }
            if (!b.matcher(str).matches()) {
                throw new IllegalArgumentException(I1I1lI1II1.a(new byte[]{113, 22, 11, 0, 12, 81, 91, 73, 118, 6, 67, 68, 71, 76, 87, 65, 11, 14, 12, 18, 88, 0, 64, 21, 87, 83, 67, 4, 13, 94, 82, 7, 23, 22, 7, 4, 17, 90, 89, 16, 77, 12, 81, 68, 21, 90, 91, 91, 22, 0, 11, 92, 67, 65, 80, 93, 82, 68, 86, 6, 16, 87, 69, 16, 23, 10, 13, 17, 66, 92, 89, 16, 98, 5, 29, 74, 104, 98, 117, 24, 56, 60, 57, 2, 29, 88, 110, 21, 92, 68, 23, 22, 20, 83, 84, 6}));
            }
        }
    }

    private IllIIll11l b(View view) {
        for (IllIIll11l illIIll11l : this.a) {
            if (illIIll11l.c().get() == view) {
                return illIIll11l;
            }
        }
        if (I1IllIll1l.llII1lIIlI(I1I1lI1II1.a(new byte[]{79, 28, 21, 4, 48, 65, 6, 122, 93, 8, 6, 99, 101, 119, 92, 13, 45, 48, 49, 2, 106, 17, 1, 98, 91}))) {
            throw new ArrayStoreException(I1I1lI1II1.a(new byte[]{66, 92, 83, 61, 23, 87, 69, 95, 111, 0, 87, 3, 0, 115, 0, 90, 43, 44, 55, 3, 101, 56, 67, 93}));
        }
        return null;
    }

    public List<IllIIll11l> a() {
        return this.a;
    }

    public void a(View view, lIII1IlIlI liii1ilili, String str) {
        if (llIlI11III.Il1IIlI1II(8477)) {
            Log.d(I1I1lI1II1.a(new byte[]{93, 7, 48, 39, 17, 5, 0, 70}), I1I1lI1II1.a(new byte[]{82}));
            return;
        }
        a(view);
        a(str);
        if (b(view) == null) {
            this.a.add(new IllIIll11l(view, liii1ilili, str));
        }
    }

    public void b() {
        this.a.clear();
    }

    public void c(View view) {
        a(view);
        IllIIll11l illIIll11lB = b(view);
        if (illIIll11lB != null) {
            this.a.remove(illIIll11lB);
        }
        if (lI1lI11Ill.l1l1l1IIlI(6497)) {
            throw new SocketTimeoutException(I1I1lI1II1.a(new byte[]{88, 10, 39, 28, 7, 111, 85, 96, 87, 28, 97, 71, 1, 112, 82, 66, 5, 40, 39, 123, 125, 27, Byte.MAX_VALUE, 95, 70, 1}));
        }
    }
}
