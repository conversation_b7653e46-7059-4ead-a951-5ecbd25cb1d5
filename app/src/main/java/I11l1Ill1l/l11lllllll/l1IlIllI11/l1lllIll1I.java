package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.ActivityManager$RunningAppProcessInfo;
import android.app.Application;
import android.app.Application$ActivityLifecycleCallbacks;
import android.content.Context;
import android.media.content.Il1llIl111;
import android.os.Bundle;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.util.Log;
import androidx.core.location.I1111IIl11;
import androidx.core.location.I111I11Ill;
import androidx.core.location.I11II1l1lI;
import androidx.core.location.IllIlllIII;
import androidx.core.location.l1l1I111I1;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.IIlI1ll1ll;
import androidx.recyclerview.widget.content.adapter.lIIlI111II;
import java.io.NotActiveException;
import java.io.UTFDataFormatException;
import java.security.AccessControlException;
import java.security.KeyManagementException;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class l1lllIll1I implements Application$ActivityLifecycleCallbacks {
    private boolean a;
    protected boolean b;
    private I1llI1ll1l c;

    private void a(boolean z) {
        if (this.b != z) {
            this.b = z;
            if (this.a) {
                b(z);
                I1llI1ll1l i1llI1ll1l = this.c;
                if (i1llI1ll1l != null) {
                    i1llI1ll1l.a(z);
                }
            }
        }
        if (Il11II1llI.IlII1Illll(I1I1lI1II1.a(new byte[]{88, 9, 37, 52, 80, 80, 98, 114, 83, 3, 123, 96, 87, 67, 110, 70, 8, 34, 22, 87, 68}), 349562055L)) {
            Log.w(I1I1lI1II1.a(new byte[]{14, 48, 21, 84, 3, 111}), I1I1lI1II1.a(new byte[]{126, 14, 22, 29, 11, 92, 79, 84, 84, 41, 120, 105, 121, 77}));
        }
    }

    private boolean a() throws UTFDataFormatException {
        boolean z = true;
        if (!(b().importance == 100) && !d()) {
            z = false;
        }
        if (lIIlI111II.II1lIllIll(4139)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{0, 42, 87, 50, 39, 12, 124, 66, 8, 84, 68, 83, 122, 67, 97, 94, 43, 27, 16, 74, 88, 13, 2, 114, Byte.MAX_VALUE, 81}));
        }
        return z;
    }

    public void a(I1llI1ll1l i1llI1ll1l) {
        this.c = i1llI1ll1l;
        if (I11II1l1lI.l1Il11I1Il(I1I1lI1II1.a(new byte[]{118, 12, 91, 15, 54, 13, 118, 64, 107, 43, 7, 89, 68, 88, 90, 113, 47, 89, 8, 102, 93}), 322899998L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{79, 3, 10, 6, 58, 77, 93, 102, 64, 7, 114, 7, 68, 83, 3, 81, 91, 14, 23, 104, 97, 34, 1, 4}));
        }
    }

    public void a(Context context) throws CertificateEncodingException {
        if (context instanceof Application) {
            ((Application) context).registerActivityLifecycleCallbacks(this);
        }
        if (IIlI1ll1ll.IlIIlIllI1(I1I1lI1II1.a(new byte[]{15, 19, 85, 87, 24, 77, 65, 5, 123, 44, 86, 99, 6, 82, 64, 109, 11, 83, 48, 87, 98, 47}), I1I1lI1II1.a(new byte[]{111, 60, 87, 82, 9, 91, 1}))) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{101, 44}));
        }
    }

    ActivityManager$RunningAppProcessInfo b() {
        ActivityManager$RunningAppProcessInfo activityManager$RunningAppProcessInfo = new ActivityManager$RunningAppProcessInfo();
        ActivityManager.getMyMemoryState(activityManager$RunningAppProcessInfo);
        return activityManager$RunningAppProcessInfo;
    }

    protected void b(boolean z) {
        if (IllIlllIII.Il1IIlI1II(1538440359L)) {
            throw new OutOfMemoryError(I1I1lI1II1.a(new byte[]{98, 61, 6, 36, 40, 118, 80, 90, 91, 48, 67, 126, 116, 114, 108, 2, 47, 54}));
        }
        if (Il1llIl111.I1lllI1llI(8999)) {
            throw new VerifyError(I1I1lI1II1.a(new byte[]{5, 93, 58, 55, 55, 77, 71, 84, 105, 80, 85, 100, 13, 11, 5, 86, 42, 42, 80, 116, 4, 56, 95, 89, 89, 100, 6}));
        }
    }

    public boolean c() throws KeyManagementException {
        if (I1111IIl11.Ill1lIIlIl(249594955L)) {
            throw new KeyManagementException(I1I1lI1II1.a(new byte[]{82, 14, 5, 2, 85, 112, 111, 94, 120, 30, 116, 83, 126, 126, 121}));
        }
        return this.b;
    }

    protected boolean d() throws NotActiveException {
        if (Il1llIl111.l1l1l1IIlI(425296269L)) {
            throw new NotActiveException(I1I1lI1II1.a(new byte[]{4, 92, 51, 85, 55, 99}));
        }
        return false;
    }

    public void e() throws UTFDataFormatException {
        if (l1l1I111I1.IlII1Illll(507921043L)) {
            Log.d(I1I1lI1II1.a(new byte[]{65, 52, 17, 61, 55, 118, 96, 5, 117, 15, 2, 3, 77, 119, 89, 120, 56, 84, 0, 124, 124, 85, 68, 6, 124}), I1I1lI1II1.a(new byte[]{112, 83, 82, 29, 7, 69, 122, 103, 124, 61, 95, 125, 67, 113, 92, 12, 20, 87, 10, 94, 126, 4, 68, 121}));
            return;
        }
        this.a = true;
        boolean zA = a();
        this.b = zA;
        b(zA);
    }

    public void f() {
        this.a = false;
        this.c = null;
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityCreated(Activity activity, Bundle bundle) {
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityDestroyed(Activity activity) {
        if (IlIIlI11I1.I1lllI1llI(509393705L)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{100, 41, 81, 2, 6, 111, 83, 5, 80, 46, 7, 119, Byte.MAX_VALUE, 1, 94, 126, 21, 50, 50, 69, 124}));
        }
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityPaused(Activity activity) {
        if (I111I11Ill.l11I11I11l(536293922L)) {
            throw new NoClassDefFoundError(I1I1lI1II1.a(new byte[]{121, 80, 87, 13, 15, 1, 2, 96, 75, 49, 95, Byte.MAX_VALUE}));
        }
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityResumed(Activity activity) {
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityStarted(Activity activity) {
        a(true);
        if (lIIllIlIl1.Il1IIlI1II(254438651L)) {
            throw new BootstrapMethodError(I1I1lI1II1.a(new byte[]{90, 82, 81, 1, 46, 126, 101, 68, 114}));
        }
    }

    @Override // android.app.Application$ActivityLifecycleCallbacks
    public void onActivityStopped(Activity activity) {
        a(a());
    }
}
