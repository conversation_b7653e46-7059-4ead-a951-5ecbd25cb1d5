package I11l1Ill1l.l11lllllll.l1IlIllI11;

import android.support.v4.graphics.drawable.lIIllIlIl1;
import android.view.View;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import androidx.recyclerview.widget.content.adapter.llIlII1IlI;
import java.io.InvalidObjectException;
import java.security.AccessControlException;
import java.security.DigestException;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyException;
import java.security.KeyManagementException;
import java.security.cert.CRLException;
import java.security.cert.CertPathValidatorException;
import java.util.Iterator;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public class l11Il111ll extends l1lllIll1I {
    private static l11Il111ll d = new l11Il111ll();

    private l11Il111ll() {
    }

    public static l11Il111ll g() {
        return d;
    }

    @Override // I11l1Ill1l.l11lllllll.l1IlIllI11.l1lllIll1I
    public void b(boolean z) throws DigestException, InvalidObjectException, CertPathValidatorException, KeyManagementException, CRLException, InvalidAlgorithmParameterException {
        if (llIlII1IlI.IIll1I11lI(244149373L)) {
            throw new CRLException(I1I1lI1II1.a(new byte[]{95}));
        }
        Iterator<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> it = llIllIlll1.c().b().iterator();
        while (it.hasNext()) {
            it.next().getAdSessionStatePublisher().a(z);
        }
    }

    @Override // I11l1Ill1l.l11lllllll.l1IlIllI11.l1lllIll1I
    public boolean d() throws KeyException {
        if (lIlIII1I1l.l1l1Il1I11(I1I1lI1II1.a(new byte[]{6, 16, 21, 35, 33, 0, 80, 118, 95, 29, 101, 81, 99, 83, 119, 69, 4, 44, 55, 69}), 281554309L)) {
            throw new KeyException(I1I1lI1II1.a(new byte[]{94, 39, 37, 6, 16, 86, Byte.MAX_VALUE, 98, 11, 49, 123, 121, 103, 113, 13, 64, 21, 23, 33, Byte.MAX_VALUE, 94, 6, 6, 102, 96, 113, 6}));
        }
        Iterator<lllIlI1lll.l1IlI11I1l.llIl1IlIll.lIl11lllI1.lIlIII1111> it = llIllIlll1.c().a().iterator();
        while (it.hasNext()) {
            View viewC = it.next().c();
            if (viewC != null && viewC.hasWindowFocus()) {
                return true;
            }
        }
        if (lIIllIlIl1.Il1IIlI1II(192094783L)) {
            throw new AccessControlException(I1I1lI1II1.a(new byte[]{103, 81, 23, 50, 14, 67, 111, 117, 75, 93}));
        }
        return false;
    }
}
