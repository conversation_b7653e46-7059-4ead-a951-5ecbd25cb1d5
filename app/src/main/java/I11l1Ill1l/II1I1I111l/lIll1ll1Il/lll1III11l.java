package I11l1Ill1l.II1I1I111l.lIll1ll1Il;

import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;
import ll11lllIIl.II111IIl1l.lI1lII1l1I.IIIII1I11I.llIIlI1llI;
import ll1l111llI.llIIIll1ll.I1lIlI1ll1.ll1111lllI.l1IIl11Ill;

/* loaded from: classes.dex */
public final class lll1III11l {
    public static final lll1III11l NOT_SET = new lll1III11l(-1, -1, -1);
    public final int bytesPerFrame;
    public final int channelCount;
    public final int encoding;
    public final int sampleRate;

    public lll1III11l(int i, int i2, int i3) {
        this.sampleRate = i;
        this.channelCount = i2;
        this.encoding = i3;
        this.bytesPerFrame = llIIlI1llI.isEncodingLinearPcm(i3) ? llIIlI1llI.getPcmFrameSize(i3, i2) : -1;
    }

    public String toString() {
        return I1I1lI1II1.a(new byte[]{118, 17, 6, 12, 13, 115, 88, 66, 84, 5, 68, 107, 70, 88, 89, 69, 14, 4, 48, 83, 68, 4, 14}) + this.sampleRate + I1I1lI1II1.a(new byte[]{27, 68, 1, 13, 3, 91, 89, 85, 85, 39, 95, 69, 91, 77, 9}) + this.channelCount + I1I1lI1II1.a(new byte[]{27, 68, 7, 11, 1, 90, 83, 89, 87, 3, 13}) + this.encoding + ']';
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof lll1III11l)) {
            return false;
        }
        lll1III11l lll1iii11l = (lll1III11l) obj;
        return this.sampleRate == lll1iii11l.sampleRate && this.channelCount == lll1iii11l.channelCount && this.encoding == lll1iii11l.encoding;
    }

    public int hashCode() {
        return l1IIl11Ill.a(Integer.valueOf(this.sampleRate), Integer.valueOf(this.channelCount), Integer.valueOf(this.encoding));
    }
}
