package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll;
import android.accounts.utils.lIIlI111II;
import android.support.v4.graphics.drawable.IlIIlI11I1;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.net.PortUnreachableException;
import java.security.cert.CertPathValidatorException;
import java.util.List;
import java.util.Map;
import java.util.Map$Entry;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.ai;
import kotlin.jvm.internal.am;
import kotlin.reflect.c;
import l1Il111I11.lI1I1l1I1l.l11IIl1l1I.l1I1lI1lIl.ll11llI1Il;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class l11ll111lI extends Ill1llllll {
    public final Map<c<?>, Map<c<?>, I11IIl1I1I<?>>> a;
    private final Map<c<?>, lIIlI11IlI> b;
    private final Map<c<?>, Function1<?, II11llll1I<?>>> c;
    private final Map<c<?>, Map<String, I11IIl1I1I<?>>> d;
    private final Map<c<?>, Function1<String, Il11I111ll<?>>> e;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    /* JADX WARN: Multi-variable type inference failed */
    public l11ll111lI(Map<c<?>, ? extends lIIlI11IlI> map, Map<c<?>, ? extends Map<c<?>, ? extends I11IIl1I1I<?>>> map2, Map<c<?>, ? extends Function1<?, ? extends II11llll1I<?>>> map3, Map<c<?>, ? extends Map<String, ? extends I11IIl1I1I<?>>> map4, Map<c<?>, ? extends Function1<? super String, ? extends Il11I111ll<?>>> map5) {
        super(null);
        Intrinsics.checkNotNullParameter(map, I1I1lI1II1.a(new byte[]{84, 8, 3, 22, 17, 7, 116, 95, 87, 16, 85, 72, 65, 76, 85, 89, 36, 0, 1, 70, 95, 19, 74}));
        Intrinsics.checkNotNullParameter(map2, I1I1lI1II1.a(new byte[]{71, 11, 14, 28, 32, 84, 68, 85, 11, 55, 85, 66, 92, 88, 88, 92, 24, 4, 16, 65}));
        Intrinsics.checkNotNullParameter(map3, I1I1lI1II1.a(new byte[]{71, 11, 14, 28, 32, 84, 68, 85, 11, 32, 85, 86, 84, 76, 88, 65, 49, 4, 16, 91, 81, 13, 90, 79, 86, 68, 103, 23, 11, 68, 94, 7, 82, 22}));
        Intrinsics.checkNotNullParameter(map4, I1I1lI1II1.a(new byte[]{71, 11, 14, 28, 32, 84, 68, 85, 11, 42, 81, 93, 80, 93, 103, 80, 16, 8, 3, 94, 89, 27, 86, 71, 64}));
        Intrinsics.checkNotNullParameter(map5, I1I1lI1II1.a(new byte[]{71, 11, 14, 28, 32, 84, 68, 85, 11, 32, 85, 86, 84, 76, 88, 65, 38, 4, 17, 87, 66, 8, 82, 89, 90, 76, 82, 23, 52, 64, 88, 21, 94, 0, 7, 23}));
        this.b = map;
        this.a = map2;
        this.c = map3;
        this.d = map4;
        this.e = map5;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll
    public <T> II11llll1I<T> a(c<? super T> cVar, T t) throws CloneNotSupportedException {
        Intrinsics.checkNotNullParameter(cVar, I1I1lI1II1.a(new byte[]{85, 5, 17, 0, 33, 89, 86, 67, 74}));
        Intrinsics.checkNotNullParameter(t, I1I1lI1II1.a(new byte[]{65, 5, 14, 16, 7}));
        if (!ll11llI1Il.a(t, cVar)) {
            if (Il11II1llI.l11I11I11l(5538)) {
                throw new CloneNotSupportedException(I1I1lI1II1.a(new byte[]{77, 49, 16, 4, 24, 120, 82, 103, 87, 85, 64, 89, 96, 125, 103, 87, 16, 81, 36, 116, 83, 42, 5, 5, 97, 108, 89}));
            }
            return null;
        }
        Map<c<?>, I11IIl1I1I<?>> map = this.a.get(cVar);
        I11IIl1I1I<?> i11IIl1I1I = map != null ? map.get(ai.b(t.getClass())) : null;
        I11IIl1I1I<?> i11IIl1I1I2 = i11IIl1I1I instanceof II11llll1I ? i11IIl1I1I : null;
        if (i11IIl1I1I2 != null) {
            return i11IIl1I1I2;
        }
        Function1<?, II11llll1I<?>> function1 = this.c.get(cVar);
        Function1<?, II11llll1I<?>> function12 = am.a(function1, 1) ? function1 : null;
        if (function12 != null) {
            return (II11llll1I) function12.invoke(t);
        }
        return null;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll
    public <T> Il11I111ll<? extends T> a(c<? super T> cVar, String str) throws CertPathValidatorException {
        if (IlIIlI11I1.Ill1lIIlIl(6449)) {
            throw new CertPathValidatorException(I1I1lI1II1.a(new byte[]{78, 83, 43, 11}));
        }
        Intrinsics.checkNotNullParameter(cVar, I1I1lI1II1.a(new byte[]{85, 5, 17, 0, 33, 89, 86, 67, 74}));
        Map<String, I11IIl1I1I<?>> map = this.d.get(cVar);
        I11IIl1I1I<?> i11IIl1I1I = map != null ? map.get(str) : null;
        if (!(i11IIl1I1I instanceof I11IIl1I1I)) {
            i11IIl1I1I = null;
        }
        if (i11IIl1I1I != null) {
            I11IIl1I1I<?> i11IIl1I1I2 = i11IIl1I1I;
            if (lIIlI111II.I1IlI11II1(545282087L)) {
                throw new IndexOutOfBoundsException(I1I1lI1II1.a(new byte[]{88, 6, 12, 33, 42, 7, 86, 116, 87, 54, 96, 94, 5, 12, 5, 69, 41, 4, 38, 94, 66, 46, 7, 124, 88, 81, 121, 54, 39, 96}));
            }
            return i11IIl1I1I2;
        }
        Function1<String, Il11I111ll<?>> function1 = this.e.get(cVar);
        Function1<String, Il11I111ll<?>> function12 = am.a(function1, 1) ? function1 : null;
        if (function12 != null) {
            return (Il11I111ll) function12.invoke(str);
        }
        return null;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll
    public <T> I11IIl1I1I<T> a(c<T> cVar, List<? extends I11IIl1I1I<?>> list) throws PortUnreachableException {
        if (lIlIII1I1l.I1II1111ll(1077191223L)) {
            throw new PortUnreachableException(I1I1lI1II1.a(new byte[]{109, 84, 54, 52, 15, 0, 117, 81, 106, 28, 7, 65, 2, 86, 65, 81, 23, 15, 8, 84, 72, 41, 121, 97}));
        }
        Intrinsics.checkNotNullParameter(cVar, I1I1lI1II1.a(new byte[]{92, 39, 14, 4, 17, 70}));
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{67, 29, 18, 0, 35, 71, 80, 69, 84, 1, 94, 68, 70, 106, 81, 71, 11, 0, 14, 91, 74, 4, 65, 70}));
        lIIlI11IlI liili11ili = this.b.get(cVar);
        I11IIl1I1I<?> i11IIl1I1IA = liili11ili != null ? liili11ili.a(list) : null;
        if (i11IIl1I1IA instanceof I11IIl1I1I) {
            return (I11IIl1I1I<T>) i11IIl1I1IA;
        }
        return null;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.Ill1llllll
    public void a(ll111IIlI1 ll111iili1) {
        Intrinsics.checkNotNullParameter(ll111iili1, I1I1lI1II1.a(new byte[]{84, 11, 14, 9, 7, 86, 67, 95, 75}));
        for (Map$Entry<c<?>, lIIlI11IlI> map$Entry : this.b.entrySet()) {
            c<?> key = map$Entry.getKey();
            lIIlI11IlI value = map$Entry.getValue();
            if (value instanceof IlllIIlIII) {
                Intrinsics.a((Object) key, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 71, 92, 82, 89, 7, 2, 22, 28, 123, 34, 95, 84, 64, 69, 11, 14, 11, 70, 91, 10, 89, 74, 35, 11, 27, 11}));
                I11IIl1I1I<?> i11IIl1I1IA = ((IlllIIlIII) value).a();
                Intrinsics.a((Object) i11IIl1I1IA, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 72, 27, 74, 81, 71, 11, 0, 14, 91, 74, 0, 71, 92, 92, 88, 25, 46, 55, 87, 69, 10, 86, 8, 11, 31, 7, 71, 11, 91, 86, 16, 92, 89, 91, 23, 117, 91, 27, 95}));
                ll111iili1.a(key, i11IIl1I1IA);
            } else if (value instanceof II1Il1llIl) {
                ll111iili1.a(key, ((II1Il1llIl) value).a());
            }
        }
        for (Map$Entry<c<?>, Map<c<?>, I11IIl1I1I<?>>> map$Entry2 : this.a.entrySet()) {
            c<?> key2 = map$Entry2.getKey();
            for (Map$Entry<c<?>, I11IIl1I1I<?>> map$Entry3 : map$Entry2.getValue().entrySet()) {
                c<?> key3 = map$Entry3.getKey();
                I11IIl1I1I<?> value2 = map$Entry3.getValue();
                Intrinsics.a((Object) key2, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 71, 92, 82, 89, 7, 2, 22, 28, 123, 34, 95, 84, 64, 69, 11, 14, 11, 70, 91, 10, 89, 74, 35, 11, 27, 11}));
                Intrinsics.a((Object) key3, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 71, 92, 82, 89, 7, 2, 22, 28, 123, 34, 95, 84, 64, 69, 11, 14, 11, 70, 91, 10, 89, 74, 35, 11, 27, 11}));
                Intrinsics.a((Object) value2, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 72, 27, 74, 81, 71, 11, 0, 14, 91, 74, 0, 71, 92, 92, 88, 25, 46, 55, 87, 69, 10, 86, 8, 11, 31, 7, 71, 11, 100, 25, 11, 86, 16, 94, 86, 64, 89, 11, 15, 26, 28, 67, 4, 65, 92, 82, 90, 94, 31, 5, 70, 94, 12, 89, 74, 11, 11, 22, 80, 69, 94, 88, 8, 30, 96, 89, 88, 64, 83, 13, 19, 15, 109, 83, 14, 94, 88, 92, 88, 124, 17, 74, 81, 86, 16, 67, 90}));
                ll111iili1.a(key2, key3, value2);
            }
        }
        for (Map$Entry<c<?>, Function1<?, II11llll1I<?>>> map$Entry4 : this.c.entrySet()) {
            c<?> key4 = map$Entry4.getKey();
            Function1<?, II11llll1I<?>> value3 = map$Entry4.getValue();
            Intrinsics.a((Object) key4, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 71, 92, 82, 89, 7, 2, 22, 28, 123, 34, 95, 84, 64, 69, 11, 14, 11, 70, 91, 10, 89, 74, 35, 11, 27, 11}));
            Intrinsics.a((Object) value3, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 115, 76, 90, 86, 22, 8, 13, 92, 1, 93, 115, 110, 99, 87, 69, 4, 9, 87, 67, 6, 69, 42, 3, 8, 7, 29, 89, 81, 84, 1, 16, 13, 21, 30, 66, 84, 14, 20, 7, 21, 25, 60, 19, 94, 92, 66, 91, 12, 10, 28, 118, 13, 78, 72, 66, 14, 13, 65, 91, 89, 87, 28, 30, 67, 80, 75, 93, 84, 14, 8, 24, 83, 68, 8, 92, 91, 29, 101, 82, 23, 13, 83, 91, 10, 77, 5, 22, 12, 13, 91, 100, 68, 75, 5, 68, 85, 82, 64, 8, 94, 13, 21, 14, 91, 94, 79, 114, 91, 74, 8, 8, 91, 31, 18, 92, 12, 67, 8, 11, 11, 26, 27, 68, 85, 75, 13, 81, 92, 92, 67, 85, 65, 11, 14, 12, 28, 93, 14, 87, 64, 95, 83, 68, 75, 55, 87, 69, 10, 86, 8, 11, 31, 7, 71, 68, 125, 86, 0, 69, 92, 80, 114, 64, 27, 50, 14, 14, 75, 93, 14, 65, 69, 91, 95, 84, 54, 1, 64, 94, 2, 91, 13, 24, 0, 16, 101, 69, 95, 79, 13, 84, 85, 71, 5, 95, 90, 22, 13, 11, 92, 30, 32, 93, 76, 13, 22, 74}));
            ll111iili1.b(key4, (Function1) am.b(value3, 1));
        }
        for (Map$Entry<c<?>, Function1<String, Il11I111ll<?>>> map$Entry5 : this.e.entrySet()) {
            c<?> key5 = map$Entry5.getKey();
            Function1<String, Il11I111ll<?>> value4 = map$Entry5.getValue();
            Intrinsics.a((Object) key5, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 71, 92, 82, 89, 7, 2, 22, 28, 123, 34, 95, 84, 64, 69, 11, 14, 11, 70, 91, 10, 89, 74, 35, 11, 27, 11}));
            Intrinsics.a((Object) value4, I1I1lI1II1.a(new byte[]{89, 17, 14, 9, 66, 86, 86, 94, 87, 11, 68, 16, 87, 92, 20, 86, 3, 18, 22, 18, 68, 14, 19, 91, 92, 88, 26, 11, 17, 94, 91, 67, 67, 29, 18, 0, 66, 94, 88, 68, 85, 13, 94, 30, 115, 76, 90, 86, 22, 8, 13, 92, 1, 93, 115, 110, 99, 87, 69, 4, 9, 87, 67, 6, 69, 42, 3, 8, 7, 29, 89, 81, 84, 1, 16, 13, 21, 30, 87, 89, 3, 18, 17, 124, 81, 12, 86, 18, 26, 107, 23, 14, 11, 70, 91, 10, 89, 74, 49, 17, 16, 92, 89, 87, 6, 72, 16, 91, 90, 77, 88, 92, 12, 25, 76, 65, 85, 19, 90, 84, 95, 95, 77, 4, 16, 91, 88, 13, 25, 32, 7, 22, 7, 71, 94, 81, 85, 13, 74, 81, 65, 80, 91, 91, 49, 21, 16, 83, 68, 4, 84, 76, 15, 89, 66, 17, 68, 89, 88, 23, 91, 13, 12, 75, 35, 91, 78, 14, 6, 90, 75, 16, 94, 86, 64, 89, 11, 15, 26, 28, 67, 4, 65, 92, 82, 90, 94, 31, 5, 70, 94, 12, 89, 74, 15, 10, 6, 64, 91, 85, 74, 74, 99, 85, 71, 80, 85, 89, 11, 27, 7, 64, 67, 44, 92, 81, 70, 90, 82, 46, 16, 28, 103, 12, 91, 29, 15, 10, 16, 69, 95, 89, 90, 32, 85, 67, 80, 75, 93, 84, 14, 8, 24, 87, 66, 49, 65, 90, 69, 95, 83, 0, 22, 14, 88, 22, 67, 68, 9, 10, 22, 89, 94, 94, 23, 37, 94, 73, 11, 25, 73}));
            ll111iili1.c(key5, (Function1) am.b(value4, 1));
        }
    }
}
