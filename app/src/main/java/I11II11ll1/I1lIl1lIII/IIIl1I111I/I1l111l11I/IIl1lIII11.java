package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import android.support.v4.graphics.drawable.Il1I1lllIl;
import android.support.v4.graphics.drawable.lIIlI111II;
import android.util.Log;
import com.google.android.gms.ads.RequestConfiguration;
import java.io.EOFException;
import java.io.UTFDataFormatException;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.s;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

@Metadata(d1 = {"\u0000\u0014\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0000\u0010\u0000\u001a\u0006\u0012\u0002\b\u00030\u0001\"\b\b\u0000\u0010\u0002*\u00020\u00032\u0010\u0010\u0004\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00010\u0005H\n¢\u0006\u0002\b\u0006"}, d2 = {"<anonymous>", "Lkotlinx/serialization/KSerializer;", RequestConfiguration.MAX_AD_CONTENT_RATING_T, "", "it", "", "invoke"}, k = 3, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
final class IIl1lIII11 extends s implements Function1<List<? extends I11IIl1I1I<?>>, I11IIl1I1I<?>> {
    final /* synthetic */ I11IIl1I1I<T> $$serializer;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    IIl1lIII11(I11IIl1I1I<T> i11IIl1I1I) {
        super(1);
        this.$$serializer = i11IIl1I1I;
    }

    public final I11IIl1I1I<?> invoke(List<? extends I11IIl1I1I<?>> list) throws UTFDataFormatException {
        if (Il1I1lllIl.l111IIlII1(I1I1lI1II1.a(new byte[]{122, 21, 4, 11, 14, 115, 122, 98, 13, 60, 104, 4, 66}), 177626012L)) {
            throw new UTFDataFormatException(I1I1lI1II1.a(new byte[]{85, 37, 83, 38, 14, 98, 70, 115, 67, 40, 74, 84, 70, 107, 77, 96, 18, 10, 8, 104, 101, 40, 101, 98, 88, Byte.MAX_VALUE, 85, 10}));
        }
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{94, 16}));
        return this.$$serializer;
    }

    @Override // kotlin.jvm.functions.Function1
    public /* synthetic */ I11IIl1I1I<?> invoke(List<? extends I11IIl1I1I<?>> list) throws EOFException, UTFDataFormatException {
        if (lIIlI111II.I1111IIl11(4078)) {
            throw new EOFException(I1I1lI1II1.a(new byte[]{1, 46, 35, 63, 55, 79, 118, 103, 13}));
        }
        I11IIl1I1I<?> i11IIl1I1IInvoke = invoke(list);
        if (!androidx.versionedparcelable.custom.entities.lIIlI111II.I1Ill1lIII(710432222L)) {
            return i11IIl1I1IInvoke;
        }
        Log.e(I1I1lI1II1.a(new byte[]{64, 28, 15, 19, 26, 118, 88, 115, 94, 83, 120, 1, 123, 83, 64, 95, 82, 8, 8, 4, 105, 54, 95, 1, 116, 90, 64, 35, 39}), I1I1lI1II1.a(new byte[]{103, 62, 8, 93, 54, 89, 121, 0, 96, 80, 89, 3, 5, 114, 101, 116}));
        return null;
    }
}
