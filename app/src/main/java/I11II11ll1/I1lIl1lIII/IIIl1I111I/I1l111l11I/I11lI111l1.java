package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import androidx.recyclerview.widget.content.adapter.I1I1IIIIl1;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import kotlin.collections.ak;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I11lI111l1 {
    private static final Ill1llllll a = new l11ll111lI(ak.b(), ak.b(), ak.b(), ak.b(), ak.b());

    public static final Ill1llllll a() {
        if (I1I1IIIIl1.l1l1l1IIlI(5556)) {
            throw new SecurityException(I1I1lI1II1.a(new byte[]{124, 22, 82, 16, 87, 66, 7, 86, 91, 52, 0, 90}));
        }
        Ill1llllll ill1llllll = a;
        if (lIlIII1I1l.l1l1Il1I11(I1I1lI1II1.a(new byte[]{96, 29, 6, 0, 58, 96, 97, 2, 64, 52, 115, 64, 6, 72, 97, 67, 81, 4, 56, 89, 90, 37, 122, 120, 120, 116, 15, 41, 50}), 827944809L)) {
            throw new NullPointerException(I1I1lI1II1.a(new byte[]{91, 3, 5, 80, 39, 87, 92, 105, 119, 17, 68, 101, 1, 79, 113, 3, 4, 14, 32}));
        }
        return ill1llllll;
    }
}
