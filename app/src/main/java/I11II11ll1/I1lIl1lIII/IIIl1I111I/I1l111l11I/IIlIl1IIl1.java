package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import androidx.interpolator.view.animation.lI11IlI1lI;
import androidx.recyclerview.widget.content.adapter.lIlIII1I1l;
import java.security.cert.CertificateEncodingException;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class IIlIl1IIl1 {
    public static final Ill1llllll a() throws CertificateEncodingException {
        if (lI11IlI1lI.l111l1I1Il(I1I1lI1II1.a(new byte[]{114, 38, 54, 9, 38, 119, 88, 7, 105, 6, 122, Byte.MAX_VALUE, 111, 108, 64, 82, 42, 34, 40, 84, 100, 89, 75, 65, 92, 102, 3, 32, 55, 4}), 599828912L)) {
            throw new CertificateEncodingException(I1I1lI1II1.a(new byte[]{92, 9, 56}));
        }
        Ill1llllll ill1llllllA = I11lI111l1.a();
        if (lIlIII1I1l.llII1lIIlI(177178185L)) {
            throw new IncompatibleClassChangeError(I1I1lI1II1.a(new byte[]{64, 30, 40, 47, 17, 126, 0, 4, 78, 39, 69}));
        }
        return ill1llllllA;
    }
}
