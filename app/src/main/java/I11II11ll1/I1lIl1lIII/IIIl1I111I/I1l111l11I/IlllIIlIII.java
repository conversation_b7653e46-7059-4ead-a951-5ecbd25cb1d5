package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import android.accounts.utils.I1lllI11II;
import android.media.content.lIIllIlIl1;
import androidx.constraintlayout.widget.l111Il1lI1;
import androidx.constraintlayout.widget.lIIlI111II;
import androidx.interpolator.view.animation.Il11II1llI;
import androidx.interpolator.view.animation.lI11IlI1lI;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.InvalidParameterException;
import java.util.List;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class IlllIIlIII extends lIIlI11IlI {
    private final I11IIl1I1I<?> a;

    public final I11IIl1I1I<?> a() {
        if (lIIllIlIl1.III11111Il(I1I1lI1II1.a(new byte[]{97, 3, 52}))) {
            throw new IllegalMonitorStateException(I1I1lI1II1.a(new byte[]{91, 84, 4, 34, 49, 115, 97, 106, 11, 62, 4, 2, 91, 82, 3, 113, 21, 56, 12, 75, 87, 6, 102, 82, 69, 0, 84, 52}));
        }
        return this.a;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.lIIlI11IlI
    public I11IIl1I1I<?> a(List<? extends I11IIl1I1I<?>> list) {
        if (Il11II1llI.Ill1lIIlIl(I1I1lI1II1.a(new byte[]{2, 17, 27, 19}), 744448680L)) {
            throw new NegativeArraySizeException(I1I1lI1II1.a(new byte[]{103, 18, 7, 84, 50}));
        }
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{67, 29, 18, 0, 35, 71, 80, 69, 84, 1, 94, 68, 70, 106, 81, 71, 11, 0, 14, 91, 74, 4, 65, 70}));
        return this.a;
    }

    public boolean equals(Object obj) throws InvalidKeyException, InvalidAlgorithmParameterException {
        if (lI11IlI1lI.ll1I1lII11(I1I1lI1II1.a(new byte[]{90, 32, 3, 80, 86, 70, 122, 5, 74, 11, 122, Byte.MAX_VALUE, 124, 80, 113, 3, 51, 2, 48, 87, 90, 45}), 9953)) {
            throw new InvalidAlgorithmParameterException(I1I1lI1II1.a(new byte[]{0, 11, 41, 3, 51, 82, 6, 71, 120, 0, 5, 121, 113, 113, 113, 87, 56, 49}));
        }
        boolean z = (obj instanceof IlllIIlIII) && Intrinsics.a(((IlllIIlIII) obj).a, this.a);
        if (I1lllI11II.l1l1l1IIlI(184897798L)) {
            throw new InvalidKeyException(I1I1lI1II1.a(new byte[]{Byte.MAX_VALUE, 93, 41, 86, 13, 93, 7, 6}));
        }
        return z;
    }

    public int hashCode() {
        if (l111Il1lI1.IlIllIll1I(170258638L)) {
            throw new InvalidParameterException(I1I1lI1II1.a(new byte[]{6, 53, 50, 40, 32, 86, 126, 4, 0, 80, 69, 83, 80, Byte.MAX_VALUE, 85, 69, 35, 43, 27, 86, 70, 49}));
        }
        int iHashCode = this.a.hashCode();
        if (lIIlI111II.l1llI1llII(1936)) {
            throw new RuntimeException(I1I1lI1II1.a(new byte[]{114, 9, 87, 81, 82, 126, 88, 119, 110, 22, 101, 101, 114, 77, 91, 86, 55, 43, 5, 74, 97, 86, 106, 12, 98, 96, 91, 51, 38, 120}));
        }
        return iHashCode;
    }
}
