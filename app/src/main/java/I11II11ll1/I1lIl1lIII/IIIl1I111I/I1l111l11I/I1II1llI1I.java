package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import androidx.recyclerview.widget.content.adapter.IIll1llI1l;
import java.io.CharConversionException;
import kotlin.jvm.internal.Intrinsics;
import kotlin.reflect.c;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class I1II1llI1I {
    public static <T> void a(ll111IIlI1 ll111iili1, c<T> cVar, I11IIl1I1I<T> i11IIl1I1I) throws CharConversionException {
        if (IIll1llI1l.Il1IIlI1II(6954)) {
            throw new CharConversionException(I1I1lI1II1.a(new byte[]{83, 19, 51, 13, 36, 125, 77, 81, 8}));
        }
        Intrinsics.checkNotNullParameter(cVar, I1I1lI1II1.a(new byte[]{92, 39, 14, 4, 17, 70}));
        Intrinsics.checkNotNullParameter(i11IIl1I1I, I1I1lI1II1.a(new byte[]{68, 1, 16, 12, 3, 89, 94, 74, 92, 22}));
        ll111iili1.a(cVar, new IIl1lIII11(i11IIl1I1I));
    }
}
