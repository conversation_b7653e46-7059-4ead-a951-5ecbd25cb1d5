package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import java.util.List;
import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\b0\u0018\u00002\u00020\u0001:\u0002\t\nB\t\b\u0004¢\u0006\u0004\b\u0007\u0010\bJ&\u0010\u0005\u001a\u0006\u0012\u0002\b\u00030\u00032\u0010\u0010\u0004\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00030\u0002H§\u0002¢\u0006\u0004\b\u0005\u0010\u0006\u0082\u0001\u0002\u000b\f"}, d2 = {"LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/lIIlI11IlI;", "", "", "LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "p0", "a", "(Ljava/util/List;)LI1lIIl11I1/l1lI1lll1l/IIlIllllII/lllII1II1I/I11IIl1I1I;", "<init>", "()V", "IlllIIlIII", "II1Il1llIl", "LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/IlllIIlIII;", "LI11II11ll1/I1lIl1lIII/IIIl1I111I/I1l111l11I/II1Il1llIl;"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public abstract class lIIlI11IlI {
    public abstract I11IIl1I1I<?> a(List<? extends I11IIl1I1I<?>> p0);

    private lIIlI11IlI() {
    }
}
