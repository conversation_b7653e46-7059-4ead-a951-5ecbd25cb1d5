package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.II11llll1I;
import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.Il11I111ll;
import java.util.List;
import kotlin.jvm.functions.Function1;
import kotlin.reflect.c;

/* loaded from: classes.dex */
public interface ll111IIlI1 {
    <T> void a(c<T> cVar, I11IIl1I1I<T> i11IIl1I1I);

    <T> void a(c<T> cVar, Function1<? super List<? extends I11IIl1I1I<?>>, ? extends I11IIl1I1I<?>> function1);

    <Base, Sub extends Base> void a(c<Base> cVar, c<Sub> cVar2, I11IIl1I1I<Sub> i11IIl1I1I);

    <Base> void b(c<Base> cVar, Function1<? super Base, ? extends II11llll1I<? super Base>> function1);

    <Base> void c(c<Base> cVar, Function1<? super String, ? extends Il11I111ll<? extends Base>> function1);
}
