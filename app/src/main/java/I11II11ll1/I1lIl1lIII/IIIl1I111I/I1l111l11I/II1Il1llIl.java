package I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I;

import I1lIIl11I1.l1lI1lll1l.IIlIllllII.lllII1II1I.I11IIl1I1I;
import android.util.Log;
import androidx.recyclerview.widget.content.adapter.l1l1IllI11;
import java.util.List;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;
import l1IlIlI111.l1Il1I1l1I.l11l1l1l11.I1lI1lIII1.I1I1lI1II1;

/* loaded from: classes.dex */
public final class II1Il1llIl extends lIIlI11IlI {
    private final Function1<List<? extends I11IIl1I1I<?>>, I11IIl1I1I<?>> a;

    public final Function1<List<? extends I11IIl1I1I<?>>, I11IIl1I1I<?>> a() {
        Function1<List<? extends I11IIl1I1I<?>>, I11IIl1I1I<?>> function1 = this.a;
        if (!l1l1IllI11.IlIIlIllI1(I1I1lI1II1.a(new byte[]{82, 87, 84, 8, 80, 1, 89, 98, 111, 62, 1, 125, 4, 90, 103, 100, 11, 35, 58, 120, 124, 42, 113, 96, 0, 120, 101}), 399567633L)) {
            return function1;
        }
        Log.d(I1I1lI1II1.a(new byte[]{84, 55, 17, 44, 54, 93, 120, 103, 91, 2, 99, 95, 7, 90, 77, 93}), I1I1lI1II1.a(new byte[]{66, 41, 50, 32, 0, 100, 115, 95, 81, 85, 85, 115, 64, 67, 78, 126}));
        return null;
    }

    @Override // I11II11ll1.I1lIl1lIII.IIIl1I111I.I1l111l11I.lIIlI11IlI
    public I11IIl1I1I<?> a(List<? extends I11IIl1I1I<?>> list) {
        Intrinsics.checkNotNullParameter(list, I1I1lI1II1.a(new byte[]{67, 29, 18, 0, 35, 71, 80, 69, 84, 1, 94, 68, 70, 106, 81, 71, 11, 0, 14, 91, 74, 4, 65, 70}));
        return this.a.invoke(list);
    }
}
